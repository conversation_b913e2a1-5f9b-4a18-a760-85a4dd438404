//server.js
const { readFileSync } = require('fs')
const path = require('path')
var express = require('express')
var app = express()
var { createProxyMiddleware: proxyMiddleWare } = require('http-proxy-middleware')
const getCookie = () => readFileSync(path.join(__dirname, './', 'cookie.txt')).toString().trim()
const SERVER_HOST = 'https://my.just.edu.cn/'

//如果它在最前面，后面的/开头的都会被拦截
app.get('/', (req, res) => res.send('Hello World!'))

// app.use(express.static('web'))
app.use(express.static('portalWechat'))

// app.get('/index.htm', function (req, res) {
//   res.sendFile(__dirname + '/' + 'index.htm')
// })

//404
app.use('/test', function (req, res, next) {
  res.status(404).send("Sorry can't find that!")
})

app.use(function (req, res, next) {
  //TODO 中间件，每个请求都会经过
  next()
})

const proxy = {
  '/sopplus': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/sopplus': '/sopplus' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  },
  '/mnews': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/mnews': '/mnews' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  },
  '/calendar': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/calendar': '/calendar' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  },
  '/default': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/default': '/default' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  },
  '/customized': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/customized': '/customized' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  },
  '/ywtbapi': {
    target: SERVER_HOST,
    changeOrigin: true,
    secure: false,
    pathRewrite: { '/ywtbapi': '/ywtbapi' },
    onProxyReq: function (proxyReq, res, req) {
      proxyReq.setHeader('cookie', getCookie())
    }
  }
}

for (let key in proxy) {
  app.use(key, proxyMiddleWare(proxy[key]))
}

app.listen(2000, () => console.log('Example app listening on port 2000!'))
