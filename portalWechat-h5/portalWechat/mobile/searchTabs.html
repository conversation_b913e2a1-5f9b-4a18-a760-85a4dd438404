<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta name="format-detection" content="telephone=no" />
    <title>信息检索</title>
    <link rel="stylesheet" href="../_libs/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="../_libs/css/arm.css" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="./css/searchTabs.css" />
  </head>
  <body class="bg-body">
    <section class="ui-section">

        <div class="listContent">
          <div class="topContent">
            <div class="topBox">
              <div class="motto">
                <img src="./images/logo.png" alt="" />
              </div>
            </div>
            <img class="topBg" src="images/topbg.png" alt="" />
            <div class="blurBox"></div>
          </div>
          <div class="searchContent">
            <div class="searchBox">
              <div class="inputBox">
                <div class="svgImg searchIcon">
                  <svg
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-57kesc"
                    focusable="false"
                    aria-hidden="true"
                    viewBox="0 0 16 16"
                  >
                    <path
                      d="M10.76,18.02a7.26,7.26,0,1,1,7.26-7.26A7.268,7.268,0,0,1,10.76,18.02Zm0-13.52a6.26,6.26,0,1,0,6.26,6.26A6.267,6.267,0,0,0,10.76,4.5Z"
                      transform="translate(-3.5 -3.5)"
                    ></path>
                    <path
                      d="M20.082,13.481a.5.5,0,0,1-.354-.146,2.851,2.851,0,0,0-4.031,0,.5.5,0,0,1-.707-.707,3.851,3.851,0,0,1,5.445,0,.5.5,0,0,1-.354.854Z"
                      transform="translate(-10.402 -8.312)"
                    ></path>
                    <path
                      d="M36.775,37.275a.5.5,0,0,1-.354-.146l-3.554-3.554a.5.5,0,1,1,.707-.707l3.554,3.554a.5.5,0,0,1-.354.854Z"
                      transform="translate(-21.203 -21.203)"
                    ></path>
                  </svg>
                </div>
                <input type="text" name="" id="" placeholder="请输入关键字" />
                <div class="svgImg cancelIcon hideBtn">
                  <svg
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1k33q06"
                    focusable="false"
                    aria-hidden="true"
                    viewBox="0 0 24 24"
                    data-testid="CloseIcon"
                  >
                    <path
                      d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="searchBtn">搜索</div>
            </div>
            <div class="mainTabContent">
              <div class="searchTabs">
                <div class="tabItem active" data-type="zx">资讯</div>
                <div class="tabItem" data-type="apps">应用</div>
              </div>
              <div class="searchInfo">
                <div class="searchInfoContent zx active">
                  <!-- <div class="item">
                    <div class="title"></div>
                    <div class="date"></div>
                    <div class="summary"></div>
                </div> -->
                </div>
                <div class="searchInfoContent apps">
                  <!-- <div class="item">
                    <img src="./images/appIcon1.png" alt="">
                    <div class="name">信息报送</div>
                </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>

    </section>
    <footer class="ui-footer ui-footer-btn">
      <ul class="ui-tiled">
        <li data-href="index.html">
          <div class="ysf-box">
            <div class="ub-img wx01"></div>
            <p class="ui-txt-tips">首页</p>
          </div>
        </li>
        <li data-href="articleCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx02"></div>
            <p class="ui-txt-tips">资讯</p>
          </div>
        </li>
        <li data-href="search.html" class="tiled-middle">
          <div class="ysf-box">
            <div class="ui-tiled-middle">
              <div class="ub-img wx03"></div>
            </div>
            <p class="ui-txt-tips">检索</p>
          </div>
        </li>
        <li data-href="serviceCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx04"></div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="my.html">
          <div class="ysf-box">
            <div class="ub-img wx05"></div>
            <p class="ui-txt-tips">我的</p>
          </div>
        </li>
      </ul>
    </footer>
  </body>
  <script src="js/jquery.min.js"></script>
  <script src="../_libs/js/arm.js"></script>
  <script src="../_libs/layui/layui.js"></script>
  <script src="js/utiles.js"></script>
  <script src="js/index_url.js"></script>
  <script src="js/searchTabs.js"></script>
</html>
<script>
  // 初始化日程日期
  // A.use(['arm.modal', 'arm.touch', 'arm.tpl', 'arm.mask', 'arm.paging'], function () {
    $('.ui-footer>ul>li').on('click', function () {
      var url = $(this).attr('data-href')
      window.location.replace(url)
    })
  // })
</script>
