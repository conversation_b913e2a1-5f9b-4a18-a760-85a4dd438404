﻿<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, user-scalable=0" />
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="pragma" content="no-cache" />
    <!-- Set render engine for 360 browser -->
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>事务中心</title>
    <link rel="stylesheet" href="../_libs/arm/css/arm.css" />
    <link rel="stylesheet" href="css/info.css" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/taskCenter.css" />
  </head>

  <body class="bg-body">
    <section class="ui-section">
      <div class="listContent">
        <div class="topContent">
          <div class="topBox">
            <div class="motto">
              <img src="./images/logo.png" alt="" />
            </div>
            <!-- <div class="searchBox">
              <input type="text" />
              <img src="./images/searchIcon.png" alt="" />
            </div> -->
          </div>
          <img class="topBg" src="images/topbg.png" alt="" />
          <div class="blurBox"></div>
        </div>
        <div class="taskContent">
          <div class="taskSearchBox">
            <div class="layui-btn-container">
              <div class="ui-select layui-btn layui-btn-primary taskCategory"></div>
            </div>
            <div class="searchBox">
              <div class="svgImg searchIcon">
                <svg
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-57kesc"
                  focusable="false"
                  aria-hidden="true"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M10.76,18.02a7.26,7.26,0,1,1,7.26-7.26A7.268,7.268,0,0,1,10.76,18.02Zm0-13.52a6.26,6.26,0,1,0,6.26,6.26A6.267,6.267,0,0,0,10.76,4.5Z"
                    transform="translate(-3.5 -3.5)"
                  ></path>
                  <path
                    d="M20.082,13.481a.5.5,0,0,1-.354-.146,2.851,2.851,0,0,0-4.031,0,.5.5,0,0,1-.707-.707,3.851,3.851,0,0,1,5.445,0,.5.5,0,0,1-.354.854Z"
                    transform="translate(-10.402 -8.312)"
                  ></path>
                  <path
                    d="M36.775,37.275a.5.5,0,0,1-.354-.146l-3.554-3.554a.5.5,0,1,1,.707-.707l3.554,3.554a.5.5,0,0,1-.354.854Z"
                    transform="translate(-21.203 -21.203)"
                  ></path>
                </svg>
              </div>
              <form action="" style="width: 100%">
                <input class="searchKey" type="search" placeholder="请输入关键字" />
              </form>
            </div>
          </div>
          <div class="taskInfo">
            <div class="ui-tab ui-tab-list">
              <ul class="ui-tab-nav ui-border-b">
                <li class="taskTabItem current ba-c bp-c lh17">
                  <div class="ysf-box bp-c">
                    <div>待办</div>
                  </div>
                </li>
                <li class="taskTabItem ba-c bp-c lh17">
                  <div class="ysf-box bp-c">
                    <div>已办</div>
                  </div>
                </li>
                <li class="taskTabItem ba-c bp-c lh17">
                  <div class="ysf-box bp-c">
                    <div>流程跟踪</div>
                  </div>
                </li>
                <!-- <li class="taskTabItem ba-c bp-c lh17">
                  <div class="ysf-box bp-c">
                    <div>办结</div>
                  </div>
                </li> -->
                <!-- <li class="taskTabItem ba-c bp-c lh17">
            <div class="ysf-box bp-c">
              <div>草稿箱</div>
            </div>
          </li> -->
              </ul>
            </div>
            <div class="taskInfoContent">
              <section class="taskList ui-container">
                <section class="content">
                  <div class="demo-block">
                    <div class="ui-tab">
                      <ul class="ui-tab-content daiban">
                        <li id="todotask" class="">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-0">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-0"></ul>
                            </div>
                          </div>
                        </li>
                        <!-- <li id="todoSend" class="">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-1">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-1"></ul>
                            </div>
                          </div>
                        </li> -->
                        <li id="donetask">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-1">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-1"></ul>
                            </div>
                          </div>
                        </li>
                        <li id="processTrack">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-2">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-2"></ul>
                            </div>
                          </div>
                        </li>
                        <li id="processDone">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-4">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-4"></ul>
                            </div>
                          </div>
                        </li>
                        <li id="cgx">
                          <div class="pullaction-box">
                            <div class="pullaction" id="pullaction-5">
                              <ul class="taskList ui-list ui-list-pure" id="news-list-5"></ul>
                            </div>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </section>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>
    <footer class="ui-footer ui-footer-btn">
      <ul class="ui-tiled">
        <li data-href="index.html">
          <div class="ysf-box">
            <div class="ub-img wx01"></div>
            <p class="ui-txt-tips">首页</p>
          </div>
        </li>
        <li data-href="articleCenter.html" >
          <div class="ysf-box">
            <div class="ub-img wx02"></div>
            <p class="ui-txt-tips">资讯</p>
          </div>
        </li>
        <li data-href="search.html" class="tiled-middle">
          <div class="ysf-box">
            <div class="ui-tiled-middle">
              <div class="ub-img wx03"></div>
            </div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="serviceCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx04"></div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="my.html">
          <div class="ysf-box">
            <div class="ub-img wx05"></div>
            <p class="ui-txt-tips">我的</p>
          </div>
        </li>
      </ul>
    </footer>
    <!-- 待办事项 -->
    <script type="text/html" id="daibaninfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var url =d[i].url;%> <%var time =
      d[i].createTimeStr?.length>16? d[i]?.createTimeStr?.slice(0, 16) :d[i].createTimeStr;%>
      <div class="taskItem" onclick="checkOpen('<%-url%>');">
        <%if(d[i].circulation){%>
        <div class="title"><span style="color:#007cff">[传阅]</span><%-d[i].title%></div>
        <%}else{%>
        <div class="title"><%-d[i].title%></div>
        <%}%>
        <div class="taskItemInfo todoInfo">
          <div class="taskItemInfo-form">
            <div class="from">
              <span>来源：</span>
              <span><%-d[i].category?.name%></span>
            </div>
            <div class="creater">
              <span>上一步经办人：</span>
              <span><%-d[i].creater?.name%></span>
            </div>
          </div>
          <div class="createTime">
            <span>创建时间：</span>
            <span><%-time%></span>
          </div>
        </div>
        <%if(d[i].markName && d[i].markName=="收回"){%>
        <img class="taskItem-img" src="./images/hs.png" alt="" />
        <%}%> <%if(!d[i].circulation){%>
        <img class="taskItem-img" src="./images/shz.png" alt="" />
        <%}%>
      </div>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>
    <!-- 待发事项 -->
    <script type="text/html" id="daifainfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var url = d[i].mobileUrl? d[i].mobileUrl :
      d[i].url;%> <%var time = d[i].createTimeStr?.length>16? d[i]?.createTimeStr?.slice(0, 16) :d[i].createTimeStr;%>
      <div class="taskItem" onclick="checkOpen('<%-url%>');">
        <div class="title"><%-d[i].title%></div>
        <div class="taskItemInfo">
          <div class="from">
            <span>来源：</span>
            <span><%-d[i]?.category?.name%></span>
          </div>
          <div class="createTime">
            <span>创建时间：</span>
            <span><%-time%></span>
          </div>
        </div>
      </div>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>
    <!-- 已办事项 -->
    <script type="text/html" id="yibaninfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var url = d[i].mobileDoneUrl? d[i].mobileDoneUrl:
      d[i].doneUrl? d[i].doneUrl:d[i].url;%> <%var time = d[i].doneTimeStr?.length>16? d[i]?.doneTimeStr?.slice(0, 16)
      :d[i].doneTimeStr;%>
      <div class="taskItem" onclick="checkOpen('<%-url%>');">
        <div class="title"><%-d[i].title%></div>
        <div class="taskItemInfo todoInfo">
          <div class="taskItemInfo-form">
            <div class="from">
              <span>来源：</span>
              <span><%-d[i].category?.name%></span>
            </div>
            <div class="creater">
              <span>上一步经办人：</span>
              <span><%-d[i].creater?.name%></span>
            </div>
          </div>

          <div class="createTime">
            <span>办理时间：</span>
            <span><%-time%></span>
          </div>
        </div>
        <%if(d[i].parentState==0){%>
        <img class="taskItem-img" src="./images/shz.png" alt="" />
        <%}%> <%if(d[i].parentState==1){%>
        <img class="taskItem-img" src="./images/bj.png" alt="" />
        <%}%> <%if(d[i].parentState==-1||d[i].parentState==-2||d[i].parentState==2){%>
        <img class="taskItem-img" src="./images/zf.png" alt="" />
        <%}%> <%if(d[i].parentState==-4){%>
        <img class="taskItem-img" src="./images/cgx.png" alt="" />
        <%}%>
      </div>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>

    <!-- 流程跟踪 -->
    <script type="text/html" id="genzonginfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var url = d[i].mobileUrl? d[i].mobileUrl :
      d[i].url;%> <%var time = d[i].createTimeStr?.length>16? d[i]?.createTimeStr?.slice(0, 16) :d[i].createTimeStr;%>
      <div class="taskItem" onclick="checkOpen('<%-url%>');">
        <div class="title"><%-d[i].title%></div>
        <div class="taskItemInfo todoInfo">
          <div class="taskItemInfo-form">
            <div class="from">
              <span>来源：</span>
              <span><%-d[i].category?.name%></span>
            </div>
            <div class="creater">
              <span style="white-space: nowrap;">当前环节：</span>
              <span><%-d[i].processCurrentTask%></span>
            </div>
          </div>
          <div class="createTime">
            <span>创建时间：</span>
            <span><%-time%></span>
          </div>
        </div>
        <img class="taskItem-img" src="./images/shz.png" alt="" />
      </div>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>

    <!-- 办结流程 -->
    <script type="text/html" id="banjieinfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var url = d[i].mobileUrl? d[i].mobileUrl :
      d[i].url;%> <%var time = d[i].doneTimeStr?.length>16? d[i]?.doneTimeStr?.slice(0, 16) :d[i].doneTimeStr;%>
      <div class="taskItem" onclick="checkOpen('<%-url%>');">
        <div class="title"><%-d[i].title%></div>
        <div class="taskItemInfo">
          <div class="from">
            <span>来源：</span>
            <span><%-d[i].category?.name%></span>
          </div>
          <div class="createTime">
            <span>办理时间：</span>
            <span><%-time%></span>
          </div>
        </div>
        <img class="taskItem-img" src="./images/bj.png" alt="" />
      </div>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>

    <!-- 草稿箱 -->
    <script type="text/html" id="cgxinfo">
      <%if(d && d.length>0){%> <%for(var i=0;i < d.length;i++){%> <%var cgxUrl = d[i].url;%>
      <li class="pad11 border-b gohref draft-item">
        <div class="draft-tile" onclick="checkOpen('<%-cgxUrl%>');">
          <div class="pad15  ysf-box bo-v">
            <div class="ysf-box  ysf-box-f1 ba-c pad16" style="font-size:16px;">
              <%=d[i].title.replace(/＜/g,"<").replace(/＞/g,">")%>
            </div>
            <div class="ftc05 fts14">来源：<%-d[i].processChName%></div>
          </div>
        </div>
        <div class="del-draft" style="margin-left: 10px;" data-id="<%-d[i].id%>">
          <img src="./images/delete.png" alt="" style="width: 24px;" />
        </div>
      </li>
      <%}%> <%}else{%>
      <div class="nodataBox">
        <div class="noDataImg">
          <svg
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 64 41"
          >
            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
              <ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse>
              <g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                ></path>
                <path
                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                  style="fill: rgb(250, 250, 250);"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <div class="nodata">暂无数据</div>
      </div>
      <%}%>
    </script>

  </body>
    <script src="../_libs/js/arm.js"></script>
    <script src="modules/underscore.js"></script>
    <script src="js/utiles.js"></script>
    <script src="js/index_url.js"></script>
    <script src="js/fastclick.js"></script>
    <script src="js/taskCenter.js"></script>
    <script>
     $('.ui-footer>ul>li').on('click', function () {
        var url = $(this).attr('data-href')
        window.location.replace(url)
      })
</script>
</html>

