﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, user-scalable=0"/>
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="pragma" content="no-cache"/>
    <!-- Set render engine for 360 browser -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>消息中心</title>
    <link rel="stylesheet" href="../_libs/arm/css/arm.css" />
    <link rel="stylesheet" href="css/info.css" />
    <script src="js/underscore.js"></script>
    <script src="../_libs/js/arm.js"></script>
     <!-- <script src="./js/utiles.js"></script> -->
    <!-- <script src="./js/common.js"></script> -->
    <script src="js/utiles.js"></script>
    <script src="js/index_url.js"></script>
    <script type="text/javascript">
        app.config({
            debug: false, // 调试
            iPortal: false, // 是否需要 iPortal API
            base: "../"
        })
    </script>
    <style type="text/css">
        li{
            cursor: pointer;
        }
        span{
            cursor: pointer;
        }
        .ui-form-item input{
            height: 100%;
        }
        #xiaoxi{
            max-height: calc(100% - 190px);
            overflow-y: auto;
        }
        .bg-content{
            box-sizing: border-box;
            margin-top: 0;
            width: 100%;
            padding: 0 10px;
        }
        .bg-content .ui-form-item input{
            font-size: 16px;
        }
        .ui-pullaction-container{
            height: 100%;
        }
        .btn-search{
            background-color: #007CFF;
            border-color: #007CFF;
            background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #007CFF), to(#007CFF));
        }
       
        @media (max-width: 375px){
            .bg-content .ui-form-item{
                padding-left: 5px;
                padding-right: 5px;
            }
          
        }
        @media (max-width: 320px){

            .bg-content .ui-form-item input{
            font-size: 14px;
        }
        .bg-content .ui-form-item .endweek{
                padding-right: 5px;
            }
        .bg-content .ui-form-item .startweek,.bg-content .ui-form-item .endweek{
            font-size: 11px;
        }
        }
    </style>
</head>
<body class="bg-body">
<!--header class="ui-header ui-header-positive ui-border-b">
    <i class="ui-icon-return" onclick="history.back()"></i>

    <h1>消息中心</h1><i class=""></i>
</header-->
<section class="ui-container">
    <section style="height: 100%;overflow: hidden">
        <div class="ui-form-item ui-border-b mar01 bg-content">
            <input type="text" placeholder="输入关键字" class="keywords"/>
        </div>
        <div class="ysf-box ysf-box-f1 bg-content">
            <div class="ysf-box-f1 ysf-box bo-v border-r wid50">
                <div class="pad04 ftc03 fts13">开始时间</div>
                <div class="ui-form-item ui-border-b ysf-box ysf-box-f1">
                    <div class="wid65">
                        <input type="text" class="ui-select-date starttime ftc04 fts18" placeholder="开始时间">
                    </div>
                    <div class="fts13 startweek"></div>
                </div>
            </div>
            <div class="ysf-box ysf-box-f1  wid50">
                <div class="ysf-box  bo-v border-b pad05">
                    <div class="pad04 ftc03 fts13">结束时间</div>
                    <div class="ui-form-item  ysf-box ysf-box-f1">
                        <div class="wid65 ysf-box-f1">
                            <input type="text" class="ui-select-date endtime ftc04 fts18" placeholder="结束时间">
                        </div>
                        <div class="fts13 pad10 endweek"></div>
                    </div>
                </div>
            </div>
        </div>
        <div style="position: relative;">
            <span class="countday">
            7天
            </span>
        </div>
        <div class="bg-content">
            <button class="ui-btn-lg btn-search ">开始搜索</button>
        </div>
        <section class="message" id="xiaoxi">
            <div class="bg-content mar01 pad01 infolist">
                <!--<div class="ysf-box ysf-box-f1 bo-v border-b">-->
                    <!--<div class="ysf-box pad08 ysf-box-f1 ba-c">-->
                        <!--<div class="ysf-box ysf-box-f1 ba-c">-->
                            <!--<div class="cicle"></div>-->
                            <!--<div class="pad07 fts16">系统管理员</div>-->
                            <!--<div class="theme">主题一</div>-->
                        <!--</div>-->
                        <!--<div class="ysf-box ba-c">-->
                            <!--<div class="fts-time ub-img"></div>-->
                            <!--<div class="ftc05">11-15 09:00</div>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="ftc06 pad09">-->
                        <!--功能纳米与软物质研究院学术报告：传统电池电极反应的新应用和下一代电池体系的研究。-->
                    <!--</div>-->
                <!--</div>-->
            </div>
        </section>
    </section>
</section>
<script type="text/html" id="message">
    <%if(m && m.length){%>
    <%for(var i=0;i < m.length;i++){%>
    <div class="ysf-box ysf-box-f1 bo-v border-b countheight">
        <div class="ysf-box pad08 ysf-box-f1 ba-c">
            <div class="ysf-box ysf-box-f1 ba-c">
                <div class="cicle"></div>
                <div class="pad07 fts16"><%-m[i].sender%></div>
                <!--<%if(m[i].subject != ""){%>-->
                <!--<div class="theme"><%-m[i].subject%></div>-->
                <!--<%}%>-->
            </div>
            <div class="ysf-box ba-c">
                <div class="fts-time ub-img"></div>
                <div style="margin-right:20px" class="ftc05"><%-m[i].createTime%></div>
            </div>
        </div>
        <div class="ftc06 pad09">
           <%-m[i].content%>
        </div>
    </div>
    <%}%>
    <%}else{%>
    <%}%>
</script>
<script type="text/javascript">
    app.init(function () {
        A.use(['../ding/mobile/js/message.js']); // 初始化APP，载入当前页模块
    });
</script>
<script type="text/javascript">
    if(window.localStorage){
        localStorage.clear();
    }
</script>
</body>
</html>