<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta name="format-detection" content="telephone=no" />
    <title>江苏科技大学</title>
    <!-- 日程相关 -->
    <link rel="stylesheet" href="../_libs/calendar/css/arm.css" />
    <link rel="stylesheet" href="../_libs/calendar/css/arm.animation.css" />
    <link rel="stylesheet" href="../_libs/calendar/css/arm.calendar.css" />
    <link rel="stylesheet" href="../_libs/calendar/css/demo.css" />
    <link rel="stylesheet" href="css/mytimeTable.css" />

    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/swiper-bundle.min.css" />
    <link rel="stylesheet" href="css/index_swiper.css" />
    <link rel="stylesheet" href="css/index.css" />
    <!-- <script src="js/vconsole.min.js"></script> -->
  </head>
  <!-- <style>
    #__vconsole .vc-switch{z-index: 999999999;}
    #__vconsole .vc-panel {z-index: 9999999999;}
    </style> -->
  <body class="bg-body">
    <section class="ui-section">
      <div class="mainContent">
        <!-- 头部内容 -->
        <div class="topContent">
          <div class="topBox">
            <div class="motto">
              <img src="./images/logo.png" alt="" />
            </div>
            <div class="searchBox">
              <input type="text" />
              <img src="./images/searchIcon.png" alt="" />
            </div>
          </div>
          <div class="appNav"></div>
          <img class="topBg" src="images/topbg.png" alt="" />
          <!-- 背景图底部效果 -->
          <div class="blurBox"></div>
        </div>
        <div class="middleContent">
          <div class="newsModel todaWeek">
            <div class="common">
              <div class="tabBox">
                <div class="tab-list">
                  <div class="tab-item tab-selected" data-type="task">
                    <div class="toda">我的待办</div>
                  </div>
                  <div class="tab-item"data-type="mySchedule">
                    <div>我的日程</div>
                  </div>
                  <!-- <div class="tab-item" data-type="meeting">
                    <div>周会表</div>
                  </div> -->
                </div>
                <div class="tab-con">
                  <div class="tab-item todotask tab-selected loading" data-type="todo">
                    <div class="todo-list"></div>
                  </div>
                  <div class="tab-item processTrack loading">
                    <div class="weekInfo">
                      <div class="mySchedule-container">
                        <div class="mySchedule-content">
                          <!-- 日程日期部分 -->
                          <div id="scheduleDate"></div>
                          <!-- 日程内容区域 -->
                          <div class="scheduleInfoContent">
                            <!-- 我的日程 -->
                            <div class="contentBox mySchedule">
                              <div class="content">
                                <div class="myScheduleBox">
                                  <!-- 我的日程 -->
                                  <div class="scheduleItem all active">
                                    <div class="scheduleList"></div>
                                    <div class="calButton">
                                      <div class="addCal">添加</div>
                                      <div class="delCal">删除个人日程</div>
                                      <div class="cancelDel">取消删除</div>
                                    </div>
                                    <div class="schedule-arrow">
                                      <div class="schedule-arrowBtn"><img src="./images/down.png" alt="" /></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="tab-item meeting loading">
                    <div class="news"><div class="news_box"></div></div>
                  </div>
                  <div class="tab-more">
                    <div class="more moreBtn" data-type="task">查看更多</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 特色主题 -->
          <div class="specialTopic notice">
            <div class="moduleTitleBox">
              <div class="titleTab">
                <div class="tabItem active">
                  <span>校内通知</span>
                  <!-- <img src="./images/titleIcon.png" alt="" /> -->
                </div>
              </div>
              <div class="moreBtn" data-type="topic">更多</div>
            </div>
            <div class="specialTop_tab"></div>
            <div class="topicList">
              <!-- 内容 -->
            </div>
          </div>
          <!-- 特色主题 -->
          <div class="specialTopic article">
            <div class="moduleTitleBox">
              <div class="titleTab">
                <div class="tabItem active">
                  <span>学校资讯</span>
                  <!-- <img src="./images/titleIcon.png" alt="" /> -->
                </div>
              </div>
              <div class="moreBtn" data-type="articleCenter">更多</div>
            </div>
            <div class="specialTop_tab"></div>
            <div class="topicList">
              <!-- 内容 -->
            </div>
          </div>
        </div>
      </div>
    </section>
    <footer class="ui-footer ui-footer-btn">
      <ul class="ui-tiled">
        <li data-href="index.html" class="active">
          <div class="ysf-box">
            <div class="ub-img wx01"></div>
            <p class="ui-txt-tips">首页</p>
          </div>
        </li>
        <li data-href="articleCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx02"></div>
            <p class="ui-txt-tips">资讯</p>
          </div>
        </li>
        <li data-href="search.html" class="tiled-middle">
          <div class="ysf-box">
            <div class="ui-tiled-middle">
              <div class="ub-img wx03"></div>
            </div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="serviceCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx04"></div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="my.html">
          <div class="ysf-box">
            <div class="ub-img wx05"></div>
            <p class="ui-txt-tips">我的</p>
          </div>
        </li>
      </ul>
    </footer>
  </body>
</html>
<script src="../_libs/calendar/lib/zepto.min.js"></script>
<script src="../_libs/calendar/js/arm.js"></script>
<script src="js/swiper-bundle.min.js"></script>

<script src="js/utiles.js"></script>
<script src="js/index_url.js"></script>
<script src="js/index.js"></script>
<script>
  // 初始化日程日期
  A.use(['arm.calendar', 'arm.modal', 'arm.touch', 'arm.tpl', 'arm.mask', 'arm.paging'], function () {
    var calendar = new $.calendar('#scheduleDate')
    //可设置日历开始时间
    // calendar.setCurday(curDate);
    $('.ui-footer>ul>li').on('click', function () {
      var url = $(this).attr('data-href')
      window.location.replace(url)
    })
  })
</script>
