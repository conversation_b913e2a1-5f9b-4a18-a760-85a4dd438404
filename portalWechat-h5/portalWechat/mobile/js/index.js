$(function () {
  openModule('com.just.mhfwtj-wechat');//访问统计
  getTopApps(); //获取头部应用
  // 日程相关变量
  scheduleInfo = {
      showKb:false,
      categoryids:[],
      categoryInfo:{},
      hasLoadCalCategory:false,
      weekStartDate:'',
      weekEndDate:'',
      hasLoadHaveKb:false
  }
  var userLb= localStorage.getItem('userLb');
  var _noticeTab = [
    {
        "id": "2",
        "name": "党委文件"
    },
    {
        "id": "4",
        "name": "教育通报"
    },
    {
        "id": "1",
        "name": "行政文件"
    },
    {
        "id": "9",
        "name": "校内通知"
    },
    {
        "id": "6",
        "name": "部门文件"
    },
    {
        "id": "5",
        "name": "纪委文件"
    },
    {
        "id": "8",
        "name": "信息简报"
    }
  ]

  if(userLb ==='ls'){
    $(".specialTopic.article").css({"display":"none"})
    //校内通知
    _noticeTab.forEach(function (item,index) {
      $(".specialTopic.notice .specialTop_tab").append(`<div class="topicItem" data-id=${item.id}>${item.name}</div>`);
      if(index === 3){
        $(".specialTopic.notice .specialTop_tab").append(`<div class="addToggle active"></div>`);
      }
    });
  }else{
    $(".specialTopic.notice").css({"display":"none"})
    querySubs()
  }

  //获取订阅栏目
  function querySubs() {
    $.ajax({
      url: index_urls.querySubsUrl,
      data: {},
      success: function (res) {
        var data = res.data ? res.data : {}
        var subs = data.subs ? data.subs : []
        var subsHtml = ''
        for (var i = 0; i < subs.length; i++) {
          var this_isActive = i === 0 ? 'active' : ''
          var item = subs[i]
           subsHtml += `
            <div class="topicItem ${this_isActive}" data-siteid="${item.siteId}" data-columnid="${item.columnId}">${item.columnName}</div>
          `
          if(i === 3){
            subsHtml += `<div class="addToggle"></div>`;
          }
          if (i === 0) {
            getArticleList(item.siteId, item.columnId)
          }
        }
        $('.specialTopic.article .specialTop_tab').html(subsHtml)
      },
      error(err) {}
    })
  }

  /* 资讯或通知共用tab */
  $(document).on("click", ".specialTopic .specialTop_tab .addToggle", function (e){
    e.stopPropagation();
    if($(this).hasClass('active')){
      $(this).removeClass('active');
      $(this).parents('.specialTop_tab').css({height:'auto',"overflow": "inherit"})
    }else{
      $(this).addClass('active');
       $(this).parents('.specialTop_tab').css({height:38,"overflow": "hidden"})
    }
  })

  //通知
  $(document).on("click", ".specialTopic.notice .specialTop_tab .topicItem", function (e){
    e.stopPropagation();
    $(this).addClass('active').siblings().removeClass('active');
    getArticleNoticeList($(this).data('id'))
  })

  //学校资讯
  $(document).on("click", ".specialTopic.article .specialTop_tab .topicItem", function (e){
    e.stopPropagation();
    $(this).addClass('active').siblings().removeClass('active');
    getArticleList($(this).data('siteid'),$(this).data('columnid'))
  })

  $(".specialTopic .specialTop_tab .topicItem").eq(0).trigger('click');

  //校内通知
  function getArticleNoticeList(noticeId){
    $('.specialTopic.notice .topicList').html('<div class="loading">加载中</div>')
    $.ajax({
      url: index_urls.getXntxList,
      data: {
        page: 1,
        pageSize: 4,
        id:noticeId,
      },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        const {columnLinkUrl,articles:data} = res.data ?? {};
        var _tplHtml = '';
        if(data.length > 0){
          data.forEach(item => { 
            _tplHtml +=`<div class="topicItem">
                  <a class="item-box" href=${item.url}>
                      <div class="news-time">${new Date(item.publishDate.replace(/-/g,'/')).format('YYYY-MM-dd')}</div>
                      <div class="news-title">${item.title}</div>
                  </a>
                </div>`;
          });
        }else{
          _tplHtml = `<div class="todo-item noData">
              <div class="noDate nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
            </div>`
        }
        $('.specialTopic.notice .topicList').html(_tplHtml)
        $('.specialTopic.notice .moreBtn').attr('data-url',columnLinkUrl)
      }
    });
  }

  //学校资讯
  function getArticleList(siteid,columnid){
    $('.specialTopic.article .topicList').html('<div class="loading">加载中</div>')
    $.ajax({
      url: index_urls.getPortalArticleList16,
      data: {
        beginIndex: 0,
        pageSize: 4,
        siteId:siteid,
        columnId:columnid
      },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        const {columnLinkUrl,articles:data} = res.data ?? {};
        var _tplHtml = '';
        if(data.length > 0){
          data.forEach(item => { 
            _tplHtml +=`<div class="topicItem">
                  <a class="item-box" href=${item.linkUrl}>
                      <div class="news-time">${new Date(Number(item.publishTimestamp)).format('YYYY-MM-dd')}</div>
                      <div class="news-title">${item.title}</div>
                  </a>
                </div>`;
          });
        }else{
          _tplHtml = `<div class="todo-item noData">
              <div class="noDate nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
            </div>`
        }
        $('.specialTopic.article .topicList').html(_tplHtml)
        $('.specialTopic.article .moreBtn').attr('data-url',`siteId=${siteid}&columnId=${columnid}`)
      }
    });
  }
});

//待办数据
function getTodoTaskNum(){
  $('.middleContent .todo-list').html('<div class="loading">加载中</div>')
  $.ajax({
    url: index_urls.getToDoUrl,
    data: {},
    crossDomain: true,
    dataType: "jsonp",
    success: function (res) {
      const {data,total} = res.result ?? {};
      var taskNum = parseInt(total ?? 0);
      var _tplHtml = '';
      if(taskNum >= 0){
        $('.middleContent .todaWeek .toda').append(`<i class="taskNum">${taskNum>99?'99+':taskNum}</i>`)
      }
      if(data.length > 0){
        data.forEach((item,index) => { 
          if(index <=4 ){
          _tplHtml +=`<div class="todo-item" data-url="${item.url + '&f=wechat&appload=0'}">
                    <div class="todo-item-box">
                        <div class="todo-title">${item.title}</div>
                        <div class="todo-content">
                          <span class="todo-meta">来源: ${item?.category?.name ?? '-'}</span>
                          <span class="todo-meta">申请人: ${item.creater?.name ?? '-'}</span>
                          <span class="todo-meta">创建时间: ${item.createTimeStr}</span>
                        </div>
                    </div>
                  </div>`;
          } 
        });
      }else{
        _tplHtml = `<div class="todo-item noData">
              <div class="noDate nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
            </div>`
        var _item = $(".todaWeek .tab-more .moreBtn")  
        if(_item.data("type") === 'task'){
        _item.css({'display':'none'});   
        }
      }
      $('.middleContent .todo-list').html(_tplHtml)
    }
  });
}

//获取当前用户身份
function getUserLb(callback){
    $.ajax({
        url: index_urls.getUserLbUrl,
        data: {},
        crossDomain: true,
        dataType: "jsonp",
        success: function (res) {
            var data = res.data || 'xs'
            userLb = data;
            localStorage.setItem('userLb', userLb);
            if(typeof callback=="function"){
              callback(data)
            }
        },
        error(err){
          // callback('xs')
        }
    });
}

//获取头部应用
async function getTopApps() {
  $.ajax({
    url: index_urls.getTopAppsUrl,
    type: 'get',
    data: {
      clientType: 3,
      categoryId: 56,
      page: 0,
      pageSize: 4,
      _t: new Date().getTime()
    },
    dataType: 'jsonp',
    success: async function (res) {
      const appList = res.data ?? [];
      let hasTask = false;
      let hasMail = false;
      let hasMsg = false;

      const APP_TYPES = {
        'com.just.fwdt-wechat': { type: 'appCenter', count: false, className: 'appNum', flag: () => { hasTask = true } },
        'com.mycc.todo-dingtalk': { type: 'task', count: true, className: 'taskNum', flag: () => { hasTask = true } },
        'com.just.email-wechat': { type: 'mail', count: true, className: 'mailNum', flag: () => { hasMail = true } },
        'com.mycc.sudy.message-dingtalk': { type: 'message', count: true, className: 'messageNum', flag: () => { hasMsg = true } },
        'com.just.yjt-wechat': { type: 'card' }
      };

      // 异步生成每个 item 的 HTML
      const htmlPromises = appList.map(item => generateAppItemHtml(item, APP_TYPES));
      const htmlItems = await Promise.all(htmlPromises);
      const appListStr = htmlItems.join('');

      if (appListStr) {
        $('.topContent .appNav').html(appListStr);
      }

      if (hasMail) getUnReadCount();
      if (hasMsg) getMsgNum(); // 确保该函数存在
    },
    error: function (err) {}
  });
}

//获取应用列表
async function generateAppItemHtml(item, APP_TYPES) {
  const { appName: _appName } = item
  const appConfig = APP_TYPES[_appName] || {}
  if (appConfig.flag) appConfig.flag() // 设置标志位
  const { type = '', count = false, className = '' } = appConfig

  let _isSvg = false
  let _svgDetail = ''
  if (item.iconUrl?.slice(-4) === '.svg') {
    _svgDetail = await imgToSvg(item.iconUrl)
    _isSvg = true
  }

  return `
    <div class="appItem ${type}"
         data-type="${type}"
         data-appurl="${item.entranceUrl}"
         data-enableservice="${item.enableService}"
         data-isallow="${item.isAllow}"
         data-reason="${item.reason}"
         data-appname="${item.appName}"
         data-id="${item.id}">
      <div class="imgBox">
        ${_isSvg && _svgDetail ? `<div class="svgImg">${_svgDetail}</div>` : `<img src="${item.iconUrl}" alt="">`}
        ${count ? `<span class="count ${className}"></span>` : ''}
      </div>
      <div class="title">${item.name}</div>
    </div>`
}

//头部应用跳转
$(document).on("click", " .topContent .appNav .appItem", function (e){
    e.stopPropagation();
    var _type = $(this).data('type');
    var _url = $(this).data('appurl');
    switch(_type){
      case "mail":
        if($(this).hasClass("createMail")){
             dia = $.iframe({
              title:"绑定邮箱",
              content:"createMail.html?_p=YXM9MSZwPTEmbT1OJg__",
              modalClassName:'iframe-schedule',
              onOpen: function(e, iframe){
                var iframe = $('iframe'); 
                iframe.on('load', function() {
                    iframe.attr('id', 'iframeWrapper');
                });
              }
            }).on("iframe:cancel",function(e, iframe){
            }).on("iframe:closed",function(e, iframe){
            });
      }else{
        if(_url){
          commonToUrl(_url,'_blank');   //进入邮箱
        }
      }
        break;
      case "appCenter":
         commonToUrl(commonUrlTwo + '/_web/portalWechat/mobile/serviceCenter.html');
        break;
      case "task":
         commonToUrl(_url, '_blank');
        break;
      case "card":
        commonToUrl(_url, '_blank');
        // $.poptips('正在建设中!')
        break;
      default:
        //其他应用
        toAppUrl($(this));
        break;
    }
})

//待办tab切换
const newModeltab = document.querySelectorAll('.newsModel .tab-list .tab-item');
const newModelcon = document.querySelectorAll('.newsModel .tab-con .tab-item');
newModeltab.forEach((item, index) => {
  item.addEventListener('click', function () {
    newModeltab.forEach((el, i) => {
      index !== i ? el.classList.remove('tab-selected') : el.classList.add('tab-selected');
      index !== i ? newModelcon[i].classList.remove('tab-selected') : newModelcon[i].classList.add('tab-selected');
      $(".todaWeek .moreBtn").css({'display':index !== 0 ? 'block' : 'block'})
      $(".todaWeek .moreBtn").attr("data-type",index !== 0 ? 'schedule' : 'task')
    });
  });
});

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * 邮箱
 */
//获取邮箱未读数量
function getUnReadCount(){
  $('.topContent .appNav .appItem.mail').attr('data-appurl',index_urls.getMailUrl);//邮箱地址修改
  $.ajax({
    url: index_urls.getUnReadCountUrl,
    data: {},
    crossDomain: true,
    success: function (res) {
      const data = res.data || {};
      if(Object.prototype.hasOwnProperty.call(data, 'unreadCount')){
        let unreadCount = parseInt(data.unreadCount, 10);
        if (!isNaN(unreadCount) && unreadCount > 0) {
          const $mailNum = $('.topContent .appNav .appItem .mailNum');
          $mailNum.html(unreadCount > 99 ? '99+' : unreadCount).show();
        }
      }else{
        $('.topContent .appNav .appItem.mail').addClass('createMail');
        $('.topContent .appNav .appItem .mailNum').html('待绑定').show();
      }
    },
  });
}

//////////////////////////////////////////////////////////////////////////////////////////////
//判断人员是否有日程和周会表
function getUserIsHaveKb(){
  getUserLb(function(user){
    // console.log('======user',user)
    // if(user === 'xs' ){
      renderScheduleTab(0)
    // }else{
    //   renderScheduleTab(0)
    // }
  })
}

//日程初始化-已整理
function renderScheduleTab(total){
  if(total === 0){
    scheduleInfo.showKb = false;
    $('#scheduleDate .calendar-week-bar').addClass('myScheduleWeek').removeClass('meetingWeek');
    $(".mySchedule-container .scheduleInfoContent .contentBox.mySchedule").addClass('active').siblings().removeClass('active');
    getTodoTaskNum()
  }else{
    // var tabStr = '<div class="tab-item"><div>周会表</div></div>';
    // $('.middleContent .todaWeek .tab-list').append(tabStr);
    // getKbWeek();//加载一周会表
  }
}

//日程删除
$(document).on("click", ".mySchedule-container .delCal", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.mySchedule-container .mySchedule-content .cancelDel').show();
  $('.mySchedule-container .schedule-item .delItem').show();
});

//取消日程删除
$(document).on("click", ".mySchedule-container  .cancelDel", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.mySchedule-container .mySchedule-content .delCal').show();
  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
  $('.mySchedule-container .schedule-item .delItem').hide();
})

//加载日程分类-已整理
function renderCalCategory(){
  scheduleInfo.hasLoadCalCategory = true;
  // var checkedUrl = checkIdentity(index_urls.getCalendarCategoryUrl);
  // 不用走weAuth
  var checkedUrl = index_urls.getCalendarCategoryUrl;
  $.ajax({
      url: checkedUrl,
      data: { queryType: 2 },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        var data = res.result.data;
        for (var i = 0; i < data.length; i++) {
          var obj = data[i];
          if (obj.teamCategoryVOS) {
            for (var j = 0; j < obj.teamCategoryVOS.length; j++) {
              var obj2 = obj.teamCategoryVOS[j];
              var this_categoryid = obj.teamCategoryVOS[j].id;
              // if (obj2.checked && (this_categoryid !== -2)) {
              if (obj2.checked) {
                scheduleInfo.categoryids.push(obj2.id);
              }
              var this_categoryColor = obj.teamCategoryVOS[j].calendarCateColor;
              scheduleInfo.categoryInfo[this_categoryid] = this_categoryColor;
            }
          }
        }
        console.log('===scheduleInfo==',scheduleInfo)
        getCalendarWeekData(); //加载各日程分类一周数据
      },
      error:function(err){
        getCalendarWeekData();
      }
    });
}

//获取日程一周数据
function getOneWeekSched(start,end){
    scheduleInfo.weekStartDate=start;
    scheduleInfo.weekEndDate = end;
    //是否加载过日程分类-
    var hasLoadCalCategory = scheduleInfo.hasLoadCalCategory;
    if(hasLoadCalCategory === false){
      // 先获取日程分类
      renderCalCategory();
    }else{
      // 加载过直接获取各日程分类的一周数据
      getCalendarWeekData();
    }
}

//获取各日程数据-已整理
function getCalendarWeekData(){
  //先清掉之前的数据标记
  $('#scheduleDate .calendar-week-bar li').removeClass('hasSchedule hasMeeting');
  var start=scheduleInfo.weekStartDate;
  var end= scheduleInfo.weekEndDate;
  var categoryids = scheduleInfo.categoryids;
  //获取后一天日期
  end = getDateLater(1,end);
  $.ajax({
      url: index_urls.getCalendarListUrl,
      type: 'get',
      dataType: 'jsonp',
      data: {
        categoryIds: categoryids.join(),
        queryType: 2,
        type: 0,
        fromDate: new Date(start).toISOString(),
        endDate: new Date(end).toISOString()
      },
      success: function (res) {
        const currentTime = new Date().format('YYYY-MM-DD')
        const _res = res?.map(item => {
            return {
                day: item.day?.slice(0, 10),
                events: (item.events ?? [])?.map(i => ({ ...i, cateGoryId: (i.schedule.cateGory?.id)?.toString(), beginTime: i.schedule.effectiveTime, endTime: i.schedule.finishTime }))
            }
        })
        const currentDate = _res.filter(item => item.day === currentTime)
        const _tempData = currentDate?.[0]?.events.filter(item => item.cateGoryId === '0')
        // setState({
        //     eventsData: _res,
        //     currentDateEvents: currentDate,
        //     currentEvents: _tempData
        // })
     
      console.log('==数据结果=',_res)
      //分别渲染会议和日程数据--已分开
      renderScheduleData(_res,'all');
      // renderScheduleData(myScheduleList,'mySchedule');
      // renderScheduleData(conferenceList,'conference');
      // renderScheduleData(ddScheduleList,'ddSchedule');
      },
      error: function (err) {
        renderScheduleData([],'all');
        // renderScheduleData([],'conference');
        // renderScheduleData([],'ddSchedule');
      }
  })
}

//按类-渲染日程数据-已整理
function renderScheduleData(res, type){
    var calWeekStr = "";
    for(var i=0;i<(res.length>7?7:res.length);i++){
        var this_events =res[i].events;
        this_events = this_events?this_events:[];
        if(this_events.length>0){
            //日期下方 添加点
            $('.myScheduleWeek li').eq(i).addClass('hasSchedule');
        }
        calWeekStr += renderPerCalList(res[i],i,type);
    }
    if(calWeekStr ===''){
      calWeekStr =`
                <div class="dayInfo Sun"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Mon"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Tue"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Wed"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Thu"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Fri"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Sat"><div class="noData">当前日期没有相关事件</div></div>
      `;
    }

    // console.log('===calWeekStr==',calWeekStr)
    //获取当天周几
    var curIndex = $('#scheduleDate .calendar-days.calendar-cur-view .grid-curday ').index();
    $('.mySchedule-container .mySchedule-content .myScheduleBox .scheduleList').html(calWeekStr);
    //展示当天对应数据
    $('.mySchedule-container .scheduleInfoContent .scheduleItem.all .dayInfo').eq(curIndex).addClass('active').siblings().removeClass('active');

}

//渲染日程列表 
function renderPerCalList(data, activeClass, type){
  var calendarHtml="";
  var this_day = data.day;
  this_day = this_day.length>10?this_day.slice(0,10):this_day;
  data = (data.events)?(data.events):[];
   //  <div class="dayInfo Sun">
   var this_weekdayClass = ''
    switch(activeClass){
    case 0:
      this_weekdayClass='Sun';
        break;
    case 1:
      this_weekdayClass='Mon';
        break;
    case 2:
      this_weekdayClass='Tue';
         break;
    case 3:
      this_weekdayClass='Wed';
        break;
        case 4:
      this_weekdayClass='Thu';
        break;
    case 5:
      this_weekdayClass='Fri';
        break;
    case 6:
      this_weekdayClass='Sat';
        break;
  }
  if(data.length>0){
      //各自添加高亮
      if(type === 'meeting'){
          $('.calendar-week-bar .calendar-week-label').eq(activeClass).addClass('hasMeeting');
      } else if((type === 'mySchedule') || (type === 'conference') || (type === 'ddSchedule')){
          $('.calendar-week-bar .calendar-week-label').eq(activeClass).addClass('hasSchedule');
      }
  }
  calendarHtml +='<div class="dayInfo '+this_weekdayClass+'" data-date="'+this_day+'">'
  for(var i=0;i<data.length;i++){
    let beginTime=data[i].beginTime;
    let endTime=data[i].endTime;
      var this_schedule = data[i].schedule?data[i].schedule:{};
     var this_begin = this_schedule.effectiveTime;
     var this_begin_arr = this_begin.split(' ');
     var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
     var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
     var this_end = this_schedule.finishTime;;
     var this_end_arr = this_end.split(' ');
     var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
     var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
     this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
     this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;
      //跨天 时间处理
      this_begin_date = this_begin_date.replace(/-/g, "/");
      this_end_date = this_end_date.replace(/-/g, "/");
      this_day_new = this_day.replace(/-/g, "/");
      if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
          this_begin_time = '00:00';
      }
      if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
          this_end_time = '23:59';
      }
    
     var this_category = this_schedule.cateGory?this_schedule.cateGory:{};
     var this_categoryid = this_category.id;
     var bgColor = this_category.color;
     var this_color = '#999999';
     var this_categoryName = this_category.name.substr(0,1);
     var categoryInfo = scheduleInfo.categoryInfo?scheduleInfo.categoryInfo:{};
     var this_categoryColor = categoryInfo[this_categoryid]?categoryInfo[this_categoryid]:'#0081c2';
     var this_location = this_schedule.location?this_schedule.location:'';
    var this_title = this_schedule.title?this_schedule.title:'';
    var tag8 = this_schedule.tag8?this_schedule.tag8:'';
    var tag3 = this_schedule.tag3?this_schedule.tag3:'';
    
    var this_content = this_schedule.content?this_schedule.content:'';
    // 完整开始-结束时间--不含秒
    var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    let category='';
    switch(this_categoryid){
      case 0:
        // category='kb'
        category='mySchedule'
      break;
      case -4:
         category='dingtalkCal'
        break;
      case -9:
        category='conference'
        break;
      case -2:
        category='kb'
          break;
    }
     if(type === 'all'){ //个人日程
          //有删除按钮
          var this_id = this_schedule.id;
          let time=this_begin_time+'-'+this_end_time;
          if(category==='dingtalkCal'){
            time=this_whole_begin+'-'+this_whole_end;
          }
          calendarHtml +='<div class="schedule-item" data-id="'+this_id+'" data-category="'+category+'" data-title="'+this_title+'" data-tag8="'+tag8+'" data-tag3="'+tag3+'" data-time="'+time+'" data-begintime="'+beginTime+'" data-endtime="'+endTime+'" data-location="'+this_location+'"  data-content="'+this_content+'" data-createby="'+createBy+'" data-participantsStr="'+participantsStr+'">'+
          '<div class="schedule-timeList">'+
          '<div class="schedule-beginTime">'+this_begin_time+'</div>'+
          '<div class="schedule-endTime">'+this_end_time+'</div>'+
        '</div>'+
        '<div class="schedule-left">'+
          '<div class="schedule-titleName"><div class="schedule-bg" style="background:'+bgColor+'"></div>'+
            '<div class="schedule-icon" style="background:'+bgColor+'"><svg xmlns="http://www.w3.org/2000/svg" width="15.061" height="16.16" viewBox="0 0 15.061 16.16"><path id="geren" d="M49.8,15.846a4.31,4.31,0,1,1,4.31-4.31,4.316,4.316,0,0,1-4.31,4.31Zm0-7.649a3.338,3.338,0,1,0,3.338,3.338A3.343,3.343,0,0,0,49.8,8.2Zm6.153,15.188H43.645a1.378,1.378,0,0,1-1.377-1.377v-.932L42.3,21c.926-2.566,4.01-4.36,7.5-4.36S56.374,18.43,57.3,21l.029.081v.932A1.378,1.378,0,0,1,55.951,23.385ZM43.243,21.25v.758a.406.406,0,0,0,.405.405h12.3a.406.406,0,0,0,.405-.405v-.761c-.824-2.147-3.506-3.636-6.558-3.636S44.064,19.1,43.243,21.25Z" transform="translate(-42.268 -7.225)" fill="#fff"/></svg></div>'+
            '<div class="schedule-title">'+
                  '<div>'+this_title+'</div>'
              if(this_location){
                  calendarHtml +='<div class="schedule-location">'+
                        '地点：'+
                      '<span>'+this_location+'</span>'+
                    '</div>'
              }
            calendarHtml+='</div>'+
              '</div>'
            calendarHtml+='</div>';
            if(this_categoryName === '个'){
              calendarHtml+='<div class="schedule-del delItem delCalItem" data-id="'+this_id+'">删除</div>'
            }
            calendarHtml+='</div>';
      } 
  }
  if(data.length<1){
      calendarHtml +='<div class="noData">当前日期没有相关事件</div>';
  }
  calendarHtml +='</div>';
  return calendarHtml;
}

//切换展示日期 - 已整理
function changeShowDayInfo(){
   // 删除按钮隐藏
   hideDelCalItem();
   //获取点击周几下标
   var curIndex = $('#scheduleDate .calendar-days.calendar-cur-view .grid-curday ').index();
   //展示当天对应数据--会议和日程--
   $('.mySchedule-container .scheduleInfoContent .contentBox.mySchedule .dayInfo').eq(curIndex).addClass('active').siblings().removeClass('active');
   $(".mySchedule-container .schedule-del").hide()
}

//日程详情弹窗
var detailCaldia;
$(document).on("click", ".mySchedule-container .schedule-item", function (e){
    e.stopPropagation();
    var detailTime = $(this).data('time') ?? '';
    var beginTime = $(this).data('begintime');
    var endTime = $(this).data('endtime');
    var detailLocation = $(this).data('location') ?? '';//地点
    var detailTitle = $(this).data('title') ?? '';
    var detailCategory ='';
    var detailContent = $(this).data('content') ?? '';
    var remark = $(this).data('tag8') ?? '';//备注

    var category = $(this).data('category');
    console.log("category=" + category);
    var calSpanTitle = '日历：';
    var calSpanLocation = '日程地点：';
    var calSpanContent = '备注：';
    var layerHtml = '';
    if(category === 'mySchedule'){
        detailCategory ='个人日程';
        var scheduleId = $(this).data('id');
        layerHtml +='<div class="calendar-cons">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">日程名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">日程时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        /**layerHtml +='<p class="calendar-category"><span class="calendar-text">'+calSpanTitle+'</span><span class="calendar-val">'+detailCategory+'</span></p>';**/
        // layerHtml +='<p class="calendar-location"><spa class="calendar-text"n>'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanContent+'</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        // if(remark){
        //   layerHtml +='<p class="calendar-remark"><span class="calendar-text">备注：</span><span class="calendar-val">'+remark+'</span></p>';
        // }
       
        layerHtml +='<div class="edit-btn"><div class="editCal" data-id="'+scheduleId+'">编辑</div><div class="delCalItem delCal" data-id="'+scheduleId+'">删除</div></div>'
        layerHtml +='</div>';
    }else if(category == 'kb'){
        detailCategory ='个人日程';
        var scheduleId = $(this).data('id');
        layerHtml +='<div class="calendar-cons">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">课表名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">上课时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">上课地点：</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text"></span><span class="calendar-val">'+detailContent.replace(/:/g,'：')+'</span></p>';
        }
        layerHtml +='</div>';
    }
  
    detailCaldia = $.modal({
        modalClassName:'calDetail iframe-schedule',
        durationIn:220,
        durationOut:500,
        animateOutClass:"ani-zoomout",
        content: layerHtml,
        // closeOnMask:true,
        title: detailCategory + '详细信息',
        onOpen: function(e, modal){
          // $.poptips({type:"warning",color:true, content:"正在打开"});
        }
    }).on("modal:cancel",function(e, modal){
        // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
    }).on("modal:opened",function(e, modal){
      var detailHeight = $('.calDetail .ui-modal-bd .calendar-cons').height();
          // var calDetailBoxHeight = $('.calDetail .ui-modal-bd').height();
          var calDetailBoxHeight = $('.calDetail').height()*0.45;
          if(category === 'mySchedule'){
            detailHeight = detailHeight+8;
          }
          if(detailHeight < calDetailBoxHeight){
            var detailHeightNew = detailHeight+54;
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":detailHeightNew+'px!important',
              "height":detailHeightNew+'px!important',
              "max-height":detailHeightNew+'px!important'
            });
          }else{
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":'50%!important',
              "height":'50%!important'
            });
          }
         
        // $.poptips({tipPosition:"top",tipLevel:"success",animateInClass:"ani-fadeinT",animateOutClass:"ani-fadeoutT",tipIsColor:true, content:"完成打开"});
    }).on("modal:close",function(e, modal){
        // $.poptips({tipPosition:"bottom",tipLevel:"warning",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",tipIsColor:false, content:"正在关闭"});
    }).on("modal:closed",function(e, modal){
        // $.alert("已经关闭");
    });
})

//日程添加
var dia;
$(document).on("click", " .mySchedule-container .mySchedule-content .calButton .addCal", function (e) {
  e.stopPropagation();
  //默认日期
  var defaultDate  = $('.arm-calendar-default .calendar-days.calendar-cur-view .grid-curday').data('date');
  defaultDate = defaultDate.replace(/\//g, "-");
   dia = $.iframe({
    id:'addFrame',
    title:"日程添加",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=add&date="+defaultDate,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});

  });
});
window.onmessage = function(e) {
  var iframeHeight = parseInt(e.data, 10);
  document.getElementById('iframeWrapper').style.height = iframeHeight + 'px';
};

//取消添加
window.addEventListener('message', function(event) {
  if(event.data === 'reloadEmail'){
    dia.iframe[0].close();
    getUnReadCount()
  }
  if(event.data === 'reloadSched'){
    //关闭日程添加弹窗
    dia.iframe[0].close();
    var weekStartDate=scheduleInfo.weekStartDate;
    var weekEndDate=scheduleInfo.weekEndDate;
    getOneWeekSched(weekStartDate,weekEndDate);
  }
  //子页面直接关闭-暂不需要
  // if(event.data === 'cancelAddApps'){
  //   //关闭应用收藏弹窗
  //   addAppDia.iframe[0].close();
  // }
  //刷新我的收藏数据
  if(event.data === 'reloadMyFavourite'){
    getMyFavourite();
  }
  //关闭日程弹窗
  if(event.data === 'closeSchedIframe'){
    dia.iframe[0].close();
  }
}, false);

//个人日程编辑
$(document).on("click", ".ui-page.calDetail .editCal", function (e){
  e.stopPropagation();
  // 关闭详情弹窗
  detailCaldia.modal[0].close();
  var scheduleId = $(this).data('id');
   dia = $.iframe({
    title:"日程编辑",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=edit&id="+scheduleId,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });
})

// 点击删除按钮
$(document).on("click", ".delCalItem ", function (e) {
  e.preventDefault();
  e.stopPropagation();
  e.stopImmediatePropagation();
  var id = $(this).data('id');
  var dia = $.dialog({
    modalClassName:'delCalDialog',
    title:'信息提示',
    content:"您确定要删除吗？",
    onOpen: function(e, dialog){
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("dialog:cancel",function(e, dialog){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
      // console.log('取消')
  }).on("dialog:confirm",function(e, dialog){
      // $.poptips({tipLevel:"success", tipIsColor:true, content:"您点击了"+ e.relatedTarget.innerText});
      var urls = index_urls.getDelCalendarUrl+'?selectedIds='+id;
      //删除
      $.ajax({
          url: urls+"&selectedIds="+id,
          type: 'delete',
          data: {},
          dataType: 'json',
          success: function (res) {
              if (res.result.data) {
                $.poptips("删除成功");
                  // 重新加载该周数据
                  var weekStartDate=scheduleInfo.weekStartDate;
                  var weekEndDate=scheduleInfo.weekEndDate;
                  getOneWeekSched(weekStartDate,weekEndDate);
                  $('.calButton .cancelDel').hide();
                  $('.calButton .delCal').show();
                  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
                  detailCaldia.modal[0].close();
              } else {
                $.poptips(data.errorMsg);
              }

          }
      })
  });
});

//日程删除按钮隐藏
function hideDelCalItem(){
  $('.mySchedule-container .mySchedule-content .calButton .cancelDel').hide();
  $('.mySchedule-container .mySchedule-content .delCal').show();
  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
}

//点击箭头-展示/收缩
$(".schedule-arrowBtn").on("click",function(e){
  e.stopPropagation()
  $(this).toggleClass("active")
  $(".calButton").toggleClass("active")
})

/**
 * 日程工具函数
 */
//获取完整日期
function formatWholeDate(date){
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  month = month<10?('0'+month):month;
  var date = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate();
  return  year + "-" + month + "-" + date;
}
//获取月-日
function formatMonthDate(date){
  var month = date.getMonth() + 1;
  // month = month<10?('0'+month):month;
  // var date = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate();
  month = parseInt(month);
  var date = parseInt(date.getDate());
  return  month + "-" + date;
}

function doHandleMonth(month) {
  var m = month;
  if (month.toString().length == 1) {
    m = "0" + month;
  }
  return m;
}

function getDay(day) {
  var today = new Date();
  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
  today.setTime(targetday_milliseconds);
  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + "/" + tMonth + "/" + tDate;
}

function timestampToTime(timestamp) {
  var date = new Date(parseInt(timestamp)); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  var Y = date.getFullYear() + "-";
  var M =
    (date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1) + "-";
  var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
  var h =
    (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  var m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  // var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return Y + M + D;
}

// 获取当天 日期
function getCurrentData(){
  var currentDay = new Date();
  var curDay = currentDay.getDate();
  var curMonth = currentDay.getMonth() + 1;
  curDay = curDay < 10 ? "0" + Math.abs(curDay) : curDay;
  curMonth = curMonth < 10 ? "0" + Math.abs(curMonth) : curMonth;
  currentDay = currentDay.getFullYear() + "-" + curMonth + "-" + curDay;
  return currentDay;
}
// 获取几天后日期
function getDateLater(day,date=''){
  var today = date?(new Date(date)):(new Date());
  var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;
  today.setTime(targetday_milliseconds);
  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
   return tYear+"/"+tMonth+"/"+tDate;
}

//////////////////////////////////////////////////////////////////////////////////////////////
//待办跳转
$(document).on("click", ".middleContent .todo-list .todo-item", function (e){
    e.stopPropagation();
    var this_url = $(this).data('url');
    if(this_url){
      commonToUrl(this_url,'_self');
    }
})

//资讯栏目跳转
$(document).on("click", ".middleContent .topicInfo .topicItem", function (e){
    e.stopPropagation();
    var this_url = $(this).data('url');
    if(this_url){
      commonToUrl(this_url,'_self');
    }
})


//应用跳转
function toAppUrl(this_node){
    var this_enableservice = $(this_node).data('enableservice');
    var this_reason = $(this_node).data('reason');
    var this_isallow = $(this_node).data('isallow');
    var this_appurl = $(this_node).data('appurl');
    var this_appname = $(this_node).data('appname');
    var this_id = $(this_node).data('id');
    if(this_enableservice == 1){
        //有服务指南页 跳应用详情页
        var this_url=commonUrlTwo+'/_web/portalWechat/mobile/detail.html?appVersionId='+this_id+'&_t='+new Date().getTime();
        commonToUrl(this_url,'_blank');
    }else{
        if((this_isallow === "true") || (this_isallow === true)){
            openModule(this_appname);
            let url=this_appurl
            // if(checkUrlStart(url)){
            //   url=addParameterToUrl(url)
            // }
            commonToUrl(url,'_blank');
        }else{
          this_reason = this_reason?this_reason:'暂无权限';
            $.poptips(this_reason);
        }
    }
}

//更多按钮跳转
$(document).on("click", ".middleContent .moreBtn", function (e) {
    e.stopPropagation();
    var this_type = $(this).data('type');
    var url = "";
    if(this_type === 'app'){
        url = commonUrlTwo + '/_web/portalWechat/mobile/serviceCenter.html';
    } else if(this_type === 'ddApps'){
        url = commonUrlTwo + '/_web/portalWechat/mobile/serviceCenter.html?categoryid=56';
    } else if(this_type === 'topic'){
        url= $(this).data('url');
    } else if(this_type === 'articleCenter'){
        url= commonUrlTwo+'/_web/portalWechat/mobile/articleCenter.html?'+ $(this).data('url');
    } else if(this_type === 'task'){
        url= commonUrlTwo+'/_web/portalWechat/mobile/taskCenter.html';
    } else if(this_type === 'schedule'){
        var this_schedule_type=$('.middleContent .todaWeek .tab-list .tab-item.tab-selected').data('type');
        url= commonUrlTwo+'/_web/portalWechat/mobile/scheduleCenter.html?type='+this_schedule_type;
    }
    if(url){
        url = (url.indexOf('?')>-1)?(url+"&_t="+new Date().getTime()):(url+"?_t="+new Date().getTime());
        commonToUrl(url,'_self');
    }
})

//应用统计
function openModule(appName) {
  var screenSize=window.screen.width+"*"+window.screen.height;
  var clientVersion=navigator.platform+"-"+myBrowser()+"-"+navigator.appVersion.substring(0, navigator.appVersion.indexOf("(")-1);
  $.ajax({
      url: index_urls.openModule,
      type: 'get',
      data: {
          appName:appName,
          clientVersion: clientVersion,
          screenSize:screenSize
      },
      dataType: 'json',
      success: function (){
      }
  })
}