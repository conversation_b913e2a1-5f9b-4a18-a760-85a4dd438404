/**
 * 点击编辑
 */
$(".bindColumn-edit").tap(function () {
    var _html = $(this).text();
    if (_html == "编辑") {
        $(this).html("完成");
        $(this).addClass("bindColumn-finish");
        $(".bindColumn-del,.unBindColumn-add").show();

    } else {
        window.location.href = "./../mobile/articleCenter.html"
        // $(this).html("编辑");
        // $(".bindColumn-del,.unBindColumn-add").hide();
        // $(this).addClass("bindColumn-finish");
    }
})
$(".bindColumn-finish").tap(function () {
    saveUserSub();
})
getColumn();
var parentSiteId = getParams('siteid');
var parentColumnId = getParams('columnid');

/**
 * 地址栏上参数
 */
function getParams(param) {
    var search = window.location.search;
    var arr = search.substr(1, search.length).split('&');
    for (var i = 0; i < arr.length; i++) {
        var paramArr = arr[i].split("=");
        if (paramArr[0] == param) {
            return decodeURI(paramArr[1]);
        }
    }
    return "";
}
/**
 * 渲染我的栏目
 */
function getColumn(callBack) {
    $.ajax({
        url: index_urls.queryAllSiteAndColumn,
        success: function (res) {
            if (res.result == "1") {
                var data = res.data, unBindHtml = "", bindHtml = "";
                var _bindata = [];
                for (var i = 0; i < data.length; i++) {
                    var columns = data[i].columns;
                    if (columns && columns.length) {
                        unBindHtml += ` <div class="unBindColumn-items" data-site="${data[i].siteId}"><div class="unBindColumn-tag">
                        <i></i>
                        <span>${data[i].siteName}</span>
                      </div>
                      <ul>`
                        for (var j = 0; j < columns.length; j++) {
                            columns[j].siteId = data[i].siteId;
                            if (columns[j].isChecked == false) {
                                unBindHtml += `<li class="unBindColumn-add-item"  data-site="${data[i].siteId}" data-columnId="${columns[j].columnId}" data-name="${columns[j].columnName}"><span class="${columns[j].columnName.length > 5 ? 'unBindColumn-short' : ''}">${columns[j].columnName}</span><i class="unBindColumn-add"></i></li>`
                            }
                            if (columns[j].isChecked == true) {
                                _bindata.push(columns[j]);
                            }
                        }
                        unBindHtml += `</ul></div>`
                    }
                }
                var bindData=renderData(_bindata);
                for (var i = 0; i < bindData.length; i++) {
                    if (bindData[i].isChange == false) {
                        bindHtml += `<li><span class="bindColumn-checked ${bindData[i].siteId == parentSiteId && bindData[i].columnId == parentColumnId ? " bindColumn-checked-actived" : ""}">${bindData[i].columnName}</span></li>`
                    } else {
                        bindHtml += `<li data-site="${bindData[i].siteId}" data-columnId="${bindData[i].columnId}" data-name="${bindData[i].columnName}" class="bindColumn-del-item bindColumn-checkedItem ">
                        <span class="${bindData[i].siteId == parentSiteId && bindData[i].columnId == parentColumnId ? "bindColumn-checked-actived" : ""}">${bindData[i].columnName}</span>
                        <i class="bindColumn-del" >
                        </i></li>`
                    }
                }
                $(".bindColumn-item>ul").html(bindHtml);
                $(".unBindColumn-item").html(unBindHtml);
                $(".unBindColumn-items").each(function () {
                    if ($(this).find("ul").html() == "") {
                        $(this).hide()
                    }
                })
                callBack && typeof callBack === 'function' && callBack();
            } else {
                $.poptips(res.reason)
            }
        }
    })
}
/**
 * 点击添加
 */
$(document).tap(".unBindColumn-add-item .unBindColumn-add", function () {
    $(this).parent().addClass("bindColumn-checkedItem");
    saveUserSub();
})
/**
 * 点击取消
 */
$(document).tap(".bindColumn-del-item .bindColumn-del", function () {
    $(this).parent().removeClass("bindColumn-checkedItem");
    saveUserSub()
})

function saveUserSub() {
    var subscription = [];
    for (var i = 0; i < $(".bindColumn-checkedItem").length; i++) {
        var obj = {};
        obj.siteId = $(".bindColumn-checkedItem:eq(" + i + ")").attr("data-site");
        obj.columnId = $(".bindColumn-checkedItem:eq(" + i + ")").attr("data-columnId");
        obj.name = $(".bindColumn-checkedItem:eq(" + i + ")").attr("data-name");
        obj.id = 0;
        subscription.push(obj);
    }

    var _params = {
        isReturn: false
    }
	var saveUserSubUrl = index_urls.saveUserSub;
    $.ajax({
		url: saveUserSubUrl,
		data: {
			subscription: JSON.stringify(subscription)
		},
		cache: false,
		type: "post",
		success: function (res) {
			if (res.result == 1) {
				getColumn(function () {
					$('.bindColumn-edit').html("完成");
					$('.bindColumn-edit').addClass("bindColumn-finish");
					$(".bindColumn-del,.unBindColumn-add").show();
				});
			}else{
				$.poptips(res.reason)
			}
		}
	})   
}
/**
 * 重新处理数据
 */
function renderData(data){
    var data1=[],data2=[];
    if(data && data.length>0){
        for(var i=0;i<data.length;i++){
            if(!data[i].isChange){
                data1.push(data[i]);
            }else{
                data2.push(data[i]);
            }
        }
    }
    data1.push(...data2)
    return data1
}