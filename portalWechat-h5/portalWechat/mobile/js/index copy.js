$(function () {
    //访问统计
    openModule('com.just.mhfwtj-wechat');
    //判断是否展示课表
    // getUserIsHaveKb();
    //获取当前身份
    userLb='';
    // getUserLb();
    // getUserInfo()
     //获取头像
    //  getUserImg()
    //获取头部背景
    // getTopBg();
    //获取头部应用
    getTopApps();
    // 获取轮播图图片
    // setSwiperImg();
    //日程相关
    //加载一周日程数据--calendar.js 渲染日期后已调该接口
    // getOneWeekSched(start,end);
    // 日程相关变量
    scheduleInfo = {
        showKb:false,
        categoryids:[],
        categoryInfo:{},
        hasLoadCalCategory:false,
        weekStartDate:'',
        weekEndDate:'',
        hasLoadHaveKb:false
    }
    //获取服务大厅
    getFwdt();
    //最近使用
    getRecentlyUsedApps();
    //钉钉应用
    getDingTalkApps();
    //获取我的收藏
    getMyFavourite();
    //获取资讯栏目
    // getZxTabs();
    //唤醒该页面时-需要刷新task数量
    // isReloadTaskNum();
   
});

//点击头部搜索图标
$(document).on("click", " .topContent .topBox .searchBox img", function (e){
  e.stopPropagation();
  window.open('search.html?_t='+new Date().getTime(),'_self');
})


//获取头待办数据
function getTodoTaskNum(){
  $.ajax({
    url: index_urls.getToDoUrl,
    data: {},
    crossDomain: true,
    dataType: "jsonp",
    success: function (res) {
      const {data,total} = res.result ?? {};
      var result = res.result?res.result:{};
      var taskNum = parseInt(total ?? 0);
      if(taskNum >= 0){
        $('.middleContent .todaWeek .toda').append(`<i class="taskNum">${taskNum>99?'99+':taskNum}</i>`)
      }
      if(data.length > 0){
        data.forEach(item => { 
          $('.middleContent .todo-list').append(`<div class="todo-item">
                      <div class="todo-item-box">
                          <div class="todo-title">${item.title}</div>
                          <div class="todo-content">
                            <span class="todo-meta">来源: 网上办事大厅</span>
                            <span class="todo-meta">申请人: 倪翠霞</span>
                            <span class="todo-meta">创建时间: 2023/07/04</span>
                          </div>
                      </div>
                    </div>`)
        });
      }else{
        $('.middleContent .todo-list').append(`<div class="todo-item">
            <div class="todo-item-box">
                当前没有相关数据
            </div>
          </div>`)
      }
      return taskNum
    }
  });
}

//获取当前用户身份
function getUserLb(callback){
    $.ajax({
        url: index_urls.getUserLbUrl,
        data: {},
        crossDomain: true,
        dataType: "jsonp",
        success: function (res) {
            var data = res.data || 'xs'
            userLb = data;
            localStorage.setItem('userLb', userLb);
            if(typeof callback=="function"){
              callback(data)
            }
        },
        error(err){
            
        }
    });
}

//获取头部应用
function getTopApps(){
  $.ajax({
    url: index_urls.getTopAppsUrl,
    type: "get",
    data: {
      clientType: 4,
      categoryId: 57,
      page:0,
      pageSize:4,
      _t:new Date().getTime()
    },
    dataType: "jsonp",
    success: async function (data) {
      var appList = (data.data)?(data.data):[];
      var appListStr = "";
      var hasTask=false;
      var hasMail=false;
      var hasMsg=false;
      for(var i=0;i<appList.length;i++){
        var this_appname = appList[i].appName;
        var data_type = '';
        var has_count = false;
        var countClass='';
        if(this_appname ==='com.mycc.todo-dingtalk'){
          data_type = 'task';
          has_count = true;
          countClass = 'taskNum';
          hasTask = true;
        }
        if(this_appname ==='com.mycc.email-dingtalk'){
          data_type = 'mail';
          has_count = true;
          countClass = 'mailNum';
          hasMail = true;
        }
        if(this_appname ==='com.mycc.sudy.message-dingtalk'){
          data_type = 'message';
          has_count = true;
          countClass = 'messageNum';
          hasMsg = true;
        }
        if(this_appname ==='rxm-dingtalk'){
          data_type = 'card';
        }
        //判断 svg 格式
        var item = appList[i];
        var this_isSvg = false;  
        if( item.iconUrl.slice(-4) === '.svg'){
          var this_svgDetail = await imgToSvg(item.iconUrl);  
          this_isSvg = true;
        }
        appListStr +='<div class="appItem '+data_type+'" data-type="'+data_type+'" data-appurl="'+appList[i].entranceUrl+'" data-enableservice="'+appList[i].enableService+'"'
        +' data-isallow="'+appList[i].isAllow+'" data-reason="'+appList[i].reason+'" data-appname="'+appList[i].appName+'" data-id="'+appList[i].id+'">';
        appListStr +='<div class="imgBox">';
        if((this_isSvg === true) && this_svgDetail){
          appListStr +='<div class="svgImg">'+this_svgDetail+'</div>';
        }else{
          appListStr +='<img src="'+appList[i].iconUrl+'" alt="">';
        }
        
        if(has_count === true){
          appListStr +='<span class="count '+countClass+'"></span>';
        }
        appListStr +='</div>';
        appListStr +='<div class="title">'+appList[i].name+'</div>';
        appListStr +='</div>';
      }
     
     if(appListStr){
      $('.topContent .appNav').html(appListStr);
     }
     if(hasTask === true){
      //获取头部待办个数
      getTodoTaskNum();
    }
    if(hasMail === true){
        //获取头部邮件个数
        getMailNum();
    }
    if(hasMsg === true){
      //获取消息个数
      getMsgNum();
  }
    },
    error: function (err) {
    },
  });
}

//待办tab切换
const newModeltab = document.querySelectorAll('.newsModel .tab-list .tab-item');
const newModelcon = document.querySelectorAll('.newsModel .tab-con .tab-item');
newModeltab.forEach((item, index) => {
  item.addEventListener('click', function () {
    newModeltab.forEach((el, i) => {
      index !== i ? el.classList.remove('tab-selected') : el.classList.add('tab-selected');
      index !== i ? newModelcon[i].classList.remove('tab-selected') : newModelcon[i].classList.add('tab-selected');
    });
  });
});

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//获取头部邮件个数
function getMailNum(){
  //不同身份-显示不同
  let _userLb=localStorage.getItem('userLb')||userLb;
  if(_userLb === 'xs'){
    //判断是否有邮箱
    hasMailUrl();
  }else if(_userLb === 'ls'){
    //直接获取邮箱未读数量
    getUnReadCount();
  }
}
//获取邮箱未读数量-教师
function getUnReadCount(){
  //邮箱地址修改
  $('.topContent .appNav .appItem.mail').attr('data-appurl',index_urls.getMailUrl);
  $.ajax({
    url: index_urls.getUnReadCountUrl,
    data: {},
    crossDomain: true,
    success: function (res) {
      const data = res.data || {};
      let transactionCount = parseInt(data.transactionCount, 10);

      if (!isNaN(transactionCount) && transactionCount > 0) {
        const $mailNum = $('.topContent .appNav .appItem .mailNum');
        $mailNum.html(transactionCount > 99 ? '99+' : transactionCount).show();
      }
    },
    error(err){
      
    }
  });
}
//是否有邮箱--学生
function hasMailUrl(){
  $.ajax({
    url: index_urls.hasMailUrl,
    data: {},
    success: function (res) {
      if((res.code) === 0){
         //有邮箱
         //获取学生未读邮箱
        getStuUnReadCount();
      }else{
       //显示激活按钮
       $('.topContent .appNav .appItem.mail').addClass('createMail');
       $('.topContent .appNav .appItem .mailNum').html('待激活').show();
      }
    },
    error(err){
    
    }
  });
 
}
//获取学生邮箱未读数量
function getStuUnReadCount(){
  //激活邮箱类名删除
  $('.topContent .appNav .appItem.mail').removeClass('createMail');
  //邮箱地址修改
  $('.topContent .appNav .appItem.mail').attr('data-appurl', commonUrl + index_urls.lookUserEmailUrl);
  $.ajax({
    url: index_urls.getStuUnReadCountUrl,
    data: {},
    crossDomain: true,
    success: function (res) {
      var result = (res.result)?res.result:{};
      var newmsgcnt = (result.newmsgcnt)?result.newmsgcnt:0;
      newmsgcnt = parseInt(newmsgcnt);
      if(newmsgcnt>0){
        newmsgcnt =(newmsgcnt>99)?'99+':newmsgcnt;
        $('.topContent .appNav .appItem .mailNum').html(newmsgcnt).show();
      }
    },
    error(err){
      
    }
  });
}
//创建注册邮箱--学生
function createMail(){
  $.ajax({
    url: index_urls.createEmailUrl,
    data: {},
    crossDomain: true,
    success: function (res) {
      // var code = (res.code)?res.code:'';
      var code = res.code;
      if((code === 0) || (code === '0')){
        $.poptips('激活成功');
        getStuUnReadCount();
      }else{
        //创建失败
        $.poptips('请稍后重试')
      }
    },
    error(err){
      $.poptips('请稍后重试');
    }
  });
}

//头部应用跳转
$(document).on("click", " .topContent .appNav .appItem", function (e){
    e.stopPropagation();
    var this_type = $(this).data('type');
    var this_url = $(this).data('appurl');
    switch(this_type){
      case "mail":
         //邮箱
        //判断是否需要点击激活
        if($(this).hasClass("createMail")){
          var dia = $.dialog({
            modalClassName:'createMailDialog',
            title:'',
            content:"确认激活邮箱吗",
            onOpen: function(e, dialog){
              // $.poptips({type:"warning",color:true, content:"正在打开"});
            }
          }).on("dialog:cancel",function(e, dialog){
              // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
              // console.log('取消')
          }).on("dialog:confirm",function(e, dialog){
              // $.poptips({tipLevel:"success", tipIsColor:true, content:"您点击了"+ e.relatedTarget.innerText});
             //确认 激活
             createMail();
          });
      }else{
          //进入邮箱
          if(this_url){
            commonToUrl(this_url,'_blank');
          }
      }
      break;
      case "task":
         //我的事务页面
         commonToUrl(this_url, '_blank');
        break;
      case "card":
        //一卡通or入校码
        $.poptips('正在建设中!')
        break;
      default:
        //其他应用
        var this_node = $(this);
        toAppUrl(this_node);
        break;
    }
})
//////////////////////////////////////////////////////////////////////////////////////////////
//判断人员是否有日程和周会表
function getUserIsHaveKb(){
  getUserLb(function(user){
    console.log('======user',user)
    if(user==='xs'){
      renderScheduleTab(0)
    }else{
      renderScheduleTab(0)
    }
  })
}

//日程初始化-已整理
function renderScheduleTab(total){
  if(total === 0){
    scheduleInfo.showKb=false;
    // var tabStr ='<div class="tab-item myScheduleTab tab-selected" data-type="mySchedule">';
    //     tabStr +='<div><span>我的日程</span><span class="scheduleNum">0</span></div>';
    //     tabStr +='</div>';
    // //课表显示
    // $('.middleContent .oneWeek .tab-list').html(tabStr);
    // $('.middleContent .oneWeek .tabBox').addClass('singleTab');
    $('#scheduleDate .calendar-week-bar').addClass('myScheduleWeek').removeClass('meetingWeek');
    $(".mySchedule-container .scheduleInfoContent .contentBox.mySchedule").addClass('active').siblings().removeClass('active');
    getTodoTaskNum()
  }else{
    var tabStr = '<div class="tab-item"><div>周会表</div></div>';
    $('.middleContent .todaWeek .tab-list').append(tabStr);
    // getKbWeek();//加载一周会表
  }
}

//日程删除
$(document).on("click", ".mySchedule-container .delCal", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.mySchedule-container .mySchedule-content .cancelDel').show();
  $('.mySchedule-container .schedule-item .delItem').show();
});

//取消日程删除
$(document).on("click", ".mySchedule-container  .cancelDel", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.mySchedule-container .mySchedule-content .delCal').show();
  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
  $('.mySchedule-container .schedule-item .delItem').hide();
})

//加载日程分类-已整理
function renderCalCategory(){
  scheduleInfo.hasLoadCalCategory = true;
  // var checkedUrl = checkIdentity(index_urls.getCalendarCategoryUrl);
  // 不用走weAuth
  var checkedUrl = index_urls.getCalendarCategoryUrl;
  $.ajax({
      url: checkedUrl,
      data: { queryType: 2 },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        var data = res.result.data;
        for (var i = 0; i < data.length; i++) {
          var obj = data[i];
          if (obj.teamCategoryVOS) {
            for (var j = 0; j < obj.teamCategoryVOS.length; j++) {
              var obj2 = obj.teamCategoryVOS[j];
              var this_categoryid = obj.teamCategoryVOS[j].id;
              if (obj2.checked && (this_categoryid !== -2)) {
                scheduleInfo.categoryids.push(obj2.id);
              }
              var this_categoryColor = obj.teamCategoryVOS[j].calendarCateColor;
              scheduleInfo.categoryInfo[this_categoryid] = this_categoryColor;
            }
          }
        }
        console.log('===scheduleInfo==',scheduleInfo)
        
        getCalendarWeekData(); //加载各日程分类一周数据
      },
      error:function(err){
        getCalendarWeekData();
      }
    });
}

//获取日程一周数据
function getOneWeekSched(start,end){
    scheduleInfo.weekStartDate=start;
    scheduleInfo.weekEndDate = end;
    //是否加载过日程分类-
    var hasLoadCalCategory = scheduleInfo.hasLoadCalCategory;
    if(hasLoadCalCategory === false){
      // 先获取日程分类
      renderCalCategory();
    }else{
      // 加载过直接获取各日程分类的一周数据
      getCalendarWeekData();
    }
}

//获取各日程数据-已整理
function getCalendarWeekData(){
  //先清掉之前的数据标记
  $('#scheduleDate .calendar-week-bar li').removeClass('hasSchedule hasMeeting');
  var start=scheduleInfo.weekStartDate;
  var end= scheduleInfo.weekEndDate;
  var categoryids = scheduleInfo.categoryids;
  //获取后一天日期
  end = getDateLater(1,end);
  $.ajax({
      url: index_urls.getCalendarListUrl,
      type: 'get',
      dataType: 'jsonp',
      data: {
        categoryIds: categoryids.join(),
        queryType: 2,
        type: 0,
        fromDate: new Date(start).toISOString(),
        endDate: new Date(end).toISOString()
      },
      success: function (res) {
        const currentTime = new Date().format('YYYY-MM-DD')
        const _res = res?.map(item => {
            return {
                day: item.day?.slice(0, 10),
                events: (item.events ?? [])?.map(i => ({ ...i, cateGoryId: (i.schedule.cateGory?.id)?.toString(), beginTime: i.schedule.effectiveTime, endTime: i.schedule.finishTime }))
            }
        })
        const currentDate = _res.filter(item => item.day === currentTime)
        const _tempData = currentDate?.[0]?.events.filter(item => item.cateGoryId === '0')
        // setState({
        //     eventsData: _res,
        //     currentDateEvents: currentDate,
        //     currentEvents: _tempData
        // })
     
      console.log('==数据结果=',_res)
      //分别渲染会议和日程数据--已分开
      renderScheduleData(_res,'all');
      // renderScheduleData(myScheduleList,'mySchedule');
      // renderScheduleData(conferenceList,'conference');
      // renderScheduleData(ddScheduleList,'ddSchedule');
      },
      error: function (err) {
        renderScheduleData([],'all');
        // renderScheduleData([],'conference');
        // renderScheduleData([],'ddSchedule');
      }
  })
}

//按类-渲染日程数据-已整理
function renderScheduleData(res, type){
    var calWeekStr = "";
    for(var i=0;i<(res.length>7?7:res.length);i++){
        var this_events =res[i].events;
        this_events = this_events?this_events:[];
        if(this_events.length>0){
            //日期下方 添加点
            $('.myScheduleWeek li').eq(i).addClass('hasSchedule');
        }
        calWeekStr += renderPerCalList(res[i],i,type);
    }
    if(calWeekStr ===''){
      calWeekStr =`
                <div class="dayInfo Sun"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Mon"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Tue"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Wed"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Thu"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Fri"><div class="noData">当前日期没有相关事件</div></div>
                <div class="dayInfo Sat"><div class="noData">当前日期没有相关事件</div></div>
      `;
    }

    console.log('===calWeekStr==',calWeekStr)
    //获取当天周几
    var curIndex = $('#scheduleDate .calendar-days.calendar-cur-view .grid-curday ').index();
    $('.mySchedule-container .mySchedule-content .myScheduleBox .scheduleList').html(calWeekStr);
    //展示当天对应数据
    $('.mySchedule-container .scheduleInfoContent .scheduleItem.all .dayInfo').eq(curIndex).addClass('active').siblings().removeClass('active');

}

//渲染日程列表 
function renderPerCalList(data, activeClass, type){
  var calendarHtml="";
  var this_day = data.day;
  this_day = this_day.length>10?this_day.slice(0,10):this_day;
  data = (data.events)?(data.events):[];
   //  <div class="dayInfo Sun">
   var this_weekdayClass = ''
    switch(activeClass){
    case 0:
      this_weekdayClass='Sun';
        break;
    case 1:
      this_weekdayClass='Mon';
        break;
    case 2:
      this_weekdayClass='Tue';
         break;
    case 3:
      this_weekdayClass='Wed';
        break;
        case 4:
      this_weekdayClass='Thu';
        break;
    case 5:
      this_weekdayClass='Fri';
        break;
    case 6:
      this_weekdayClass='Sat';
        break;
  }
  if(data.length>0){
      //各自添加高亮
      if(type === 'meeting'){
          $('.calendar-week-bar .calendar-week-label').eq(activeClass).addClass('hasMeeting');
      } else if((type === 'mySchedule') || (type === 'conference') || (type === 'ddSchedule')){
          $('.calendar-week-bar .calendar-week-label').eq(activeClass).addClass('hasSchedule');
      }
  }
  calendarHtml +='<div class="dayInfo '+this_weekdayClass+'" data-date="'+this_day+'">'
  for(var i=0;i<data.length;i++){
    let beginTime=data[i].beginTime;
    let endTime=data[i].endTime;
      var this_schedule = data[i].schedule?data[i].schedule:{};
     var this_begin = this_schedule.effectiveTime;
     var this_begin_arr = this_begin.split(' ');
     var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
     var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
     var this_end = this_schedule.finishTime;;
     var this_end_arr = this_end.split(' ');
     var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
     var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
     this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
     this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;
      //跨天 时间处理
      this_begin_date = this_begin_date.replace(/-/g, "/");
      this_end_date = this_end_date.replace(/-/g, "/");
      this_day_new = this_day.replace(/-/g, "/");
      if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
          this_begin_time = '00:00';
      }
      if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
          this_end_time = '23:59';
      }
    
     var this_category = this_schedule.cateGory?this_schedule.cateGory:{};
     var this_categoryid = this_category.id;
     var bgColor = this_category.color;
     var this_color = '#999999';
     var this_categoryName = this_category.name.substr(0,1);
     var categoryInfo = scheduleInfo.categoryInfo?scheduleInfo.categoryInfo:{};
     var this_categoryColor = categoryInfo[this_categoryid]?categoryInfo[this_categoryid]:'#0081c2';
     var this_location = this_schedule.location?this_schedule.location:'';
    var this_title = this_schedule.title?this_schedule.title:'';
    var tag8 = this_schedule.tag8?this_schedule.tag8:'';
    var tag3 = this_schedule.tag3?this_schedule.tag3:'';
    
    var this_content = this_schedule.content?this_schedule.content:'';
    // 完整开始-结束时间--不含秒
    var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    let category='';
    switch(this_categoryid){
      case 0:
        // category='kb'
        category='mySchedule'
      break;
      case -4:
         category='dingtalkCal'
        break;
      case -9:
        category='conference'
        break;
      case -2:
        category='kb'
          break;
    }
     if(type === 'all'){ //个人日程
          //有删除按钮
          var this_id = this_schedule.id;
          let time=this_begin_time+'-'+this_end_time;
          if(category==='dingtalkCal'){
            time=this_whole_begin+'-'+this_whole_end;
          }
          calendarHtml +='<div class="schedule-item" data-id="'+this_id+'" data-category="'+category+'" data-title="'+this_title+'" data-tag8="'+tag8+'" data-tag3="'+tag3+'" data-time="'+time+'" data-begintime="'+beginTime+'" data-endtime="'+endTime+'" data-location="'+this_location+'"  data-content="'+this_content+'" data-createby="'+createBy+'" data-participantsStr="'+participantsStr+'">'+
          '<div class="schedule-timeList">'+
          '<div  class="schedule-beginTime">'+this_begin_time+'</div>'+
          '<div  class="schedule-endTime">'+this_end_time+'</div>'+
        '</div>'+
        '<div class="schedule-left">'+
          '<div class="schedule-titleName">'+
            '<div class="schedule-icon" style="background:'+bgColor+'"></div>'+
            '<div class="schedule-title">'+this_title+'</div>'+
          '</div>'
        if(this_location){
          calendarHtml +='<div class="schedule-location">'+
          '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="11.142669677734375" height="13.000003814697266" viewBox="0 0 11.142669677734375 13.000003814697266"><g><path d="M11.1427,5.48315C11.1427,7.4772,9.48096,9.86415,6.21349,12.7566C5.84686,13.0811,5.29581,13.0811,4.92917,12.7566C1.66145,9.86442,0,7.47722,0,5.48317C0,2.45187,2.49741,0,5.57133,0C8.64526,0,11.1427,2.45187,11.1427,5.48317C11.1427,5.48317,11.1427,5.48315,11.1427,5.48315ZM1.45338,5.48315C1.45338,6.8801,2.80939,8.87947,5.57132,11.3808C8.33324,8.87947,9.68925,6.88008,9.68925,5.48315C9.68927,3.26069,7.84856,1.45339,5.57133,1.45339C3.29411,1.45339,1.4534,3.26044,1.4534,5.48317C1.4534,5.48317,1.45338,5.48315,1.45338,5.48315ZM5.57133,7.02471C4.76865,7.02471,4.11795,6.37402,4.11795,5.57133C4.11795,4.76863,4.76865,4.11793,5.57133,4.11793C6.37401,4.11793,7.02472,4.76863,7.02472,5.57133C7.02472,6.37402,6.37401,7.02471,5.57133,7.02471C5.57133,7.02471,5.57133,7.02471,5.57133,7.02471Z" fill="'+bgColor+'" fill-opacity="1"/></g></svg>'+
          '<span style="color:#818181">'+this_location+'</span></div>'
        }
        calendarHtml+='</div>';
            if(this_categoryName === '个'){
              calendarHtml+='<div class="schedule-del delItem delCalItem" data-id="'+this_id+'">删除</div>'
            }
            calendarHtml+='</div>';
      } 
  }
  if(data.length<1){
      calendarHtml +='<div class="noData">当前日期没有相关事件</div>';
  }
  calendarHtml +='</div>';
  return calendarHtml;
}

//切换展示日期 - 已整理
function changeShowDayInfo(){
   // 删除按钮隐藏
   hideDelCalItem();
   //获取点击周几下标
   var curIndex = $('#scheduleDate .calendar-days.calendar-cur-view .grid-curday ').index();
   //展示当天对应数据--会议和日程--
   $('.mySchedule-container .scheduleInfoContent .contentBox.mySchedule .dayInfo').eq(curIndex).addClass('active').siblings().removeClass('active');
   $(".mySchedule-container .schedule-del").hide()
}

//日程详情弹窗
var detailCaldia;
$(document).on("click", ".mySchedule-container .schedule-item", function (e){
    e.stopPropagation();
    var detailTime = $(this).data('time') ?? '';
    var beginTime = $(this).data('begintime');
    var endTime = $(this).data('endtime');
    var detailLocation = $(this).data('location') ?? '';//地点
    var detailTitle = $(this).data('title') ?? '';
    var detailCategory ='';
    var detailContent = $(this).data('content') ?? '';
    var remark = $(this).data('tag8') ?? '';//备注

    var category = $(this).data('category');
    console.log("category=" + category);
    var calSpanTitle = '日历：';
    var calSpanLocation = '日程地点：';
    var calSpanContent = '日程内容：';
    var layerHtml = '';
    if(category === 'mySchedule'){
        detailCategory ='个人日程';
        var scheduleId = $(this).data('id');
        layerHtml +='<div class="calendar-cons">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">日程名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">日程时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        /**layerHtml +='<p class="calendar-category"><span class="calendar-text">'+calSpanTitle+'</span><span class="calendar-val">'+detailCategory+'</span></p>';**/
        // layerHtml +='<p class="calendar-location"><spa class="calendar-text"n>'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanContent+'</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        if(remark){
          layerHtml +='<p class="calendar-remark"><span class="calendar-text">备注：</span><span class="calendar-val">'+remark+'</span></p>';
        }
       
        layerHtml +='<div class="edit-btn"><div class="editCal" data-id="'+scheduleId+'">编辑</div><div class="delCalItem delCal" data-id="'+scheduleId+'">删除</div></div>'
        layerHtml +='</div>';
    }
  
    detailCaldia = $.modal({
        modalClassName:'calDetail',
        durationIn:220,
        durationOut:500,
        animateOutClass:"ani-zoomout",
        content: layerHtml,
        // closeOnMask:true,
        title: detailCategory + '详细信息',
        onOpen: function(e, modal){
          // $.poptips({type:"warning",color:true, content:"正在打开"});
        }
    }).on("modal:cancel",function(e, modal){
        // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
    }).on("modal:opened",function(e, modal){
      var detailHeight = $('.calDetail .ui-modal-bd .calendar-cons').height();
          // var calDetailBoxHeight = $('.calDetail .ui-modal-bd').height();
          var calDetailBoxHeight = $('.calDetail').height()*0.45;
          if(category === 'mySchedule'){
            detailHeight = detailHeight+8;
          }
          if(detailHeight < calDetailBoxHeight){
            var detailHeightNew = detailHeight+54;
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":detailHeightNew+'px!important',
              "height":detailHeightNew+'px!important',
              "max-height":detailHeightNew+'px!important'
            });
          }else{
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":'50%!important',
              "height":'50%!important'
            });
          }
         
        // $.poptips({tipPosition:"top",tipLevel:"success",animateInClass:"ani-fadeinT",animateOutClass:"ani-fadeoutT",tipIsColor:true, content:"完成打开"});
    }).on("modal:close",function(e, modal){
        // $.poptips({tipPosition:"bottom",tipLevel:"warning",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",tipIsColor:false, content:"正在关闭"});
    }).on("modal:closed",function(e, modal){
        // $.alert("已经关闭");
    });
})

//日程添加
var dia;
$(document).on("click", " .mySchedule-container .mySchedule-content .calButton .addCal", function (e) {
  e.stopPropagation();
  //默认日期
  var defaultDate  = $('.arm-calendar-default .calendar-days.calendar-cur-view .grid-curday').data('date');
  defaultDate = defaultDate.replace(/\//g, "-");
   dia = $.iframe({
    id:'addFrame',
    title:"日程添加",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=add&date="+defaultDate,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});

  });
});
window.onmessage = function(e) {
  var iframeHeight = parseInt(e.data, 10);
  document.getElementById('iframeWrapper').style.height = iframeHeight + 'px';
};
//取消添加
window.addEventListener('message', function(event) {
  if(event.data === 'reloadSched'){
    //关闭日程添加弹窗
    dia.iframe[0].close();
    var weekStartDate=scheduleInfo.weekStartDate;
    var weekEndDate=scheduleInfo.weekEndDate;
    getOneWeekSched(weekStartDate,weekEndDate);
  }
  //子页面直接关闭-暂不需要
  // if(event.data === 'cancelAddApps'){
  //   //关闭应用收藏弹窗
  //   addAppDia.iframe[0].close();
  // }
  //刷新我的收藏数据
  if(event.data === 'reloadMyFavourite'){
    getMyFavourite();
  }
  //关闭日程弹窗
  if(event.data === 'closeSchedIframe'){
    dia.iframe[0].close();
  }
}, false);

//个人日程编辑
$(document).on("click", ".ui-page.calDetail .editCal", function (e){
  e.stopPropagation();
  // 关闭详情弹窗
  detailCaldia.modal[0].close();
  var scheduleId = $(this).data('id');
   dia = $.iframe({
    title:"日程编辑",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=edit&id="+scheduleId,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });
})

// 点击删除按钮
$(document).on("click", ".delCalItem ", function (e) {
  e.preventDefault();
  e.stopPropagation();
  e.stopImmediatePropagation();
  var id = $(this).data('id');
  var dia = $.dialog({
    modalClassName:'delCalDialog',
    title:'信息提示',
    content:"您确定要删除吗？",
    onOpen: function(e, dialog){
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("dialog:cancel",function(e, dialog){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
      // console.log('取消')
  }).on("dialog:confirm",function(e, dialog){
      // $.poptips({tipLevel:"success", tipIsColor:true, content:"您点击了"+ e.relatedTarget.innerText});
      var urls = index_urls.getDelCalendarUrl+'?selectedIds='+id;
      //删除
      $.ajax({
          url: urls+"&selectedIds="+id,
          type: 'delete',
          data: {},
          dataType: 'json',
          success: function (res) {
              if (res.result.data) {
                $.poptips("删除成功");
                  // 重新加载该周数据
                  var weekStartDate=scheduleInfo.weekStartDate;
                  var weekEndDate=scheduleInfo.weekEndDate;
                  getOneWeekSched(weekStartDate,weekEndDate);
                  $('.calButton .cancelDel').hide();
                  $('.calButton .delCal').show();
                  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
                  detailCaldia.modal[0].close();
              } else {
                $.poptips(data.errorMsg);
              }

          }
      })
  });
});

//日程删除按钮隐藏
function hideDelCalItem(){
  $('.mySchedule-container .mySchedule-content .calButton .cancelDel').hide();
  $('.mySchedule-container .mySchedule-content .delCal').show();
  $('.mySchedule-container .mySchedule-content .content .item .delCalItem').hide();
}

//点击箭头-展示/收缩
$(".schedule-arrowBtn").on("click",function(e){
  e.stopPropagation()
  $(this).toggleClass("active")
  $(".calButton").toggleClass("active")
})

/**
 * 日程工具函数
 */
//获取完整日期
function formatWholeDate(date){
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  month = month<10?('0'+month):month;
  var date = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate();
  return  year + "-" + month + "-" + date;
}
//获取月-日
function formatMonthDate(date){
  var month = date.getMonth() + 1;
  // month = month<10?('0'+month):month;
  // var date = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate();
  month = parseInt(month);
  var date = parseInt(date.getDate());
  return  month + "-" + date;
}

function doHandleMonth(month) {
  var m = month;
  if (month.toString().length == 1) {
    m = "0" + month;
  }
  return m;
}

function getDay(day) {
  var today = new Date();
  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
  today.setTime(targetday_milliseconds);
  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + "/" + tMonth + "/" + tDate;
}

function timestampToTime(timestamp) {
  var date = new Date(parseInt(timestamp)); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  var Y = date.getFullYear() + "-";
  var M =
    (date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1) + "-";
  var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
  var h =
    (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  var m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  // var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return Y + M + D;
}

// 获取当天 日期
function getCurrentData(){
  var currentDay = new Date();
  var curDay = currentDay.getDate();
  var curMonth = currentDay.getMonth() + 1;
  curDay = curDay < 10 ? "0" + Math.abs(curDay) : curDay;
  curMonth = curMonth < 10 ? "0" + Math.abs(curMonth) : curMonth;
  currentDay = currentDay.getFullYear() + "-" + curMonth + "-" + curDay;
  return currentDay;
}
// 获取几天后日期
function getDateLater(day,date=''){
  var today = date?(new Date(date)):(new Date());
  var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;
  today.setTime(targetday_milliseconds);
  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
   return tYear+"/"+tMonth+"/"+tDate;
}



//////////////////////////////////////////////////////////////////////////////////////////////
//服务大厅、最新应用tab切换
$(document).on("click", " .middleContent .moduleTitleBox .titleTab .tabItem_0", function (e) {
    e.stopPropagation();
    $(this).addClass('active').siblings().removeClass('active');
    var curTab = $(this).data('type');
    $('.middleContent .appModule .appList .appListInfo.'+curTab).addClass('current').siblings().removeClass('current');
});

//钉钉应用、我的收藏tab切换
$(document).on("click", " .middleContent .moduleTitleBox .titleTab .tabItem_1", function (e) {
  e.stopPropagation();
  $(this).addClass('active').siblings().removeClass('active');
  var curTab = $(this).data('type');
  if(curTab === 'dingtalkApps'){
      $('.moreBtn2').attr("data-type", "ddApps");
  } else if(curTab === 'myFavourite'){
      $('.moreBtn2').attr("data-type", "app");
  }
  $('.middleContent .appModule .appList .appListInfo.'+curTab).addClass('current').siblings().removeClass('current');
  
});

//getFwdt  获取服务大厅数据
function getFwdt(){
   $.ajax({
    url: index_urls.getFwdtUrl,
    data: {
      clientType: 4,
      parentCategoryId: 38,
	  showCategoryType: 1
    },
    crossDomain: true,
    dataType: "jsonp",
    success:async function (res) {
      var data = res.data?res.data:[];
      var categoryStr = '';
      categoryStr += '<div class="swiper fwdtSwiper">';
      categoryStr +='<div class="swiper-wrapper">';
      for(var i=0;i<data.length;i++){
        //判断是否svg 图片
        var item = data[i];
        var this_iconUrl = item.icon;
        var this_isSvg = false;  
        if(this_iconUrl.slice(-4) === '.svg'){
          var this_svgDetail = await imgToSvg(this_iconUrl);  
          this_isSvg = true;
        }
        if(i===0 || (i!=0 && (i%8) ===0)){
          // console.log(i,'f')
          //每页 第一个应用之前加上分页容器
          categoryStr +='<div class="swiper-slide">';
          categoryStr +='<div class="appPage">';
        }
        categoryStr +='<div class="appItem" data-id="'+data[i].id+'">';
          if((this_isSvg === true) && this_svgDetail){
          //svg类型图片
          categoryStr +='<div class="svgImg">'+this_svgDetail+'</div>';
        }else{
          //正常png等类型图片
          categoryStr +='<img src="'+data[i].icon+'" alt="">';
        }
        categoryStr +='<div class="title">'+data[i].typeName+'</div>';
        categoryStr +='</div>';
        // console.log(i,'m')
        if((i===(data.length-1)) || (i!=0 && ((i+1)%8) ===0)){
          //每页最后一个应用 或者最后一个应用
          categoryStr +='</div>';
          categoryStr +='</div>';
          // console.log(i,'d')
        }
      }
      categoryStr +='</div><div class="swiper-pagination fwdtSwiperPagination"></div></div>';
      if(data.length === 0){
        // categoryStr = '<div class="noData">暂无数据</div>';
        categoryStr = `
          <div class="nodataBox">
          <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
          <div class="nodata">暂无数据</div>
          </div>
        `;
      }
      $('.middleContent .appModule .appList .appListInfo.fwdt').html(categoryStr);
      if(data.length>8){
        // 超过一一页
        $('.middleContent .appModule .appList .fwdt .fwdtSwiper .swiper-slide').addClass('morePage');
        var appFwdtSwiper = new Swiper(" .fwdtSwiper", {
          loop: false,
          autoplay: false,
          pagination: {
            el: ".fwdtSwiperPagination",
            clickable: true
          },
        });
      }
      
    },
    error(err){
      var categoryStr = `
      <div class="nodataBox">
      <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
      <div class="nodata">暂无数据</div>
      </div>
      `;
      $('.middleContent .appModule .appList .appListInfo.fwdt').html(categoryStr);
    }
  });
}

//点击 服务大厅 -子项
$(document).on("click", " .middleContent .appModule .fwdt .appItem", function (e) {
  e.stopPropagation();
  var this_id = $(this).data('id');
  var this_url = commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/serviceCenter.html?_t='+new Date().getTime();
  this_url = this_id?(this_url+"&categoryid="+this_id):this_url;
  commonToUrl(this_url,'_self');
});

//获取我的收藏列表
function getMyFavourite(){
   $.ajax({
    url: index_urls.getMyFavoriteAppsUrl,
    data: {
      targetType: 4
    },
    crossDomain: true,
    dataType: "jsonp",
    success: async function (res) {
      var data = res.data?res.data:[];
      data.push({
        'id':'addApps',
        "name": "添加",
        "iconUrl": "./images/addBtnIcon.png"
      })
      var appHtml = '<div class="swiper myAppsSwiper">';
      appHtml +='<div class="swiper-wrapper">';
      for(var i=0;i<data.length;i++){
        //判断是否svg 图片
        var item = data[i];
        var this_iconUrl = item.iconUrl;
        var this_isSvg = false;  
        if(this_iconUrl.slice(-4) === '.svg'){
          var this_svgDetail = await imgToSvg(this_iconUrl);  
          this_isSvg = true;
        }
        if(i===0 || (i!=0 && (i%8) ===0)){
          // console.log(i,'f')
          //每页 第一个应用之前加上分页容器
          appHtml +='<div class="swiper-slide">';
          appHtml +='<div class="appPage">';
        }
        appHtml +='<div class="appItem" data-id="'+data[i].id+'" data-enableservice="'+data[i].enableService+'" data-isallow="'+data[i].isAllow+'" '
        +' data-reason="'+data[i].reason+'" data-appurl="'+data[i].entranceUrl+'" data-appname="'+data[i].appName+'">';
        if((this_isSvg === true) && this_svgDetail){
          appHtml +='<div class="svgImg">'+this_svgDetail+'</div>';
        }else{
          appHtml +='<img src="'+data[i].iconUrl+'" alt="">';
        }
       
        appHtml +='<span>'+data[i].name+'</span>';
        appHtml +='</div>';
        // console.log(i,'m')
        if((i===(data.length-1)) || (i!=0 && ((i+1)%8) ===0)){
          //每页最后一个应用 或者最后一个应用
          appHtml +='</div>';
          appHtml +='</div>';
          // console.log(i,'d')
        }
      }
      appHtml +='</div><div class="swiper-pagination myAppSwiperPagination"></div></div>';
      $('.middleContent .appModule .appList .appListInfo.myFavourite ').html(appHtml);
      if(data.length>8){
        // 超过一一页
        $('.middleContent .appModule .appList .myFavourite .myAppsSwiper .swiper-slide').addClass('morePage');
        var appSwiper = new Swiper(" .myAppsSwiper", {
          loop: false,
          autoplay: false,
          pagination: {
            el: ".myAppSwiperPagination",
            clickable: true,
          },
        });
      }
    },
    error(err){
    var data = {
      'id':'addApps',
      "name": "添加",
      "iconUrl": "./images/addBtnIcon.png"
    }
    var appHtml='';
    appHtml +='<div class="swiper-slide">';
    appHtml +='<div class="appPage">';
    appHtml +='<div class="appItem" data-id="'+data.id+'" data-enableservice="'+data.enableService+'" data-isallow="'+data.isAllow+'" '
    +' data-reason="'+data.reason+'" data-appurl="'+data.entranceUrl+'" data-appname="'+data.appName+'">';
    appHtml +='<img src="'+data.iconUrl+'" alt="">';
    appHtml +='<span>'+data.name+'</span>';
    appHtml +='</div>';
    appHtml +='</div>';
    appHtml +='</div>';
    appHtml +='</div><div class="swiper-pagination myAppSwiperPagination"></div></div>';
    $('.middleContent .appModule .appList .appListInfo.myFavourite ').html(appHtml);
    }
  });
}

//最近使用
function getRecentlyUsedApps(){
  $.ajax({
    url: index_urls.getRecentlyUsedApps,
    data: {
      targetType: 4,
      count:16,
      parentCategoryId: 38
    },
    crossDomain: true,
    dataType: "jsonp",
    success: async function (res) {
      var data = res.data?res.data:[];
      if(data.length > 0){
          var appHtml = '<div class="swiper newAppsSwiper">';
          appHtml +='<div class="swiper-wrapper">';
          for(var i=0;i<data.length;i++){
            //判断是否svg 图片
            var item = data[i];
            var this_iconUrl = item.iconUrl;
            var this_isSvg = false;  
            if(this_iconUrl.slice(-4) === '.svg'){
              var this_svgDetail = await imgToSvg(this_iconUrl);  
              this_isSvg = true;
            }
            if(i===0 || (i!=0 && (i%8) ===0)){
              // console.log(i,'f')
              //每页 第一个应用之前加上分页容器
              appHtml +='<div class="swiper-slide">';
              appHtml +='<div class="appPage">';
            }
            appHtml +='<div class="appItem" data-id="'+data[i].id+'" data-enableservice="'+data[i].enableService+'" data-isallow="'+data[i].isAllow+'" '
            +' data-reason="'+data[i].reason+'" data-appurl="'+data[i].entranceUrl+'" data-appname="'+data[i].appName+'">';
            if((this_isSvg === true) && this_svgDetail){
              appHtml +='<div class="svgImg">'+this_svgDetail+'</div>';
            }else{
              appHtml +='<img src="'+data[i].iconUrl+'" alt="">';
            }
           
            appHtml +='<span>'+data[i].name+'</span>';
            appHtml +='</div>';
            // console.log(i,'m')
            if((i===(data.length-1)) || (i!=0 && ((i+1)%8) ===0)){
              //每页最后一个应用 或者最后一个应用
              appHtml +='</div>';
              appHtml +='</div>';
            }
          }
          appHtml +='</div><div class="swiper-pagination myAppSwiperPagination"></div></div>';
          $('.middleContent .appModule .appList .appListInfo.recentlyUsedApps ').html(appHtml);
          if(data.length>8){
            // 超过一一页
            $('.middleContent .appModule .appList .recentlyUsedApps .myAppsSwiper .swiper-slide').addClass('morePage');
            var newsSwiper = new Swiper(" .newAppsSwiper", {
              loop: false,
              autoplay: false,
              pagination: {
                el: ".myAppSwiperPagination",
                clickable: true,
              },
            });
          }
      } else {
          $('.middleContent .appModule .appList .appListInfo.recentlyUsedApps ').html("<div class='noDate'>暂无数据</div>");
      }
    },
    error(err){
        $('.middleContent .appModule .appList .appListInfo.recentlyUsedApps ').html("<div class='noDate'>暂无数据</div>");
    }
  });
}

function getDingTalkApps(){
  $.ajax({
    url: index_urls.getAllServiceAppsUrl,
    data: {
      clientType: 4,
      categoryId: 56
    },
    crossDomain: true,
    dataType: "jsonp",
    success: async function (res) {
      var data = res.data.appList?res.data.appList:[];
      if(data.length > 0){
          var appHtml = '<div class="swiper dingAppsSwiper">';
          appHtml +='<div class="swiper-wrapper">';
          for(var i=0;i<data.length;i++){
            //判断是否svg 图片
            var item = data[i];
            var this_iconUrl = item.iconUrl;
            var this_isSvg = false;  
            if(this_iconUrl.slice(-4) === '.svg'){
              var this_svgDetail = await imgToSvg(this_iconUrl);  
              this_isSvg = true;
            }
            if(i===0 || (i!=0 && (i%8) ===0)){
              // console.log(i,'f')
              //每页 第一个应用之前加上分页容器
              appHtml +='<div class="swiper-slide">';
              appHtml +='<div class="appPage">';
            }
            appHtml +='<div class="appItem" data-id="'+data[i].id+'" data-enableservice="'+data[i].enableService+'" data-isallow="'+data[i].isAllow+'" '
            +' data-reason="'+data[i].reason+'" data-appurl="'+data[i].entranceUrl+'" data-appname="'+data[i].appName+'">';
            if((this_isSvg === true) && this_svgDetail){
              appHtml +='<div class="svgImg">'+this_svgDetail+'</div>';
            }else{
              appHtml +='<img src="'+data[i].iconUrl+'" alt="">';
            }
           
            appHtml +='<span>'+data[i].name+'</span>';
            appHtml +='</div>';
            // console.log(i,'m')
            if((i===(data.length-1)) || (i!=0 && ((i+1)%8) ===0)){
              //每页最后一个应用 或者最后一个应用
              appHtml +='</div>';
              appHtml +='</div>';
            }
          }
          appHtml +='</div><div class="swiper-pagination myAppSwiperPagination"></div></div>';
          $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html(appHtml);
          if(data.length>8){
            // 超过一一页
            $('.middleContent .appModule .appList .dingtalkApps .myAppsSwiper .swiper-slide').addClass('morePage');
            var dingSwiper = new Swiper(" .dingAppsSwiper", {
              loop: false,
              autoplay: false,
              pagination: {
                el: ".myAppSwiperPagination",
                clickable: true,
              },
            });
          }
      } else {
          $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html("<div class='noDate'>暂无数据</div>");
      }
    },
    error(err){
      $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html("<div class='noDate'>暂无数据</div>");
    }
  });
}

//获取资讯栏目
function getZxTabs(){
  $.ajax({
    url: index_urls.getPortalServiceSpecialTopicUrl,
    data: {},
    crossDomain: true,
    dataType: "jsonp",
    success: function (res) {
        var data = res.data?res.data:[];
        data = (data.length>0)?data[0]:{};
        var zxData = (data.data)?data.data:[];
        var zxDataStr='<div class="topicInfo">';
        for(var i=0;i<zxData.length;i++){
          var this_url = commonUrlTwo+"/_web/customized/DTalkPortal/ding/mobile/articleCenter.html?id="+zxData[i].id;
          //第一个栏目放左上;2-左下；3-右
          var this_position_class=(i===0)?'topItem':((i===1)?'bottomItem':((i===2)?'rightItem':''))
          zxDataStr += `
          <div class="topicItem ${this_position_class}" data-url="${this_url}">
          <div class="itemInfo">
            <div class="title">${zxData[i].title}</div>
            <div class="detailBox">
             <span>查看内容</span>
             <img src="./images/detailIcon.png" alt="">
            </div>
          </div>
         <img class="itemBg" src="${zxData[i].icon}" alt="">
        </div>
          `;
        }
        zxDataStr +='</div>';
        if(zxData.length===0){
          zxDataStr =`
          <div class="nodataBox">
          <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
          <div class="nodata">暂无数据</div>
          </div>
          `;
        }
        $('.middleContent .topicContent').html(zxDataStr);
    },
    error(err){
        var zxDataStr =`
        <div class="nodataBox">
        <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
        <div class="nodata">暂无数据</div>
        </div>
        `;
        $('.middleContent .topicContent').html(zxDataStr);
    }
  });
}

//资讯栏目跳转
$(document).on("click", ".middleContent .topicInfo .topicItem", function (e){
    e.stopPropagation();
    var this_url = $(this).data('url');
    if(this_url){
      commonToUrl(this_url,'_self');
    }
})


//访问应用
var addAppDia;
var addAppDiaOpen = false;
//.middleContent .appModule .appList .myFavourite 
$(document).on("click", " .appList .appItem", function (e) {
    e.stopPropagation();
    var this_id = $(this).data('id');
    if(this_id === 'addApps'){
        //应用添加按钮
        addAppDia = $.iframe({
          title:"应用收藏",
          content:"addMyApp.html?_p=YXM9MSZwPTEmbT1OJg__",
          modalClassName:"iframe-addApps",
          onOpen: function(e, iframe){
            // $.poptips({type:"warning",color:true, content:"正在打开"});
            addAppDiaOpen = true;
          }
        }).on("iframe:open",function(e, iframe){
          // $.poptips({tipLevel:"warning",color:true, content:"正在打开"});
        }).on("iframe:cancel",function(e, iframe){
            // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
        }).on("iframe:closed",function(e, iframe){
          addAppDiaOpen = false;
          // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
        });
    }else{
        //正常应用-跳转
        var this_node = $(this);
        toAppUrl(this_node)
    }
});

/*$(document).on("click", ".appItem", function (e) {
    e.stopPropagation();
    //正常应用-跳转
    var this_node = $(this);
    toAppUrl(this_node)
});
*/
//应用跳转
function toAppUrl(this_node){
    var this_enableservice = $(this_node).data('enableservice');
    var this_reason = $(this_node).data('reason');
    var this_isallow = $(this_node).data('isallow');
    var this_appurl = $(this_node).data('appurl');
    var this_appname = $(this_node).data('appname');
    var this_id = $(this_node).data('id');
    if(this_enableservice == 1){
        //有服务指南页 跳应用详情页
        var this_url=commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/detail.html?appVersionId='+this_id+'&_t='+new Date().getTime();
        commonToUrl(this_url,'_blank');
    }else{
        if((this_isallow === "true") || (this_isallow === true)){
            openModule(this_appname);
            let url=this_appurl
            // if(checkUrlStart(url)){
            //   url=addParameterToUrl(url)
            // }
            commonToUrl(url,'_blank');
        }else{
          this_reason = this_reason?this_reason:'暂无权限';
            $.poptips(this_reason);
        }
    }
}



//更多按钮跳转
$(document).on("click", ".middleContent .moreBtn", function (e) {
    e.stopPropagation();
    var this_type = $(this).data('type');
    var url = "";
    if(this_type === 'app'){
        url = commonUrlTwo + '/_web/customized/DTalkPortal/ding/mobile/serviceCenter.html';
    } else if(this_type === 'ddApps'){
        url = commonUrlTwo + '/_web/customized/DTalkPortal/ding/mobile/serviceCenter.html?categoryid=56';
    } else if(this_type === 'topic'){
        url=commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/articleCenter.html';
    } else if(this_type === 'schedule'){
        //当前选中日程分类
        var this_schedule_type=$('.middleContent .todaWeek .tab-list .tab-item.tab-selected').data('type');
        url=commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/scheduleCenter.html?type='+this_schedule_type;
    }
    if(url){
        url = (url.indexOf('?')>-1)?(url+"&_t="+new Date().getTime()):(url+"?_t="+new Date().getTime());
        commonToUrl(url,'_self');
    }
})

//应用统计
function openModule(appName) {
  var screenSize=window.screen.width+"*"+window.screen.height;
  var clientVersion=navigator.platform+"-"+myBrowser()+"-"+navigator.appVersion.substring(0, navigator.appVersion.indexOf("(")-1);
  $.ajax({
      url: index_urls.openModule,
      type: 'get',
      data: {
          appName:appName,
          clientVersion: clientVersion,
          screenSize:screenSize
      },
      dataType: 'json',
      success: function (){
      }
  })
}