/**
 * Created by Administrator on 2017/11/17.
 */

getTaskCategory()
var arr = []
var tabindex = Number(location.hash.replace('#tab', ''))
var tab = $('.ui-tab').tab({
  start: tabindex || 0,
  callback: function (index, from, cur) {
    console.log(index, from, cur)
    current = cur
    if (cur == 0) {
      $('.taskInfoContent').addClass('first')
      $('.taskInfoContent').removeClass('last')
    }
    if (cur == 2) {
      $('.taskInfoContent').addClass('last')
      $('.taskInfoContent').removeClass('first')
    }
    if (cur == 1) {
      $('.taskInfoContent').removeClass('last')
      $('.taskInfoContent').removeClass('first')
    }
    $('.num' + cur).removeClass('hid')
    $('.ui-searchbar i').attr('data-searchtype', cur)
    $('.ui-searchbar-input input').attr('data-searchtype', cur)
    $('.layui-btn-container>input').val('全部')
    $('.taskCategory').val('all')
    checkidentity(cur)
  }
})
//搜索框
A.use('arm.touch', function () {
  $('.ui-searchbar').tap(function () {
    $('.ui-searchbar-wrap').addClass('focus')
    $('.ui-searchbar-input input').focus()
    $('html,body').css('overflow', 'hidden')
  })
  $('.ui-searchbar-cancel').tap(function () {
    $('.ui-searchbar-wrap').removeClass('focus')
    $('.keyword').val('')
    checkidentity(current)
  })
})
isDDing()
function isDDing() {
  var userAgent = navigator.userAgent
  if (userAgent.indexOf('DingTalk') > -1) {
    dd.ready(function () {
      // 页面被唤醒的事件监听(webview)
      document.addEventListener(
        'resume',
        function (e) {
          e.preventDefault()
          var cur = $('.ui-tab .current').index()
          checkidentity(cur)
        },
        false
      )
    })
  }
}
//隐藏搜索取消框
$('.ui-searchbar-wrap .search').hide()
$('#cancle').hide()
$('.ui-searchbar-input').bind('input propertychange', function () {
  $('.search').show()
  $('#cancle').show()
})

// 搜索，点击搜索小图标
$('.ui-icon-search').tap(function () {
  var t = $(this).data('searchtype')
  $('#news-list-' + t).html(' ')
  checkidentity(t)
})

$('.ui-icon-close').tap(function () {
  $(this).siblings().eq(2).find('input').val('')
})

$('.search').on('click', function (e) {
  checkidentity(current)
})

// 监听键盘事件
document.addEventListener('keydown', function (event) {
  if (event.key === 'Enter') {
    checkidentity(current)
  }
})

/*
    target : elem
    c : 代表属于那个tab
    type ： 获取总数的不同的类型， 1 代表 data.page.count。其他代表 data.count
*/
function getCount(target, c, type) {
  var targeturl = ''
  if (c === 0) {
    targeturl = index_urls.getToDoUrl
  } else if (c === 1) {
    targeturl = index_urls.getDraftTaskUrl
  } else if (c === 2) {
    targeturl = index_urls.getDoneUrl
  } else if (c === 3) {
    targeturl = index_urls.getTraceUrl
  } else if (c === 4) {
    targeturl = index_urls.getFinishedUrl
  }
  var u = targeturl
  getCountTwo(target, u, type)
}

function getCountTwo(target, address, type) {
  $.ajax({
    url: address,
    data: {},
    dataType: 'jsonp',
    success: function (data) {
      var page
      if (type === 1) {
        page = data.result.total
      } else {
        page = data.result.total
      }
      if (page > 99) {
        $(target).text('99+')
      } else {
        $(target).text(page)
      }
    },
    error: function (err) {
      $.poptips('接口暂时无法调用，请稍候访问！')
    }
  })
}

function checkidentity(c) {
  var targeturl = ''
  if (c === 0) {
    targeturl = index_urls.getToDoUrl
  } else if (c === 1) {
    targeturl = index_urls.getDoneUrl + '&orderBy=doneTime'
  } else if (c === 2) {
    targeturl = index_urls.getTraceUrl
  }
  loadinfo(c, targeturl)
}

function loadinfo(c, u) {
  var curPage = 1
  var key = $('.searchKey').val()
  let value = $('.taskCategory').val()

  var taskCategoryCode = value == 'all' ? '' : value
  var _keyword = key == '全部' ? '' : key
  if (_keyword) {
    _keyword = _keyword.trim()
  }
  if (arr[0]) {
    $('#news-list-' + c).html(' ')
    arr[0].options.fnAjaxSettings = function (data, url, action) {
      curPage = data.pageIndex
      // if (c === 4 || c === "4") {
      //   // 草稿箱
      //   return {
      //     url: u,
      //     data: {
      //       taskCategoryCode: taskCategoryCode,
      //       begin: (data.pageIndex - 1) * data.pageRows,
      //       length: data.pageRows,
      //       isCount: true,
      //       title: _keyword,
      //     },
      //     dataType: "jsonp",
      //   };
      // } else {
      return {
        url: u,
        data: {
          taskCategoryCode: taskCategoryCode,
          page: data.pageIndex,
          rows: data.pageRows,
          isCount: true,
          title: _keyword
        },
        dataType: 'jsonp'
      }
      // }
    }
    arr[0].options.done = function (data, action) {
      var result = {
        d: data
      }
      if (curPage == 1) {
        rander(c, result, data)
      }
      if (curPage > 1 && data.length > 0) {
        rander(c, result, data)
      }
    }
    arr[0].action(1)
  } else {
    arr[0] = $('.content').pullaction({
      target: '#news-list-' + c,
      ajaxDataProp: 'data',
      initRefresh: true, // 初始化刷新
      autoRefresh: false, // 自动刷新
      interval: 2000, // 自动刷新时间
      pageRows: 10,
      otherParams: {},
      disablePullDown: false,
      goTop: 1000,
      fnAjaxSettings: function (data, url, action) {
        curPage = data.pageIndex
        return {
          url: u,
          data: {
            taskCategoryCode: taskCategoryCode,
            page: data.pageIndex,
            rows: data.pageRows,
            title: _keyword
          },
          dataType: 'jsonp'
        }
      },
      pullDownBefore: function (e) {
        // A.console().log(e);
      },
      pullDownReady: function (e) {
        // A.console().log(e);
      },
      pullDownAction: function (e) {
        $('#news-list-' + current).html(' ')
        // A.console().log(e);
      },
      pullDownError: function (e) {
        // A.console().log(e);
        window.location.href = 'taskCenter.html?tt=' + Math.random()
      },
      pullDownSuccess: function (e) {
        // A.console().log(e);
      },
      pullDownComplete: function (e) {
        // A.console().log(e);
      },
      pullUpBefore: function (e) {
        // A.console().log(e);
      },
      pullUpReady: function (e) {
        // A.console().log(e);
      },
      pullUpAction: function (e) {
        // A.console().log(e);
      },
      pullUpError: function (e) {
        // A.console().log(e);
      },
      pullUpSuccess: function (e) {
        // A.console().log(e);
      },
      pullUpComplete: function (e) {
        // A.console().log(e);
      },
      fnDataFilter: function (res) {
        // if (current === 4 || current === "4") {
        //   var data = res.drafts;
        //   for (var i = 0; i < data.length; i++) {
        //     var createTime_ = formatDate_(data[i].createtime);
        //     data[i].createTimeStr =
        //       createTime_.year +
        //       "-" +
        //       createTime_.month +
        //       "-" +
        //       createTime_.date +
        //       " " +
        //       createTime_.hour +
        //       ":" +
        //       createTime_.minute;
        //   }
        // } else {
        var data = res && res.result && res.result.data
        // if(current == 0){
        //   data=data?.map(item=>{
        //     let url=item.mobileUrl? item.mobileUrl:item.url
        //     if(checkUrlStart(url)){
        //       url=addParameterToUrl(url)
        //     }
        //     return {
        //         ...item,
        //         url:url
        //     }
        //   })
        // }

        // }
        if (data) {
          return data
        } else {
          return []
        }
        // console.log(arguments);
      },
      fail: function (errCode, state) {
        if (errCode === -1) $.poptips('没有了~')
      },
      done: function (data, action) {
        // console.log(data);
        var result = {
          // d: data.list
          d: data
        }
        if (curPage == 1) {
          rander(c, result, data)
        }
        if (curPage > 1 && data.length > 0) {
          rander(c, result, data)
        }
      },
      onRendered: function () {
        // console.log(arguments);
      }
    })
  }
}

// 模板加载
function rander(c, r, data) {
  console.log('===c==',c)
  $('.daiban li').removeClass('active')
  if (c === 0) {
    var temp = _.template($('#daibaninfo').html())
    var html = temp(r)
    $('#news-list-' + c).append(html)

    $('#todotask').addClass('active')
  } else if (c === 1) {
    var temp = _.template($('#yibaninfo').html())
    var html = temp(r)
    $('#news-list-' + c).append(html)
    $('#donetask').addClass('active')
  } else if (c === 2) {
    var temp = _.template($('#genzonginfo').html())
    var html = temp(r)
    $('#news-list-' + c).append(html)
    $('#processTrack').addClass('active')
  } else if (c === 4) {
    var temp = _.template($('#banjieinfo').html())
    var html = temp(r)
    $('#news-list-' + c).append(html)
    $('#processDone').addClass('active')
  } else if (c === 5 || c === '5') {
    var temp = _.template($('#cgxinfo').html())
    var html = temp(r)
    $('#news-list-' + c).append(html)
    $('#cgx').addClass('active')
  }
}

// 格式时间
function formatDate(now) {
  now = new Date(now)
  var year = now.getFullYear()
  var month = now.getMonth() < 9 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1
  var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
  var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours()
  var minute = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes()
  var second = now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds()
  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}

//日期格式化
function formatDate_(date) {
  var now = new Date(parseInt(date))
  var year = now.getFullYear()
  var month = now.getMonth() + 1 >= 10 ? now.getMonth() + 1 : '0' + (now.getMonth() + 1)
  var date = now.getDate() >= 10 ? now.getDate() : '0' + now.getDate()
  var hour = now.getHours() >= 10 ? now.getHours() : '0' + now.getHours()
  var minute = now.getMinutes() >= 10 ? now.getMinutes() : '0' + now.getMinutes()
  var second = now.getSeconds() >= 10 ? now.getSeconds() : '0' + now.getSeconds()
  return {
    year: year,
    month: month,
    date: date,
    hour: hour,
    minute: minute,
    second: second
  }
}

function checkOpen(workUrl) {
  if (workUrl.startsWith('http://my.just.edu.cn/default/')) {
    var _platform = localStorage.getItem('_platform')
    if (workUrl.indexOf('?') > -1) {
      workUrl = workUrl + '&f=' + _platform + '&appload=0'
    } else {
      workUrl = workUrl + '?f=' + _platform + '&appload=0'
    }
  }
  if (workUrl) {
    var userAgent = navigator.userAgent // 获取User Agent信息
    if (userAgent.indexOf('DingTalk') > -1) {
      dd.biz.util.openLink({
        url: workUrl,
        onSuccess: function (result) {
          //msg(result);
        },
        onFail: function (err) {
          //msg(err);
          console.log(err)
        }
      })
    } else {
      window.open(workUrl, '_blank')
    }
  }
  // window.open(workUrl, "_blank");
}
//点击删除草稿箱
$(document).on('click', '.del-draft', function () {
  let id = $(this).attr('data-id')
  var dia_ = $.alert({
    buttons: ['取消', '确定'],
    content: '确定删除吗？',
    onAction: function (e, modal) {},
    onOpen: function (e, modal) {},
    onConfirm: function () {
      $.ajax({
        url: index_urls.deleteDraftByIds + '?ids=' + id,
        type: 'get',
        data: {
          isReturn: false
        },
        dataType: 'jsonp',
        success: function (res) {
          if (res.success) {
            $.poptips('成功删除')
            checkidentity(4)
          } else {
            $.poptips(res.msg)
          }
        },
        error: function (err) {
          // console.log(err);
        }
      })
    },
    onCancel: function () {}
  })
})

//获取事务分类列表
function getTaskCategory() {
  $.ajax({
    url: index_urls.getTaskCategoryUrl,
    data: {},
    crossDomain: true,
    dataType: 'jsonp',
    success: function (res) {
      var result = res.result ? res.result : {}
      var data = result.data ? result.data : []
      renderTaskCategory(data)
    },
    error(err) {}
  })
}
//渲染下拉框
function renderTaskCategory(data) {
  let html = '<option value="all">全部</option>'
  for (var i = 0; i < data.length; i++) {
    html += '<option value="' + data[i].code + '">' + data[i].name + '</option>'
  }
  $('.taskCategory').html(html)
  $('.layui-btn-container>input').val('全部')
}
//点击下拉框
$('.taskCategory')
  .mobiscroll()
  .select({
    groupLabel: '分类',
    label: '',
    rows: 5,
    group: {
      groupWheel: true,
      header: true,
      clustered: true
    },
    onSelect: function (valueText, inst) {
      // console.log("select", arguments, inst.getValues(true, true));
      let val = inst.getVal()
      $('.taskCategory').val(val)
      checkidentity(current)
    },
    onShow: function (html, valueText, inst) {
      // console.log("show", arguments, inst.getValues(true, true));
    },
    onBeforeShow: function (inst) {
      // console.log("beforeshow", arguments, inst.getValues(true, true));
    },
    onInit: function (inst) {}
  })
//点击搜索
$('input[type="search"]').keypress(function (e) {
  if (e.keyCode === 13) {
    e.preventDefault() // 阻止表单默认提交行为
    // 在这里执行搜索逻辑，比如发送AJAX请求等
    checkidentity(current)
  }
})
$(document).on('click', '.searchIcon', function (e) {
  e.preventDefault() // 阻止表单默认提交行为
  checkidentity(current)
})
