$(function () {
  //默认获取主题下分类
    $('.parentCategoryTab>.parentItemTab:nth-child(2)').addClass('active').siblings().removeClass('active')
    var urlCategoryId = getURLParameter('categoryid') ?? 'all'
    getZtCategory(urlCategoryId)
})
//默认获取分类
function getZtCategory(urlCategoryId) {
  $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').show()
  $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo').css({
    width: 'calc(100% - 100px)',
    'margin-left': '-12px'
  })

  $.ajax({
    url: index_urls.getFwdtUrl,
    dataType: 'jsonp',
    data: {
      clientType: 3,
      showType: 1
    },
    success: function (res) {
      var ztCategory = res.data ? res.data : []
      var ztCategoryStr = `<div class="leftCategory" data-categoryid=""><span>全部</span></div>`
      for (var i = 0; i < ztCategory.length; i++) {
        var isActive = ''
        //地址栏带参数
        if (urlCategoryId) {
          if (parseInt(urlCategoryId) === parseInt(ztCategory[i].id)) {
            isActive = 'active'
            loadZtAppList(ztCategory[i].id)
          }
        } else {
          //地址栏不带参数-默认选中第一项
          if (i === 1) {
            isActive = 'active'
            loadZtAppList(ztCategory[i].id)
          }
        }
        ztCategoryStr += `
                        <div class="leftCategory ${isActive}" data-categoryid="${ztCategory[i].id}"><span>${ztCategory[i].typeName}</span></div>
                    `
      }
      $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').html(ztCategoryStr)
      //判断是否已有默认选中项-没有则默认第一个-防止地址栏带参数但分类列表里没有
      if (
        ztCategory.length > 0 &&
        urlCategoryId &&
        $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').length === 0
      ) {
        $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory').eq(0).addClass('active')
        var this_id = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory')
          .eq(0)
          .attr('data-categoryid')
        loadZtAppList(this_id)
      }
      //没有分类-右侧列表数据直接暂无数据
      if (ztCategory.length === 0) {
        var rightAppStr = `
                    <div class="nodataBox">
                    <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                    <div class="nodata">暂无数据</div>
                    </div>
                    `
        $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr)
      } else {
        //有应用分类
        //当前选中项的offset().top值
        var this_active_top =
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').offset().top - 231
        var leftCategoryHeight = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').height()
        var this_distance = this_active_top - leftCategoryHeight
        if (this_distance > 0) {
          //滚动到当前选中项-使其显示出来
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').animate({ scrollTop: this_distance }, 'slow')
        }
        //判断左边分类高度是否小于右边-即整体盒子高度
        var allCategoryHeight = ztCategory.length * 54.5
        if (allCategoryHeight < leftCategoryHeight) {
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child').addClass(
            'resetAfter'
          )
        } else {
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child').removeClass(
            'resetAfter'
          )
        }
      }
    },
    error: function (err) {
      var rightAppStr = `
                  <div class="nodataBox">
                  <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                  <div class="nodata">暂无数据</div>
                  </div>
                  `
      $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr)
    }
  })
}

//加载主题分类下的应用
function loadZtAppList(categoryId = '', appName = '') {
  categoryId = categoryId ? categoryId : ''
  $.ajax({
    url: index_urls.getAllServiceAppsUrl,
    dataType: 'jsonp',
    data: {
      clientType: 3,
      categoryId: categoryId,
      appName: appName
    },
    success: function (res) {
      var data = res.data ? res.data : {}
      var appList = data.appList ? data.appList : []
      //测试用
      // let appList = appList_.flatMap(value => Array(7).fill(value));
      renderAppLists(appList)
    },
    error: function (err) {
      renderAppLists([])
    }
  })
}

//左侧分类切换
$(document).on('click', ' .serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory ', function (e) {
  e.stopPropagation()
  //输入框 关键字
  var keyword = $('.serviceContent .searchBox .inputBox input').val()
  keyword = $.trim(keyword)
  if (keyword.length === 0 || checkVal(keyword) === 'true') {
    $(this).addClass('active').siblings().removeClass('active')
    //当前选中分类
    var categoryId = $(this).attr('data-categoryid')
    loadZtAppList(categoryId, keyword)
  }
})

//点击应用跳转
$(document).on('click', ' .rightCategoryInfo .appGrid .appItem', function (e) {
  e.stopPropagation()
  var this_enableservice = $(this).data('enableservice')
  var this_reason = $(this).data('reason')
  var this_isallow = $(this).data('isallow')
  var this_appurl = $(this).data('appurl')
  var this_appname = $(this).data('appname')
  var this_id = $(this).data('id')
  if (this_enableservice == 1) {
    //有服务指南页 跳应用详情页
    //  window.open('detail.html?appVersionId='+this_id+'&_t='+new Date().getTime());
    var this_url = './detail.html?appVersionId=' + this_id + '&_t=' + new Date().getTime()
    commonToUrl(this_url, '_blank')
  } else {
    if (this_isallow === true || this_isallow === 'true') {
      openModule(this_appname)
      let url = this_appurl
      //  if(checkUrlStart(url)){
      //    url=addParameterToUrl(url)
      //  }
      //  window.open(this_appurl);
      commonToUrl(url, '_blank')
    } else {
      this_reason = this_reason ? this_reason : '暂无权限'
      $.poptips(this_reason)
    }
  }
})

//点击搜索
$(document).on('click', ' .serviceContent .searchBox .searchBtn', function (e) {
  e.stopPropagation()
  var keyword = $('.serviceContent .searchBox .inputBox input').val()
  keyword = $.trim(keyword)
  if (keyword.length === 0 || checkVal(keyword) === 'true') {
    //正常搜索
    //当前选中tab项
    var this_type = $('.serviceContent .serviceMod .parentCategoryTab .parentItemTab.active').data('type')
    var this_id = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').attr(
      'data-categoryid'
    )
    if (this_type === 'zt') {
      //搜索主题应用
      loadZtAppList(this_id, keyword)
    } else if (this_type === 'bm') {
      //搜索部门应用
      loadBmAppList(this_id, keyword)
    } else {
      loadZtAppList(this_id, keyword)
    }
  }
})

//渲染右侧应用数据
async function renderAppLists(appList) {
  var appListStr = '<div class="appGrid">'
  for (var i = 0; i < appList.length; i++) {
    var item = appList[i]
    var this_iconUrl = item.iconUrl
    var this_isSvg = false
    if (this_iconUrl.slice(-4) === '.svg') {
      var this_svgDetail = await imgToSvg(this_iconUrl)
      this_isSvg = true
    }
    if (this_isSvg === true && this_svgDetail) {
       var _iconColor = item.iconColor ? item.iconColor : iconColor(i);
      //svg图标
      appListStr += `
      <div class="appItem" data-enableservice="${item.enableService}" data-id="${item.id}" data-isallow="${item.isAllow}"
      data-reason="${item.reason}" data-appurl="${item.entranceUrl}" data-appname="${item.appName}">
        <div class="imgBox">
        <div class="svgImg" style="background-color:${_iconColor}">${this_svgDetail}</div>
        </div>
        <div class="name">${item.name}</div>
      </div>
    `
    } else {
      //正常图片
      appListStr += `
      <div class="appItem" data-enableservice="${item.enableService}" data-id="${item.id}" data-isallow="${item.isAllow}"
      data-reason="${item.reason}" data-appurl="${item.entranceUrl}" data-appname="${item.appName}">
        <div class="imgBox">
        <img src="${item.iconUrl}" alt="">
        </div>
        <div class="name">${item.name}</div>
      </div>
    `
    }
  }
  if (appList.length < 1) {
    appListStr = `
    <div class="nodataBox">
    <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
    <div class="nodata">暂无数据</div>
    </div>
    `
  }
  $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(appListStr)
}

//应用统计
function openModule(appName) {
  var screenSize = window.screen.width + '*' + window.screen.height
  var clientVersion =
    navigator.platform +
    '-' +
    myBrowser() +
    '-' +
    navigator.appVersion.substring(0, navigator.appVersion.indexOf('(') - 1)
  $.ajax({
    url: index_urls.openModule,
    type: 'get',
    data: {
      appName: appName,
      clientVersion: clientVersion,
      screenSize: screenSize
    },
    dataType: 'json',
    success: function () {}
  })
}

//检查搜索文字
function checkVal(keyWord) {
  keyWord = $.trim(keyWord)
  if (keyWord) {
    let re = /^[\u4E00-\u9FA5A-Za-z0-9]+$/ //只能输入汉字、英文字母或数字
    if (re.test(keyWord)) {
      return 'true'
    } else {
      //提示信息
      $.poptips('请输入汉字、英文字母或数字！')
      return 'false'
    }
  } else {
    //提示信息
    $.poptips('请输入您要查找的内容')
    return 'false'
  }
}

//获取地址栏参数
function getURLParameter(name) {
  var url = window.location.search.substring(1) // 获取URL中?后的参数字符串
  var paramsArr = url.split('&') // 将参数字符串按'&'分割成数组
  for (var i = 0; i < paramsArr.length; i++) {
    var param = paramsArr[i].split('=') // 将每个参数按'='分割成键值对数组
    if (param[0] === name) {
      return param[1] // 返回指定参数名对应的值
    }
  }
  return null // 如果没有找到则返回null
}

$('body').on('keyup', '.serviceContent .searchBox .inputBox input', function (e) {
  //筛选框赋值
  if (e.keyCode == 13) {
    //enter键为13
    //触发搜索的点击事件
    $('.serviceContent .searchBox .searchBtn').trigger('click')
  }
})
