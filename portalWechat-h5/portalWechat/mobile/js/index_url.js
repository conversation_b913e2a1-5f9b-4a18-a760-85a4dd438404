var commonUrl = "/";
var commonUrlTwo = "/sopplus";
localStorage.setItem("domainUrl", commonUrl);
localStorage.setItem("portalDomainUrl", commonUrlTwo);

var _p = "YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__";
var _p2 = "YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m";
var firstArticleImgUrl = ''; 

var index_urls = {
  //获取人员身份
  getUserLbUrl: "/sopplus/_web/customized/getUserLb.jsp?_p=" + _p,

  getUserInfo:
    "/sopplus/_web/customized/loginInfo.jsp?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
  // 首页 新闻资讯
  getPortalArticleList16:
    "/mnews/mobile/getPortalArticleList16.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__&forceHttps=1",
  // 首页 热门分类
  getPortalIndexAppList:
    "/sopplus/mobile/getPortalIndexAppList.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
  // 首页 我的收藏
  getMyFavoriteAppsForMobile:
    "/sopplus/mobile/getMyFavoriteAppsForMobile.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
  // 应用 功能
  getPortalAppsListByOS:
    "/sopplus/mobile/getPortalAppsListByOS.rst?_p=YXA9MSZtPU4m",
  // 收藏 取消收藏
  cancelApp: "/sopplus/mobile/favoritesPortalApp.rst?_p=" + _p,
  //最近使用
  getRecentlyUsedApps: "/sopplus/mobile/getRecentlyUsedApps.rst?_p=" + _p,

  
  //获取首页头部应用
  getTopAppsUrl:
    "/sopplus/_web/customized/getPortalAppListByPCAppType.jsp?_p=" + _p,

    
  /**
   * 应用 
   * */  
  //获取服务大厅数据
  getFwdtUrl: "/sopplus/mobile/getPortalCategory.rst?targetOs=wechat&_p=" + _p,
  //获取我的收藏
  getMyFavoriteAppsUrl: "/sopplus/mobile/getMyFavoriteApps.rst?_p=" + _p,
  //常用应用
  queryFixRecommendAppList:"/sopplus/mobile/queryFixRecommendAppList.rst?_p=" + _p,
  //获取对应分类下应用
  getAllServiceAppsUrl: "/sopplus/mobile/loadAllServiceApps.rst?_p=" + _p,
  //我的收藏排序
  saveMyFavoriteAppsSortUrl: "/sopplus/mobile/saveMyFavoriteAppsSort.rst?_p=" + _p,
  //统计应用访问信息
  openModule:
    "/sopplus/mobile/openModule.do?_p=" +
    _p +
    "&timeStamp=" +
    new Date().getTime(),

  /**
   * 日程接口 
   * */  
  //日程分类接口
  getCalendarCategoryUrl: "/calendar/mgr/api/category/list.rst?_p=" + _p,
  //日程接口
  getCalendarListUrl: "/calendar/mgr/api/just/calendarList.rst?_p=YXQ9MSZwPTEmbT1OJg__", //"/calendar/mgr/api/mycc/calendarList.rst",
  //删除日程
  getDelCalendarUrl: "/calendar/mgr/api/calendar.rst?_p=YXQ9MSZwPTEmbT1OJg__", //删除某个日程接口,

  /**
   * 检索中心
   * */  
  //搜索页-获取热词接口
  getHotWords: "/sopplus/_web/_plugs/elsearch/api/searchItem/rate/hotWords.rst",
  //获取热词接口-新
  getHotWordsNew: "/sopplus/_web/customized/es/rateHotWords.jsp?_p=" + _p,
  //搜索咨询页-搜索
  getSearchUrl: "/sopplus/_web/_plugs/elsearch/api/custom/search.rst",


  /**
   * 资讯订阅
   *  */  
  querySubsUrl: "/sopplus/mobile/querySubs.mo",//获取订阅栏目
  getAllArticleList: '/sopplus/mobile/getAllArticleList.rst?_p='+_p,//搜索条件
  querySub: '/sopplus/mobile/querySubs.mo',//获取栏目我的栏目
  saveUserSub: '/sopplus/mobile/saveUserSub.mo',//保存栏目
  queryAllSiteAndColumn: '/sopplus/mobile/queryAllSiteAndColumn.mo',//未订阅栏目 

  /**
   * 邮箱
   */
  //获取未读邮箱数量
  getUnReadCountUrl:
    "/sopplus/_web/customized/email/loadUnreadCount.jsp?_p=" + _p,
  //邮箱单点登录地址
  getMailUrl: "/sopplus/_web/customized/email/jumpToEmail.jsp?_p=" + _p,
  //邮箱绑定
  boundMail:"/sopplus/_web/customized/email/boundMail.jsp?_p=" + _p,
  //邮箱发送验证码
  sendEmail:"/sopplus/_web/customized/email/sendEmail.jsp?_p=" + _p,

   //获取校内通知
  getXntxList:"/sopplus/_web/customized/getXntxList.jsp?_p=" + _p,
  


  //待办
  getToDoUrl:
    "/ywtbapi/_web/_apps/taskcenter/query/api/v3/taskcenter/todotask?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" +
    "&isCount=true",
  //已办
  getDoneUrl:
    "/ywtbapi/_web/_apps/taskcenter/query/api/v3/taskcenter/donetask?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" +
    "&isCount=true",
  //流程追踪
  getTraceUrl:
    "/ywtbapi/_web/_apps/taskcenter/query/api/v3/taskcenter/processTrack?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" +
    "&isCount=true",
  //办结
  getFinishedUrl:
    "/ywtbapi/_web/_apps/taskcenter/query/api/v3/taskcenter/processDone?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" +
    "&isCount=true",
  //代发
  getDraftTaskUrl:"/sopplus/_web/customized/draftTask.jsp?_p=YXM9MiZ0PTEmcD0xJm09TiY_",
  //草稿
  getDraftUrl:
    "/default/base/draft/queryDraftsByTitleAndCreatetime.jsp?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m", 
  //获取事务分类
  getTaskCategoryUrl:
    "/sopplus/_web/_apps/taskcenter/query/api/v3/taskCategory.rst",

    
  //消息中心接口
  fetchReceiveBoxJsonpUrl:
    "/ucp/_web/onlinenews/receiveBox/api/fetchReceiveBoxJsonp.rst?_p=YXM9MSZwPTEmbT1OJg__",
  fetchReceiveBoxDetailJsonpUrl:
    "/ucp/_web/onlinenews/receiveBox/api/findMessageDetailJsonp.rst?_p=YXM9MiZ0PTUmZD0xMzYmcD0xJmY9NTAmbT1OJg__",
  //获取消息
  getUnReadMsg: "/ucp/_web/onlinenews/receiveBox/api/fetchUnreadReceiveBoxBychannelJsonp.rst?_p=" + _p,

  //获取应用详情
  getServiceAppDetailUrl: "/sopplus/mobile/getServiceAppDetail.do?_p=" + _p,

  checkidentity: "/sopplus/mobile/wechatAuth?ret=",
};

//打开目标页面
function DDOpen(tabAppUrl) {
  if (tabAppUrl) {
    dd.biz.util.openLink({
      url: tabAppUrl,
      onSuccess: function (result) {
        //msg(result);
      },
      onFail: function (err) {
        //msg(err);
        console.log(err);
      },
    });
  }
}

// 获取浏览器的版本
function myBrowser() {
  var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
  var isOpera = userAgent.indexOf("Opera") > -1;
  if (isOpera) {
    return "Opera";
  } else if (userAgent.indexOf("Firefox") > -1) {
    return "Firefox";
  } else if (userAgent.indexOf("Chrome") > -1) {
    return "Chrome";
  } else if (userAgent.indexOf("Safari") > -1) {
    return "Safari";
  } else if (
    userAgent.indexOf("compatible") > -1 &&
    userAgent.indexOf("MSIE") > -1 &&
    !isOpera
  ) {
    return "IE";
  } else {
    return "Other";
  }
}

//签名认证
function checkIdentity(paramUrl) {
  var url = "/sopplus/mobile/wechatAuth?ret=" + encodeURIComponent(paramUrl);
  var params = {
    isReturn: "false",
  };
  var returnUrl = "";
  $.ajax({
    url: url,
    type: "get",
    data: params,
    dataType: "json",
    async: false,
    timeout: 10000,
    success: function (data) {
      var u = data.data;
      returnUrl = u;
    },
    error: function (err) {
      $.poptips("接口暂时无法调用，请稍候访问！");
    },
  });
  return returnUrl;
}

//.svg 图片转 svg
async function imgToSvg(svgUrl) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url: svgUrl,
      dataType: "text",
      type: "get",
      crossDomain: true,
      success: function (res) {
        var reg = /<title[^>]*>(.|\n)*<\/title>/gi;
        var newRes = res.replace(reg, "");
        resolve(newRes);
        console.log(res);
      },
      error: function (err) {
        reject("");
      },
    });
  }).catch((err) => {
    console.log(err);
  });
}

//公共跳转方法
function commonToUrl(url, type = "_self") {
  if (url) {
    //判断是否钉钉环境
    var userAgent = navigator.userAgent; // 获取User Agent信息
    if (userAgent.indexOf("DingTalk") > -1) {
      //钉钉环境-钉钉方式跳转
      DDOpen(url);
    } else {
      // return false; // 其他情况返回false
      window.open(url, type);
    }
  }
}

//待办拼接特殊字段
function addParameterToUrl(url) {
  const hashIndex = url.indexOf('#');
  const hashPart = hashIndex !== -1 ? url.slice(hashIndex) : '';
  const baseUrl = hashIndex !== -1 ? url.slice(0, hashIndex) : url;

  if (baseUrl.includes('?')) {
      return baseUrl + '&dd_nav_bgcolor=FF007EFF' + hashPart;
  } else {
      return baseUrl + '?dd_nav_bgcolor=FF007EFF' + hashPart;
  }
}
function checkUrlStart(url) {
  return url.startsWith("https://http://my.just.edu.cn/app");
}

//图标颜色
function iconColor(index){
  var _color = ["#F7A842", "#59A0FF", "#ED752F", "#4A70F0", "#4BC3A7", "#E83C50", "#be1529", "#578cd4", "#F7A842", "#F7A842", "#59A0FF", "#ED752F", "#4A70F0", "#4BC3A7", "#E83C50", "#be1529", "#578cd4", "#F7A842"]
  return _color?.[index]
}