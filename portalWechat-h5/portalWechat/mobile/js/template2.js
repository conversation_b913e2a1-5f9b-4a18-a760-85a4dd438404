/**
 * Created by PCX on 2016/9/13.
 */
/*******************************事务中心 start**********************************/
var commonUrl = "/sopplus";
var tem_center = '<%# for (var i = 0; i < d.data.length; i++) { %>' +
    '<div onclick="detail(\'<% d.data[i].appName %>\',\'<% d.data[i].onlyEntry %>\',\'<% d.data[i].id %>\',\'<% d.data[i].entranceUrl %>\')" appname="<% d.data[i].appName %>" onlyEntry="<% d.data[i].onlyEntry %>" entranceUrl="<% d.data[i].entranceUrl %>" appVersionId="<% d.data[i].id %>" class=" app_datil ui-border-b">' +
        '<div><div class="ui-list-thumb">' +
        	'<span style="background-image:url(<%d.data[i].iconUrl%>)"></span>' +
        '</div>' +
        '<div class="ui-mylist-cont1">' +
            '<h4 class="ui-nowrap"><% d.data[i].name %></h4>' +
            '<p class="ui-mylist-colorgray"><% d.data[i].appType %></p>' +
            '<%# if(d.data[i].isfavorite){%>'+
            	'<p class="ui-mylist-sc2 ui-mylist-colorgray">'+
		    '<%# }else{ %>'+
		    	'<p class="ui-mylist-sc ui-mylist-colorgray">'+
		    '<%# }%>'+
            '<img /><%d.data[i].favoritesCount%>人收藏</p>' +
        '</div>' +
        '<div class="ui-mylist-cont2 ui-star">'+
            '<div><%# for(var j=0;j<d.data[i].remark;j++){%>'+
        		'<img src="img/star.png" />'+
        	'<%#}%>'+
        		'<%# for(var k=0;k<(5-d.data[i].remark);k++){%>'+
        			'<img src="img/star2.png" />'+
        		'<%#}%>'+
            '</div>'+
        '</div>'+
    '</div></div>'+
    '<%# } %>';

var tem_center_option = '<%# for (var i = 0; i < d.data.serviceCategory.length; i++) { %>' +
		'<option value="<% d.data.serviceCategory[i].id %>"><% d.data.serviceCategory[i].typeName %></option>'+
		'<%# } %>';

var faq = '<%# for (var i = 0; i < d.data.consultList.length; i++) { %>' +
		'<li style="padding: 0px 15px;" class="ui-border-t">' +
		'<div class="ui-nowrap ui-mylist-font16 ui-mylist-title" style="height: 50px;line-height: 50px;position: relative"><%d.data.consultList[i].content%>' +
		'</div>' +
		'<div style="line-height: 30px;" class="ui-mylist-hide ui-mylist-colorgray ui-mylist-font15"> 答：<%d.data.consultList[i].teacherReply%></div>' +
		'</li>' +
		'<%# } %>';

var app_detail = '<div class="ui-border-b" style="padding: 20px 10px"><div><h4><%d.data.appName%></h4></div>'+
				'<div class="ui-mylist-flow ui-mylist-flex" style="justify-content: center;align-items: center;display: block;">'+
				'<%d.data.mobileDescriptionDetail%>'+
				'</div>'+
				'</div>'+
				'<div class="ui-border-b ui-mylist-padding" id="appInfo">'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">申请人员范围：</span><%d.data.applyUserScope%></p>'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">负责单位：</span><%d.data.responsibleDept%></p>'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">联系人：</span><%d.data.contactUser%></p>'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">联系电话：</span><%d.data.contactPhone%></p>'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">办理时间：</span><%d.data.handleTime%></p>'+
					'<p><span class="ui-mylist-colorgray ui-mylist-font16">办理地点：</span><%d.data.handleAddress%></p>'+
				'</div>'
				//+
				//'<div class="ui-border-b ui-mylist-flex" style="">'+
				//	'<div class="ui-mylist-subflex" id="sc">'+
				//		'<%# if(d.data.isfavorite){%>'+
				//	    '<p class="ui-mylist-sc-g ui-mylist-sc-y"></p>'+
				//	    '<%# }else{ %>'+
				//	    '<p class="ui-mylist-sc-g"></p>'+
				//	    '<%# }%>'+
				//	    '<p class="ui-mylist-colorgray ui-mylist-font14">收藏</p>'+
				//	'</div>'+
				//	'<div class="ui-mylist-subflex" id="comment">'+
				//	    '<p class="ui-mylist-pl"></p>'+
				//	    '<p class="ui-mylist-colorgray ui-mylist-font14">评论</p>'+
				//	'</div>'+
				//	'<div class="ui-mylist-subflex" id="consult">'+
				//	    '<p class="ui-mylist-zx"></p>'+
				//	    '<p class="ui-mylist-colorgray ui-mylist-font14">我要咨询</p>'+
				//	'</div>'+
				//'</div>'
				;
/*******************************事务中心 end**********************************/

/*******************************数据统计 start**********************************/
var data_list = '<%# for (var i = 0; i < d.list.length; i++) { %>' +
    '<li onclick="ondetail(\'<% d.list[i].appName %>\',\'<% d.list[i].onlyEntry %>\',\'<% d.list[i].entranceUrl %>\',<% d.list[i].id %>)" style="margin: 0px 10px;" class="ui-border-b app_datil">' +
    '<div class="ui-mylist-flex ui-border-b">' +
'<div class="ui-mylist-subflex" style="line-height: 80px;height: 80px;"><p class="ui-mylist-colorgray ui-mylist-font20"><%((d.page-1)*10)+i+1 %></p></div>' +
'<div class="ui-mylist-subflex" style="-webkit-box-flex: 4;">' +
'<div class="ui-mylist-flex">' +
'<div style="padding: 5px;width: 80px;height: 80px;">' +
'<img src="<%d.list[i].iconUrl%>" style="width: 100%;height: 100%">' +
'</div>' +
'<div style="-webkit-box-flex:3;padding: 5px;line-height: 30px;text-align: left;">' +
'<h4><%d.list[i].name%></h4>' +
'<p class="ui-mylist-colorgray"><%d.list[i].appType%></p>' +
'<div>' +
'<%# for(var j=0;j<d.list[i].remark;j++){%>'+
	'<div class="ui-mylist-star"></div>' +
'<%#}%>'+
'<%# for(var k=0;k<(5-d.list[i].remark);k++){%>'+
	'<div class="ui-mylist-star2"></div>' +
'<%#}%>'+
'</div>' +
'</div>' +
'</div>' +
'<div class="ui-mylist-flex">' +
'<div class="ui-mylist-subflex"><p class="ui-mylist-font18"><%d.list[i].useCount%></p><p class="ui-mylist-colorgray ui-mylist-font14">使用次数</p></div>' +
'<div class="ui-mylist-subflex"><p class="ui-mylist-font18"><%d.list[i].completedTime%></p><p class="ui-mylist-colorgray ui-mylist-font14">平均办结时间</p></div>' +
'<div class="ui-mylist-subflex"><p class="ui-mylist-font18"><%d.list[i].favoritesCount%></p><p class="ui-mylist-colorgray ui-mylist-font14">收藏总量</p></div>' +
'</div>' +
'</div>' +
'</div>' +
    '</li>'+
    '<%# } %>';

var data_list_bydate =  '<%# for (var i = 0; i < d.data.length; i++) { %>' +
    '<option>过去一个月</option>'+
    '<%# } %>';

var data_list_bytime =  '<%# for (var i = 0; i < d.data.length; i++) { %>' +
    '<option>1-10次</option>'+
    '<%# } %>';
/*******************************数据统计 end**********************************/

/*******************************事物收藏 start**********************************/
var collect_list_often = '<%# for (var i = 0; i < d.data.length; i++) { %>' +
    '<li class="app_li" appname="<% d.data[i].appName %>" onlyEntry="<% d.data[i].onlyEntry %>" entranceUrl="<% d.data[i].entranceUrl %>" appVersionId="<% d.data[i].id %>" style="float: left;width: 33.33333%;text-align: center;">'+
        '<div><img src="<%d.data[i].iconUrl%>" style="width: 55%;"><p style="padding: 8px 0px;color: #6A6A6A;">'+
        '<%# if(d.data[i].name.trim().length>6){ %>'+
        	'<% d.data[i].name.trim().substring(0,5) %>...</p></div>'+
        '<%# }else{ %>'+
        	'<% d.data[i].name.trim() %></p></div>'+
        '<%# }%>'+
        
    '</li>'+
    '<%# } %>' +
    '<li class="" style="float: left;width: 33.33333%;text-align: center;padding: 0px;">'+
    '<div class="addApp"><img src="img/add-trans.png" style="width: 55%;"><p style="padding: 8px 0px;color: #6A6A6A;">添加</p></div>'+
    '</li>';
var collect_list_recommend =  '<%# for (var i = 0; i < d.data.length; i++) { %>' +
    '<li class="app_li" appname="<% d.data[i].appName %>" onlyEntry="<% d.data[i].onlyEntry %>" entranceUrl="<% d.data[i].entranceUrl %>" appVersionId="<% d.data[i].id %>" class="app_li" style="float: left;width: 33.33333%;text-align: center;padding:0px;">'+
    '<div><img src="<%d.data[i].iconUrl%>" style="width: 55%;"><p style="padding: 8px 0px;color: #6A6A6A;">'+
    '<%# if(d.data[i].name.trim().length>6){ %>'+
		'<% d.data[i].name.trim().substring(0,5) %>...</p></div>'+
	'<%# }else{ %>'+
		'<% d.data[i].name.trim() %></p></div>'+
	'<%# }%>'+
    '</li>'+
    '<%# } %>';
    //'<li class="" style="float: left;width: 33.33333%;text-align: center;padding: 10px 0px;">'+
    //'<div class="addApp"><img src="img/add-trans.png" style="width: 55%;"><p style="padding: 8px 0px;color: #6A6A6A;">添加</p></div>'+
    //'</li>';

// 添加应用

var collect_list_add = '<%#var ishave="0";%>'+
						'<%# for (var i = 0; i < d.data.list.length; i++) { %>' +
                            '<%# if(d.data.list[i].appList.length > 0){%>'+
                                '<div class="ui-border-t ui-mycollect-addframe">'+
                                    '<div class="ui-border-b ui-mylist-font18" style="height: 45px;line-height: 45px;padding: 0px 15px;"><%d.data.list[i].categoryName%> &nbsp;<span>（<%d.data.list[i].appList.length%>）</span></div>'+
                                    '<div class="ui-border-b" style="padding: 10px 0px;">'+
                                    '<ul class="" style="width: 100%;height: auto;">'+
                                    '<%# for (var j = 0; j < d.data.list[i].appList.length; j++) { ishave="1"%>' +
                                        '<div class="ui-mylist-block">'+
                                            '<p class="ui-icon-p" data-id="<% d.data.list[i].appList[j].microAppId %>">'+
                                                '<img src="<%d.data.list[i].appList[j].iconUrl%>">'+
                                            '</p>'+
                                            '<p class="ui-last-p ui-mylist-colorblackgray">'+
                                            '<%# if(d.data.list[i].appList[j].name.trim().length>6){ %>'+
                                                '<% d.data.list[i].appList[j].name.trim().substring(0,5) %>...</p>'+
                                            '<%# }else{ %>'+
                                                '<% d.data.list[i].appList[j].name.trim() %></p>'+
                                            '<%# }%>'+
                                        '</div>'+
                                    '<%# } %>'+
                                    '</ul>'+
                                    '<div style="clear: both"></div>'+
                                '</div>'+
                            '</div>'+
                        '<%# } %>'+
						'<%# } %>'+
						'<%# if(ishave=="0"){%>'+
						 '<div style="text-align:center;margin-top:300px">你已收藏全部应用</div>'+
						 '<%# } %>';


/*******************************事物收藏 end**********************************/

/*******************************办理中心 start**********************************/



var manage_list_backlog = '<%# for (var i = 0; i < d.list.length; i++) { %>' +
'<div>'+
    '<div class="ui-managecenter-inner-padding ui-border-t">'+
        '<h4 style="padding: 0px 0px 12px 0px;"><%d.list[i].processInstName%></h4>'+
        '<div class="ui-mylist-colorgray ui-mylist-inlineblock ui-mylist-font14" style="width:48%;line-height: 25px;">'+
            '<div class="ui-mylist-ellipsis">来源：<span class="ui-nowrap ui-whitespace"><%d.list[i].processDefName%></span></div>'+
            '<div class="ui-mylist-ellipsis">发送人：<span><%d.list[i].sender%></span></div>'+
        '</div>'+
        '<div class="ui-mylist-colorgray ui-mylist-inlineblock ui-mylist-font14" style="width:48%;line-height: 25px;">'+
            '<div class="ui-mylist-ellipsis">流量已用时：<span><%d.list[i].usedTime%></span></div>'+
            '<div class="ui-mylist-ellipsis">承办时间：<span><%d.list[i].handleTime%></span></div>'+
        '</div>'+
    '</div>'+
    '<ul class="ui-list ui-list-text ui-list-link ui-border-tb backlog-list">'+
      '<li class="ui-border-t" data-id="<%d.list[i].processInstId%>" onclick="javascript:window.location.href=\'http://eos.luas.edu.cn/default/base/workflow/do.jsp?workItemID=<%d.list[i].workItemId%>&needBack=1&appload=0&f=wechat&layerId=2\';" >'+
         '<h4 style="color: #b13b28;border-right:1px solid #e0e0e0;">我要办理</h4>'+
      '</li>'+
      '<li style="border:0px;" class="ui-border-t" onclick="javascript:window.location.href=\'processDetail.html?id=<%d.list[i].processInstId%>\';">'+
        '<h4 style="color: #b13b28">流转历史</h4>'+
      '</li>'+
    '</ul>'+
'</div>'+
'<%# } %>';



var transTime = function(times){
	var date = new Date(times);
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var day = date.getDate();
	var Hour = date.getHours();
	var min = date.getMinutes();
	var second = date.getSeconds();
	return year + "-" + month + "-" + day;
	//+" "+Hour+":"+min+":"+second;
}

var manage_list_hasdone = '<%# for (var i = 0; i < d.list.length; i++) { %>' +
'<div>'+
'<div class="ui-managecenter-inner-padding ui-border-t">'+
'<h4 style="padding: 0px 0px 12px 0px;"><%d.list[i].processInstName%></h4>'+
'<div class="ui-mylist-colorgray ui-mylist-inlineblock ui-mylist-font14" style="width:48%;line-height: 25px;">'+
    '<div class="ui-mylist-ellipsis">来源：<span class="ui-nowrap ui-whitespace"><%d.list[i].processDefName%></span></div>'+
    '<div class="ui-mylist-ellipsis">发送人：<span><span><%d.list[i].creator%></span></div>'+
'</div>'+
'<div class="ui-mylist-colorgray ui-mylist-inlineblock ui-mylist-font14" style="width:48%;line-height: 25px;">'+
    '<div class="ui-mylist-ellipsis">流量已用时：<span><%d.list[i].usedTime%></span></div>'+
    '<div class="ui-mylist-ellipsis">承办时间：<span><%transTime(Number(d.list[i].createTime))%></span></div>'+
'</div>'+
'</div>'+
'<ul class="ui-list ui-list-text ui-list-link ui-border-tb hasdone-list" >'+
'<li class="ui-border-t" data-id="<%d.list[i].processInstId%>" onclick="javascript:window.location.href=\'http://eos.luas.edu.cn/default/base/workflow/preview.jsp?processInstId=<%d.list[i].processInstId%>&needBack=1&appload=0&f=wechat&layerId=2&currworkItemID=<%d.list[i].workItemId%>\';" >'+
  '<h4 style="color: #b13b28;border-right:1px solid #e0e0e0;">查看详情</h4>'+
'</li>'+
'<li style="border:0px;" class="ui-border-t" onclick="javascript:window.location.href=\'processDetail.html?id=<%d.list[i].processInstId%>\';">'+
 '<h4 style="color:#b13b28">流转历史</h4>'+
'</li>'+
'</ul>'+
 '</div>'+
'<%# } %>';




var manage_list_flowtracing = '<%# for (var i = 0; i < d.list.length; i++) { %>' +
'<div class="processdetail" data-id="<%d.list[i].PROCESSINSTID%>">'+
'<div class="ui-border-t" style="padding: 15px;margin-top: 15px;">'+
'<div><h4 style="float: left;padding: 0px 0px 12px 0px;"><%d.list[i].PROCESSINSTNAME%></h4><!--<span style="float: right;color:#E7A627;">候保处审核</span>--></div>'+
'<div style="clear: both"></div>'+
'<div class="ui-mylist-colorgray ui-mylist-font14" style="display: inline-block;;line-height: 25px;">'+
    '<div class="ui-mylist-ellipsis">来源：<span class="ui-nowrap ui-whitespace"><%d.list[i].PROCESSDEFNAME%></span></div>'+
    '<div class="ui-mylist-ellipsis">创建时间：<span><%d.list[i].CREATETIME%></span></div>'+
'</div>'+
'</div>'+
'<ul class="ui-list ui-list-text ui-list-link ui-border-tb flowtracing-list">'+
  '<li class="ui-border-t" data-id="<%d.list[i].PROCESSINSTID%>" onclick="javascript:window.location.href=\'http://eos.luas.edu.cn/default/base/workflow/preview.jsp?processInstId=<%d.list[i].PROCESSINSTID%>&needBack=1&appload=0&f=wechat&layerId=2\';" >'+
    '<h4 style="color: #b13b28;border-right:1px solid #e0e0e0;">查看详情</h4>'+
  '</li>'+
  '<li style="border:0px;" class="ui-border-t" onclick="javascript:window.location.href=\'processDetail.html?id=<%d.list[i].PROCESSINSTID%>\';">'+
   '<h4 style="color: #b13b28">流转历史</h4>'+
  '</li>'+
'</ul>'+
'</div>'+
'<%# } %>';




var manage_list_makingprocess = '<%# for (var i = 0; i < d.list.length; i++) { %>' +
'<div class="processdetail" data-id="<%d.list[i].PROCESSINSTID%>">'+
'<div class="ui-border-t" style="padding: 15px;margin-top: 15px;">'+
'<div><h4 style="float: left;padding: 0px 0px 12px 0px;"><%d.list[i].PROCESSINSTNAME%></h4></div>'+
'<div style="clear: both"></div>'+
    '<div class="ui-mylist-colorgray ui-mylist-font14" style="display: inline-block;;line-height: 25px;">'+
        '<div class="ui-mylist-ellipsis">来源：<span class="ui-nowrap ui-whitespace"><%d.list[i].PROCESSDEFNAME%></span></div>'+
        '<div class="ui-mylist-ellipsis">创建时间：<span><%d.list[i].CREATETIME%></span></div>'+
    '</div>'+
'</div>'+
    '<ul class="ui-list ui-list-text ui-list-link ui-border-tb makingprocess-list">'+
       '<li class="ui-border-t" data-id="<%d.list[i].PROCESSINSTID%>" onclick="javascript:window.location.href=\'http://eos.luas.edu.cn/default/base/workflow/preview.jsp?processInstId=<%d.list[i].PROCESSINSTID%>&needBack=1&appload=0&f=wechat&layerId=2\';" >'+
          '<h4 style="color: #b13b28;border-right:1px solid #e0e0e0;">查看详情</h4>'+
       '</li>'+
       '<li style="border:0px;" class="ui-border-t" onclick="javascript:window.location.href=\'processDetail.html?id=<%d.list[i].PROCESSINSTID%>\';">'+
          '<h4 style="color: #b13b28">流转历史</h4>'+
       '</li>'+
    '</ul>'+
'</div>'+
'<%# } %>';


var processdetail = '<%# for (var i = 0; i < d.curr.steps.length; i++) { %>' +
    '<div class="ui-mylist-font14" style="position: relative;border-left: 1px solid #D4D4D4;">'+
'<div style="position: absolute;top: -10px;left: -10px;padding-left: 35px;;background: url(img/point.png) no-repeat left center;background-size: 20px 20px;"><% d.curr.steps[i].date %></div>'+
'<div style="border-radius: 5px;padding: 25px;height: 100%">'+
'<div class="ui-mylist-flex change" style="color: #fff;background-color: #b13b28;border-top-right-radius: 6px;border-top-left-radius: 6px;">'+
'<div class="ui-mylist-subflex ui-mylist-font17" style="text-align: left;padding: 10px 15px;line-height: 22px;"><p style="font-size: larger"><% d.curr.steps[i].activityName %></p>'+
'<p style="background: url(img/xq.png) no-repeat left center; background-size: 15px;padding: 0px 20px;"><% d.curr.steps[i].handleTime %></p></div>'+
'<div class="ui-mylist-subflex" style="position: relative;text-align: right;padding: 10px 15px;"><p class="ui-icon-mylist-arrow"></p></div>'+
'</div>'+
'<div class="list-content"><div class="ui-process-content">'+
'<p class="ui-border-b"><span class="ui-mylist-colorgray ui-mylist-font15">办理节点名称：</span><% d.curr.steps[i].activityName %></p>'+

'<p class="ui-border-b"><span class="ui-mylist-colorgray ui-mylist-font15">办理人：</span>'+
'<% queryUser(d.curr.steps[i].userName.hasPosition) %>'+
//'<%# for( key in d.curr.steps[i].userName.hasPosition){%>'+
//	'<% key %>('+
//	'<%# for(var j=0;j<d.curr.steps[i].userName.hasPosition[key].length; j++){ %>'+
//		'<% d.curr.steps[i].userName.hasPosition[key][j].empName %>'+
//	'<%# } %>'+
//	')'+
//'<%# } %>'+



'<%# for(var k=0;k<d.curr.steps[i].userName.withoutPosition.length; k++){ %>'+
'<% d.curr.steps[i].userName.withoutPosition[k].empName %>'+
'<%# } %>'+

'</p>'+

'<p class="ui-border-b"><span class="ui-mylist-colorgray ui-mylist-font15">办理时间：</span><% d.curr.steps[i].date %></p>'+
'<p><span class="ui-mylist-colorgray ui-mylist-font16">处理意见：</span><% queryPosition(d.curr.steps[i].opinion) %></p>'+
'</div></div>'+
'</div>'+
'</div>'+
'<%# } %>';

/*******************************办理中心 end**********************************/
