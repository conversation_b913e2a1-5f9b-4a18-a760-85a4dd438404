// var type = U.getSearch('type')
var _userLb = localStorage.getItem('userLb')

if (_userLb === 'ls') {
  $('#createMailDomain').val('@just.edu.cn')
} else {
  $('#createMailDomain').val('@stu.just.edu.cn')
}

let countdown = 120;
let timer;

$(document).on("click", ".createMailDialog-sender", function () {
    const $sender = $('.createMailDialog-sender');
    // 防止重复点击
    if ($sender.hasClass('disabled')) {
      return;
    }
    // 设置禁用状态并开始倒计时
    $sender.addClass('disabled').text(`重新发送(${countdown})s`);
    timer = setInterval(() => {
      countdown--;

      if (countdown > 0) {
        $sender.text(`重新发送(${countdown})`);
      } else {
        clearInterval(timer);
        $sender.removeClass('disabled').text('发送验证码');
        countdown = 120;
      }
    }, 1000);

    var title = $('#createMailName').val()
    var location = $('#createMailDomain').val()
    $.ajax({
      url: index_urls.sendEmail,
      contentType: 'application/json',
      data: {
        userMailAddress: title + location,
      },
      type: 'get',
      success: function (res) {
        if (res.result === '1') {
          $.poptips({ content: '验证码发送成功，请查看邮件' })
        }else{
          $.poptips({ content: res.reason || '发送验证码出错' })
        }
      }
    })
 })

function save() {
  var title = $('#createMailName').val()
  var location = $('#createMailDomain').val()
  var remark = $('#contentInput').val() || ''

  if ($.trim(title).length < 1) {
    $.poptips({ content: '请填写邮箱地址' })
    return false
  }
  if ($.trim(remark).length < 1) {
    $.poptips({ content: '请填写验证码' })
    return false
  }

  $.ajax({
    url: index_urls.boundMail,
    contentType: 'application/json',
    data: {
      userMailAddress: title + location,
      identifyingCode: remark
    },
    type: 'get',
    success: function (res) {
      if (res.result === '1') {
         $.poptips({ content: '绑定成功，请联系管理员！' })
      }else{
         $.poptips({ content: res.reason || '绑定失败，请联系管理员！' })
      }
      setTimeout(() => {
        if (res.result === '1') {
          window.parent.postMessage('reloadEmail', '*')
        }
        // window.parent.dia.iframe[0].close();
      }, 1000)
    }
  })
}

window.onload = function () {
  var iframeHeight = document.body.scrollHeight
  window.parent.postMessage(iframeHeight, '*')
}
