"use strict";

$(function () {
  //默认获取主题下分类
  var urlCategoryId = getURLParameter('categoryid');

  if (urlCategoryId === 'all') {
    loadZtAppList(38, appName = '');
  } else {
    $(".parentCategoryTab>.parentItemTab:nth-child(2)").addClass('active').siblings().removeClass('active');
    var urlCategoryId = getURLParameter('categoryid');
    getZtCategory(urlCategoryId);
  }
}); //默认获取主题下分类

function getZtCategory(urlCategoryId) {
  $(".serviceContent .serviceMod .parentItemBox .leftCategoryBox").show();
  $(".serviceContent .serviceMod .parentItemBox .rightCategoryInfo").css({
    "width": "calc(100% - 100px)",
    "margin-left": "-12px"
  });
  $.ajax({
    url: index_urls.getFwdtUrl,
    dataType: 'jsonp',
    data: {
      clientType: 4,
      parentCategoryId: 38,
      showCategoryType: 1,
      showType: 1
    },
    success: function success(res) {
      var ztCategory = res.data ? res.data : []; //测试用
      // let ztCategory = ztCategory_.flatMap(value => Array(7).fill(value));
      //-----jieshu

      var ztCategoryStr = '';

      for (var i = 1; i < ztCategory.length; i++) {
        var isActive = ''; //地址栏带参数

        if (urlCategoryId) {
          if (parseInt(urlCategoryId) === parseInt(ztCategory[i].id)) {
            isActive = 'active';
            loadZtAppList(ztCategory[i].id);
          }
        } else {
          //地址栏不带参数-默认选中第一项
          if (i === 1) {
            isActive = 'active';
            loadZtAppList(ztCategory[i].id);
          }
        }

        ztCategoryStr += "\n                          <div class=\"leftCategory ".concat(isActive, "\" data-categoryid=\"").concat(ztCategory[i].id, "\"><span>").concat(ztCategory[i].typeName, "</span></div>\n                    ");
      }

      $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').html(ztCategoryStr); //判断是否已有默认选中项-没有则默认第一个-防止地址栏带参数但分类列表里没有

      if (ztCategory.length > 0 && urlCategoryId && $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').length === 0) {
        $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory').eq(0).addClass('active');
        var this_id = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory').eq(0).attr('data-categoryid');
        loadZtAppList(this_id);
      } //没有分类-右侧列表数据直接暂无数据


      if (ztCategory.length === 0) {
        var rightAppStr = "\n                    <div class=\"nodataBox\">\n                    <div class=\"noDataImg\"><svg class=\"MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 64 41\"><g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\"><ellipse cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" style=\"fill: rgb(245, 245, 245);\"></ellipse><g fill-rule=\"nonzero\" style=\"stroke: rgb(217, 217, 217);\"><path d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"></path><path d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\" style=\"fill: rgb(250, 250, 250);\"></path></g></g></svg></div>\n                    <div class=\"nodata\">\u6682\u65E0\u6570\u636E</div>\n                    </div>\n                    ";
        $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr);
      } else {
        //有应用分类
        //当前选中项的offset().top值
        var this_active_top = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').offset().top - 231;
        var leftCategoryHeight = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').height();
        var this_distance = this_active_top - leftCategoryHeight;

        if (this_distance > 0) {
          //滚动到当前选中项-使其显示出来
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').animate({
            scrollTop: this_distance
          }, 'slow');
        } //判断左边分类高度是否小于右边-即整体盒子高度


        var allCategoryHeight = ztCategory.length * 54.5;

        if (allCategoryHeight < leftCategoryHeight) {
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child').addClass('resetAfter');
        } else {
          $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child').removeClass('resetAfter');
        }
      }
    },
    error: function error(err) {
      var rightAppStr = "\n                  <div class=\"nodataBox\">\n                  <div class=\"noDataImg\"><svg class=\"MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 64 41\"><g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\"><ellipse cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" style=\"fill: rgb(245, 245, 245);\"></ellipse><g fill-rule=\"nonzero\" style=\"stroke: rgb(217, 217, 217);\"><path d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"></path><path d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\" style=\"fill: rgb(250, 250, 250);\"></path></g></g></svg></div>\n                  <div class=\"nodata\">\u6682\u65E0\u6570\u636E</div>\n                  </div>\n                  ";
      $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr);
    }
  });
} //获取部门下分类


function getBmCategory() {
  $.ajax({
    url: index_urls.getOrgListUrl,
    dataType: 'jsonp',
    data: {
      clientType: 4,
      parentCategoryId: 38
    },
    success: function success(res) {
      var bmCategory = res.data ? res.data : [];
      var bmCategoryStr = '';

      for (var i = 0; i < bmCategory.length; i++) {
        var isActive = '';

        if (i === 0) {
          isActive = 'active';
          loadBmAppList(bmCategory[i].orgId);
        }

        bmCategoryStr += "\n                          <div class=\"leftCategory ".concat(isActive, "\" data-categoryid=\"").concat(bmCategory[i].orgId, "\"><span>").concat(bmCategory[i].orgName, "</span></div>\n                    ");
      }

      $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox').html(bmCategoryStr);

      if (bmCategory.length === 0) {
        var rightAppStr = "\n              <div class=\"nodataBox\">\n              <div class=\"noDataImg\"><svg class=\"MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 64 41\"><g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\"><ellipse cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" style=\"fill: rgb(245, 245, 245);\"></ellipse><g fill-rule=\"nonzero\" style=\"stroke: rgb(217, 217, 217);\"><path d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"></path><path d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\" style=\"fill: rgb(250, 250, 250);\"></path></g></g></svg></div>\n              <div class=\"nodata\">\u6682\u65E0\u6570\u636E</div>\n              </div>\n              ";
        $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr);
      }
    },
    error: function error(err) {
      var rightAppStr = "\n          <div class=\"nodataBox\">\n          <div class=\"noDataImg\"><svg class=\"MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 64 41\"><g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\"><ellipse cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" style=\"fill: rgb(245, 245, 245);\"></ellipse><g fill-rule=\"nonzero\" style=\"stroke: rgb(217, 217, 217);\"><path d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"></path><path d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\" style=\"fill: rgb(250, 250, 250);\"></path></g></g></svg></div>\n          <div class=\"nodata\">\u6682\u65E0\u6570\u636E</div>\n          </div>\n          ";
      $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(rightAppStr);
    }
  });
} //加载主题分类下的应用


function loadZtAppList() {
  var categoryId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  var appName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
  categoryId = categoryId ? categoryId : '';
  $.ajax({
    url: index_urls.getAllServiceAppsUrl,
    dataType: 'jsonp',
    data: {
      clientType: 4,
      parentCategoryId: categoryId,
      appName: appName
    },
    success: function success(res) {
      var data = res.data ? res.data : {};
      var appList = data.appList ? data.appList : []; //测试用
      // let appList = appList_.flatMap(value => Array(7).fill(value));

      renderAppLists(appList);
    },
    error: function error(err) {
      renderAppLists([]);
    }
  });
} //加载主题分类下的应用


function loadBmAppList() {
  var categoryId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  var appName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
  categoryId = categoryId ? categoryId : '';
  $.ajax({
    url: index_urls.getAllServiceAppsUrl,
    dataType: 'jsonp',
    data: {
      clientType: 4,
      parentCategoryId: 38,
      orgId: categoryId,
      appName: appName
    },
    success: function success(res) {
      var data = res.data ? res.data : {};
      /**var appList = [];
      if(data.length > 0){
      	appList = data[0].appList?data[0].appList:[];
      }**/

      var appList = data.appList ? data.appList : [];
      renderAppLists(appList);
    },
    error: function error(err) {
      renderAppLists([]);
    }
  });
} //主题部门切换


$(document).on("click", " .serviceContent .serviceMod .parentCategoryTab .parentItemTab ", function (e) {
  e.stopPropagation();
  $(this).addClass('active').siblings().removeClass('active');
  var this_type = $(this).attr('data-type'); //清空搜索栏

  $('.serviceContent .searchBox .inputBox input').val('');

  if (this_type === 'zt') {
    getZtCategory();
  } else if (this_type === 'bm') {
    getBmCategory();
  } else {
    getAll();
  }
}); //获取全部

function getAll() {
  $(".serviceContent .serviceMod .parentItemBox .leftCategoryBox").hide();
  $(".serviceContent .serviceMod .parentItemBox .rightCategoryInfo").css({
    "width": "100%",
    "margin-left": "0px"
  });
  loadZtAppList(38, appName = '');
} //左侧分类切换


$(document).on("click", " .serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory ", function (e) {
  e.stopPropagation(); //输入框 关键字

  var keyword = $('.serviceContent .searchBox .inputBox input').val();
  keyword = $.trim(keyword);

  if (keyword.length === 0 || checkVal(keyword) === 'true') {
    $(this).addClass('active').siblings().removeClass('active'); //当前选中分类

    var categoryId = $(this).attr('data-categoryid');
    var parentCategoryType = $('.serviceContent .serviceMod .parentCategoryTab .active').attr('data-type');

    if (parentCategoryType === 'zt') {
      loadZtAppList(categoryId, keyword);
    } else if (parentCategoryType === 'bm') {
      loadBmAppList(categoryId, keyword);
    }
  }
}); //点击应用跳转

$(document).on("click", " .rightCategoryInfo .appGrid .appItem", function (e) {
  e.stopPropagation();
  var this_enableservice = $(this).data('enableservice');
  var this_reason = $(this).data('reason');
  var this_isallow = $(this).data('isallow');
  var this_appurl = $(this).data('appurl');
  var this_appname = $(this).data('appname');
  var this_id = $(this).data('id');

  if (this_enableservice == 1) {
    //有服务指南页 跳应用详情页
    //  window.open('detail.html?appVersionId='+this_id+'&_t='+new Date().getTime());
    var this_url = commonUrlTwo + '/_web/customized/DTalkPortal/ding/mobile/detail.html?appVersionId=' + this_id + '&_t=' + new Date().getTime(); // console.log(this_url);

    commonToUrl(this_url, '_blank');
  } else {
    if (this_isallow === true || this_isallow === 'true') {
      openModule(this_appname);
      var url = this_appurl; //  if(checkUrlStart(url)){
      //    url=addParameterToUrl(url)
      //  }
      //  window.open(this_appurl);

      commonToUrl(url, '_blank');
    } else {
      this_reason = this_reason ? this_reason : '暂无权限';
      $.poptips(this_reason);
    }
  }
}); //点击搜索

$(document).on("click", " .serviceContent .searchBox .searchBtn", function (e) {
  e.stopPropagation();
  var keyword = $('.serviceContent .searchBox .inputBox input').val();
  keyword = $.trim(keyword);

  if (keyword.length === 0 || checkVal(keyword) === 'true') {
    //正常搜索
    //当前选中tab项
    var this_type = $('.serviceContent .serviceMod .parentCategoryTab .parentItemTab.active').data('type');
    var this_id = $('.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active').attr('data-categoryid');

    if (this_type === 'zt') {
      //搜索主题应用
      loadZtAppList(this_id, keyword);
    } else if (this_type === 'bm') {
      //搜索部门应用
      loadBmAppList(this_id, keyword);
    } else {
      loadZtAppList(38, keyword);
    }
  }
}); //渲染右侧应用数据

function renderAppLists(appList) {
  var appListStr, i, item, this_iconUrl, this_isSvg, this_svgDetail;
  return regeneratorRuntime.async(function renderAppLists$(_context) {
    while (1) {
      switch (_context.prev = _context.next) {
        case 0:
          appListStr = '<div class="appGrid">';
          i = 0;

        case 2:
          if (!(i < appList.length)) {
            _context.next = 15;
            break;
          }

          item = appList[i];
          this_iconUrl = item.iconUrl;
          this_isSvg = false;

          if (!(this_iconUrl.slice(-4) === '.svg')) {
            _context.next = 11;
            break;
          }

          _context.next = 9;
          return regeneratorRuntime.awrap(imgToSvg(this_iconUrl));

        case 9:
          this_svgDetail = _context.sent;
          this_isSvg = true;

        case 11:
          if (this_isSvg === true && this_svgDetail) {
            //svg图标
            appListStr += "\n      <div class=\"appItem\" data-enableservice=\"".concat(item.enableService, "\" data-id=\"").concat(item.id, "\" data-isallow=\"").concat(item.isAllow, "\"\n      data-reason=\"").concat(item.reason, "\" data-appurl=\"").concat(item.entranceUrl, "\" data-appname=\"").concat(item.appName, "\">\n        <div class=\"imgBox\">\n        <div class=\"svgImg\">").concat(this_svgDetail, "</div>\n        </div>\n        <div class=\"name\">").concat(item.name, "</div>\n      </div>\n    ");
          } else {
            //正常图片
            appListStr += "\n      <div class=\"appItem\" data-enableservice=\"".concat(item.enableService, "\" data-id=\"").concat(item.id, "\" data-isallow=\"").concat(item.isAllow, "\"\n      data-reason=\"").concat(item.reason, "\" data-appurl=\"").concat(item.entranceUrl, "\" data-appname=\"").concat(item.appName, "\">\n        <div class=\"imgBox\">\n        <img src=\"").concat(item.iconUrl, "\" alt=\"\">\n        </div>\n        <div class=\"name\">").concat(item.name, "</div>\n      </div>\n    ");
          }

        case 12:
          i++;
          _context.next = 2;
          break;

        case 15:
          if (appList.length < 1) {
            appListStr = "\n    <div class=\"nodataBox\">\n    <div class=\"noDataImg\"><svg class=\"MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 64 41\"><g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\"><ellipse cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" style=\"fill: rgb(245, 245, 245);\"></ellipse><g fill-rule=\"nonzero\" style=\"stroke: rgb(217, 217, 217);\"><path d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"></path><path d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\" style=\"fill: rgb(250, 250, 250);\"></path></g></g></svg></div>\n    <div class=\"nodata\">\u6682\u65E0\u6570\u636E</div>\n    </div>\n    ";
          }

          $('.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList').html(appListStr);

        case 17:
        case "end":
          return _context.stop();
      }
    }
  });
} //应用统计


function openModule(appName) {
  var screenSize = window.screen.width + "*" + window.screen.height;
  var clientVersion = navigator.platform + "-" + myBrowser() + "-" + navigator.appVersion.substring(0, navigator.appVersion.indexOf("(") - 1);
  $.ajax({
    url: index_urls.openModule,
    type: 'get',
    data: {
      appName: appName,
      clientVersion: clientVersion,
      screenSize: screenSize
    },
    dataType: 'json',
    success: function success() {}
  });
} //检查搜索文字


function checkVal(keyWord) {
  keyWord = $.trim(keyWord);

  if (keyWord) {
    var re = /^[\u4E00-\u9FA5A-Za-z0-9]+$/; //只能输入汉字、英文字母或数字

    if (re.test(keyWord)) {
      return 'true';
    } else {
      //提示信息
      $.poptips('请输入汉字、英文字母或数字！');
      return 'false';
    }
  } else {
    //提示信息
    $.poptips('请输入您要查找的内容');
    return 'false';
  }
} //获取地址栏参数


function getURLParameter(name) {
  var url = window.location.search.substring(1); // 获取URL中?后的参数字符串

  var paramsArr = url.split('&'); // 将参数字符串按'&'分割成数组

  for (var i = 0; i < paramsArr.length; i++) {
    var param = paramsArr[i].split('='); // 将每个参数按'='分割成键值对数组

    if (param[0] === name) {
      return param[1]; // 返回指定参数名对应的值
    }
  }

  return null; // 如果没有找到则返回null
}

$("body").on("keyup", ".serviceContent .searchBox .inputBox input", function (e) {
  //筛选框赋值
  if (e.keyCode == 13) {
    //enter键为13
    //触发搜索的点击事件
    $(".serviceContent .searchBox .searchBtn").trigger("click");
  }
});