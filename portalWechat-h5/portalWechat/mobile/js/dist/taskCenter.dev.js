"use strict";

// $(function () {
//获取事务分类列表
var arr = [];
getTaskCategory(); //判断当前地址栏是否有参数

var this_tab = getURLParameter("tab");

if (this_tab !== null) {
  this_tab = parseInt(this_tab);
  $(".taskContent .taskInfo .taskTab .taskTabItem").eq(this_tab).addClass("active").siblings().removeClass("active");
} //加载待办信息


getTask(); //钉钉环境监听

isDDing(); // setInterval(function () {
//   //当前选中tab项
//   var type = $(" .taskContent .taskInfo .taskTab .taskTabItem.active").data(
//     "type"
//   );
//   if (type === "todo") {
//     getTask();
//   }
// }, 60 * 1000); // 每1分钟执行一次
// })

function isDDing() {
  var userAgent = navigator.userAgent;

  if (userAgent.indexOf("DingTalk") > -1) {
    dd.ready(function () {
      // 页面被唤醒的事件监听(webview)
      document.addEventListener("resume", function (e) {
        e.preventDefault();
        getTask();
      }, false);
    });
  }
} //获取事务分类列表


function getTaskCategory() {
  $.ajax({
    url: index_urls.getTaskCategoryUrl,
    data: {},
    crossDomain: true,
    dataType: "jsonp",
    success: function success(res) {
      var result = res.result ? res.result : {};
      var data = result.data ? result.data : [];
      renderTaskCategory(data);
    },
    error: function error(err) {}
  });
} //渲染下拉框


function renderTaskCategory(data) {
  // var renderData = [
  //   {
  //     title: "全部",
  //     id: "all",
  //   },
  // ];
  var html = '<option value="all">全部</option>';

  for (var i = 0; i < data.length; i++) {
    html += '<option value="' + data[i].code + '">' + data[i].name + '</option>';
  }

  $(".taskCategory").html(html);
  $(".layui-btn-container>input").val("全部"); // getTaskSel('all')
  // for (var i = 0; i < data.length; i++) {
  //   renderData.push({
  //     title: data[i].name,
  //     id: data[i].code,
  //   });
  // }
  // layui.use(["dropdown", "util", "layer", "table"], function () {
  //   var dropdown = layui.dropdown,
  //     util = layui.util,
  //     layer = layui.layer,
  //     table = layui.table,
  //     $ = layui.jquery;
  //   //初演示
  //   dropdown.render({
  //     elem: ".taskCategory",
  //     data: renderData,
  //     click: function (obj) {
  //       //   layer.tips('点击了：'+ obj.title, this.elem, {tips: [1, '#5FB878']});
  //       $(".layui-btn-container .layui-btn.taskCategory")
  //         .html(obj.title)
  //         .attr("data-type", obj.id);
  //       //重新加载列表
  //       getTask(obj.id);
  //     },
  //   });
  // });
} //点击下拉框


$(".taskCategory").mobiscroll().select({
  groupLabel: "分类",
  label: "",
  rows: 5,
  group: {
    groupWheel: true,
    header: true,
    clustered: true
  },
  onSelect: function onSelect(valueText, inst) {
    console.log("select", arguments, inst.getValues(true, true));
    $(".layui-btn-container>input").val(valueText); // $(".taskCategory").val(valueText)
    // getTask()
  },
  onShow: function onShow(html, valueText, inst) {
    console.log("show", arguments, inst.getValues(true, true));
  },
  onBeforeShow: function onBeforeShow(inst) {
    console.log("beforeshow", arguments, inst.getValues(true, true));
  },
  onInit: function onInit(inst) {// $(".layui-btn-container>input").val(inst)
    //        inst.setVal(value, true, true);
    // console.log("init",arguments, inst.getValues(true,true));
  }
});
var total = 0; //获取我的事务数据

function getTask() {
  //当前选中tab项
  var type = $(" .taskContent .taskInfo .taskTab .taskTabItem.active").data("type"); //先清空原盒子内数据

  $(".taskContent .taskInfo .taskList").html(""); //当前选中的分类
  // taskCategoryCode = taskCategoryCode
  //   ? taskCategoryCode
  //   : $(".layui-btn-container .layui-btn.taskCategory").data("type");
  // taskCategoryCode = taskCategoryCode === "all" ? "" : taskCategoryCode;

  var value = $(".taskCategory").val();
  var taskCategoryCode = value === "all" ? "" : value; //当前关键字

  var title = $(".taskContent .searchBox input").val();
  var url = "";

  switch (type) {
    case "todo":
      url = index_urls.getToDoUrl;
      break;

    case "done":
      url = index_urls.getDoneUrl;
      break;

    case "trace":
      url = index_urls.getTraceUrl;
      break;

    case "finished":
      url = index_urls.getFinishedUrl;
      break;
  }

  loadinfo(0, url, taskCategoryCode, type);
}

function loadinfo(c, u, taskCategoryCode, type) {
  var _keyword = $(".taskContent .searchBox input").val();

  if (_keyword) {
    _keyword = _keyword.trim();
  }

  if (arr[0]) {
    // $("#news-list-" + c).html(" ");
    arr[0].options.fnAjaxSettings = function (data, url, action) {
      if (c === 4 || c === "4") {
        // 草稿箱
        return {
          url: u,
          // data: {
          //     begin: (data.pageIndex-1)*data.pageRows,
          //     length: data.pageRows,
          //     isCount: true,
          //     title: _keyword
          // },
          data: {
            taskCategoryCode: taskCategoryCode,
            title: _keyword,
            page: data.pageIndex,
            rows: data.pageRows
          },
          dataType: "jsonp"
        };
      } else {
        return {
          url: u,
          // data: {
          //     page: data.pageIndex,
          //     rows: data.pageRows,
          //     isCount: true,
          //     title: _keyword
          // },
          data: {
            taskCategoryCode: taskCategoryCode,
            title: _keyword,
            page: data.pageIndex,
            rows: data.pageRows
          },
          dataType: "jsonp"
        };
      }
    };

    arr[0].options.done = function (data, action) {
      console.log(data, "//////");
      var result = {
        // d: data.list
        d: data
      };
      rander(c, result, data, type);
    };

    arr[0].action(1);
  } else {
    arr[0] = $(".content").pullaction({
      target: "#news-list-" + c,
      ajaxDataProp: "data",
      initRefresh: true,
      // 初始化刷新
      autoRefresh: false,
      // 自动刷新
      interval: 2000,
      // 自动刷新时间
      pageRows: 10,
      otherParams: {},
      disablePullDown: false,
      goTop: 300,
      fnAjaxSettings: function fnAjaxSettings(data, url, action) {
        return {
          url: u,
          // data: {
          //   page: data.pageIndex,
          //   rows: data.pageRows,
          //   title: _keyword
          // },
          data: {
            taskCategoryCode: taskCategoryCode,
            title: _keyword,
            page: data.pageIndex,
            rows: data.pageRows
          },
          dataType: "jsonp"
        };
      },
      pullDownBefore: function pullDownBefore(e) {
        A.console().log(e);
      },
      pullDownReady: function pullDownReady(e) {
        A.console().log(e);
      },
      pullDownAction: function pullDownAction(e) {
        // $("#news-list-" + 0).html(" ");
        A.console().log(e);
      },
      pullDownError: function pullDownError(e) {
        A.console().log(e);
      },
      pullDownSuccess: function pullDownSuccess(e) {
        A.console().log(e);
      },
      pullDownComplete: function pullDownComplete(e) {
        A.console().log(e);
      },
      pullUpBefore: function pullUpBefore(e) {
        A.console().log(e);
      },
      pullUpReady: function pullUpReady(e) {
        A.console().log(e);
      },
      pullUpAction: function pullUpAction(e) {
        A.console().log(e);
      },
      pullUpError: function pullUpError(e) {
        A.console().log(e);
      },
      pullUpSuccess: function pullUpSuccess(e) {
        A.console().log(e);
      },
      pullUpComplete: function pullUpComplete(e) {
        A.console().log(e);
      },
      fnDataFilter: function fnDataFilter(res) {
        if (c === 4 || c === "4") {
          var data = res.drafts;

          for (var i = 0; i < data.length; i++) {
            var createTime_ = formatDate_(data[i].createtime);
            data[i].createTimeStr = createTime_.year + "-" + createTime_.month + "-" + createTime_.date + " " + createTime_.hour + ":" + createTime_.minute;
          }
        } else {
          var data = res && res.result && res.result.data;
        }

        if (data) {
          return data;
        } else {
          return [];
        }

        console.log(arguments);
      },
      fail: function fail(errCode, state) {
        if (errCode === -1) $.poptips("没有了~");
      },
      done: function done(data, action) {
        console.log(data, "****");
        var result = {
          // d: data.list
          d: data
        };
        rander(c, result, data, type);
      },
      onRendered: function onRendered() {
        console.log(arguments);
      }
    });
  }
}

function rander(c, result, data, type) {
  var html = "";

  for (var i = 0; i < data.length; i++) {
    var item = data[i];
    var this_creater = item.creater && item.creater.name;
    this_creater = this_creater ? this_creater : "";
    var this_from = item.category && item.category.name;
    this_from = this_from ? this_from : "";
    var createTimeStr = item.createTimeStr ? item.createTimeStr : "";
    var this_date = createTimeStr.length >= 16 ? createTimeStr.slice(0, 16) : createTimeStr;
    var this_url = "";
    var imgUrl = '';

    if (type === "todo" || type === "trace") {
      createName = "上一步经办人：";

      if (item.markName && item.markName == "收回") {
        imgUrl = "./images/hs.png";
      } else {
        imgUrl = "./images/shz.png";
      }
    }

    if (type === "done") {
      createName = "审批人：";
      this_url = item.mobileDoneUrl ? item.mobileDoneUrl : item.doneUrl ? item.doneUrl : item.url;

      if (item.parentState == 0) {
        imgUrl = "./images/shz.png";
      } else if (item.parentState == 1) {
        imgUrl = "./images/bj.png";
      } else if (item.parentState == -100) {
        imgUrl = '';
      } else if (item.parentState == -1 || item.parentState == -2 || item.parentState == 2 || item.parentState == 3) {
        imgUrl = "./images/zf.png";
      }
    } else {
      this_url = item.mobileUrl ? item.mobileUrl : item.url;
    }

    if (type === "finished") {
      createName = "";
      imgUrl = "./images/bj.png";
    }

    if (type === "trace") {
      imgUrl = "./images/shz.png";
    }

    html += "<div class=\"taskItem\" data-url=\"".concat(this_url, "\">\n    <div class=\"title\">").concat(item.title, "</div>\n    <div class=\"taskItemInfo\">\n        <div class=\"from\">\n            <span>\u6765\u6E90\uFF1A</span>\n            <span>").concat(this_from, "</span>\n        </div>\n        <div class=\"createTime\">\n          <span>\u521B\u5EFA\u65F6\u95F4\uFF1A</span>\n          <span>").concat(this_date, "</span>\n        </div>\n        <div class=\"creater ").concat(createName ? "" : "hasName", "\">\n            <span>").concat(createName, "</span>\n            <span>").concat(this_creater, "</span>\n        </div>\n    </div>");

    if (imgUrl) {
      html += "<img class=\"taskItem-img\" src=\"".concat(imgUrl, "\" alt=\"\" />");
    }

    html += "</div>";
  }

  console.log($("#news-list-0"), "1");
  console.log(html, "2");
  console.log($("").html(), "3");
  $("#news-list-0").html(html); // $(".pullaction>ul").append(html);

  return false;
} // 模板加载


function rander2(c, r, data) {
  $('.daiban li').removeClass('active');

  if (c === 0) {
    var temp = _.template($("#daibaninfo").html());

    var html = temp(r);
    $("#news-list-" + c).append(html);
    $('#todotask').addClass('active');
  } else if (c === 1) {
    var temp = _.template($("#yibaninfo").html());

    var html = temp(r);
    $("#news-list-" + c).append(html);
    $('#donetask').addClass('active');
  } else if (c === 2) {
    var temp = _.template($("#genzonginfo").html());

    var html = temp(r);
    $("#news-list-" + c).append(html);
    $('#processTrack').addClass('active');
  } else if (c === 3) {
    var temp = _.template($("#banjieinfo").html());

    var html = temp(r);
    $("#news-list-" + c).append(html);
    $('#processDone').addClass('active');
  } else if (c === 4 || c === "4") {
    var temp = _.template($('#cgxinfo').html());

    var html = temp(r);
    $('#news-list-' + c).append(html);
    $('#cgx').addClass('active');
  }
} //点击单个task 跳转


$(document).on("click", " .taskContent .taskInfo .taskList .taskItem", function (e) {
  e.stopPropagation();
  var this_url = $(this).data("url");

  if (this_url) {
    //当前选中tab项
    var this_tab = $(".taskContent .taskInfo .taskTab .taskTabItem.active").data("type");

    if (this_tab === "todo") {
      //待办回来需要刷新
      toDoUrl(this_url);
    } else {
      commonToUrl(this_url, "_blank");
    }
  }
}); //事务 tab切换

$(document).on("click", " .taskContent .taskInfo .taskTab .taskTabItem ", function (e) {
  e.stopPropagation();
  $(this).addClass("active").siblings().removeClass("active");
  $(".taskCategory").val('all'); // $(".layui-btn.taskCategory").html("全部").data("type", "all");

  $(".taskContent .searchBox input").val("");
  getTask();
}); //点击搜索按钮

$(document).on("click", " .taskContent .searchBox .svgImg.searchIcon", function (e) {
  e.stopPropagation();
  getTask();
});
$("body").on("keyup", ".taskContent .searchBox input", function (e) {
  //筛选框赋值
  if (e.keyCode == 13) {
    //enter键为13
    // console.log('关键字搜索');
    getTask();
  }
}); //获取地址栏参数

function getURLParameter(name) {
  var url = window.location.search.substring(1); // 获取URL中?后的参数字符串

  var paramsArr = url.split("&"); // 将参数字符串按'&'分割成数组

  for (var i = 0; i < paramsArr.length; i++) {
    var param = paramsArr[i].split("="); // 将每个参数按'='分割成键值对数组

    if (param[0] === name) {
      return param[1]; // 返回指定参数名对应的值
    }
  }

  return null; // 如果没有找到则返回null
} //时间转换


function timestampToDate(timestamp) {
  var date = new Date(parseInt(timestamp)); //时间戳为10位需*1000，时间戳为13位的话不需乘1000

  var Y = date.getFullYear() + "-";
  var M = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
  var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
  var h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  var m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(); // var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();

  return Y + M + D;
} //待办跳转


function toDoUrl(url) {
  if (url) {
    //判断是否钉钉环境
    var userAgent = navigator.userAgent; // 获取User Agent信息

    if (userAgent.indexOf("DingTalk") > -1) {
      // return true; // User Agent包含"DingTalk"关键词，则表明当前环境为钉钉
      //钉钉环境-钉钉方式跳转
      DDOpen(url);
    } else {
      var todoOpen = window.open(url, "_blank");
      var timer;
      timer = setInterval(function () {
        if (todoOpen.closed) {
          getTask();
          clearInterval(timer);
        }
      }, 1000);
    }
  }
}