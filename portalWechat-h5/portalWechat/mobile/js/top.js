$(function () {
    getTopBg();
    firstArticleImgUrl='';
})

 //获取头部背景
 function getTopBg(){
    $.ajax({
      url: index_urls.getPortalArticleList16,
      type: "get",
      data: {
        beginIndex: 0,
        siteId: 4,
        columnId: 19,
        pageSize: 6,
        _t:new Date().getTime()
      },
      dataType: "jsonp",
      success: function (data) {
        var resData = (data.data)?(data.data):{};
        var articles = (resData.articles)?(resData.articles):[];
        var topBg='../mobile/images/topBg.jpeg';
        var resTopBg = '';
        for(var i=0;i<articles.length;i++){
          if(articles[i].title ==='移动头部背景'){
            var this_iconUrls = articles[i].iconUrls;
            resTopBg = this_iconUrls.length>0?this_iconUrls[0]:'../images/topBg.jpeg';
          }
          //将资讯文章的默认图存一下-资讯更多页面可能会用上
          if(articles[i].title ==='资讯文章的默认图'){
            var this_zxImg = articles[i].iconUrls;
            firstArticleImgUrl = this_zxImg.length>0?this_zxImg[0]:(articles[i].videoThumb);
          }
        }
        topBg = resTopBg?resTopBg:topBg;
        $('.topBg').show().attr('src',topBg);
      },
      error: function (err) {
        var topBg = '../mobile/images/topBg.jpeg';
        $('.topBg').show().attr('src',topBg);
      },
    });
  }

  //头部搜索按钮跳转
  $(document).on("click", " .topContent .topBox .searchBox img", function (e){
    e.stopPropagation();
    // window.open('search.html?_t='+new Date().getTime(),'_self');
    // DDOpen('search.html?_t='+new Date().getTime());
    var this_url = commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/search.html?_t='+new Date().getTime();
    commonToUrl(this_url);
  })