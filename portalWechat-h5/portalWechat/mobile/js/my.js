$(function () {
  getUserInfo()
  //常用应用
  getDingTalkApps()
  //我的收藏
  getMyFavourite()
})

//获取人员信息
function getUserInfo() {
  $.ajax({
    url: index_urls.getUserInfo,
    data: {
      _p: 'YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m'
    },
    crossDomain: true,
    dataType: 'jsonp',
    success: function (res) {
      let userStrategy = res?.data?.userStrategy
      let data = res?.data
      $('.userName').html(data?.userName)
      $('.loginName').html(data?.loginName)
      $('.userDept').html(data?.userDept)
      if (userStrategy.indexOf('student') > -1) {
        $('.login-name').html('学号：')
        $('.login-depart').html('学院：')
      } else {
        $('.login-name').html('工号：')
        $('.login-depart').html('部门：')
      }
    },
    error(err) {}
  })
}

//获取我的收藏列表
function getMyFavourite() {
  $.ajax({
    url: index_urls.getMyFavoriteAppsUrl,
    data: {
      clientType: 3
    },
    crossDomain: true,
    dataType: 'jsonp',
    success: async function (res) {
      var data = res.data ? res.data : []
      data.push({
        id: 'addApps',
        name: '添加',
        iconUrl: './images/addBtnIcon.png'
      })
      var appHtml = '<div class="swiper myAppsSwiper">'
      appHtml += '<div class="swiper-wrapper">'
      for (var i = 0; i < data.length; i++) {
        //判断是否svg 图片
        var item = data[i]
        var this_iconUrl = item.iconUrl
        var this_isSvg = false
        if (this_iconUrl.slice(-4) === '.svg') {
          var this_svgDetail = await imgToSvg(this_iconUrl)
          this_isSvg = true
        }
        if (i === 0 || (i != 0 && i % 8 === 0)) {
          // console.log(i,'f')
          //每页 第一个应用之前加上分页容器
          appHtml += '<div class="swiper-slide">'
          appHtml += '<div class="appPage">'
        }
        appHtml +=
          '<div class="appItem ' +
          data[i].id +
          '" data-id="' +
          data[i].id +
          '" data-enableservice="' +
          data[i].enableService +
          '" data-isallow="' +
          data[i].isAllow +
          '" ' +
          ' data-reason="' +
          data[i].reason +
          '" data-appurl="' +
          data[i].entranceUrl +
          '" data-appname="' +
          data[i].appName +
          '">'
        appHtml += '<div class="appBox">' 
        if (this_isSvg === true && this_svgDetail) {
          var _iconColor = data[i].iconColor ? data[i].iconColor : iconColor(i);
          appHtml += '<div class="svgImg" style="background-color:'+_iconColor+' ">' + this_svgDetail + '</div>'
        } else {
          appHtml += '<img src="' + data[i].iconUrl + '" alt="">'
        }


        appHtml += '<span>' + data[i].name + '</span>'
        appHtml += '</div>'
        // appHtml += '<img class="btnImg cancelBtn" src="./images/cancelBtn.png" alt="">'

        appHtml += '</div>'
        // console.log(i,'m')
        if (i === data.length - 1 || (i != 0 && (i + 1) % 8 === 0)) {
          //每页最后一个应用 或者最后一个应用
          appHtml += '</div>'
          appHtml += '</div>'
          // console.log(i,'d')
        }
      }
      appHtml += '</div><div class="swiper-pagination myAppSwiperPagination"></div></div>'
      $('.middleContent .appModule .appList .appListInfo.myFavourite ').html(appHtml)
      if (data.length > 8) {
        // 超过一一页
        $('.middleContent .appModule .appList .myFavourite .myAppsSwiper .swiper-slide').addClass('morePage')
        var appSwiper = new Swiper(' .myAppsSwiper', {
          loop: false,
          autoplay: false,
          pagination: {
            el: '.myAppSwiperPagination',
            clickable: true
          }
        })
      }
    },
    error(err) {
      var data = {
        id: 'addApps',
        name: '添加',
        iconUrl: './images/addBtnIcon.png'
      }
      var appHtml = ''
      appHtml += '<div class="swiper-slide">'
      appHtml += '<div class="appPage">'
      appHtml +=
        '<div class="appItem" data-id="' +
        data.id +
        '" data-enableservice="' +
        data.enableService +
        '" data-isallow="' +
        data.isAllow +
        '" ' +
        ' data-reason="' +
        data.reason +
        '" data-appurl="' +
        data.entranceUrl +
        '" data-appname="' +
        data.appName +
        '">'
      appHtml += '<img src="' + data.iconUrl + '" alt="">'
      appHtml += '<span>' + data.name + '</span>'
      appHtml += '</div>'
      appHtml += '</div>'
      appHtml += '</div>'
      appHtml += '</div><div class="swiper-pagination myAppSwiperPagination"></div></div>'
      $('.middleContent .appModule .appList .appListInfo.myFavourite ').html(appHtml)
    }
  })
}

function getDingTalkApps() {
  $.ajax({
    url: index_urls.queryFixRecommendAppList,
    data: {
      clientType:'wechat'
    },
    crossDomain: true,
    dataType: 'jsonp',
    success: async function (res) {
      var data = res.data?.fixRecommendApps ?? []
      if (data.length > 0) {
        var appHtml = '<div class="swiper dingAppsSwiper">'
        appHtml += '<div class="swiper-wrapper">'
        for (var i = 0; i < data.length; i++) {
          //判断是否svg 图片
          var item = data[i]
          var this_iconUrl = item.iconUrl
          var this_isSvg = false
          if (this_iconUrl.slice(-4) === '.svg') {
            var this_svgDetail = await imgToSvg(this_iconUrl)
            this_isSvg = true
          }
          if (i === 0 || (i != 0 && i % 8 === 0)) {
            // console.log(i,'f')
            //每页 第一个应用之前加上分页容器
            appHtml += '<div class="swiper-slide">'
            appHtml += '<div class="appPage">'
          }
          appHtml +=
            '<div class="appItem" data-id="' +
            data[i].id +
            '" data-enableservice="' +
            data[i].enableService +
            '" data-isallow="' +
            data[i].isAllow +
            '" ' +
            ' data-reason="' +
            data[i].reason +
            '" data-appurl="' +
            data[i].entranceUrl +
            '" data-appname="' +
            data[i].appName +
            '">'
           appHtml += '<div class="appBox">'  
          if (this_isSvg === true && this_svgDetail) {
            var _iconColor = data[i].iconColor ? data[i].iconColor : iconColor(i);
            appHtml += '<div class="svgImg" style="background-color:'+_iconColor +' ">' + this_svgDetail + '</div>'
          } else {
            appHtml += '<img src="' + data[i].iconUrl + '" alt="">'
          }

          appHtml += '<span>' + data[i].name + '</span>'
          appHtml += '</div></div>'
          // console.log(i,'m')
          if (i === data.length - 1 || (i != 0 && (i + 1) % 8 === 0)) {
            //每页最后一个应用 或者最后一个应用
            appHtml += '</div>'
            appHtml += '</div>'
          }
        }
        appHtml += '</div><div class="swiper-pagination myAppSwiperPagination"></div></div>'
        $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html(appHtml)
        if (data.length > 8) {
          // 超过一一页
          $('.middleContent .appModule .appList .dingtalkApps .myAppsSwiper .swiper-slide').addClass('morePage')
          var dingSwiper = new Swiper(' .dingAppsSwiper', {
            loop: false,
            autoplay: false,
            pagination: {
              el: '.myAppSwiperPagination',
              clickable: true
            }
          })
        }
      } else {
        $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html(`<div class="noDate nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>`)
      }
    },
    error(err) {
      $('.middleContent .appModule .appList .appListInfo.dingtalkApps ').html("<div class='noDate'>暂无数据</div>")
    }
  })
}

//点击应用-添加应用
var addAppDia
var addAppDiaOpen = false
$(document).on('click', '.appList .appItem .appBox', function (e) {
  e.stopPropagation()
  var this_id = $(this).parent().data('id')
  if (this_id === 'addApps') {
    //应用添加按钮
    addAppDia = $.iframe({
      title: '应用收藏',
      content: 'addMyApp.html?_p=YXM9MSZwPTEmbT1OJg__',
      modalClassName: 'iframe-addApps',
      onOpen: function (e, iframe) {
        // $.poptips({type:"warning",color:true, content:"正在打开"});
        addAppDiaOpen = true
      }
    })
      .on('iframe:open', function (e, iframe) {
        // $.poptips({tipLevel:"warning",color:true, content:"正在打开"});
      })
      .on('iframe:cancel', function (e, iframe) {
        // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
      })
      .on('iframe:closed', function (e, iframe) {
        addAppDiaOpen = false
        // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
      })
  } else {
    //正常应用-跳转
    var this_node = $(this).parent()
    toAppUrl(this_node)
  }
})

//应用跳转
function toAppUrl(this_node) {
  var this_enableservice = $(this_node).data('enableservice')
  var this_reason = $(this_node).data('reason')
  var this_isallow = $(this_node).data('isallow')
  var this_appurl = $(this_node).data('appurl')
  var this_appname = $(this_node).data('appname')
  var this_id = $(this_node).data('id')
  if (this_enableservice == 1) {
    //有服务指南页 跳应用详情页
    var this_url =
      commonUrlTwo +
      '/_web/portalWechat/mobile/detail.html?appVersionId=' +
      this_id +
      '&_t=' +
      new Date().getTime()
    commonToUrl(this_url, '_blank')
  } else {
    if (this_isallow === 'true' || this_isallow === true) {
      openModule(this_appname)
      commonToUrl(this_appurl, '_blank')
    } else {
      this_reason = this_reason ? this_reason : '暂无权限'
      $.poptips(this_reason)
    }
  }
}

//取消收藏
$(document).on('click', '.appList .appItem .btnImg.cancelBtn', function (e) {
  e.stopPropagation()
  if (!$(e.target).closest('.cancelBtn').length) return;
  var this_id = $(this).parents('.appItem').data('id')
  var dia = $.dialog({
    content: '您确认取消收藏此应用吗?',
    title: '提示',
    width: 60
  }).on('dialog:confirm', function (e, dialog) {
    //取消收藏
    $.ajax({
      url: index_urls.cancelApp,
      dataType: 'jsonp',
      type: 'GET',
      data: {
        appVersionId: this_id,
        act: 'cancel'
      },
      success: function (res) {
        //隐藏的模块
        var this_hide_module = $('.main .hideModule').data('type')
        if (this_hide_module === 'addMod') {
          //当前是搜素模块
          //重新搜索
          var keyword = $('.main .searchModule .top input').val()
          checkVal(keyword)
        }
        //模块
        getMyFavourite()
        //重新加载选中分类下应用
        var this_category = $('.main .addModule .allApps .categoryBox .tabBox .item.active').data('cateogryid')
        loadAllServiceApps(this_category)
        //父级页面刷新一下我的收藏
        window.parent.postMessage('reloadMyFavourite', '*')
      }
    })
  })
  return false
})

//应用统计
function openModule(appName) {
  var screenSize = window.screen.width + '*' + window.screen.height
  var clientVersion =
    navigator.platform +
    '-' +
    myBrowser() +
    '-' +
    navigator.appVersion.substring(0, navigator.appVersion.indexOf('(') - 1)
  $.ajax({
    url: index_urls.openModule,
    type: 'get',
    data: {
      appName: appName,
      clientVersion: clientVersion,
      screenSize: screenSize
    },
    dataType: 'json',
    success: function () {}
  })
}


//取消添加
window.addEventListener('message', function(event) {
  //刷新我的收藏数据
  if(event.data === 'reloadMyFavourite'){
    getMyFavourite();
  }
}, false);