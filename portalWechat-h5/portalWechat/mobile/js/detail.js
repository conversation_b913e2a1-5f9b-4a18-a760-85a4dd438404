$(function (){
    //获取应用详情
    var appId = GetQueryString("appVersionId");
    getAppDetail(appId);

})
//获取应用详情
function getAppDetail(appId){
   
    $.ajax({
        url:'/sopplus/mobile/getServiceAppDetail.do?_p=YXM9MSZwPTEmbT1OJg__',
            dataType : "json",
            type:"get",
            data : {
                appVersionId:appId
            },
            success : function(res){
                var data = res.data?res.data:{};
                //应用名称
                $('.containBox .appTitle').html(data.appName?data.appName:'');
                //应用地址
                var entranceUrl = data.entranceUrl?data.entranceUrl:'';
                $(".wybl").attr("onclick","myapply('"+entranceUrl+"')");
                //应用详情
                var mobileDescriptionDetail = data.mobileDescriptionDetail?data.mobileDescriptionDetail:'';
                $('.containBox .description').html(mobileDescriptionDetail);
                if(mobileDescriptionDetail === ''){
                    $('.containBox .description').hide();
                }
                //申请人员范围
                var applyUserScope = data.applyUserScope?data.applyUserScope:'';
                var responsibleDept = data.responsibleDept?data.responsibleDept:'';
                var contactUser = data.contactUser?data.contactUser:'';
                var contactPhone = data.contactPhone?data.contactPhone:'';
                var handleTime = data.handleTime?data.handleTime:'';
                var handleAddress = data.handleAddress?data.handleAddress:'';
                $('.containBox .detailInfo .detailItem .applyUserScope').html(applyUserScope);
                $('.containBox .detailInfo .detailItem .responsibleDept').html(responsibleDept);
                $('.containBox .detailInfo .detailItem .contactUser').html(contactUser);
                $('.containBox .detailInfo .detailItem .contactPhone').html(contactPhone);
                $('.containBox .detailInfo .detailItem .handleTime').html(handleTime);
                $('.containBox .detailInfo .detailItem .handleAddress').html(handleAddress);
                        },
            error : function(err){
                    $('.containBox .description').hide();
            }
        })
}
function myapply(url){
    commonToUrl(url,'_blank');
}
function GetQueryString(name)
{
    var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if(r!=null)return  unescape(r[2]); return null;
}