$(function () {
  getColumn(2)
  isFirstImg = true
})


/**
 * 点击订阅
 */
$('.searchBox').tap(function () {
    var siteid = $(".active").attr("data-siteid");
    var columnid = $(".active").attr("data-columnid");
    var name = $(".active").text();
    window.location.href = "../mobile/articleSubscribe.html?siteid=" + siteid + "&columnid=" + columnid + "&name=" + name
})

//获取一级分类下二级分类
function getColumn(id) {
  if (id === 2) {
    //官网资讯下的二级分类
    querySubs()
  } else {
    //其他分类待定
    //原有数据先清空
    $('.articleContent .subItemTab').html('')
    var articleStr = `
    <div class="nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
    `
    $('.articleContent .articleInfo .articleList').html(articleStr)
  }
}
//获取订阅栏目
function querySubs() {
  $.ajax({
    url: index_urls.querySubsUrl,
    data: {},
    success: function (res) {
      var data = res.data ? res.data : {}
      var subs = data.subs ? data.subs : []
      var subsHtml = ''
      for (var i = 0; i < subs.length; i++) {
        var item = subs[i]
        var this_isActive = ''
        if(getURLParameter('siteId') && getURLParameter('columnId')){
          if (item.siteId == getURLParameter('siteId') && item.columnId == getURLParameter('columnId')) {
            this_isActive = 'active'
            loadFlowArticles(item.siteId, item.columnId)
          }
        }else{
          if (i === 0) {
            this_isActive = 'active'
            loadFlowArticles(item.siteId, item.columnId)
          }
        }
        
        subsHtml += `
          <div class="subItem ${this_isActive}" data-siteid="${item.siteId}" data-columnid="${item.columnId}">${item.columnName}</div>
        `
      }
      $('.articleContent .subItemTab').html(subsHtml)

      $('.articleContent .subItemTab .subItem').map(function (i, item) {
        if($(this).hasClass('active')){
          var _width = $(this).width();
          var index = $(this).index();
          $(this).parent().scrollLeft(_width * (index - 1))
        }
      })

    },
    error(err) {}
  })
}

//加载对应分类下的文章---分页
function loadArticles(siteId, columnId, beginIndex = 0) {
  if (beginIndex === 0) {
    isFirstImg = true
  }
  $.ajax({
    url: index_urls.getPortalArticleList16,
    data: {
      beginIndex: beginIndex,
      siteId: siteId,
      columnId: columnId,
      pageSize: 5,
      _t: new Date().getTime()
    },
    dataType: 'jsonp',
    success: function (res) {
      var resData = res.data ? res.data : {}
      var articles = resData.articles ? resData.articles : []
      //总数
      var articlesCount = resData.count ? resData.count : 0
      var articlesHtml = ''
      for (var i = 0; i < articles.length; i++) {
        var this_iconUrl = articles[i].iconUrls
        this_iconUrl = this_iconUrl.length > 0 ? this_iconUrl[0] : ''
        if (i === 0 && this_iconUrl === '') {
          this_iconUrl = firstArticleImgUrl
        }
        // console.log(i,isFirstImg)
        if (i === 0 && isFirstImg === true) {
          //第一篇-图文上下排列
          articlesHtml += '<div class="articleItem" data-url="' + articles[i].articleUrl + '">'
          if (this_iconUrl) {
            articlesHtml += '<img src="' + this_iconUrl + '" alt="">'
          }
          articlesHtml += '<div class="title" >' + articles[i].title + '</div>'
          articlesHtml += '<div class="date">' + timestampToDate(articles[i].publishTimestamp) + '</div>'
          articlesHtml += '</div>'
        } else {
          //其他文章-图文左右排列
          articlesHtml += '<div class="articleItem itemFlex" data-url="' + articles[i].articleUrl + '">'
          var isFullWidth = ''
          if (this_iconUrl) {
            articlesHtml += '<img src="' + this_iconUrl + '" alt="">'
          } else {
            isFullWidth = 'fullWidth'
          }
          articlesHtml += '<div class="rightInfo ' + isFullWidth + '">'
          articlesHtml += '<div class="title">' + articles[i].title + '</div>'
          articlesHtml += '<div class="date">' + timestampToDate(articles[i].publishTimestamp) + '</div>'
          articlesHtml += '</div>'
          articlesHtml += '</div>'
        }
        isFirstImg = false
      }
      if (articlesHtml === '') {
        articlesHtml = `
        <div class="nodataBox">
        <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
        <div class="nodata">暂无数据</div>
        </div>
        `
      }
      $('.articleContent .articleInfo .articleList').html(articlesHtml)
      //页面回到顶部
      $(window).scrollTop(0)
      //分页
      getArticlePages(siteId, columnId, articlesCount, beginIndex + 1)
    },
    error(err) {
      var articlesHtml = `
      <div class="nodataBox">
      <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
      <div class="nodata">暂无数据</div>
      </div>
      `
      $('.articleContent .articleInfo .articleList').html(articlesHtml)
    }
  })
}

//文章分页
function getArticlePages(siteId, columnId, total, currPage) {
  layui.use(['laypage', 'layer'], function () {
    var laypage = layui.laypage,
      layer = layui.layer
    //自定义每页条数的选择项<em><</em>  <em>></em>
    laypage.render({
      elem: 'articlePages',
      count: total,
      curr: currPage || 1, // 当前页
      limit: 5,
      limits: [5],
      prev: '<i class="layui-icon"></i>',
      next: '<i class="layui-icon"></i>',
      theme: '#007CFF',
      jump: function (obj, first) {
        // console.log(obj);
        //当前页
        var this_page = obj.curr - 1
        this_page = this_page < 0 ? 0 : this_page
        if (!first) {
          loadArticles(siteId, columnId, this_page)
        }
      }
    })
  })
}

//流加载文章资讯--needFirstImg 是否需要第一张大图单独显示，默认true，需要单独显示；false不需要
function loadFlowArticles(siteId, columnId, needFirstImg = true) {
  //先清空资讯容器
  $('.articleContent .articleInfo .articleList').html('')
  layui.use('flow', function () {
    var flow = layui.flow
    flow.load({
      elem: '.articleList', //指定列表容器
      isAuto: true,
      scrollElem: '.articleList', //滚动条所在容器
      end: ' ',
      done: function (page, next) {
        // console.log(page)
        //到达临界点（默认滚动触发），触发下一页
        var lis = []
        //以jQuery的Ajax请求为例，请求下一页数据（注意：page是从2开始返回）
        //假设你的列表返回在data集合中
        $.ajax({
          url: index_urls.getPortalArticleList16,
          data: {
            beginIndex: page - 1,
            siteId: siteId,
            columnId: columnId,
            pageSize: 10,
            _t: new Date().getTime()
          },
          crossDomain: true,
          dataType: 'jsonp',
          success: function (res) {
            var resData = res.data ? res.data : {}
            var articles = resData.articles ? resData.articles : []
            //总数
            var articlesCount = resData.count ? resData.count : 0
            var zxPages = Math.ceil(articlesCount / 10)
            if (articles.length > 0) {
              for (var i = 0; i < articles.length; i++) {
                var articlesHtml = ''
                var this_iconUrl = articles[i].iconUrls
                this_iconUrl = this_iconUrl.length > 0 ? this_iconUrl[0] : ''
                if (i === 0 && this_iconUrl === '' && needFirstImg === true) {
                  this_iconUrl = firstArticleImgUrl
                }
                // console.log(i,isFirstImg)
                if (i === 0 && isFirstImg === true && needFirstImg === true) {
                  //第一篇-图文上下排列
                  articlesHtml += '<div class="articleItem" data-url="' + articles[i].articleUrl + '">'
                  if (this_iconUrl) {
                    articlesHtml += '<img src="' + this_iconUrl + '" alt="">'
                  }
                  articlesHtml += '<div class="title" >' + articles[i].title + '</div>'
                  articlesHtml += '<div class="date">' + timestampToDate(articles[i].publishTimestamp) + '</div>'
                  articlesHtml += '</div>'
                } else {
                  //其他文章-图文左右排列
                  articlesHtml += '<div class="articleItem itemFlex" data-url="' + articles[i].articleUrl + '">'
                  var isFullWidth = ''
                  if (this_iconUrl) {
                    articlesHtml += '<img src="' + this_iconUrl + '" alt="">'
                  } else {
                    isFullWidth = 'fullWidth'
                  }
                  articlesHtml += '<div class="rightInfo ' + isFullWidth + '">'
                  articlesHtml += '<div class="title">' + articles[i].title + '</div>'
                  articlesHtml += '<div class="date">' + timestampToDate(articles[i].publishTimestamp) + '</div>'
                  articlesHtml += '</div>'
                  articlesHtml += '</div>'
                }
                isFirstImg = false
                lis.push(`${articlesHtml}`)
              }
              //执行下一页渲染，第二参数为：满足“加载更多”的条件，即后面仍有分页
              //pages为Ajax返回的总页数，只有当前页小于总页数的情况下，才会继续出现加载更多
              next(lis.join(''), page < zxPages)
            } else {
              if (page === 1) {
                lis.push(`
              <div class="nodataBox">
              <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
              <div class="nodata">暂无数据</div>
              </div>
              `)
                //第一页不需要显示-没有更多了
                $('.layui-flow-more').hide()
              } else {
                $('.layui-flow-more').show()
              }
              next(lis.join(''), page < 1)
            }
          },
          error(err) {
            if (page === 1) {
              lis.push(`
            <div class="nodataBox">
            <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
            <div class="nodata">暂无数据</div>
            </div>
            `)
              //第一页不需要显示-没有更多了
              $('.layui-flow-more').hide()
            } else {
              $('.layui-flow-more').show()
            }

            next(lis.join(''), page < 1)
          }
        })
      }
    })
  })
}

//大分类下子栏目切换
$(document).on('click', '.articleContent .subItemTab .subItem', function (e) {
  e.stopPropagation()
  // 获取当前点击元素的下标
  var _width = $(this).width();
  var index = $(this).index();
  $(this).parent().scrollLeft(_width * (index - 1))

  $(this).addClass('active').siblings().removeClass('active')
  var siteId = $(this).data('siteid')
  var columnId = $(this).data('columnid')
  isFirstImg = true
  // loadArticles(siteId,columnId);
  loadFlowArticles(siteId, columnId)
  // 
})

//文章点击跳转
$(document).on('click', ' .articleContent .articleInfo .articleList .articleItem', function (e) {
  e.stopPropagation()
  var url = $(this).data('url')
  if (url) {
    // window.open(url,'_blank');
    commonToUrl(url, '_blank')
  }
})

//获取地址栏参数
function getURLParameter(name) {
  var url = window.location.search.substring(1) // 获取URL中?后的参数字符串
  var paramsArr = url.split('&') // 将参数字符串按'&'分割成数组
  for (var i = 0; i < paramsArr.length; i++) {
    var param = paramsArr[i].split('=') // 将每个参数按'='分割成键值对数组
    if (param[0] === name) {
      return param[1] // 返回指定参数名对应的值
    }
  }
  return null // 如果没有找到则返回null
}

//时间转换
function timestampToDate(timestamp) {
  var date = new Date(parseInt(timestamp)) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  // var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return Y + M + D
}

