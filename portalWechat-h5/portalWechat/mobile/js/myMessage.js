$(function () {
  // getMsgInfo();
  var msgId = getParams("msgId");
  $.ajax({
    type: "get",
    url: index_urls.fetchReceiveBoxDetailJsonpUrl,
    data:{
      msgId:msgId
    },
    dataType: "jsonp",
    crossDomain: true,
    error: function () {},
    success: function (data) {
      var data = data.result.data;
            $(".zhanghao").html(data.subject);
            $(".sender").html(data.sender);
            $(".postTime .sender").html(data.createTime);
            $(".content").html(data.content);
            if(JSON.stringify(data.attachMents) != '{}'){
              $(".attachMents").show();
              $(".attachMentsContent").attr("href",data.attachMents.url);
              $(".attachMentsContent").html(data.attachMents.name);
            }
    },
});
});

//获取 信息
function getMsgInfo() {
  $.ajax({
    type: "get",
    url: index_urls.getUserLbUrl,
    dataType: "jsonp",
    crossDomain: true,
    error: function () {},
    success: function (data) {
      
    },
  });
}

//点击跳转
$(document).on("click", ".mainContent .msgItem", function (e) {
  e.stopPropagation();
  var toUrl = $(this).data('url');
  if(toUrl){
    window.open(toUrl);
  }else{
    $.poptips('正在建设中');
  }
});

/**获取地址栏参数 */
function getParams(variable){
  var query = window.location.search.substring(1);
  var vars = query.split("&");
  for (var i=0;i<vars.length;i++) {
          var pair = vars[i].split("=");
          if(pair[0] == variable){return pair[1];}
  }
  return '';
}

