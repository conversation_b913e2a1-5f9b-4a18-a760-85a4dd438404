/**
 * Created by Administrator on 2017/11/16.
 */
$('.ui-list li,.ui-tiled li').click(function () {
    if ($(this).data('href')) {
        location.href = $(this).data('href')+"?timeStamp="+(new Date()).getTime();
    }
});
var p = [];
var st = getsevenDaysAgo();
var et = getnowday();
$(function () {
    $('.starttime').val(getsevenDaysAgo());
    $('.endtime').val(getnowday());
    $('.startweek').text(getWeek(getsevenDaysAgo()));
    $('.endweek').text(getWeek(getnowday()));
    searchinfo();
    //监听返回刷新，兼容ios设备
	 var isPageHide = false;   
	  window.addEventListener('pageshow', function () {   
		if (isPageHide) {   
		  window.location.reload();   
		}   
	  });   
	  window.addEventListener('pagehide', function () {   
		isPageHide = true;   
	  });


        //监听返回刷新，兼容ios设备
		 var isPageHide = false;   
		  window.addEventListener('pageshow', function () {   
			if (isPageHide) {   
			  window.location.reload();   
			}   
		  });   
		  window.addEventListener('pagehide', function () {   
			isPageHide = true;   
		  });
});

$(".starttime").mobiscroll().date({
    onSelect: function (textValue) {
		if(textValue>et){
			$('.starttime').val(st);
			$.poptips("开始时间不能大于结束时间！");
		}else{
			st = textValue;
			$('.startweek').text(getWeek($('.starttime').val()));
			$('.countday').text(DateDiff($('.starttime').val(), $('.endtime').val()) + '天');
		}
    }
});
$(".endtime").mobiscroll().date({
    onSelect: function (textValue) {
		if(st>textValue){
			 $('.endtime').val(et);
			$.poptips("结束时间不能小于开始时间！");
		}else{
			et = textValue;
			$('.endweek').text(getWeek($('.endtime').val()));
			$('.countday').text(DateDiff($('.starttime').val(), $('.endtime').val()) + '天');
		}
    }
});

$('.btn-search').tap(function () {
    $('.infolist').html("");
    p[0].options.otherParams = {
        keyWord: $('.keywords').val().trim()
    };
    p[0].action(1);
});

function searchinfo() {
			var currpage = 1;
            if (!p[0]) {
                p[0] = $("#xiaoxi").pullaction({
                    target: '.infolist',
                    ajaxDataProp: "data",
                    initRefresh: true, // 初始化刷新
                    autoRefresh: false, // 自动刷新
                    interval: 2000, // 自动刷新时间
                    pageRows: 10,
                    otherParams: {},
                    disablePullDown: false,
                    goTop: 1000,
                    fnAjaxSettings: function (data, url, action) {
						currpage = data.pageIndex;
                        return {
                            url: index_urls.fetchReceiveBoxJsonpUrl,
                            data: {
                                page: data.pageIndex,
                                rows: data.pageRows,
								channelType: "DD",
                                innerMsg:1,
                                receiveBegin: $('.starttime').val()+ ' ' + '00:00:00',
                                receiveEnd: $('.endtime').val()+ ' ' + '23:59:59'
                            },
                            dataType: 'jsonp'
                        };
                    },
                    pullDownBefore: function (e) {
                        A.console().log(e);
                    },
                    pullDownReady: function (e) {
                        A.console().log(e);
                    },
                    pullDownAction: function (e) {
                        A.console().log(e);
                    },
                    pullDownError: function (e) {
                        A.console().log(e);
                    },
                    pullDownSuccess: function (e) {
	                    $('.infolist').html("");
                        A.console().log(e);
                    },
                    pullDownComplete: function (e) {
                        A.console().log(e);
                    },
                    pullUpBefore: function (e) {
                        A.console().log(e);
                    },
                    pullUpReady: function (e) {
                        A.console().log(e);
                    },
                    pullUpAction: function (e) {
                        A.console().log(e);
                    },
                    pullUpError: function (e) {
                        A.console().log(e);
                    },
                    pullUpSuccess: function (e) {
                        A.console().log(e);
                    },
                    pullUpComplete: function (e) {
                        A.console().log(e);
                    },
                    fnDataFilter: function (response) {
                       return response.result;
                    },
                    fail: function (errCode, state) {
                        console.log("errCode: " + errCode);
                        if (errCode === -1)
                            $.poptips("没有了~");
                        /*if (errCode === 1)
                            this.$target.html("<p class=\"ui-center\">暂无数据~</p>");*/
                    },
                    done: function (data, action) {
                        /*var result = {
                            m: data.result.data
                        };
                        var messageViewTmp = _.template($('#message').html());
                        var html = messageViewTmp(result);*/
                        // var this_content = result[i].content.replace(/\n/g,'</br>');
						var result = data.data;
						var html = '';
						for(var i = 0;i<result.length;i++){
							html+= '<div class="ysf-box ysf-box-f1 bo-v border-b countheight" onclick="readMessage(\''+result[i].id+'*'+result[i].msgLinkUrl+'\')">'+
										'<div class="ysf-box pad08 ysf-box-f1 ba-c">'+
										'<div class="ysf-box ysf-box-f1 ba-c">';
                            if(result[i].state){
                                html+='<div class="cicle"></div>';
                            }else{
                                html+='<div class="cicle" style="display:none"></div>';
                            }
										
										html+='<div class="pad07 fts16">'+result[i].sender+'</div>'+
										'</div>'+
											'<div class="ysf-box ba-c">'+
												'<div class="fts-time ub-img"></div>'+
												'<div class="ftc05" Style="margin-right: 20px">'+result[i].createTime+'</div>'+
											'</div>'+
										'</div>'+
										'<div class="ftc06 pad09">'+result[i].subject+'-'+result[i].contentText+'</div>'+
									'</div>';
						}
						if(currpage === 1 && html == ''){
							$('.infolist').html("<p class=\"ui-center\">暂无数据~</p>");
						}else {
							$('.infolist').append(html);
						}
						if(data.data.length > 3){
							//$('.infolist').css('margin-bottom','180px');
						}
                    },
                    onRendered: function () {

                    }

                });
            }
}


//返回yyyy-MM-dd
function getnowday() {
    var d = new Date();
    var y = d.getFullYear().toString();
    var m = d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1);
    var dd = d.getDate() < 10 ? '0' + d.getDate() : d.getDate();
    var hh = d.getHours() < 10 ? '0' + d.getHours() : d.getHours();
    var mm = d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes();
    var ss = d.getSeconds() < 10 ? '0' + d.getSeconds() : d.getSeconds();
    nowday = y + '-' + m + '-' + dd;
    return nowday;
}

//获取7天前日期，返回yyyy-MM-dd
function getsevenDaysAgo() {
    var now= new Date();
    var d = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
    var y = d.getFullYear().toString();
    var m = d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1);
    var dd = d.getDate() < 10 ? '0' + d.getDate() : d.getDate();
    var hh = d.getHours() < 10 ? '0' + d.getHours() : d.getHours();
    var mm = d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes();
    var ss = d.getSeconds() < 10 ? '0' + d.getSeconds() : d.getSeconds();
    nowday = y + '-' + m + '-' + dd;
    return nowday;
}
//计算时间差
function DateDiff(sDate1, sDate2) {    //sDate1和sDate2是2017-12-18格式
    var dateSpan,
        tempDate,
        iDays;
    sDate1 = Date.parse(sDate1);
    sDate2 = Date.parse(sDate2);
    dateSpan = sDate2 - sDate1;
    dateSpan = Math.abs(dateSpan);
    iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
   return iDays == 0 ? 1:iDays+1;
}

//获取星期
function getWeek(dateString) {
    var date;
    if (dateString == "") {
        date = new Date();
    } else {
        var dateArray = dateString.split("-");
        date = new Date(dateArray[0], parseInt(dateArray[1] - 1), dateArray[2]);
    }
    //var weeks = new Array("日", "一", "二", "三", "四", "五", "六");
    //return "星期" + weeks[date.getDay()];
    return "周" + "日一二三四五六".charAt(date.getDay());
}
function readMessage(ids){
    let id=ids.split('*')
    let url=id[1];
    $.ajax({
        url: '/ucp/_web/onlinenews/receiveBox/api/findMessageDetailJsonp.rst?_p=YXM9MiZ0PTUmZD0xMzYmcD0xJmY9NTAmbT1OJg__',
        type: "get",
        data: {
            msgId:id[0] 
        },
        dataType: 'json',
        async: false,
        timeout: 10000,
        success: function (data) {
          
        },
        error: function (err) {
            
        }
    });
    window.open(url,'_self')
}
//签名认证  
function checkIdentity(paramUrl) {
    var url = index_urls.checkidentity + encodeURIComponent(paramUrl);
    var params = {
        isReturn: 'false'
    };
    var returnUrl = '';
    $.ajax({
        url: url,
        type: "get",
        data: params,
        dataType: 'json',
        async: false,
        timeout: 10000,
        success: function (data) {
            var u = data.data;
            returnUrl = u;
        },
        error: function (err) {
            $.poptips("接口暂时无法调用，请稍候访问！")
        }
    });
    return returnUrl;
}