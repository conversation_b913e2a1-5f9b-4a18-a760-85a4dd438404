(function () {
    var baseurl="";//域名、ip
    var utiles = {
        getBrower: function () {
            var userAgent = navigator.userAgent;
            var isOpera = userAgent.indexOf("Opera") > -1;
            if (isOpera) {
                return "Opera";
            } else if (userAgent.indexOf("Firefox") > -1) {
                return "Firefox";
            } else if (userAgent.indexOf("Chrome") > -1) {
                return "Chrome";
            } else if (userAgent.indexOf("Safari") > -1) {
                return "Safari";
            } else if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
                return "IE";
            } else {
                return "Other";
            }
        }
    };
    window.U = utiles;
})();
function getReasonType(){
    var reason;
    $.ajax({
        type: "get",
        url: "/sopplus/mobile/getServiceDomain.rst?t="+new Date().getTime(),
        async: false,
        cache:false,
        success: function(data){
            if(typeof(data) === 'string'){
                data = JSON.parse(data);
            }
            if(data.result == '1'){
                reason = data.data;
            }
        },
        error:function(err){
            console.log(err)
        }
    });
    return reason;
}

localStorage.setItem('_platform', 'wechat');

// var domainConfig = getReasonType();
// if(domainConfig.domain){
//     document.domain= domainConfig.domain;
// }