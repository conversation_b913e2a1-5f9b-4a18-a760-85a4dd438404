html, body, div, span, iframe, h1, h2, h3, h4, h5, h6, p, pre, a, abbr, acronym, address, big, cite, code, em, img, strong, sub, sup, tt, u, i, center, dl, dt, dd, ol, ul, li, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, canvas, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-family: "微软雅黑";
    font-size: 100%;
}

html {
    font-size: 14px;
}

.red {
    padding-right: 2px;
    color: #db0f0f;
}

.center {
    margin: 0 auto;
}

.rili-container {
    background-color: #fff;
    padding: 30px 30px;
    overflow: hidden;
}

.rili-left {
    float: left;
    width: calc(100% - 315px);
}

.rili-right {
    float: right;
    padding-left: 15px;
    width: 300px;
}

.rili-operation {
    overflow: hidden;
}

.rili-time, .rili-classify {
    /*width: calc(100% - 300px);*/
    float: left;
}

.rili-classify {
    float: right;
}

.rili-time i {
    display: inline-block;
    vertical-align: middle;
    width: 40px;
    text-align: center;
    border: 1px solid #eee;
    color: #99a3a7;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
}

.input-group.date .input-group-addon span {
    color: #99a3a7;
}

.input-group-addon:not(:first-child):not(:last-child) {
    border-left: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.rili-time i:hover {
    background-color: #eee;
    cursor: pointer;
}

.rili-time i.icon-arrow-left {
    border-right: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.rili-time i.icon-youjiantou {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.rili-time-do {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 0;
    margin-left: 15px;
    width: 150px;
}

.rili-time-do .form-control {
    height: 32px;
    width: 110px;
    background-color: #fff;
    border-right: 0;
    border-color: #eee;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.rili-time-do .input-group-addon {
    padding: 3px 12px;
    background-color: #fff;
    border-color: #eee;
}

.rili-classify {
    text-align: right;
    height: 32px;
}

.rili-type, .rili-change {
    border: 1px solid #2b8744;
    padding: 0 18px;
    cursor: pointer;
}

.rili-classify span {
    display: inline-block;
    text-align: center;
    line-height: 30px;
    font-weight: bold;
}

.rili-classify span.active, .rili-classify span:hover {
    background-color: #2b8744;
    color: white;
}

.rili-change {
    margin-right: 15px;
    border-radius: 4px;
}

.rili-zhou {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.rili-yue {
    border-right: 0;
    border-left: 0;
}

.rili-biao {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.rili-title-lists {
    padding-bottom: 15px;
}

.rili-view {
    clear: both;
    padding: 30px 0;
}

/*分类周*/
.rili-table table {
    width: 100%;
    border-top: 1px solid #eee;
    border-right: 1px solid #eee;
}

.rili-table tbody th:first-child {
    height: 100px;
    text-align: left;
}

.rili-table th .rili-table-title {
    padding: 0 5px;
    word-break: break-all;
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 14px);
   text-align: center;
}

.rili-table thead th {
    width: 12.5%;
    line-height: 40px;
    white-space: nowrap;
    padding: 0 12px;
}

.rili-table th, .rili-table td {
    text-align: center;
    border-left: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    line-height: 22px;
}

.rili-table tbody th, .rili-grey {
    position: relative;
    background-color: #f7f7f7;
}

.rili-title-grey {
    color: #88959b;
}

.rili-line-title {
    display: inline-block;
    vertical-align: middle;
    height: 100%;
    width: 4px;
    background-color: #00a7d0;
}

.rili-table-title {
    padding: 5px;
    line-height: 22px;
}

.rili-table td {
    vertical-align: top;
    padding: 4px;
}

.rili-thing p {
    text-align: left;
    color: white;
    font-size: 12px;
    word-break: break-all;
}

.rili-thing-list p {
    font-size: 14px;
}

.rili-thing {
    padding: 5px;
    margin-bottom: 4px;
    /*background-color: #70bf64;*/
    cursor: pointer;
background-color: #0b97d4;}

.rili-thing-list {
    padding: 7px 10px;
}

.rili-search {
    border: 1px solid #eee;
    border-radius: 4px;
    width: 278px;
    margin-bottom: 18px;
}

.rili-search input {
    display: inline-block;
    border: 0;
    height: 30px;
    width: 240px;
    vertical-align: middle;
    padding: 0 10px;
}

.rili-search input:focus {
    outline: none;
}

.rili-search span {
    display: inline-block;
    height: 30px;
    vertical-align: middle;
    width: 36px;
    background: #2b8744 url("../images/search.svg") no-repeat center;
    cursor: pointer;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.rili-table td:hover .rili-thing-add {
    visibility: visible;
}

.rili-thing-add {
    white-space: nowrap;
    visibility: hidden;
    height: 34px;
    line-height: 32px;
    background-color: #e5f4ff;
    color: #008ed0;
    border: 1px dashed #008ed0;
    font-size: 14px;
    cursor: pointer;
}

.rili-thing-add i {
    font-size: 12px;
}

.rili-thing-add:hover {
    background-color: #fbfbfb;
    /*background-color:#0b97d4 ;*/
    /*color: white;*/
}

/*周*/

/*月*/
.rili-month-title {
    text-align: right;
    color: #878787;
    padding-bottom: 5px;
}
.rili-month-title span:first-child{
    font-weight: bold;
}
.rili-month-title span:last-child {
    margin-left: 10px;
}

/*列表*/
.rili-table-listing tr:nth-child(2n-1) {
    background-color: #f7f7f7;
}

.rili-table-listing tr:hover .rili-thing-add {
    visibility: visible;
}

.rili-table-listing td:last-child {
    border-left: 0;
}

.rili-listing-do {
    width: 150px;
    padding: 5px;
}

.rili-listing-time1 {
    text-align: left;
    font-weight: bold;
    font-size: 16px;
}

.rili-listing-time2 {
    color: #666;
    font-size: 12px;
    text-align: left;
    margin-bottom: 15px;
}

.rili-table-listing .rili-thing-time {
    white-space: normal;
    overflow: hidden;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.rili-table-listing .rili-thing:last-child {
    margin-bottom: 0;
}

/*右侧分类*/
.rili-classify-list, .rili-classify-add {
    display: block;
    height: 36px;
    line-height: 36px;
    margin-bottom: 2px;
    background-color: #e6f4fd;
    /*background-color: #f0f8ef;*/
    padding: 0 11px;
}

.rili-classify-add {
    text-align: center;
    cursor: pointer;
    margin-top: 10px;
    background-color: #f7f8f9;
    border: 1px dashed #d7d9da;
    color: #008ed0;
}

.rili-classify-add:hover {
    background-color: #fcfcfc;
}

.rili-classify-add i {
    font-size: 12px;
}

.rili-title {
    line-height: 44px;
    font-weight: bold;
    margin-top: 8px;
    font-size: 16px;
}

.rili-classify-list span {
    float: left;
    display: inline-block;
}

.rili-classify-color {
    width: 12px;
    height: 12px;
    margin-right: 12px;
    margin-top: 12px;
    background-color: #007aff;
}

.rili-classify-name {
    width: calc(100% - 100px);
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.rili-classify-list i {
    float: right;
    color: #2b8744;
    margin-left: 9px;
    cursor: pointer;
}

.calendar-cons {
    color: #2b8744;
    padding: 5px;
}

.calendar-time {
    line-height: 30px;
}

.calendar-title {
    font-size: 20px;
    padding: 10px 0;
    line-height: 30px;
    word-break: break-all;
}

.calendar-classify, .calendar-remark, .calendar-ceator {
    padding: 7px 0;
    border-bottom: 1px solid #eee;
    word-break: break-all;
}

.calendar-btn {
    line-height: 28px;
    text-align: center;
    padding-top: 10px;
}

.calendar-btn span {
    border: 1px solid #2b8744;
    color: #2b8744;
    padding: 5px 30px;
    border-radius: 4px;
    cursor: pointer;
}

.calendar-btn span:first-child {
    margin-right: 15px;
    background-color: #2b8744;
    color: white;
}

/*事件弹窗*/
.things-cons {
    padding: 10px 10px 0;
}

.things-info {
    position: relative;
    padding-left: 90px;
    line-height: 36px;
    margin-bottom: 10px;
}

.things-info-title {
    position: absolute;
    top: 0;
    left: 0;
    width: 90px;
    text-align: right;
}

.things-info-cons select {
    width: 100%;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    line-height: 36px;
    margin-bottom: 10px;
}

.things-info-cons input.content,.things-info-cons input.location {
    width: 100%;
    line-height: 36px;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin-bottom: 10px;
    box-sizing: border-box;
}

.things-info-cons input.content:focus {
    outline: none;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #003f87!important;
}
.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: #003f87!important;
}
.btn-success {
    background-color: #003f87!important;
    border-color: #003f87!important;
}
.layui-layer .layui-layer-btn .layui-layer-btn0 {
    background-color: #003f87!important;
    border-color: #003f87!important;
}
.select2-container .select2-selection--single {
    height: 36px;
}

.select2-container--default .select2-selection--single {
    border-color: #e1e1e1;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 34px;
}

.select2-container .select2-selection--single:focus {
    outline: none;
}

.things-info-cons .input-group {
   /* width: 190px;*/
   width:100%;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
    width: 34px;
    top: 6px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b{
    border-width: 6px 5px 0 5px;
    border-color: #b2b2b2 transparent transparent transparent;
}
.things-cons .input-group .form-control[readonly], .things-cons .input-group .input-group-addon {
    background-color: white;
    height: 36px;
    border: 1px solid #e1e1e1;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.things-cons .input-group .form-control[readonly] {
    border-right: 0;
}

.things-cons .input-group .input-group-addon {
    border-left: 0;
}

.things-cons .name-duration {
    margin: 0 25px 0 5px;
    vertical-align: middle;
}

.things-cons .remark {
    max-width: 100%;
    min-width: 100%;
    padding: 5px 10px;
    resize: none;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    line-height: 22px;
}

.things-cons .remark:focus {
    outline: none;
}

/*日历分类*/
.classify-color {
    /*padding: 3px;*/
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    margin-right: 10px;
    cursor: pointer;
}

.classify-color.active {
    color: white;
}

.classify-color-e4445e {
    background-color: rgb(228, 68, 94);
    color: #e4445e;
}

.classify-color-fa682d {
    background-color: rgb(250, 104, 45);
    color: #fa682d;
}

.classify-color-e6b11a {
    background-color: rgb(230, 177, 26);
    color: #e6b11a;
}

.classify-color-70bf64 {
    background-color: rgb(112, 191, 100);
    color: #70bf64;
}

.classify-color-15a79d {
    background-color: rgb(21, 167, 157);
    color: #15a79d;
}

.classify-color-0b97d4 {
    background-color: rgb(11, 151, 212);
    color: #0b97d4;
}

.classify-color-526ace {
    background-color: rgb(82, 106, 206);
    color: #526ace;
}

.classify-color-2689a6 {
    background-color: rgb(38, 137, 166);
    color: #2689a6;
}

.classify-color-8956d4 {
    background-color: rgb(137, 86, 212);
    color: #8956d4;
}

.classify-color-b153ae {
    background-color: rgb(177, 83, 174);
    color: #b153ae;
}

.classify-color-764986 {
    background-color: rgb(118, 73, 134);
    color: #764986;
}

.classify-color-8a8a8a {
    background-color: rgb(138, 138, 138);
    color: #8a8a8a;
}

.classify-color-344750 {
    background-color: rgb(52, 71, 80);
    color: rgb(52, 71, 80);
}

.things-info-cons input[type=number].px {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 200px;
}

.things-info-cons input[type=number]::-webkit-inner-spin-button,
.things-info-cons input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.people-select {
    position: relative;
    cursor: pointer;
}

.people-select input {
    cursor: pointer;
}

.people-select i {
    position: absolute;
    right: 10px;
    top: 0;
    color: #999;
}

/*layer*/
body .calendar-class .layui-layer-btn {
    background-color: #f0f3f5;
    padding: 8px 12px;
}

body .calendar-class .layui-layer-btn a {
    background-color: #f6f6f6;
}

body .calendar-class .layui-layer-btn .layui-layer-btn0 {
    background-color: #2b8744;
    border-color: #2b8744;
    color: white;
}

body .layui-layer-btn .layui-layer-btn0 {
    background-color: #2b8744;
    border-color: #2b8744;
    color: white;
}
.daterangepicker td.active, .daterangepicker td.active:hover{
    background-color: #2b8744;
}
.btn-success, .btn-success:hover, .btn-success:active, .btn-success:focus, .btn-success:active:focus {
    background-color: #2b8744;
    border-color: #2b8744;
    color: white;
}

body .displaynone {
    display: none;
}
.boxShadow{
    -webkit-box-shadow: #e5e5e5 0 2px 2px 2px;
    -moz-box-shadow: #e5e5e5 0 2px 2px 2px;
    box-shadow: #e5e5e5 0 2px 2px 2px;
}
.button {
    display:inline-block;
    margin-left: 15px;
    line-height: 28px;
    height: 28px;
    padding: 0 18px;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    font-size: 14px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    outline: none;
}
.addRc{
    background: #2b8744 none repeat scroll 0 0;
}
.cancleRc{
    background: #c5c5c5;
}
.select2-container {
    max-width: 100%;
    width: 100% !important;
}
.things-info-cons .input-group {
    max-width: 100%;
}

.things-info-cons .createMailDialog-sender{display:inline-block;padding:4px 5px;line-height:1.75; border-radius:4px; border: 1px solid #e8e8e8;}
.things-info-cons .createMailDialog-sender.disabled{color:#999;}

/* 确认添加按钮 */
.things-cons .confirmBox{
    width: 240px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.things-cons .confirmBox div{
    width: 100px;
    margin:0px 4px;
    border: 1px solid #b9b9b9;
    background: #fff;
    border-radius: 3px;
    text-align: center;
    padding: 5px 10px;
    display: inline-block;
    font-size: 13px;
    cursor: pointer;
}

.things-cons .confirmBox .yesBtn{background-color:#0079d8;border: 1px solid #0079d8;color:#fff}

.ui-modal-ft{
    display: none;
}
