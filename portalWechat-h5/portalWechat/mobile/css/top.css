.topContent {
  width: 100%;
  min-height: 341px;
  padding: 28px 20px;
  box-sizing: border-box;
  position: relative;
}
.topContent .topBg {
  position: absolute;
  width: 100%;
  object-fit: fill;
  height: 100%;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 0;
  opacity: 0.57;
  mix-blend-mode: soft-light;
  z-index: 1;
  /* display: none; */
}
.topContent:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: linear-gradient(180deg, #3090f5 0%, #007cff 100%);
  mix-blend-mode: multiply;
  z-index: 0;
}
.topContent .topBox,
.topContent .appNav {
  position: relative;
  z-index: 2;
}
.topContent .topBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.topContent .topBox .motto {
  width: 80%;
  white-space: nowrap;
}
.topContent .topBox .motto img {
  height: 33px;
  object-fit: contain;
  max-width: 100%;
}
.topContent .topBox .motto span {
  color: #fff;
  font-size: 26px;
  /* font-family: 'MyCustomFont'; */
  margin-right: 8px;
}
.topContent .topBox .motto.small span {
  font-size: 18px;
}
.topContent .topBox .searchBox {
  display: flex;
  width: 20%;
  justify-content: flex-end;
}
.topContent .topBox .searchBox input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: 0;
  background-color: transparent;
  vertical-align: middle;
  border: 0;
  border-bottom: 1px solid #ddd;
  transition-duration: 0.5s;
  color: #ddd;
  width: 0;
  font-size: 14px;
  display: none;
}
.topContent .topBox .searchBox img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  cursor: pointer;
}
.topContent .appNav {
  margin-top: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.topContent .blurBox {
  width: 100%;
  height: 70px;
  position: absolute;
  left: 0;
  bottom: 0px;
  right: 0;
  background: linear-gradient(180deg, rgba(239, 241, 251, 0) 0%, #f3f8fa 100%);
  z-index: 1;
}
.topContent .appNav .appItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 25%;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  padding: 10px 0;
  box-sizing: border-box;
}
.topContent .appNav .appItem:after {
  position: absolute;
  content: '';
  width: 1px;
  height: 65%;
  top: 50%;
  right: 1px;
  transform: translateY(-50%);
  background: #fff;
  opacity: 0.35;
}
.topContent .appNav .appItem:last-child:after {
  width: 0;
}
.topContent .appNav .appItem .imgBox {
  position: relative;
}
.topContent .appNav .appItem img {
  height: 35px;
  object-fit: contain;
  max-width: 35px;
}
.topContent .appNav .appItem .title {
  font-size: 14px;
  color: #fff;
  margin-top: 12px;
  display: inline-block;
  max-width: 100%;
  padding: 0 5px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.topContent .appNav .appItem .count {
  position: absolute;
  top: -7px;
  right: -10px;
  width: auto;
  padding: 0 2px;
  border-radius: 10px;
  z-index: 3;
  background: #e25051;
  min-width: 18px;
  text-align: center;
  line-height: 18px;
  height: 18px;
  font-size: 12px;
  color: #fff;
  max-width: 30px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: none;
}

@media (max-width: 400px) {
  .topContent .topBox .motto span {
    font-size: 22px;
  }
  .topContent .topBox .searchBox img {
    width: 40px;
    height: 40px;
  }
}
@media (max-width: 340px) {
  .topContent .topBox .motto span {
    font-size: 20px;
  }
  .topContent .topBox .searchBox img {
    width: 34px;
    height: 34px;
  }
}
