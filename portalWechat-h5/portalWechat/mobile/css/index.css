body{--colorPrimary:#0044b3;min-width:280px;}
*{padding:0;margin:0;box-sizing:border-box}
.mainContent{width:100%;/* padding-bottom:30px; */height: calc(100% - 68px);overflow-y: auto;}
.topContent{width:100%;min-height: 280px;padding:28px 20px;box-sizing:border-box;position:relative;display:flex;flex-direction:column;justify-content:space-between}
.topContent .topBg{position:absolute;width:100%;object-fit:fill;height:100%;top:0;right:0;left:0;bottom:0;z-index:0;opacity:0.57;mix-blend-mode:soft-light;z-index:1}
.topContent:after{content:"";position:absolute;width:100%;height:100%;top:0;right:0;left:0;bottom:0;background: linear-gradient(180deg, #3d6cab 0%, #3d6cab 100%);mix-blend-mode:multiply;z-index: 0;}
.topContent .blurBox{width:100%;height:60px;position:absolute;left:0;bottom:0px;right:0;background: linear-gradient(180deg, rgb(239 241 251 / 0%) 0%, rgb(243 248 250 / 66%) 100%);z-index:1}

.topContent .topBox,
.topContent .appNav{position:relative;z-index:2}
.topContent .topBox{display:flex;justify-content:space-between;align-items:flex-start}
.topContent .topBox .motto{width: 80%;}
.topContent .topBox .motto .userDept{flex:1;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}
.topContent .topBox .motto img{object-fit:cover;width:100%;height:100%;/* border-radius:100%; */overflow:hidden}
.topContent .topBox .motto img[src=""],
.topContent .topBox .motto img:not([src]){opacity:0}
.topContent .topBox .motto span{color:#fff;font-size:15px;font-weight:normal}
.topContent .topBox .motto.small span{font-size:18px}

.topContent .topBox .searchBox{display:flex;justify-content:flex-end;margin-left:15px;display: none;}
.topContent .topBox .searchBox input{-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;background-color:transparent;vertical-align:middle;border:0;border-bottom:1px solid #ddd;transition-duration:0.5s;color:#ddd;width:0;font-size:14px;display:none}
.topContent .topBox .searchBox img{width:48px;height:48px;object-fit:cover;cursor:pointer}
.topContent .appNav{display:flex;justify-content:space-between;align-items:center;margin-bottom: 30px;}
.topContent .appNav > * + *{border-left:1px solid #5985b8}
.topContent .appNav .appItem{display:flex;flex-direction:column;justify-content:center;align-items:center;width:33.3%;cursor:pointer;overflow:hidden;position:relative;padding:10px 0;box-sizing:border-box}
.topContent .appNav .appItem .imgBox{position:relative}
.topContent .appNav .appItem img{height:35px;object-fit:contain;max-width:35px}
.topContent .appNav .appItem .svgImg{width:35px;height:35px}
.topContent .appNav .appItem .svgImg svg{width:100%;height:100%}
.topContent .appNav .appItem .svgImg svg path,.topContent .appNav .appItem .svgImg svg rect,.topContent .appNav .appItem .svgImg svg polygon,.topContent .appNav .appItem .svgImg svg circle{fill:#fff;stroke:transparent}
.topContent .appNav .appItem .title{font-size:14px;color:#fff;margin-top:12px;display:inline-block;max-width:100%;padding:0 5px;box-sizing:border-box;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
.topContent .appNav .appItem .count{position:absolute;top:-7px;right:-10px;width:auto;padding:0 2px;border-radius:10px;z-index:3;background:#e25051;min-width:18px;text-align:center;line-height:18px;height:18px;font-size:12px;color:#fff;max-width:30px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:none}
.topContent .appNav .appItem.mail .tips{position:absolute;height:100%;width:80px;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);line-height:100%;background:rgba(0,0,0,0.45);display:flex;align-items:center;justify-content:center;color:#fff;border-radius:10px;display:none}
.topContent .appNav .appItem.createMail .mailNum{font-size:8px}
.middleContent{padding:20px 20px;box-sizing:border-box;background:url('../images/mbg.png') no-repeat bottom center;background-size: contain;}

/*日历添加*/
.mySchedule-container .mySchedule-content .calButton{display:flex;justify-content:center;align-items:center;height: 38px;transition:0.3s;overflow:hidden}
.mySchedule-container .mySchedule-content .calButton div{width:125px;background:#f3f8fa;border-radius:6px;text-align:center;padding:5px 10px;display:inline-block;font-size:13px;cursor:pointer;color:#007cff;line-height: 2;}
.mySchedule-container .mySchedule-content .calButton .addCal{margin-right:13px;color:#333;background: url(../images/icon_add.png) no-repeat 36px center;background-size: 17%;padding-left: 30px;padding: 0px 0px 0px 34px;font-size: 15px;}
.mySchedule-container .mySchedule-content .calButton .delCal{color:var(--colorPrimary);display: none!important;}
.mySchedule-container .mySchedule-content .calButton .cancelDel{display:none;}
.mySchedule-container .scheduleInfoContent .contentBox{display:none}
.mySchedule-container .scheduleInfoContent .contentBox.active{display:block;}
.mySchedule-container .scheduleInfoContent{padding:0;margin-top:10px;min-height:120px;position:relative;z-index:1}

/*日历item*/
.scheduleList{text-align:left;margin-bottom: 12px;}
.scheduleList .dayInfo{display:none}
.scheduleList .dayInfo.active{display:block;max-height: 233px;overflow-y:auto}
.scheduleList .dayInfo .schedule-item{display:flex;align-items:center;justify-content:flex-start;}
.scheduleList .dayInfo .schedule-item .schedule-left{display:flex;align-items:flex-start;justify-content:flex-start;flex-direction:column;width: calc(100% - 67px);/* flex:1; */border-left: 1px solid #f1f1f1;padding: 5px 0;padding-left: 11px;}
.scheduleList .dayInfo .schedule-item .schedule-del{font-size:10px;white-space:nowrap;padding:3px 5px;background:#d31133;box-sizing:border-box;color:#fff;border-radius:5px;display:none}
.scheduleList .schedule-titleName{display:flex;align-items:center;justify-content:flex-start;width:100%;padding: 8px;background-color:transparent;border-radius: 4px;position: relative;}
.scheduleList .schedule-titleName .schedule-icon{border-radius: 9px;width: 58px;height: 48px;margin-right: 10px;background:var(--colorPrimary);text-align: center;}
.scheduleList .schedule-titleName .schedule-icon svg{margin: auto;width: 20px!important;}
.scheduleList .schedule-titleName .schedule-title{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:calc(100% - 15px);line-height:14px}
.scheduleList .schedule-titleName .schedule-title div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.scheduleList .schedule-titleName .schedule-bg{position:absolute;left:0;top:0;bottom:0;right:0;z-index:99;opacity: 0.1;background: var(--colorPrimary);}
.scheduleList .schedule-timeList{margin-right:10px;width: 57px;line-height:1.5;text-align: right;flex: none;}
.scheduleList .schedule-timeList .schedule-timeList > div{font-size:14px}
.scheduleList .schedule-timeList .schedule-beginTime{color: #333;font-size: 19px;}
.scheduleList .schedule-timeList .schedule-endTime{color:#818181}
.scheduleList .schedule-locat-item{display:flex;justify-content:space-between;width:100%;align-items:center}
.scheduleList .schedule-location{display:flex;align-items:center;justify-content:center;width:calc(100% - 50px);margin-top:8px;color: #818181;}
.scheduleList .schedule-location svg{margin-top:-2px;width:12px;height:12px}
.scheduleList .schedule-location span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;font-size:13px;margin-left:4px;display:inline-block;line-height:1}
.scheduleList .schedule-time span{font-size:11px}
.scheduleItem{}
.scheduleItem.active{display:block;padding-bottom: 10px;}
.scheduleItem .schedule-arrow{display:flex;align-items:center;justify-content:center;flex-direction:column;margin-top:3px;position:absolute;left:calc(50% - 27px);bottom:0;display: none;}
.scheduleItem .schedule-arrowBtn{padding:5px 20px}
.scheduleItem .schedule-arrow img{height:12px;transition:0.5s}
.scheduleItem .schedule-arrowBtn.active img{transform:rotate(180deg)}
.scheduleItem .calButton.active{/* height: 38px; */}
.middleContent .noData{/* padding:20px; */text-align:center;width:100%;box-sizing:border-box;font-size:14px;color:#252525}
.middleContent .appModule{width:100%;margin-top:20px}
.middleContent .moduleTitleBox{display:flex;justify-content:space-between;align-items:center}
.middleContent .moduleTitleBox .titleTab{display:flex;justify-content:flex-start;align-items:center;max-width:calc(100% - 30px)}
.middleContent .moduleTitleBox .titleTab .tabItem{display:flex;color:#9d9d9d;font-size:18px;justify-content:center;align-items:center;font-weight: 700;}
.middleContent .moduleTitleBox .titleTab .tabItem img{width:15px;object-fit:cover;max-height:15px;display:none}
.middleContent .moduleTitleBox .titleTab .tabItem:first-child{margin-right:20px}
.middleContent .moduleTitleBox .titleTab .tabItem.active{color:#252525}
.middleContent .moduleTitleBox .titleTab .tabItem.active img{display:inline-block}
.middleContent .moreBtn{color:#eb8124;font-size:14px}

/*apps*/
.middleContent .appModule .appList{width:100%;background:#fff;position:relative;box-shadow:0px 0 8px 0px rgba(0,33,123,0.102);border-radius:12px 12px 12px 12px;padding:15px 10px;box-sizing:border-box;margin-top:20px;overflow:hidden;position:relative;height:245px}
.middleContent .appModule .appList .appContentBg{position:absolute;min-width:150px;object-fit:contain;max-height:180px;top:20px;left:50%;transform:translateX(-50%);z-index:0}
.middleContent .appModule .appList .appListInfo{display:none;position:relative;box-sizing:border-box}
.middleContent .appModule .appList .appListInfo.current{display:block}
.middleContent .appModule .appList .appListInfo.fwdt .appPage,.middleContent .appModule .appList .appListInfo.recentlyUsedApps .appPage,.middleContent .appModule .appList .appListInfo.dingtalkApps .appPage,.middleContent .appModule .appList .appListInfo.myFavourite .appPage{display:grid;grid-template-columns:repeat(4,minmax(0px,1fr));gap:0px}
.middleContent .appModule .appList .swiper-slide.morePage{height:215px}
.middleContent .appModule .appList .swiper-pagination{bottom:-5px}
.middleContent .appModule .appList .swiper-pagination span{border:1px solid #007cff}
.middleContent .appModule .appList .swiper-pagination .swiper-pagination-bullet-active{background:#007cff}
.middleContent .appModule .appList .appItem{display:flex;flex-direction:column;align-items:center;padding:10px 5px;box-sizing:border-box;gap:5px;min-height:107px}
.middleContent .appModule .appList .appItem img{width:45px;object-fit:cover;max-height:45px;border-radius:10px}
.middleContent .appModule .appList .appItem .svgImg{width:30px;height:30px;margin-bottom:5px}
.middleContent .appModule .appList .appItem .svgImg svg{width:100%;height:100%}
.middleContent .appModule .svgImg svg path,.middleContent .appModule .svgImg svg rect,.middleContent .appModule .svgImg svg polygon,.middleContent .appModule .svgImg svg circle{fill:#007cff;stroke:transparent}
.middleContent .appModule .appList .appItem span{display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:2;color:#232323;font-size:12px;word-break:break-all;text-align:center}
.middleContent .appModule .appList .appItem .title{color:#232323;font-size:12px;max-width:100%;display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:2;word-break:break-all;text-align:center}

/*校内通知*/
.middleContent .specialTopic{margin:20px 0}
.middleContent .specialTopic .specialTop_tab{display:flex;margin:10px 0;flex-wrap: wrap;position:relative;/* height: 38px; *//* overflow: hidden; */transition: height 0.3s ease;/* width: calc(100% - 30px); */padding-right: 30px;}
.middleContent .specialTopic .specialTop_tab .topicItem{padding: 2px 6px;background-color:transparent;line-height:1.75;border-radius:16px;white-space: nowrap;/* margin-bottom: 4px; */color: #999999;}
.middleContent .specialTopic .specialTop_tab .topicItem.active{background-color:#0061AA;color:#fff;}
.middleContent .specialTopic .specialTop_tab .addToggle{position:absolute;right: 0;top: 2px;width:25px;height:25px;border-radius:50%;background-color:#fff;background-image:url('../images/up.png');background-position:center;background-repeat:no-repeat;background-size:50%;}
.middleContent .specialTopic .specialTop_tab .addToggle.active{transform: rotate(180deg);}
.middleContent .specialTopic .topicList{}
.middleContent .specialTopic .topicList .loading,
.middleContent .specialTopic .topicList .noData{padding:15px;min-height:200px; text-align:center;background-color:#fff;border-radius:4px;}
.middleContent .specialTopic .topicList .topicItem{margin-bottom: 15px;background-color:#fff;border-radius:4px;}
.middleContent .specialTopic .topicList .topicItem .item-box{display:inline-block;width:100%;padding: 15px 15px;}
.middleContent .specialTopic .topicList .topicItem .news-time{line-height: 1.5;color:#0061AA; margin-bottom:5px;}
.middleContent .specialTopic .topicList .topicItem .news-title{font-size:15px; line-height:1.5;color:#333;}

/*日程-弹窗*/
.iframe-schedule .ui-modal-cnt{width:90%;max-width:350px;border-radius:10px}
.iframe-schedule .ui-modal-cnt header{background:#f9fafc}
.iframe-schedule .ui-modal-ft{display:none}
.iframe-schedule .ui-modal-cnt .ui-modal-bd{max-height:100%;border-bottom:0px solid transparent !important}
.iframe-schedule .ui-modal-cnt .ui-modal-bd iframe{
    height: 400px;
}

/*日程-详细*/
.ui-page.calDetail .ui-modal-cnt{width:80% !important;max-height:45% !important;max-width:80% !important;height:auto !important;min-height:auto !important}
.ui-page.calDetail .ui-modal-cnt header{background:#f9fafc}
.ui-page.calDetail .ui-modal-cnt .ui-modal-bd{top:44px !important;padding:5px 20px}
.ui-page.calDetail .ui-modal-cnt .ui-modal-bd p{padding:8px 0}
.ui-page.calDetail .edit-btn{display:flex;align-items:center;justify-content: flex-end;}
.ui-page.calDetail .editCal{background:#0079d8;border-radius:3px;text-align:center;padding: 5px 15px;font-size:13px;cursor:pointer;color:#fff;margin-right:10px}
.ui-page.calDetail .delCal,.ui-page.calDetails .delCal{background:#f3f8fa;border-radius:3px;text-align:center;padding:5px 10px;font-size:13px;cursor:pointer;color:#007cff}

.delCalDialog .ui-modal-bd > div{text-align:center;padding:15px 0}
.delCalDialog .ui-modal-cnt{width:90%;max-width:350px;border-radius:10px}
.delCalDialog .ui-modal-cnt header{background:#f9fafc}

/*tab*/
.middleContent .common{position:relative;filter:drop-shadow(0px 2px 4px rgba(0,33,123,0.102))}
.middleContent .common .tabBox{width:100%;min-width:170px;position:relative;border-radius:12px;box-shadow: 0px 0 8px 0px rgb(185 196 225 / 7%);background:#fff}
.middleContent .common .tabBox:after{display:none;content:'';position:absolute;right:-6px;bottom:-1px;width:12px;height:45px;border-top-right-radius:12px;background:linear-gradient(to bottom,#f1f5ff 55%,#fff);transform:skewX(15deg)}
.middleContent .common .tabBox.hideAfter:after{background:#fff}
.middleContent .common .tab-list{background:linear-gradient(180deg,#fff0f0,#fff3f3,#ffffff)}
.middleContent .common .tab-list{display:flex;position:relative;z-index:2;border-radius:12px 12px 0 0;overflow:hidden;background:linear-gradient(to bottom,#f1f5ff 55%,#fff);border-top-left-radius:12px;min-height:44px;overflow-x:auto;padding-bottom:5px}
.middleContent .common .tab-list .tab-item{height:44px;display:flex;justify-content:center;align-items:center;font-size:16px;color:#252525;position:relative;padding:0 1.2em;white-space:nowrap;position:relative}
.middleContent .common .tab-list .tab-item .taskNum{background-color:rgb(239,83,80);color:rgb(255,255,255);top:0px;position:absolute;right:2px;top:5px;z-index:10;transform-origin:100% 0%;padding:0px 6px;font-size:13px;border-radius:10px}
.middleContent .common .tab-list .tab-item.tab-selected div{font-size:18px;font-weight: 700;}
.middleContent .common .tab-list .tab-item.tab-selected div:after{content:'';position:absolute;width:34%;height:4px;border-radius:2px;background:var(--colorSecond);bottom:0px;left:50%;transform:translateX(-50%)}
.middleContent .common .tab-con .tab-item{display:none;font-size:16px;line-height:50px;padding: 10px 15px 0;}
.middleContent .common .tab-con .tab-item.tab-selected{display:block;text-align:center}
.middleContent .common .tab-con .more{padding-bottom:15px;text-align:center}
.middleContent .common .tab-list .tab-selected{opacity:1;background:#ffffff;border-radius:12px 12px 0 0}
.middleContent .common .tab-list .tab-selected::before{content:'';position:absolute;left:-6px;bottom:0;width:12px;height:100%;border-top-left-radius:12px;background-color:#fff;transform:skewX(-15deg)}
.middleContent .common .tab-list .tab-selected::after{content:'';position:absolute;right:-6px;bottom:0;width:12px;height:100%;border-top-right-radius:12px;background-color:#fff;transform:skewX(15deg)}
.middleContent .arm-calendar-default .calendar-wrap{padding:10px 0px 10px;overflow:hidden}
.middleContent .arm-calendar .calendar-days-box{overflow:visible}
.middleContent .arm-calendar-default .calendar-days .grid-curday:after{bottom:-30px;background-color: var(--colorSecond);}

.todo-list{}
.todo-list .todo-item{margin-bottom:10px;text-align:left}
.todo-list .todo-item:nth-child(3n+1) .todo-item-box{background-color:rgb(239 240 250 / 50%);border-left:4px solid #FF915A}
.todo-list .todo-item:nth-child(3n+2) .todo-item-box{background-color:rgb(255 239 231 / 50%);border-left:4px solid #42E6D6}
.todo-list .todo-item:nth-child(3n+3) .todo-item-box{background-color:rgb(238 248 247 / 50%);border-left:4px solid #6572F7}
.todo-list .todo-item .todo-item-box{padding:5px 15px;border-left:2px solid red;border-radius:4px}
.todo-list .todo-item .todo-item-box .todo-title{font-size: 15px;font-weight:bold;line-height:1.75;color:#333333;/* margin-bottom:5px; */}
.todo-list .todo-item .todo-item-box .todo-content{font-size:14px;line-height:1.5;color:#999;white-space:normal}
.todo-list .todo-item .todo-item-box .todo-content span{margin-right:20px;white-space:nowrap}