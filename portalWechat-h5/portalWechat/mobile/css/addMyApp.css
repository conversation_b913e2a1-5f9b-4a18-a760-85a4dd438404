*{
    margin: 0;
    padding: 0;
}
.main{
    min-width:280px;
    width: 100%;
    padding:15px 20px;
    box-sizing: border-box;
    height: 100%;
    position: relative;
}
.main .addModule,.main .searchModule{
    height: 100%;
}
.main .addModule .appModContent,.main .searchModule .searchModContent{
    height: calc(100% - 40px);
    overflow-y: auto;
}
.main .hideModule{
 display: none;
}
.main .addModule .top{
 display: flex;
 align-items: center;
 justify-content: space-between;
}
.main .addModule .top .left .title{
    font-size: 16px;
    font-weight: bold;
    color: rgba(0,0,0,0.7);
}
.main .addModule .top .left .extra{
    font-size: 14px;
    color: #9e9e9e;
}
.main .addModule .top svg{
    width: 22px;
    height: 22px;
    vertical-align: middle;
}
.main .addModule .top svg path{
    fill: rgba(0,0,0,0.54);
}
.main .addModule .btnBox{
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.main .addModule .btnBox .svgImg{
    box-sizing: border-box;
    width: 38px;
    height: 38px;
    text-align: center;
    line-height: 38px;
}
.main .appGrid{
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    gap: 12px;
}
.main  .appGrid .appItem{
    display: flex;
    align-items: center;
    flex-direction: column;
}
.main  .appGrid .appItem .imgBox{
    position: relative;
    width: auto;
    padding: 4px;
    border-radius: 8px;
    /* background: #007CFF; */
    box-sizing: border-box;
    display: flex;
}
.main  .appGrid .appItem .imgBox img:first-child{
    max-width: 48px;
    height: 48px;
    object-fit: contain;
}
.main  .appGrid .appItem .svgImg{
    width: 30px;
    height: 30px;
    padding: 5px;
    border-radius: 8px;
    background-color: var(--colorPrimary);
}
.main  .appGrid .appItem .svgImg svg{
    width: 100%;
    height: 100%;
}
.main  .appGrid .appItem .svgImg svg path,.main  .appGrid .appItem .svgImg svg rect, 
.main  .appGrid .appItem .svgImg svg polygon,.main  .appGrid .appItem .svgImg svg circle{
    fill: #fff;
    stroke: transparent;
  }
.main .appGrid .appItem .imgBox .btnImg{
    position: absolute;
    right: -8px;
    top: -7px;
}
.main .btnImg{
    width: 15px;
    object-fit: cover;
    max-height: 15px;
}
.main .appGrid .appItem .name{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-top: 5px;
    word-break: break-all;
}
.main .addModule .myApps{
    padding: 15px 0;
}
.main .addModule .allApps{
    border-top: 1px solid #d2d7db;
    margin-top: 10px;
}
.main .addModule .allApps .categoryBox{
    width: 100%;
    overflow-x: auto;
}
.main .addModule .allApps .categoryBox::-webkit-scrollbar{display: none;}
.main .addModule .allApps .categoryBox{scrollbar-width: none;}
.main .addModule .allApps .categoryBox{-ms-overflow-style: none;}
.main .addModule .allApps .categoryBox .tabBox{
    display:flex;
    flex-wrap: nowrap;
}
.main .addModule .allApps .categoryBox .tabBox .item{
    padding: 10px 8px;
    font-size: 16px;
    transition: all 0.4s ease-in-out;
    white-space: nowrap;
    position: relative;
    color: rgba(0, 0, 0, 0.7);
}
.main .addModule .allApps .categoryBox .tabBox .item.active{
    font-weight: bold;
}
.main .addModule .allApps .categoryBox .tabBox .item.active:after{
    position: absolute;
    content: '';
    width: 55%;
    height: 3px;
    background: #F97C37;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}
.main .addModule .tabBox{
    padding: 8px 0px;
}
.main .addModule .allApps .appList{
    margin-top: 10px;
    margin-bottom: 10px;
}
.main .searchModule .top{
    display: flex;
    align-items: center;
}
.main .searchModule .top svg{
    width: 22px;
    height: 22px;
    vertical-align: middle;
}
.main .searchModule .top svg path{
    fill: rgba(0,0,0,0.54);
}
.main .searchModule .top .inputBox{
    width: calc(100% - 85px);
    position: relative;
    border: 0px;
    width: 100%;
    height: 34px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 20px;
}
.main .searchModule .top input{
    outline: 0;
    border: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    padding: 0 16px;
    box-sizing: border-box;
    color: rgba(0,0,0,0.7);
    font-size: 15px;
}
.main .searchModule .top input::placeholder{
    color: rgba(0,0,0,0.35);
}
.main .searchModule .top .searchText{
    display: inline-block;
    width: 60px;
    text-align: center;
    vertical-align: middle;
    font-size: 14px;
    color: #2f65ae;
}
.main .searchModule .searchModContent{
    padding-top: 20px;
}

/* .main .nodata{
    width: 100%;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 5px 0;
    box-sizing: border-box;
} */
/* .searchModule .nodata{
    display: none;
} */
.main .nofavourite{
    width: 100%;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 5px 0;
    box-sizing: border-box;
    /* display: none; */
}

.main .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 10px 0;
}
.main .nodataBox .noDataImg{
    width: 60px;
    height: 46px;
}
.main .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}

@media (max-width: 350px) {
    .main .addModule .top .left .extra{
        font-size: 10px;
    }
    .main .addModule .btnBox .svgImg{
        width: 30px;
        height: 30px;
        line-height: 30px;
    }
  }