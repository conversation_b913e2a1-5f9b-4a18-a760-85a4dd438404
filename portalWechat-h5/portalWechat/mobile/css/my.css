.mainContent {
    width: 100%;
    padding: 20px;
    height: calc(100% - 68px);
    overflow-y: auto;
}

.topContent {
    width: 100%;
    /* min-height: 280px; */
    /* padding:28px 20px; */
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between
}

.topContent .topBg {
    position: absolute;
    width: 100%;
    object-fit: fill;
    height: 100%;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 0;
    opacity: 0.57;
    mix-blend-mode: soft-light;
    z-index: 1
}

.topContent .topBox,.topContent .appNav {
    position: relative;
    z-index: 2
}

.topContent .topBox {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background: #0967ac url(../images/topbg.png) no-repeat center top;
    background-size: cover;
    border-radius: 10px;
    padding: 20px 20px;
}

.topContent .topBox .motto {
    width: 80%;
}

.topContent .topBox .motto .userDept {
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden
}

.topContent .topBox .motto img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    border-radius: 100%;
    overflow: hidden;
}

.topContent .topBox .motto img[src=""],.topContent .topBox .motto img:not([src]) {
    opacity: 0
}

.topContent .topBox .motto.small span {
    font-size: 18px
}

.topContent .topBox .searchBox {
    display: flex;
    justify-content: flex-end;
    margin-left: 15px;
    display: none;
}

.topContent .topBox .searchBox input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: 0;
    background-color: transparent;
    vertical-align: middle;
    border: 0;
    border-bottom: 1px solid #ddd;
    transition-duration: 0.5s;
    color: #ddd;
    width: 0;
    font-size: 14px;
    display: none
}

.topContent .topBox .searchBox img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    cursor: pointer
}

.topContent .blurBox {
    width: 100%;
    height: 60px;
    position: absolute;
    left: 0;
    bottom: 0px;
    right: 0;
    background: linear-gradient(180deg,rgb(239 241 251 / 0%) 0%,#f3f8fa 100%);
    z-index: 1
}

.topContent .topBox .motto {
    display: flex;
    background: none;
}

.user-img {
    width: 75px;
    height: 75px;
    margin-right: 20px;
}

.motto .user-img {
    border: 2px solid #ffffff;
    border-radius: 100%;
    width: 80px;
    height: 80px;
    flex: none;
    background-color: #fff;
}

.user-box-tec {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    flex: 1;
}

.user-box-stu {
    display: none;
}

.user-box-item {
    margin-bottom: 3px;
    display: flex;
    flex-wrap: wrap;
}

.user-box-item .userName {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
}

.user-box-item .meta {
    font-size: 14px;
    color: #fff;
    display: flex;
    margin-right: 10px;
}

.user-box-item .meta span {
    white-space: nowrap;
}

.middleContent {
    /* padding:20px 20px; */
    box-sizing: border-box
}

.middleContent .appModule {
    padding: 8px 0;
    /* margin-top: 15px; */
}

.middleContent .moduleTitleBox {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.middleContent .moduleTitleBox .titleTab {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    max-width: calc(100% - 30px)
}

.middleContent .moduleTitleBox .titleTab .tabItem {
    display: flex;
    color: #9d9d9d;
    font-size: 18px;
    justify-content: center;
    align-items: center
}

.middleContent .moduleTitleBox .titleTab .tabItem img {
    width: 15px;
    object-fit: cover;
    max-height: 15px;
    display: none
}

.middleContent .moduleTitleBox .titleTab .tabItem:first-child {
    margin-right: 20px
}

.middleContent .moduleTitleBox .titleTab .tabItem.active {
    color: #252525
}

.middleContent .moduleTitleBox .titleTab .tabItem.active img {
    display: inline-block
}

.middleContent .moreBtn {
    color: #eb8124;
    font-size: 14px
}

/*apps*/
.middleContent .appModule .appList {
    width: 100%;
    background: #fff;
    position: relative;
    box-shadow: 0px 0 8px 0px rgba(0,33,123,0.102);
    border-radius: 12px 12px 12px 12px;
    padding: 15px 10px;
    box-sizing: border-box;
    margin-top: 15px;
    overflow: hidden;
    position: relative;
    min-height: 245px;
}

.middleContent .appModule .appList .appContentBg {
    position: absolute;
    min-width: 150px;
    object-fit: contain;
    max-height: 180px;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 0
}

.middleContent .appModule .appList .appListInfo {
    /* display:none; */
    position: relative;
    box-sizing: border-box
}

.middleContent .appModule .appList .appListInfo.current {
    display: block
}

.middleContent .appModule .appList .appListInfo.fwdt .appPage,.middleContent .appModule .appList .appListInfo.recentlyUsedApps .appPage,.middleContent .appModule .appList .appListInfo.dingtalkApps .appPage,.middleContent .appModule .appList .appListInfo.myFavourite .appPage {
    display: grid;
    grid-template-columns: repeat(4,minmax(0px,1fr));
    gap: 0px
}

.middleContent .appModule .appList .swiper-slide.morePage {
    height: 215px
}

.middleContent .appModule .appList .swiper-pagination {
    bottom: -5px
}

.middleContent .appModule .appList .swiper-pagination span {
    border: 1px solid #0044b3
}

.middleContent .appModule .appList .swiper-pagination .swiper-pagination-bullet-active {
    background: #0044b3
}

.middleContent .appModule .appList .appItem {
    display: flex;
    flex-direction: column;
    padding: 10px 5px;
    box-sizing: border-box;
    gap: 5px;
    min-height: 107px;
    position: relative;
    text-align: center;
    align-items: center;
}

.middleContent .appModule .appList .appItem .appBox {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.middleContent .appModule .appList .appItem img {
    width: 45px;
    object-fit: cover;
    max-height: 45px;
    border-radius: 10px
}

.middleContent .appModule .appList .appItem .btnImg {
    position: absolute;
    right: 22px;
    /* top: -7px; */
}

.middleContent .appModule .appList .appItem .btnImg {
    width: 15px;
    object-fit: cover;
    max-height: 15px;
}

.middleContent .appModule .appList .appItem.addApps .btnImg {
    display: none
}

.middleContent .appModule .appList .appItem .svgImg {
    width: 30px;
    height: 30px;
    /* margin-bottom: 5px; */
    padding: 8px;
    border-radius: 8px;
    background-color: var(--colorPrimary);
}

.middleContent .appModule .appList .appItem .svgImg svg {
    width: 100%;
    height: 100%
}

.middleContent .appModule .svgImg svg path,.middleContent .appModule .svgImg svg rect,.middleContent .appModule .svgImg svg polygon,.middleContent .appModule .svgImg svg circle {
    fill: #fff;
    stroke: transparent
}

.middleContent .appModule .appList .appItem span {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    color: #232323;
    font-size: 12px;
    word-break: break-all;
    text-align: center;
    margin-top: 5px;
}

.middleContent .appModule .appList .appItem .title {
    color: #232323;
    font-size: 12px;
    max-width: 100%;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
    text-align: center
}

.iframe-addApps header, .iframe-addApps .ui-modal-hd {
    display: none;
}

.iframe-addApps .ui-modal-cnt {
    width: 100%;
    max-width: 100%;
}

.iframe-addApps .ui-modal-ft {
    display: none;
}

.iframe-addApps .ui-modal-cnt .ui-modal-bd {
    max-height: 100%;
    border-bottom: 0px solid transparent !important;
    border-top: 0;
    padding: 0 0px;
}

.iframe-addApps .ui-modal-cnt .ui-modal-bd iframe {
    height: 90vh;
    box-sizing: border-box;
    padding-bottom: 20px;
}

.iframe-addApps i.ui-modal-close {
    position: absolute;
    top: 7px;
    right: 7px;
    width: 28px;
    height: 32px;
}

.iframe-addApps i.ui-modal-close:before {
    top: 0px;
    right: 0px;
}

.iframe-addApps .ui-modal-cnt {
    position: relative;
    top: 10vh;
    border-radius: 20px 20px 0 0;
}
