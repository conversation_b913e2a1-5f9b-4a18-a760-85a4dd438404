body,html{
    font:62.5% "Microsoft yahei",arial,sans-serif;
}
#appInfo p{
    line-height: 35px;
}
#appInfo {
    margin: 10px 0px;
}
.ui-mylist-sc-g{
    background: url('../img/sc-1.png') no-repeat center 0;
    background-size: 28px 28px;
    height: 30px;
    animation: clear 1s forwards;
}

.ui-mylist-sc-y{
    background: url('../img/sc-y.png') no-repeat center 0;
    background-size: 32px 32px;
    animation: add 1s forwards;
    height: 30px;
}
@keyframes add {
    from {transform: scale(1.5,1.5);}
    to {transform: scale(1,1);}
}
@keyframes clear {
    to {transform: scale(1,1);}
    from {transform: scale(0.5,0.5);}
}
.ui-mylist-pl{
    background: url('../img/pl-1.png') no-repeat center 0;
    background-size: 28px 28px;
    height: 30px;
}
.ui-mylist-zx{
    background: url('../img/zx-1.png') no-repeat center 0;
    background-size: 28px 28px;
    height: 30px;
}
.ui-mylist-btnbar {
    position: fixed;bottom: 0;z-index: -1;display:-webkit-box;width:100%;padding-top: 10px;padding-bottom: 10px;background-color: #fff;font-size: 1.7rem;
}
.ui-mylist-btnbar li {
    -webkit-box-flex:1;padding: 5px;text-align: center;
}
.ui-mylist-btnbar img {
    width: 24px;height: 20px;margin-right: 5px;
}
.ui-mylist-padding {
    padding: 5px 20px;
}

/*.ui-mylist-sc:after{*/
    /*content: url(../img/sc.png);*/
    /*display: block;*/
/*}*/
