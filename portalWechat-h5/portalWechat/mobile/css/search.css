@charset "UTF-8";
.clear {
	width: 100%;
	height: 0;
	line-height: 0;
	font-size: 0;
	overflow: hidden;
	clear: both;
	display: block;
	_display: inline;
}

.none {
	display: none;
}

.hidden {
	visibility: hidden;
}

.clearfix:after {
	clear: both;
	content: ".";
	display: block;
	height: 0;
	visibility: hidden;
}

.clearfix {
	display: block;
	*zoom: 1;
}
body{
	background: #f3f8fa;
	padding: 0;
	margin: 0;
	overflow: hidden;
    min-width: 280px;
}
.searchContent{
	width: 100%;
	margin: 0 auto;
	padding: 16px 20px;
	box-sizing: border-box;
	margin-top: -260px;
	position: relative;
	z-index: 2;
	height: calc(100% - 158px);
}
.searchContent .searchBox{
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 15px;
}
.searchContent .searchBox .searchBtn{
	width: 40px;
	text-align: center;
}
.searchContent .searchBox .inputBox{
	width: calc(100% - 40px);
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    
    box-sizing: border-box;
	padding: 8px 15px;
    border-radius: 12px;
    background: rgba(245, 247, 255,0.3);
}
.searchContent .searchBox .inputBox .svgImg{
	display: flex;
	justify-content: center;
	align-items: center;
}
.searchContent .searchBox .inputBox .svgImg svg{
	width: 16px;
	height: 16px;
	vertical-align: middle;
}
.searchContent .searchBox .inputBox .svgImg.searchIcon{
	margin-right: 8px;
}
.searchContent .searchBox .inputBox .svgImg.searchIcon path{
	fill: #fff
	
}
.searchContent .searchBox .inputBox .svgImg.cancelIcon{
	padding: 0px 5px;
	box-sizing: border-box;
	position: absolute;
	right: 5px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
	height: 38px;
}
.searchContent .searchBox .inputBox .svgImg.cancelIcon path{
	/* fill: rgba(0, 0, 0, 0.54); */
	fill: #fff;
}
.searchContent .searchBox .inputBox input{
	height: 22px;
	line-height: 22px;
	background: transparent;
	outline: 0;
	border: 0;
	width: calc(100% - 40px);
	font-size: 15px;
	color: #fff;
	/* padding: 4px 10px; */
}
.searchContent .searchBox .inputBox input::placeholder{
	/* color: #ccc; */
	color: #fff;
}


.searchContent .searchBox .inputBox .svgImg.cancelIcon.hideBtn{
    display: none;
}
.searchContent .searchBox .searchBtn{
	font-size: 14px;
    text-transform: uppercase;
    /* color: rgb(47, 101, 174); */
	color: #fff;
    width: 40px;
    margin: 0px;
    text-decoration: none;
	white-space: nowrap;
	box-sizing: border-box;
}
.searchContent .mainContent{
	position: relative;
	background: #fff;
	padding: 15px;
	border-radius: 10px;
	height: calc(100% - 90px);
}
.searchContent .mainContent .searchTitle{
	width: 100%;
}
.searchContent .mainContent .searchTitle img{
	height: 20px;
	object-fit: contain;
	max-width: 100%;
}
.searchContent .mainContent .hotSearch{
	padding: 12px 0;
	box-sizing: border-box;
	width: 100%;
	height: calc(100% - 15px);
	overflow-y: auto;
}
.searchContent .mainContent .hotSearch .item{
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid rgb(230, 236, 242);
}
.searchContent .mainContent .hotSearch .item .hotTitle{
	display: flex;
	justify-content: flex-start;
	align-items: center;
	width: 100%;
	/* 有右侧人气值时 */
	/* width: calc(100% - 95px); */ 
}
.searchContent .mainContent .hotSearch .item .hotTitle span:first-child{
	font-size: 15px;
	color: rgba(0, 0, 0, 0.7);
	display: inline-block;
	width: 20px;
	text-align: right;
}
.searchContent .mainContent .hotSearch .item .hotTitle span:last-child{
	font-size: 14px;
	color: rgba(0,0,0,0.7);
	margin-left: 8px;
	display: inline-block;
	max-width: calc(100% - 45px);
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.searchContent .mainContent .hotSearch .item:first-child .hotTitle span:first-child{
	color: rgb(224, 70, 68);
	/* font-size: 20px; */
}
.searchContent .mainContent .hotSearch .item:nth-child(2) .hotTitle span:first-child{
	color: rgb(237, 112, 45);
	/* font-size: 20px; */
}
.searchContent .mainContent .hotSearch .item:nth-child(3) .hotTitle span:first-child{
	color:rgb(240, 146, 53);
	/* font-size: 20px; */
}
.searchContent .mainContent .hotSearch .item .popularity{
	max-width: 90px;
	
}
.searchContent .mainContent .hotSearch .item .popularity span{
	vertical-align: middle;
	font-size: 14px;
    color: rgb(153, 153, 153);
}
.searchContent .mainContent .hotSearch .item .popularity span:last-child{
	display: inline-block;
	max-width: calc(100% - 42px);
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 13px;
}
/* .searchContent .mainContent .hotSearch .nodata{
	width: 100%;
	text-align: center;
	padding: 10px;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.7);
	box-sizing: border-box;
} */
.searchContent .mainContent .searching{
	border-radius: 8px;
    background: rgb(255, 255, 255);
    box-shadow: rgba(0, 33, 123, 0.1) 0px 3px 12px 0px;
    padding-left: initial;
    padding-right: initial;
    padding-top: 12px;
    padding-bottom: 12px;
    display: block;
}
.searchContent .mainContent .searching .titleBox{
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding: 8px 0 8px 20px;
}
.searchContent .mainContent .searching .titleBox img{
	width: 14px;
	object-fit: cover;
	margin-right: 5px;
}
.searchContent .mainContent .searching .titleBox span{
	color: rgb(37, 37, 37);
	font-size: 16px;
}
.searchContent .mainContent .searching .searchIngInfo{
	display: flex;
    flex-flow: wrap;
    align-items: flex-start;
    -webkit-box-pack: start;
    justify-content: flex-start;
}
.searchContent .mainContent .searching .searchIngInfo .searchIngItem{
	width: 50%;
    padding: 6px 20px;
   box-sizing: border-box;
    font-weight: 400;
    font-size: 14px;
    color: rgb(102, 102, 102);
	overflow:hidden;
	white-space:nowrap;
	text-overflow: ellipsis;
}
.searchContent .mainContent .searching .searchIngInfo .searchIngItem:nth-child(2n-1) {
    border-right: 1px solid rgb(230, 236, 242);
}

.searchContent .mainContent .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
}
.searchContent .mainContent .nodataBox .noDataImg{
    width: 80px;
    height: 60px;
}
.searchContent .mainContent .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}