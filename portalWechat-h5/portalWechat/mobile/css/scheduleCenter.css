* {
  padding: 0;
  margin: 0;
}
body {
  background: #f3f8fa;
  overflow: hidden;
}
.scheduleContent {
  width: 100%;
  margin: 0 auto;
  padding: 16px 20px 0;
  box-sizing: border-box;
  margin-top: -208px;
  position: relative;
  z-index: 2;
  height: calc(100% - 68px);
}
.scheduleContent .scheduleContentInfo {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  border-radius: 10px;
  padding: 20px 0px;
  background: #fff;
  min-height: 200px;
  height: 100%;
}
.scheduleContent .scheduleBgBox {
  position: relative;
  padding: 0 20px;
  box-sizing: border-box;
  min-height: 260px;
}

.scheduleContent .scheduleBgBox .scheduleBg {
  position: absolute;
  right: 0;
  bottom: -50px;
  width: 50%;
  object-fit: cover;
  max-width: 190px;
  z-index: 0;
}
.scheduleContent #scheduleDate {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}
.scheduleContent #scheduleDate .calendar-toolbar {
  background: transparent;
  color: #333333;
  margin-bottom: 15px;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-title {
  height: 34px;
  line-height: 34px;
  border: 1px solid #d1d1d1;
  border-radius: 3px;
  margin: 0 48px;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-tool {
  height: 34px;
  line-height: 34px;
  border: 1px solid #d1d1d1;
  border-radius: 3px;
  margin-top: -17px;
  width: 34px;
  text-align: center;
  box-sizing: border-box;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-tool i {
  height: 100%;
  line-height: 100%;
}
.scheduleContent #scheduleDate .calendar-toolbar .dateInfo {
  text-align: center;
  font-size: 15px;
  font-family: MicrosoftYaHei;
}
.scheduleContent #scheduleDate .calendar-toolbar .ui-icon-prev:before,
.scheduleContent #scheduleDate .calendar-toolbar .ui-icon-next:before {
  color: rgba(51, 51, 51, 0.45);
  font-size: 20px;
  vertical-align: middle;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-grid {
  color: #000000;
}
.scheduleContent #scheduleDate .ui-border-l,
.scheduleContent #scheduleDate .ui-border-b,
.scheduleContent #scheduleDate .ui-border-r {
  border: 0;
}
.scheduleContent #scheduleDate .ui-border-l,
.ui-border-r,
.ui-border-lr,
.scheduleContent #scheduleDate .ui-border-t,
.ui-border-b,
.ui-border-tb {
  background-image: none;
  font-family: MicrosoftYaHei;
}
.scheduleContent #scheduleDate .arm-calendar-default .grid-curday .grid {
  background:var(--colorPrimary);
  border-radius: 50%;
}
.scheduleContent
  #scheduleDate
  .arm-calendar-default
  .calendar-grid
  .day-number {
  font-size: 15px;
}
.scheduleContent #scheduleDate .calendar-week-bar.ui-tiled li {
  color: #007cff;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-grid .grid {
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 37px;
}
.scheduleContent #scheduleDate .arm-calendar-default .grid-today .grid {
  border-radius: 50%;
}
.scheduleContent .scheduleInfo {
  margin-top: 10px;
  height: calc(100% - 300px);
  padding: 0 20px;
  box-sizing: border-box;
}
.actionBox {
  width: 100%;
  box-sizing: border-box;
  display: none;
}
.actionBox .addBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  width: 125px;
  margin-right: 13px;
}
.actionBox img {
  width: 25px;
  object-fit: cover;
  margin-right: 10px;
}
.actionBox span {
  font-size: 15px;
  color: #252525;
}

.scheduleContent .scheduleAllList {
  position: relative;
  z-index: 2;
  height: 100%;
}
/* æ—¥ç¨‹tabé¡¹æ ·å¼ */
.scheduleContent .scheduleAllList .scheduleTab {
  display: flex;
  justify-content: space-between;
  width: 180px;
  margin: 0 auto;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0;
  font-size: 15px;
  color: #252525;
}
.scheduleContent .scheduleAllList .scheduleTab .schedTabItem {
  position: relative;
}
.scheduleContent .scheduleAllList .scheduleTab .schedTabItem.active:after {
  position: absolute;
  content: "";
  width: 40%;
  height: 4px;
  background: #007cff;
  border-radius: 2px;
  left: 50%;
  bottom: -5px;
  transform: translateX(-50%);
}
.scheduleContent .scheduleAllList .scheduleList {
  height: 100%;
  display: none;
}
.scheduleContent .scheduleAllList .scheduleList.active {
  display: block;
}
.scheduleContent .scheduleInfo .noData {
  width: 100%;
  padding: 20px 0;
  text-align: center;
  font-size: 14px;
  color: #333333;
}
.scheduleContent .scheduleInfo .scheduleList {
  padding: 0;
}
.scheduleContent .scheduleInfo .scheduleList.meeting {
  overflow-y: auto;
}
.scheduleContent .scheduleInfo .scheduleList .myScheduleBox {
  height: calc(100% - 12px);
  overflow-y: auto;
}
.scheduleContent .scheduleInfo .scheduleItem {
  display: flex;
  justify-content: flex-start;
  padding: 8px 0;
  border-top: 1px solid #ededed;
  align-items: center;
  position: relative;
}
.scheduleContent .scheduleInfo .scheduleItem p {
  word-break: break-all;
}
.scheduleContent .scheduleInfo .scheduleItem .basicInfo {
  width: 80px;
  min-width: 80px;
}
.scheduleContent .scheduleInfo .scheduleItem .basicInfo .time {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
  /* display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; */
  white-space: nowrap;
}
.scheduleContent .scheduleInfo .scheduleItem .location {
  width: 60%;
  min-width: 80px;
  font-size: 14px;
  margin: 0 5px;
  color: #252525;
}
.scheduleContent .scheduleInfo .scheduleItem .location .address {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.scheduleContent .scheduleInfo .scheduleItem .title {
  padding: 0 5px;
  box-sizing: border-box;
  font-size: 14px;
  color: #252525;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-height: 23px;
  /* display: flex;
    align-items: center; */
}
.scheduleContent .scheduleInfo .scheduleItem .delItem {
  padding: 3px;
  box-sizing: border-box;
  font-size: 10px;
  width: 30px;
  min-width: 30px;
  background: #d31133;
  color: #fff;
  border-radius: 4px;
  text-align: center;
  /* height: 100%; */
  di'speak: dis';
  position: absolute;
  right: 8px;
}
.scheduleContent .scheduleInfo .scheduleItem .delItem.hideDel {
  display: none;
}

/* æ—¥ç¨‹æœ‰æ•°æ®æ—¶çš„æ ‡è®°ç‚¹ */
.scheduleContent .calendar-cur-view .hasData:after {
  position: absolute;
  content: "";
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #007cff;
  overflow: hidden;
  left: 50%;
  transform: translateX(-50%);
  bottom: 4px;
}
.scheduleContent .calendar-cur-view .grid-curday.hasData:after {
  position: absolute;
  content: "";
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  left: 50%;
  transform: translateX(-50%);
  bottom: 4px;
}
/* æ—¥ç¨‹æ–°æ ·å¼ */
.scheduleContent .scheduleInfo .meeting .scheduleItem .title {
  color: #007cff;
  width: 40%;
  line-height: 22px;
  min-width: 65px;
}
.scheduleContent .scheduleAllList .scheduleList .myScheduleTab {
  display: flex;
  font-size: 15px;
}
.scheduleContent
  .scheduleAllList
  .scheduleList
  .myScheduleTab
  .myScheduleTabItem {
  color: #464646;
}
.scheduleContent
  .scheduleAllList
  .scheduleList
  .myScheduleTab
  .myScheduleTabItem.active {
  color: #007cff;
}
.scheduleContent
  .scheduleAllList
  .scheduleList
  .myScheduleTab
  .myScheduleTabItem {
  margin-right: 20px;
  padding: 8px 0;
}
.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem {
  display: none;
}
.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.active {
  display: block;
}
.scheduleContent .scheduleAllList .mySchedule .personal .course {
  width: calc(100% - 90px);
  min-width: 85px;
  font-size: 14px;
  margin-right: 10px;
  color: #252525;
}
.scheduleContent .scheduleAllList .mySchedule .personal .timeBox {
  width: 85px;
  min-width: 80px;
  height: auto;
}
.scheduleContent .scheduleAllList .mySchedule .personal .timeBox p {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
  white-space: nowrap;
}
.actionBox {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  margin-top: 10px;
}
.actionBox div {
  width: 125px;
  background: #f3f8fa;
  border-radius: 6px;
  text-align: center;
  padding: 5px 10px;
  font-size: 13px;
  cursor: pointer;
  color: #007cff;
  box-sizing: border-box;
}
.actionBox .cancelBtn {
  display: none;
}
.scheduleContent .scheduleAllList .mySchedule .conference .scheduleItem div {
  /* display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    box-sizing: border-box;
    justify-content: flex-start;
    word-break: break-all;*/

  /* display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: 3.0em; 
      line-height: 1.5em;  */
}
.scheduleContent .scheduleAllList .mySchedule .conference .scheduleItem p {
  font-size: 14px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .course {
  width: 50%;
  min-width: 85px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .timeBox {
  width: 85px;
  min-width: 80px;
  margin: 0 10px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .location {
  width: calc(55% - 100px);
  min-width: 60px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .location .address {
  color: #007cff;
}

.scheduleContent .scheduleAllList .mySchedule .ddSchedule .scheduleItem div {
  /* display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: 3.0em; 
      line-height: 1.5em;  */
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .scheduleItem p {
  font-size: 14px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .course {
  /**width: 50%;
    min-width: 85px;**/
  width: calc(100% - 100px);
  margin-right: 10px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .timeBox {
  width: 85px;
  min-width: 80px;
  margin: 0 10px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .location {
  width: calc(55% - 100px);
  min-width: 60px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .location .address {
  color: #007cff;
}

/* æ—¥ç¨‹æ·»åŠ iframeå±‚æ ·å¼ */
.iframe-schedule .ui-modal-cnt {
  width: 90%;
  max-width: 350px;
  border-radius: 10px;
}
.iframe-schedule .ui-modal-cnt header {
  background: #f9fafc;
}
.iframe-schedule .ui-modal-ft {
  display: none;
}
.iframe-schedule .ui-modal-cnt .ui-modal-bd {
  max-height: 100%;
  border-bottom: 0px solid transparent !important;
}
.iframe-schedule .ui-modal-cnt .ui-modal-bd iframe {
  /* height: 80vh;
    max-height: 470px; */
}
.iframe-schedule i.ui-modal-close {
  position: absolute;
  top: 7px;
  right: 7px;
  width: 28px;
  height: 32px;
}
.iframe-schedule i.ui-modal-close:before {
  top: 0px;
  right: 0px;
}

.arm-calendar-default .grid-today .grid:before {
  display: none;
}

/* æ—¥ç¨‹è¯¦æƒ…å¼¹çª— */
.ui-page.calDetail .ui-modal-cnt {
  width: 80% !important;
  min-height: auto !important;
  height: auto !important;
  max-height: 100% !important;
  max-width: 80% !important;
}
.ui-page.calDetail .ui-modal-cnt header {
  background: #f9fafc;
}
.ui-page.calDetails .ui-modal-cnt {
  width: 80% !important;
  height: 40% !important;
  max-width: 80% !important;
}
.ui-page.calDetail .ui-modal-cnt .ui-modal-bd,
.ui-page.calDetails .ui-modal-cnt .ui-modal-bd {
  top: 44px !important;
  padding: 5px 20px;
}
.ui-page.calDetail .ui-modal-cnt .ui-modal-bd p,
.ui-page.calDetails .ui-modal-cnt .ui-modal-bd p {
  padding: 8px 0;
}
.ui-page.calDetail .edit-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.ui-page.calDetail .editCal,
.ui-page.calDetails .editCal {
  background: #0079d8;
  border-radius: 3px;
  text-align: center;
  padding: 5px 15px;
  font-size: 13px;
  cursor: pointer;
  color: #fff;
  margin-right: 10px;
}
.ui-page.calDetail .delCal,
.ui-page.calDetails .delCal {
  background: #f3f8fa;
  border-radius: 3px;
  text-align: center;
  padding: 5px 10px;
  font-size: 13px;
  cursor: pointer;
  color: #007cff;
}
/* .editCal {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
} */
.calendar-content {
  /* padding-bottom: 50px !important; */
}
@media (max-width: 340px) {
  .scheduleContent #scheduleDate .arm-calendar-default .calendar-title {
    height: 30px;
    line-height: 30px;
    margin: 0 38px;
  }
  .scheduleContent #scheduleDate .arm-calendar-default .calendar-tool {
    height: 30px;
    line-height: 30px;
    width: 30px;
    margin-top: -16px;
  }
  .scheduleContent #scheduleDate .arm-calendar-default .calendar-grid .grid {
    min-height: 30px;
    max-width: 30px;
  }
}
.schedule-item{display:flex;align-items:center;justify-content:flex-start;}
.schedule-item .schedule-left{display:flex;align-items:flex-start;justify-content:flex-start;flex-direction:column;width: calc(100% - 80px);/* flex:1; */border-left: 1px solid #f1f1f1;padding: 5px 0;padding-left: 11px;}
.schedule-item .schedule-del{font-size:10px;white-space:nowrap;padding:3px 5px;background:#d31133;box-sizing:border-box;color:#fff;border-radius:5px;display:none}
.schedule-titleName{display:flex;align-items:center;justify-content:flex-start;width:100%;padding: 8px;background-color:transparent;border-radius: 4px;position: relative;box-sizing: border-box;}
.schedule-titleName .schedule-icon{border-radius: 9px;width: 76px;height: 48px;margin-right: 10px;background:var(--colorPrimary);text-align: center;line-height: 48px;}
.schedule-titleName .schedule-icon svg{margin: auto;width: 20px!important;}
.schedule-titleName .schedule-title{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space: normal;width:calc(100% - 15px);line-height:14px;padding-right: 32px;line-height: 1.5;}
.schedule-titleName .schedule-bg{position:absolute;left:0;top:0;bottom:0;right:0;z-index:99;opacity: 0.1;background: var(--colorPrimary);}
.schedule-timeList{margin-right:10px;width: 57px;line-height:1.5;text-align: right;flex: none;}
.schedule-timeList .schedule-timeList > div{font-size:14px}
.schedule-timeList .schedule-beginTime{color: #333;font-size: 19px;}
.schedule-timeList .schedule-endTime{color:#818181}
.schedule-left {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  width: calc(100% - 50px);
  flex: 1;
}


.schedule-del {
  font-size: 10px;
  white-space: nowrap;
  padding: 3px 5px;
  background: #d31133;
  box-sizing: border-box;
  color: #fff;
  border-radius: 5px;
  display: none;
}
.schedule-location {
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-left: 10px; */
}
.schedule-location span {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  font-size: 13px;
  margin-left: 4px;
}
.schedule-time-text {
  font-size: 11px;
}
.schedule-time,
.schedule-location {
  display: flex;
  align-items: center;
}
.schedule-time svg,
.schedule-location svg {
  margin-top: -2px;
}
.schedule-name {
  background: rgb(0, 128, 193);
  border-radius: 6px;
  color: rgb(255, 255, 255);
  width: 16px;
  height: 16px;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
  box-sizing: border-box;
}
.schedule-title {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  width: calc(100% - 10px);
}
.delCalDialog .ui-modal-bd > div {
  text-align: center;
  padding: 15px 0;
}
.delCalDialog .ui-modal-cnt {
  width: 90%;
  max-width: 350px;
  border-radius: 10px;
}
.delCalDialog .ui-modal-cnt header {
  background: #f9fafc;
}
.schedule-svg {
  width: 11px;
  margin-right: 3px;
}
.schedule-titleName {
  /* display: flex; */
  /* align-items: center; */
  /* justify-content: flex-start; */
  /* width: 100%; */
}
.schedule-timeList {
  margin-right: 10px;
  width: 38px;
}
.schedule-icon {
  border-radius: 2px;
  width: 4px;
  height: 17px;
  margin-right: 6px;
}
.schedule-timeList > div {
  font-size: 14px;
}
.schedule-timeList > div.schedule-endTime{
  font-size: 13px;
  color: #818181;
} 
.schedule-timeList > div
.schedule-endTime {
  color: #818181;
}