*{
    padding: 0;
    margin: 0;
}
body{
    overflow: hidden;
}
.serviceContent{
    width: 100%;
    margin: 0 auto;
    padding: 0px 0px;
    box-sizing: border-box;
    margin-top: -210px;
    position: relative;
    z-index: 2;
}
.serviceContent .searchBox{
	display: flex;
	justify-content: center;
	align-items: center;
	background: #fff;
	border-radius: 12px;
	margin: 0 0 14px;
	box-sizing: border-box;
	width: auto;
}
.serviceContent .searchBox .searchBtn{
	width: 40px;
	text-align: center;
}
.serviceContent .searchBox .inputBox{
	width: calc(100% - 60px);
	position: relative;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	box-sizing: border-box;
	padding: 6px 0px;
	border-radius: 12px;
}
.serviceContent .searchBox .inputBox:after{
    position: absolute;
    content: '';
    width: 1px;
    height: 40%;
    background: #007CFF;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
}
.serviceContent .searchBox .inputBox .svgImg{
	display: flex;
	justify-content: center;
	align-items: center;
}
.serviceContent .searchBox .inputBox .svgImg svg{
	width: 16px;
	height: 16px;
	vertical-align: middle;
}
.serviceContent .searchBox .inputBox .svgImg.searchIcon{
	/* margin-right: 8px; */
}
.serviceContent .searchBox .inputBox .svgImg.searchIcon path{
	fill: rgba(0, 0, 0, 0.54);
	
}
.serviceContent .searchBox .inputBox .svgImg.cancelIcon{
	padding: 0px 5px;
    box-sizing: border-box;
	position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
	z-index: 1;
}
.serviceContent .searchBox .inputBox .svgImg.cancelIcon path{
	fill: rgba(0, 0, 0, 0.54);
}
.serviceContent .searchBox .inputBox input{
	height: 22px;
	line-height: 22px;
	background: transparent;
	outline: 0;
	border: 0;
	width: calc(100% - 40px);
	font-size: 15px;
	color: rgba(0, 0, 0, 0.54);
	/* padding: 4px 10px; */
}
.serviceContent .searchBox .inputBox input::placeholder{
	color: #ccc;
}
.serviceContent .searchBox .inputBox .svgImg.cancelIcon.hideBtn{
    display: none;
}
.serviceContent .searchBox .searchBtn{
	font-size: 14px;
    text-transform: uppercase;
    color: rgb(47, 101, 174);
    width: 60px;
    margin: 0px;
    text-decoration: none;
	white-space: nowrap;
	box-sizing: border-box;
    height: 22px;
    line-height: 22px;
}
.serviceContent .serviceMod{
    padding: 15px 10px;
    box-sizing: border-box;
    border-radius: 10px;
    background: #fff;
    min-height: 200px;
}
.serviceContent .serviceMod .parentCategoryTab{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    padding-left: 15px;
}
.serviceContent .serviceMod .parentCategoryTab:before{
    position: absolute;
    content: '';
    position: absolute;
    width: 6px;
    height: 19px;
    border-radius: 20px;
    background: #DD252C;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
}
.serviceContent .serviceMod .parentCategoryTab div{
    font-size: 16px;
    color: #7A7A7A;
    /* width: 50px; */
    text-align: center;
}
.serviceContent .serviceMod .parentCategoryTab div.active{
    color: #181818;
    font-weight: bold;
}
.serviceContent .serviceMod .parentItemBox{
    display: flex;
    margin-top: 15px;
    filter: drop-shadow(0px 2px 6px rgba(113, 143, 174, 0.1529));
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox{
    width: 110px;
    display: flex;
    flex-direction: column;
    display: none;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory{
    padding: 0;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    color:#181818;
    font-size: 15px;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory span{
    display: inline-block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding: 14px 15px;
    box-sizing: border-box;
    position: relative;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory span:after{
    content: '';
    position: absolute;
    width: calc(100% - 10px);
    height: 1px;
    background: #E1E6EB;
    bottom: 0;
    left: 6px;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active{
    border-radius: 12px 0 0 12px;
    background: #FFFFFF;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active span:after{
    width: 0;
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active:before{
    content: "";
    position: absolute;
    right: 0px;
    top: -12px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 0 0, transparent 12px, #fff 12px);
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:first-child.active:before{
    content: "";
    position: absolute;
    right: -12px;
    top: 0px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 12px 12px, transparent 12px, #fff 12px);
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory.active:after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: -12px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 0px 12px, transparent 12px, #fff 12px);
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child.active:after{
    content: "";
    position: absolute;
    right: -12px;
    bottom: 0px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 12px 0px, transparent 12px, #fff 12px);
}
.serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:first-child:last-child.active:after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: -12px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 0px 12px, transparent 12px, #fff 12px);
}
.serviceContent .serviceMod .parentItemBox .rightCategoryInfo{
    /* width: calc(100% - 100px); */
    width: 100%;
    background: #fff;
    border-radius: 12px;
    padding: 10px 15px;
    box-sizing: border-box;
    position: relative;
    min-height: 150px;
}

.serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appListBg{
    position: absolute;
    width: 60%;
    object-fit: cover;
    max-height: 150px;
    right: 0;
    bottom: 0;
    max-width: 180px;
    z-index: 0;
}

.rightCategoryInfo .appGrid{
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 20px 5px;
}
.rightCategoryInfo  .appGrid .appItem{
    display: flex;
    align-items: center;
    flex-direction: column;
}
.rightCategoryInfo  .appGrid .appItem .imgBox{
    /* padding: 8px; */
    border-radius: 8px;
    /* background: #007CFF; */
}

.rightCategoryInfo  .appGrid .appItem .imgBox .svgImg{
	padding: 5px;
    border-radius: 8px;
    background-color: var(--colorPrimary);
}
.rightCategoryInfo  .appGrid .appItem  img{
    width:45px;
    object-fit: contain;
    height: 45px;
    max-width: 45px;
    border-radius: 10px;
}
.rightCategoryInfo .appGrid .appItem .name{
    margin-top: 8px;
    font-size: 12px;
    color: #232323;
    max-width: 100%;
    /* overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; */
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
	text-align: center;
}

/* svg 图标 */
.svgImg{
    background: transparent;
    width: 35px;
    height: 35px;
  }
  .svgImg svg{
    padding: 0;
    width: 100%;
    height: 100%;
  }
  .svgImg svg path, .svgImg svg rect, .svgImg svg polygon, .svgImg svg circle{
    fill: #fff;
    stroke: transparent;
  }
  .serviceContent{
    height: calc(100% - 81px);
  }
  .serviceContent .serviceMod{
    height: calc(100% - 60px);
  }
  .serviceContent .serviceMod .parentItemBox{
    height: calc(100% - 40px);
  }
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox{
    padding-right: 12px;
    box-sizing: border-box;
  }
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox{
    height: 100%;
    overflow-y: auto;
  }
  /* .serviceContent .serviceMod .parentItemBox .leftCategoryBox,
  .serviceContent .serviceMod .parentItemBox .rightCategoryInfo{
    height: 100%;
    overflow-y: auto;
  } */
  .serviceContent .serviceMod .parentItemBox .rightCategoryInfo{
    height: 100%;
    /* margin-left: -12px; */
    margin-left:0px;
    box-sizing: border-box;
  }
  .serviceContent .serviceMod .parentItemBox .rightCategoryInfo .appList{
    position: relative;
    z-index:1;
    height: 100%;
    overflow-y: auto;
}
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox::-webkit-scrollbar{display: none;}
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox{scrollbar-width: none;}
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox{-ms-overflow-style: none;}
  .serviceContent .serviceMod .parentItemBox .leftCategoryBox .leftCategory:last-child.active.resetAfter:after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: -12px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 0px 12px, transparent 12px, #fff 12px);
  }
  .serviceContent .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
}
.serviceContent .nodataBox .noDataImg{
    width: 70px;
    height: 50px;
}
.serviceContent .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}
@media (min-width: 768px){
    .rightCategoryInfo .appGrid {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
@media (min-width: 1000px){
    .rightCategoryInfo .appGrid {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

@media (max-width: 320px){
    .rightCategoryInfo .appGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }