!function(window,undefined){"use strict";var $,win,doms,cache,skin,_elementStyle=document.createElement("div").style,ready={getPath:function(){var a=document.scripts,b=a[a.length-1],c=b.src;if(!b.getAttribute("merge"))return c.substring(0,c.lastIndexOf("/")+1)}(),getUrlParam:function(a,b,c){var e,d=new RegExp("(^|&)"+a+"=([^&]*)(&|$)");return c=c||("boolean"==typeof b?b:!1),b=/http|https/.test(b)?b:window.location.href,e=b.substr(b.indexOf(c?"#":"?")+1).match(d),null!=e?unescape(e[2]):null},updateUrlParam:function(url,name,value,callback){var reg,tmp,r=url,change=!1,v="";return null!=r&&"undefined"!=r&&""!=r&&(value=encodeURIComponent(value),reg=new RegExp("(^|)"+name+"=([^&]*)(|$)"),tmp=name+"="+value,null!=url.match(reg)?(r=url.replace(eval(reg),tmp),v=url.match(reg)[2],change=value!==v):(r=url.match("[?]")?url+"&"+tmp:url+"?"+tmp,change=!0)),"function"==typeof callback&&callback({url:r,_url:url,name:name,value:value,_value:v,change:change}),r},_prefixStyle:function(a){if(a in _elementStyle)return a;for(var c,b=["webkit","Moz","ms","O"],d=0,e=b.length;e>d;d++)if(c=b[d]+a.charAt(0).toUpperCase()+a.substr(1),c in _elementStyle)return c;return!1},enter:function(a){13===a.keyCode&&a.preventDefault()},config:{},end:{},btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"]},layer={v:"2.4",ie6:!!window.ActiveXObject&&!window.XMLHttpRequest,index:window.layer&&window.layer.v?1e5:0,path:ready.getPath,config:function(a,b){var c=0;return a=a||{},layer.cache=ready.config=$.extend(ready.config,a),layer.path=ready.config.path||layer.path,"string"==typeof a.extend&&(a.extend=[a.extend]),layer.use("skin/layer.css",a.extend&&a.extend.length>0?function d(){var e=a.extend;layer.use(e[e[c]?c:c-1],c<e.length?function(){return++c,d}():b)}():b),this},use:function(a,b,c){var f,g,h,e=$("head")[0];return a=a.replace(/\s/g,""),f=/\.css$/.test(a),g=document.createElement(f?"link":"script"),h="layui_layer_"+a.replace(/\.|\//g,""),layer.path?(f&&(g.rel="stylesheet"),g[f?"href":"src"]=/^http:\/\//.test(a)?a:layer.path+a,g.id=h,$("#"+h)[0]||e.appendChild(g),function i(){(f?1989===parseInt($("#"+h).css("width")):layer[c||h])?function(){b&&b();try{f||e.removeChild(g)}catch(a){}}():setTimeout(i,100)}(),this):void 0},ready:function(a,b){var c="function"==typeof a;return c&&(b=a),layer.config($.extend(ready.config,function(){return c?{}:{path:a}}()),b),this},alert:function(a,b,c){var d="function"==typeof b;return d&&(c=b),layer.open($.extend({content:a,yes:c},d?{}:b))},confirm:function(a,b,c,d){var e="function"==typeof b;return e&&(d=c,c=b),layer.open($.extend({content:a,btn:ready.btn,yes:c,btn2:d},e?{}:b))},msg:function(a,b,c){var d="function"==typeof b,e=ready.config.skin,f=(e?e+" "+e+"-msg":"")||"layui-layer-msg",g=doms.anim.length-1;return d&&(c=b),layer.open($.extend({content:a,time:3e3,shade:!1,skin:f,title:!1,closeBtn:!1,btn:!1,end:c},d&&!ready.config.skin?{skin:f+" layui-layer-hui",shift:g}:function(){return b=b||{},(-1===b.icon||b.icon===undefined&&!ready.config.skin)&&(b.skin=f+" "+(b.skin||"layui-layer-hui")),b}()))},load:function(a,b){return layer.open($.extend({type:3,icon:a||0,shade:.01},b))},tips:function(a,b,c){return layer.open($.extend({type:4,content:[a,b],closeBtn:!1,time:3e3,shade:!1,fix:!1,maxWidth:210},c))}},Class=function(a){var b=this;b.index=++layer.index,b.config=$.extend({},b.config,ready.config,a),b.creat()};Class.pt=Class.prototype,doms=["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"],doms.anim=["layer-anim","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],Class.pt.config={type:0,shade:.3,fix:!0,move:doms[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,shift:0,icon:-1,scrollbar:!0,tips:2},Class.pt.vessel=function(a,b){var c=this,d=c.index,e=c.config,f=e.zIndex+d,g="object"==typeof e.title,h=e.maxmin&&(1===e.type||2===e.type),i=e.title?'<div class="layui-layer-title" style="'+(g?e.title[1]:"")+'">'+(g?e.title[0]:e.title)+"</div>":"";return e.zIndex=f,b([e.shade?'<div class="layui-layer-shade" id="layui-layer-shade'+d+'" times="'+d+'" style="'+("z-index:"+(f-1)+"; background-color:"+(e.shade[1]||"#000")+"; opacity:"+(e.shade[0]||e.shade)+"; filter:alpha(opacity="+(100*e.shade[0]||100*e.shade)+");")+'"></div>':"",'<div class="'+doms[0]+(" layui-layer-"+ready.type[e.type])+(e.fullscreen?" layui-layer-fullscreen":"")+(0!=e.type&&2!=e.type||e.shade?"":" layui-layer-border")+" "+(e.skin||"")+'" id="'+doms[0]+d+'" type="'+ready.type[e.type]+'" times="'+d+'" showtime="'+e.time+'" conType="'+(a?"object":"string")+'" style="z-index: '+f+"; width:"+e.area[0]+";height:"+e.area[1]+(e.fix?"":";position:absolute;")+'">'+(a&&2!=e.type?"":i)+'<div id="'+(e.id||"")+'" class="layui-layer-content'+(0==e.type&&-1!==e.icon?" layui-layer-padding":"")+(3==e.type?" layui-layer-loading"+e.icon:"")+'">'+(0==e.type&&-1!==e.icon?'<i class="layui-layer-ico layui-layer-ico'+e.icon+'"></i>':"")+(1==e.type&&a?"":e.content||"")+"</div>"+'<span class="layui-layer-setwin" style="z-index:3;">'+function(){var a=h?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"";return e.closeBtn&&(a+='<a class="layui-layer-ico '+doms[7]+" "+doms[7]+(e.title?e.closeBtn:4==e.type?"1":"2")+'" href="javascript:;"></a>'),a}()+"</span>"+(e.btn?function(){var b,c,a="";for("string"==typeof e.btn&&(e.btn=[e.btn]),b=0,c=e.btn.length;c>b;b++)a+='<a class="'+doms[6]+b+'">'+e.btn[b]+"</a>";return'<div class="'+doms[6]+'">'+a+"</div>"}():"")+"</div>"],i),c},Class.pt.creat=function(){var a=this,b=a.config,c=a.index,e=b.content,f="object"==typeof e;if(!$("#"+b.id)[0]){switch("string"==typeof b.area&&(b.area="auto"===b.area?["",""]:[b.area,""]),b.type){case 0:b.btn="btn"in b?b.btn:ready.btn[0],layer.closeAll("dialog");break;case 2:e=b.content=f?b.content:[b.content||"http://layer.layui.com","auto"],b.content[0]=ready.updateUrlParam(b.content[0],"layerId",c),b.content='<iframe scrolling="'+(b.content[1]||"auto")+'" allowtransparency="true" id="'+doms[4]+c+'" name="'+doms[4]+c+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+b.content[0]+'"></iframe>';break;case 3:b.title=!1,b.closeBtn=!1,-1===b.icon&&0===b.icon,layer.closeAll("loading");break;case 4:f||(b.content=[b.content,"body"]),b.follow=b.content[1],b.content=b.content[0]+'<i class="layui-layer-TipsG"></i>',b.title=!1,b.tips="object"==typeof b.tips?b.tips:[b.tips,!0],b.tipsMore||layer.closeAll("tips")}a.vessel(f,function(d,g){$("body").append(d[0]),f?function(){2==b.type||4==b.type?function(){$("body").append(d[1])}():function(){e.parents("."+doms[0])[0]||(e.show().addClass("layui-layer-wrap").wrap(d[1]),$("#"+doms[0]+c).find("."+doms[5]).before(g))}()}():$("body").append(d[1]),a.layero=$("#"+doms[0]+c),b.scrollbar||doms.html.css("overflow","hidden").attr("layer-full",c)}).auto(c),2==b.type&&layer.ie6&&a.layero.find("iframe").attr("src",e[0]),$(document).off("keydown",ready.enter).on("keydown",ready.enter),a.layero.on("keydown",function(){$(document).off("keydown",ready.enter)}),4==b.type?a.tips():a.offset(),b.fix&&!b.fullscreen&&win.on("resize",function(){a.offset(),(/^\d+%$/.test(b.area[0])||/^\d+%$/.test(b.area[1]))&&a.auto(c),4==b.type&&a.tips()}),b.fullscreen&&win.on("resize",function(){a.auto(c)}),b.time<=0||setTimeout(function(){layer.close(a.index)},b.time),a.move().callback(),doms.anim[b.shift]&&a.layero.addClass(doms.anim[b.shift])}},Class.pt.auto=function(a){function h(a){a=d.find(a);var b=e[1]-f-g-2*(0|parseFloat(a.css("padding")));c.fullscreen&&(b=win.height()),a.height(b)}var e,f,g,b=this,c=b.config,d=$("#"+doms[0]+a);switch(""===c.area[0]&&c.maxWidth>0&&(/MSIE 7/.test(navigator.userAgent)&&c.btn&&d.width(d.innerWidth()),d.outerWidth()>c.maxWidth&&d.width(c.maxWidth)),e=[d.innerWidth(),d.innerHeight()],f=d.find(doms[1]).outerHeight()||0,g=d.find("."+doms[6]).outerHeight()||0,c.type){case 2:h("iframe");break;default:""===c.area[1]?c.fix&&e[1]>=win.height()&&(e[1]=win.height(),h("."+doms[5])):h("."+doms[5])}return b},Class.pt.offset=function(){var f,a=this,b=a.config,c=a.layero,d=[c.outerWidth(),c.outerHeight()],e="object"==typeof b.offset;a.offsetTop=(win.height()-d[1])/2,a.offsetLeft=(win.width()-d[0])/2,e?(a.offsetTop=b.offset[0],a.offsetLeft=b.offset[1]||a.offsetLeft):"auto"!==b.offset&&(a.offsetTop=b.offset,"rb"===b.offset&&(a.offsetTop=win.height()-d[1],a.offsetLeft=win.width()-d[0])),b.fix||(a.offsetTop=/%$/.test(a.offsetTop)?win.height()*parseFloat(a.offsetTop)/100:parseFloat(a.offsetTop),a.offsetLeft=/%$/.test(a.offsetLeft)?win.width()*parseFloat(a.offsetLeft)/100:parseFloat(a.offsetLeft),a.offsetTop+=win.scrollTop(),a.offsetLeft+=win.scrollLeft()),f={top:a.offsetTop,left:a.offsetLeft},b.fullscreen&&(f={top:0,bottom:0,right:0,left:0}),c.css(f)},Class.pt.tips=function(){var f,g,h,a=this,b=a.config,c=a.layero,d=[c.outerWidth(),c.outerHeight()],e=$(b.follow);e[0]||(e=$("body")),f={width:e.outerWidth(),height:e.outerHeight(),top:e.offset().top,left:e.offset().left},g=c.find(".layui-layer-TipsG"),h=b.tips[0],b.tips[1]||g.remove(),f.autoLeft=function(){f.left+d[0]-win.width()>0?(f.tipLeft=f.left+f.width-d[0],g.css({right:12,left:"auto"})):f.tipLeft=f.left},f.where=[function(){f.autoLeft(),f.tipTop=f.top-d[1]-10,g.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",b.tips[1])},function(){f.tipLeft=f.left+f.width+10,f.tipTop=f.top,g.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",b.tips[1])},function(){f.autoLeft(),f.tipTop=f.top+f.height+10,g.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",b.tips[1])},function(){f.tipLeft=f.left-d[0]-10,f.tipTop=f.top,g.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",b.tips[1])}],f.where[h-1](),1===h?f.top-(win.scrollTop()+d[1]+16)<0&&f.where[2]():2===h?win.width()-(f.left+f.width+d[0]+16)>0||f.where[3]():3===h?f.top-win.scrollTop()+f.height+d[1]+16-win.height()>0&&f.where[0]():4===h&&d[0]+16-f.left>0&&f.where[1](),c.find("."+doms[5]).css({"background-color":b.tips[1],"padding-right":b.closeBtn?"30px":""}),c.css({left:f.tipLeft-(b.fix?win.scrollLeft():0),top:f.tipTop-(b.fix?win.scrollTop():0)})},Class.pt.move=function(){var a=this,b=a.config,c={setY:0,moveLayer:function(){var a=c.layero,b=parseInt(a.css("margin-left")),d=parseInt(c.move.css("left"));0===b||(d-=b),"fixed"!==a.css("position")&&(d-=a.parent().offset().left,c.setY=0),a.css({left:d,top:parseInt(c.move.css("top"))-c.setY})}},d=a.layero.find(b.move);return b.move&&d.attr("move","ok"),d.css({cursor:b.move?"move":"auto"}),$(b.move).on("mousedown",function(a){if(a.preventDefault(),"ok"===$(this).attr("move")){c.ismove=!0,c.layero=$(this).parents("."+doms[0]);var d=c.layero.offset().left,e=c.layero.offset().top,f=c.layero.outerWidth()-6,g=c.layero.outerHeight()-6;$("#layui-layer-moves")[0]||$("body").append('<div id="layui-layer-moves" class="layui-layer-moves" style="left:'+d+"px; top:"+e+"px; width:"+f+"px; height:"+g+'px; z-index:2147483584"></div>'),c.move=$("#layui-layer-moves"),b.moveType&&c.move.css({visibility:"hidden"}),c.moveX=a.pageX-c.move.position().left,c.moveY=a.pageY-c.move.position().top,"fixed"!==c.layero.css("position")||(c.setY=win.scrollTop())}}),$(document).mousemove(function(a){var d,e,f,g;c.ismove&&(d=a.pageX-c.moveX,e=a.pageY-c.moveY,a.preventDefault(),b.moveOut||(c.setY=win.scrollTop(),f=win.width()-c.move.outerWidth(),g=c.setY,0>d&&(d=0),d>f&&(d=f),g>e&&(e=g),e>win.height()-c.move.outerHeight()+c.setY&&(e=win.height()-c.move.outerHeight()+c.setY)),c.move.css({left:d,top:e}),b.moveType&&c.moveLayer(),d=e=f=g=null)}).mouseup(function(){try{c.ismove&&(c.moveLayer(),c.move.remove(),b.moveEnd&&b.moveEnd()),c.ismove=!1}catch(a){c.ismove=!1}}),a},Class.pt.callback=function(){function d(){var d=c.cancel&&c.cancel(a.index,b);d===!1||layer.close(a.index)}var a=this,b=a.layero,c=a.config;a.openLayer(),c.success&&(2==c.type?b.find("iframe").on("load",function(){c.success(b,a.index)}):c.success(b,a.index)),layer.ie6&&a.IE6(b),b.find("."+doms[6]).children("a").on("click",function(){var e,d=$(this).index();0===d?c.yes?c.yes(a.index,b):c["btn1"]?c["btn1"](a.index,b):layer.close(a.index):(e=c["btn"+(d+1)]&&c["btn"+(d+1)](a.index,b),e===!1||layer.close(a.index))}),b.find("."+doms[7]).on("click",d),c.shadeClose&&$("#layui-layer-shade"+a.index).on("click",function(){layer.close(a.index)}),b.find(".layui-layer-min").on("click",function(){var d=c.min&&c.min(b);d===!1||layer.min(a.index,c)}),b.find(".layui-layer-max").on("click",function(){$(this).hasClass("layui-layer-maxmin")?(layer.restore(a.index),c.restore&&c.restore(b)):(layer.full(a.index,c),setTimeout(function(){c.full&&c.full(b)},100))}),c.end&&(ready.end[a.index]=c.end)},ready.reselect=function(){$.each($("select"),function(){var c=$(this);c.parents("."+doms[0])[0]||1==c.attr("layer")&&$("."+doms[0]).length<1&&c.removeAttr("layer").show(),c=null})},Class.pt.IE6=function(a){function d(){a.css({top:c+(b.config.fix?win.scrollTop():0)})}var b=this,c=a.offset().top;d(),win.scroll(d),$("select").each(function(){var c=$(this);c.parents("."+doms[0])[0]||"none"===c.css("display")||c.attr({layer:"1"}).hide(),c=null})},Class.pt.openLayer=function(){var a=this;layer.zIndex=a.config.zIndex,layer.setTop=function(a){var b=function(){layer.zIndex++,a.css("z-index",layer.zIndex+1)};return layer.zIndex=parseInt(a[0].style.zIndex),a.on("mousedown",b),layer.zIndex}},ready.record=function(a){var b=[a.width(),a.height(),a.position().top,a.position().left+parseFloat(a.css("margin-left"))];a.find(".layui-layer-max").addClass("layui-layer-maxmin"),a.attr({area:b})},ready.rescollbar=function(a){doms.html.attr("layer-full")==a&&(doms.html[0].style.removeProperty?doms.html[0].style.removeProperty("overflow"):doms.html[0].style.removeAttribute("overflow"),doms.html.removeAttr("layer-full"))},window.layer=layer,layer.getChildFrame=function(a,b){return b=b||$("."+doms[4]).attr("times"),$("#"+doms[0]+b).find("iframe").contents().find(a)},layer.getFrameIndex=function(a){return $("#"+a).parents("."+doms[4]).attr("times")},layer.iframeAuto=function(a){var b,c,d,e;a&&(b=layer.getChildFrame("html",a).outerHeight(),c=$("#"+doms[0]+a),d=c.find(doms[1]).outerHeight()||0,e=c.find("."+doms[6]).outerHeight()||0,c.css({height:b+d+e}),c.find("iframe").css({height:b}))},layer.iframeSrc=function(a,b){$("#"+doms[0]+a).find("iframe").attr("src",b)},layer.style=function(a,b){var c=$("#"+doms[0]+a),d=c.attr("type"),e=c.find(doms[1]).outerHeight()||0,f=c.find("."+doms[6]).outerHeight()||0;(d===ready.type[1]||d===ready.type[2])&&(c.css(b),d===ready.type[2]&&c.find("iframe").css({height:parseFloat(b.height)-e-f}))},layer.min=function(a){var c=$("#"+doms[0]+a),d=c.find(doms[1]).outerHeight()||0;ready.record(c),layer.style(a,{width:180,height:d,overflow:"hidden"}),c.find(".layui-layer-min").hide(),"page"===c.attr("type")&&c.find(doms[4]).hide(),ready.rescollbar(a)},layer.restore=function(a){var b=$("#"+doms[0]+a),c=b.attr("area").split(",");b.attr("type"),layer.style(a,{width:parseFloat(c[0]),height:parseFloat(c[1]),top:parseFloat(c[2]),left:parseFloat(c[3]),overflow:"visible"}),b.find(".layui-layer-max").removeClass("layui-layer-maxmin"),b.find(".layui-layer-min").show(),"page"===b.attr("type")&&b.find(doms[4]).show(),ready.rescollbar(a)},layer.full=function(a){var c,b=$("#"+doms[0]+a);ready.record(b),doms.html.attr("layer-full")||doms.html.css("overflow","hidden").attr("layer-full",a),clearTimeout(c),c=setTimeout(function(){var c="fixed"===b.css("position");layer.style(a,{top:c?0:win.scrollTop(),left:c?0:win.scrollLeft(),width:win.width(),height:win.height()}),b.find(".layui-layer-min").hide()},100)},layer.title=function(a,b){var c=$("#"+doms[0]+(b||layer.index)).find(doms[1]);c.html(a)},layer.close=function(a){var d,e,b=$("#"+doms[0]+a),c=b.attr("type");if(b[0]){if(c===ready.type[1]&&"object"===b.attr("conType"))for(b.children(":not(."+doms[5]+")").remove(),d=0;2>d;d++)b.find(".layui-layer-wrap").unwrap().hide();else{if(c===ready.type[2])try{e=$("#"+doms[4]+a)[0],e.contentWindow.document.write(""),e.contentWindow.close(),b.find("."+doms[5])[0].removeChild(e)}catch(f){}b[0].innerHTML="",b.remove()}$("#layui-layer-moves, #layui-layer-shade"+a).remove(),layer.ie6&&ready.reselect(),ready.rescollbar(a),$(document).off("keydown",ready.enter),"function"==typeof ready.end[a]&&ready.end[a](),delete ready.end[a]}},layer.closeAll=function(a){$.each($("."+doms[0]),function(){var b=$(this),c=a?b.attr("type")===a:1;c&&layer.close(b.attr("times")),c=null})},cache=layer.cache||{},skin=function(a){return cache.skin?" "+cache.skin+" "+cache.skin+"-"+a:""},layer.prompt=function(a,b){a=a||{},"function"==typeof a&&(b=a);var c,d=2==a.formType?'<textarea class="layui-layer-input">'+(a.value||"")+"</textarea>":function(){return'<input type="'+(1==a.formType?"password":"text")+'" class="layui-layer-input" value="'+(a.value||"")+'">'}();return layer.open($.extend({btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:d,skin:"layui-layer-prompt"+skin("prompt"),success:function(a){c=a.find(".layui-layer-input"),c.focus()},yes:function(d){var e=c.val();""===e?c.focus():e.length>(a.maxlength||500)?layer.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(a.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",c,{tips:1}):b&&b(e,d,c)}},a))},layer.tab=function(a){a=a||{};var b=a.tab||{};return layer.open($.extend({type:1,skin:"layui-layer-tab"+skin("tab"),title:function(){var a=b.length,c=1,d="";if(a>0)for(d='<span class="layui-layer-tabnow">'+b[0].title+"</span>";a>c;c++)d+="<span>"+b[c].title+"</span>";return d}(),content:'<ul class="layui-layer-tabmain">'+function(){var a=b.length,c=1,d="";if(a>0)for(d='<li class="layui-layer-tabli xubox_tab_layer">'+(b[0].content||"no content")+"</li>";a>c;c++)d+='<li class="layui-layer-tabli">'+(b[c].content||"no  content")+"</li>";return d}()+"</ul>",success:function(b){var c=b.find(".layui-layer-title").children(),d=b.find(".layui-layer-tabmain").children();c.on("mousedown",function(b){b.stopPropagation?b.stopPropagation():b.cancelBubble=!0;var c=$(this),e=c.index();c.addClass("layui-layer-tabnow").siblings().removeClass("layui-layer-tabnow"),d.eq(e).show().siblings().hide(),"function"==typeof a.change&&a.change(e)})}},a))},layer.photos=function(a,b,c){function k(a,b,c){var d=new Image;return d.src=a,d.complete?b(d):(d.onload=function(){d.onload=null,b(d)},d.onerror=function(a){d.onerror=null,c(a)},void 0)}function l(a,b){return[$(window).width()+(a||0),$(window).height()+(b||0)]}function m(){var a=l(-10,-10),b=a[0]/a[1];d.view={width:d.img.width,height:d.img.height},b>d.img.radio?d.img.height>a[1]&&(d.view.height=a[1],d.view.width=a[1]*d.img.radio):d.img.width>a[0]&&(d.view.width=a[0],d.view.height=a[0]/d.img.radio),d.view.marginLeft=-d.view.width/2,d.view.marginTop=-d.view.height/2,d.bigimg.css(d.view)}function n(){d.bigimg[0].style[ready._prefixStyle("transform")]="rotate("+d.img.rotate+"deg) scale("+d.img.scale+")"}function o(){d.img.rotate=0,d.img.scale=1,n(),m()}var e,f,g,h,i,j,d={};if(a=a||{},a.photos){if(e=a.photos.constructor===Object,f=e?a.photos:{},g=f.data||[],h=f.start||0,d.imgIndex=(0|h)+1,a.fulltools=a.fullscreen&&a.fulltools,a.rotate=a.rotate||90,a.scale=a.scale||.1,a.img=a.img||"img",d.imgMove=null,e){if(0===g.length)return layer.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{if(i=$(a.photos),j=function(){g=[],i.find(a.img).each(function(a){var b=$(this);b.attr("layer-index",a),g.push({alt:b.attr("alt"),pid:b.attr("layer-pid"),src:b.attr("layer-src")||b.attr("src"),thumb:b.attr("src")})})},j(),0===g.length)return;if(b||i.on("click",a.img,function(){var b=$(this),c=b.attr("layer-index");layer.photos($.extend(a,{photos:{start:c,data:g,tab:a.tab},full:a.full}),!0),j()}),!b)return}d.imgprev=function(a){d.imgIndex--,d.imgIndex<1&&(d.imgIndex=g.length),d.tabimg(a)},d.imgnext=function(a,b){d.imgIndex++,d.imgIndex>g.length&&(d.imgIndex=1,b)||d.tabimg(a)},d.keyup=function(a){if(!d.end){var b=a.keyCode;a.preventDefault(),37===b?d.imgprev(!0):39===b?d.imgnext(!0):27===b&&layer.close(d.index)}},d.imgright=function(){d.img.rotate+=a.rotate,n()},d.imgleft=function(){d.img.rotate-=a.rotate,n()},d.imgzoomin=function(){d.img.scale-=a.scale,d.img.scale<0&&(d.img.scale=0),n()},d.imgzoomout=function(){d.img.scale+=a.scale,n()},d.moveStart=function(a){a.preventDefault(),d.imgMove={pageX:a.pageX,pageY:a.pageY,marginLeft:parseInt(d.bigimg.css("marginLeft")||"0px"),marginTop:parseInt(d.bigimg.css("marginTop")||"0px")}},d.move=function(a){a.preventDefault(),d.imgMove&&(d.imgMove.moveX=a.pageX-d.imgMove.pageX,d.imgMove.moveY=a.pageY-d.imgMove.pageY,console.log(d.imgMove),d.bigimg.css({marginLeft:d.imgMove.moveX+d.imgMove.marginLeft,marginTop:d.imgMove.moveY+d.imgMove.marginTop}))},d.moveEnd=function(a){a.preventDefault(),d.imgMove=null},d.tabimg=function(b){g.length<=1||(f.start=d.imgIndex-1,layer.close(d.index),layer.photos(a,!0,b))},d.event=function(a,b,c){d.cont.hover(function(){d.imgsee.show()},function(){d.imgsee.hide()}),d.cont.find(".layui-layer-imgright").on("click",function(a){a.preventDefault(),d.imgright()}),d.cont.find(".layui-layer-imgleft").on("click",function(a){a.preventDefault(),d.imgleft()}),d.cont.find(".layui-layer-imgprev").on("click",function(a){a.preventDefault(),d.imgprev()}),d.cont.find(".layui-layer-imgnext").on("click",function(a){a.preventDefault(),d.imgnext()}),d.cont.find(".layui-layer-imgzoomin").on("click",function(a){a.preventDefault(),d.imgzoomin()}),d.cont.find(".layui-layer-imgzoomout").on("click",function(a){a.preventDefault(),d.imgzoomout()}),d.cont.find(".layui-layer-imgreset").on("click",function(a){a.preventDefault(),o()}),c.shadeClose&&d.cont.find(".layui-layer-phshade").on("click",function(a){a.preventDefault(),layer.close(b)}),$("img",d.bigimg).css("cursor","move"),d.bigimg.on("mousedown touchstart",d.moveStart),d.cont.on("mousemove touchmove",d.move),d.cont.on("mouseup touchend mouseout touchcancel",d.moveEnd),$(document).on("keyup",d.keyup),$(window).on("resize",function(){o()})},d.loadi=layer.load(1,{shade:"shade"in a?!1:.9,scrollbar:!1}),k(g[h].src,function(b){layer.close(d.loadi),d.img=g[h],d.img.width=b.width,d.img.height=b.height,d.img.radio=b.width/b.height,d.img.rotate=0,d.img.scale=1,d.index=layer.open($.extend({type:1,area:function(){var c=[b.width,b.height],d=l(-50,-50);return a.fullscreen?["100%","100%"]:(!a.full&&c[0]>d[0]&&(c[0]=d[0],c[1]=c[0]*b.height/b.width),a.full&&c[1]>d[1]&&(c[1]=d[1],c[0]=c[1]*b.width/b.height),[c[0]+"px",c[1]+"px"])}(),title:!1,shade:.9,shadeClose:!0,closeBtn:!0,fullscreen:a.fullscreen,move:a.fullscreen?!1:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,shift:0|5*Math.random(),skin:"layui-layer-photos"+skin("photos"),content:'<div class="layui-layer-phshade" style="position:absolute;width:100%;height:100%; left:0; top:0; bottom:0; right:0;z-index:1;"></div><div class="layui-layer-phimg" style="position:absolute;z-index:2;"><img src="'+g[h].src+'" alt="'+(g[h].alt||"")+'" layer-pid="'+g[h].pid+'">'+"</div>"+'<div class="layui-layer-imgsee">'+(a.fulltools?'<div class="layui-layer-imgtool" style="z-index:3;"><a class="layui-layer-imgright" title="顺时针旋转'+a.rotate+'度"></a><a class="layui-layer-imgleft" title="逆时针旋转'+a.rotate+'度"></a><a class="layui-layer-imgzoomin" title="缩小"></a><a class="layui-layer-imgzoomout" title="放大"></a><a class="layui-layer-imgreset" title="复位"></a></div>':"")+(g.length>1?'<span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev" style="z-index:3;"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext" style="z-index:3;"></a></span>':"")+'<div class="layui-layer-imgbar" style="display:'+(c?"block":"")+'; z-index:3;"><span class="layui-layer-imgtit"><a title="点击打开原图" href="'+g[h].src+'" target="_blank">'+(g[h].alt||"")+"</a><em>"+d.imgIndex+"/"+g.length+"</em></span></div>"+"</div>",success:function(b,c){d.bigimg=b.find(".layui-layer-phimg"),d.imgsee=b.find(".layui-layer-imguide,.layui-layer-imgbar"),d.cont=b.find(".layui-layer-content"),m(),d.event(b,c,a),a.tab&&a.tab(g[h],b)},end:function(){d.end=!0,$(document).off("keyup",d.keyup)}},a))},function(){layer.close(d.loadi),layer.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){g.length>1&&d.imgnext(!0,!0)}})})}},ready.run=function(){$=jQuery,win=$(window),doms.html=$("html"),layer.open=function(a){var b=new Class(a);return b.index}},"function"==typeof define?define(function(){return ready.run(),layer}):function(){ready.run(),layer.use("skin/layer.css")}()}(window);