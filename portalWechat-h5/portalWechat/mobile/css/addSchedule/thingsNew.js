var type = U.getSearch('type')
var date = U.getSearch('date') || new Date()
// var nowTime = new Date();
var nowTime = new Date(date)
var initTime = U.getFormatTime({ time: nowTime.setHours(nowTime.getHours() + 1) })
var initTime2 = U.getFormatTime({ time: nowTime.setHours(nowTime.getHours() + 12) })

$('#beginDate').val(initTime.date + ' ' + initTime.hours + ':00')
$('#endDate').val(initTime2.date + ' ' + initTime2.hours + ':00')
// var lang = {
//     title: '日期',
//     cancel: '取消',
//     confirm: '确认',
//     year: '年',
//     month: '月',
//     day: '日',
//     hour: '时',
//     min: '分'
// };

var rollYear = new Date().getFullYear()
var startRolldata = new Rolldate({
  el: '#beginDate',
  format: 'YYYY-MM-DD hh:mm',
  beginYear: rollYear - 30,
  endYear: rollYear + 30,
  value: initTime.date + ' ' + initTime.hours + ':00',
  init: function () {
    // console.log('插件开始触发');
  },
  moveEnd: function (scroll) {
    // console.log(scroll)
    // console.log('滚动结束');
  },
  confirm: function (date) {
    //获取 开始时间
    var endDate = $('#endDate').val()
    //比较 开始解释时间大小，结束时间不能小于开始时间
    if (new Date(endDate).getTime() < new Date(date).getTime()) {
      layer.msg('开始时间不能晚于结束时间')
    }
    // console.log(date)
    // console.log('确定按钮触发');
  },
  cancel: function () {
    // console.log('插件运行取消');
  }
})
var endRolldate = new Rolldate({
  el: '#endDate',
  format: 'YYYY-MM-DD hh:mm',
  beginYear: rollYear - 30,
  endYear: rollYear + 30,
  value: initTime2.date + ' ' + initTime2.hours + ':00',
  init: function () {
    // console.log('插件开始触发');
  },
  moveEnd: function (scroll) {
    // console.log(scroll)
    // console.log('滚动结束');
  },
  confirm: function (date) {
    //获取 开始时间
    var startDate = $('#beginDate').val()
    //比较 开始解释时间大小，结束时间不能小于开始时间
    if (new Date(startDate).getTime() > new Date(date).getTime()) {
      layer.msg('结束时间不能早于开始时间')
    }
    // console.log(date)
    // console.log('确定按钮触发');
  },
  cancel: function () {
    // console.log('插件运行取消');
  }
})
$(document).on('click', '.input-group-addon', function (event) {
  var dataType = $(this).parent().attr('data-type')
  if (dataType === 'end') {
    endRolldate.show()
  } else if (dataType === 'start') {
    startRolldata.show()
  }
})
// endRolldate.reloadValue('2023-06-26 22:00');
// $('#beginDatePa').on('show.daterangepicker', function () {
//     $(".daterangepicker td.active").css("background-color", "")
//     // setThemeColor()
// })
// $('#endDatePa').on('show.daterangepicker', function () {
//     // setThemeColor()
// })
// $('#beginDatePa').daterangepicker({
//     singleDatePicker: true,
//     timePicker: true,
//     timePickerSeconds: false,
//     timePicker24Hour: true,
//     timePickerIncrement: 5,
//     locale: {
//         format: "YYYY-MM-DD HH:mm"
//     }
// }, function (start) {
//     $('#endDatePa').daterangepicker({
//         singleDatePicker: true,
//         timePicker: true,
//         timePickerSeconds: false,
//         timePicker24Hour: true,
//         timePickerIncrement: 5,
//         minDate: $("#beginDate").val(),
//         locale: {
//             format: "YYYY-MM-DD HH:mm"
//         }
//     });
// });
// $('#endDatePa').daterangepicker({
//     singleDatePicker: true,
//     timePicker: true,
//     timePickerSeconds: false,
//     timePicker24Hour: true,
//     timePickerIncrement: 5,
//     minDate: $("#beginDate").val(),
//     locale: {
//         format: "YYYY-MM-DD HH:mm"
//     }
// });

// $("#title").select2({
//     minimumResultsForSearch: -1
// });

$('.time-duration')
  .iCheck({
    radioClass: 'iradio_flat'
  })
  .on('ifChecked', function () {
    selectTime()
  })

if (type == 'edit') {
  var scheduleId = U.getSearch('id')
  var url = SERVER + '/calendar/mgr/api/' + scheduleId + '.rst'
} else {
  var categoryId = ''
  var url = _urls.addSchedule
}
var fnEvent = {}
$(document).on('click', '[data-event]', function (event) {
  fnEvent[$(this).attr('data-event')]($(this))
})

function save() {
  if ($('.rolldate-panel.fadeIn').length > 0) {
    layer.alert('请先选择日期！')
    return false
  }
  // 编码防止攻击
  var title = $('.content').val()
  var location = $('.location').val()
  var remark = $('#remark').val() || ''
  var content = $('#content').val() || ''
  // var content = $('#content').val() || ''
  var beginDate = $('#beginDate').val().replace(/\-/g, '/')
  var endDate = $('#endDate').val().replace(/\-/g, '/')
  if (type != 'edit' && $('#title').val() === null) {
    layer.alert('请选择日历！')
    return false
  }
  if ($.trim(title).length < 1) {
    layer.alert('请填写日程名称！')
    return false
  }
  // if (remark?.length > 100) {
  //   layer.alert('备注不能超过100字！')
  //   return false
  // }
  if (content?.length > 100) {
    layer.alert('备注不能超过100字！')
    return false
  }
  if (beginDate > endDate) {
    layer.alert('开始时间不能大于结束时间！')
    return false
  }
  var params = {
    categoryVO: {
      id: type == 'edit' ? 0 : $('#title').val()
    },
    simpleUnitVO: {
      begin: new Date(beginDate).toISOString(),
      end: new Date(endDate).toISOString(),
      repeatType: 0
    },
    content: content,
    location: location,
    tag8: remark, //备注是tag8,内容是content
    title: title
  }
  $('.addRc').prop('disabled', 'disabled')
  // var urls =checkIdentity(url);
  // 不用走weAuth
  var urls = url
  $.ajax({
    url: urls,
    contentType: 'application/json',
    data: JSON.stringify(params),
    type: 'post',
    success: function (res) {
      var data = JSON.parse(res)
      if (data.resultCode == 0) {
        $('.addRc').removeAttr('disabled')
        // parent.layer.alert((type == "edit" ? "修改" : "添加") + '成功！');
        layer.msg((type == 'edit' ? '修改' : '添加') + '成功！')
        // if (parent.$('#frameServer')[0] === undefined) {
        //    parent.interfaceEvent.init();
        //   } else {
        // window.parent.$('#frameServer')[0].contentWindow.interfaceEvent.init();
        //   }
        //getMycategory();
      } else {
        // parent.layer.alert((type == "edit" ? "修改" : "添加") + '失败，请联系管理员！');
        layer.msg((type == 'edit' ? '修改' : '添加') + '失败，请联系管理员！')
      }
      // 父页面用layer时使用
      // var index = parent.layer.getFrameIndex(window.name);
      // parent.layer.close(index);
      // 父页面用armjs-iframe时使用
      setTimeout(() => {
        //添加成功，刷新主页面日程列表
        if (data.resultCode == 0) {
          window.parent.postMessage('reloadSched', '*')
        }
        // window.parent.dia.iframe[0].close();
      }, 1000)
    }
  })
}
function thingInfo() {
  var url = SERVER + '/calendar/mgr/api/' + scheduleId + '/edit.rst'
  U.getAjax({
    url: url,
    data: {
      scheduleId: scheduleId
    },
    success: function (res) {
      var result = res.result ? res.result : {}
      var data = result.data ? result.data : {}
      var this_cateGoryName = data.categoryVO && data.categoryVO.name
      this_cateGoryName = this_cateGoryName ? this_cateGoryName : ''
      $('.things-rili-title').html(this_cateGoryName)
      $('.content').val(data.title)
      $('.location').val(data.location)
      $('#beginDate').val(data.effectiveTime)
      $('#endDate').val(data.finishTime)
      $('#content').val(data.content)
      $('#remark').val(data.tag8)
      startRolldata.reloadValue(data.effectiveTime)
      endRolldate.reloadValue(data.finishTime)
      var time1 = data.effectiveTime.substr(11, 5)
      var time2 = data.finishTime.substr(11, 5)
      if (time1 == '09:00' && time2 == '21:00') {
        var rval = 1
      } else if (time1 == '09:00' && time2 == '12:00') {
        var rval = 2
      } else if (time1 == '13:00' && time2 == '16:00') {
        var rval = 3
      } else if (time1 == '18:00' && time2 == '21:00') {
        var rval = 4
      } else {
        var rval = -1
      }
      $("input.time-duration[value='" + rval + "']").iCheck('check')
    }
  })
}
function selectTime() {
  var beginDate0 = $('#beginDate').val()
  var beginDate = beginDate0.substr(0, 11)
  var endDate0 = $('#endDate').val()
  var endDate = endDate0.substr(0, 11)
  var rval = $('input[name=duration]:checked').val()
  // if (rval == -1) {
  //   $('#beginDate').val(beginDate0)
  //   $('#endDate').val(endDate0)
  //   return false
  // }
  if (rval == 1) {
    beginDate = beginDate + '09:00'
    endDate = endDate + '21:00'
  } else if (rval == 2) {
    beginDate = beginDate + '09:00'
    endDate = endDate + '12:00'
  } else if (rval == 3) {
    beginDate = beginDate + '13:00'
    endDate = endDate + '16:00'
  } else if (rval == 4) {
    beginDate = beginDate + '18:00'
    endDate = endDate + '21:00'
  } else if (rval == -1) {
    beginDate = beginDate + '09:00'
    endDate = endDate + '10:00'
  }
  $('#beginDate').val(beginDate)
  $('#endDate').val(endDate)
  startRolldata.reloadValue(beginDate)
  endRolldate.reloadValue(endDate)
}
function getMycategory() {
  var url11 = _urls.myCategory + '&t=' + new Date().getTime()
  // var myCategory = checkIdentity(url11);
  //不用走weAuth
  var myCategory = url11
  // console.log('==myCategory==', myCategory)
  U.getAjax({
    url: myCategory,
    data: {
      queryType: 2,
      type: 1
    },
    success: function (res) {
      var data = res.result.data
      var html = ''
      var categoryArr = []
      for (var i = 0; i < data.length; i++) {
        var obj = data[i]
        if (obj.id == -1) {
          continue
        }
        if (obj.teamCategoryVOS.length > 0) {
          for (var j = 0; j < obj.teamCategoryVOS.length; j++) {
            var obj1 = obj.teamCategoryVOS[j]
            categoryArr.push({ id: obj1.id, calendarCateName: obj1.calendarCateName })
            html += '<option value="' + obj1.id + '">' + obj1.calendarCateName + '</option>'
          }
        }
      }
      $('#title').html(html)
      $('#title')
        .val(categoryId ? categoryId : categoryArr[0].id)
        .trigger('change')
    }
  })
}
if (type == 'edit') {
  thingInfo()
} else {
  getMycategory()
}

var input = document.getElementById('contentInput')
var maxLength = input.getAttribute('maxlength')

input.addEventListener('keyup', function () {
  charCount(this, maxLength)
})

var input1 = document.getElementById('remark')
var maxLength1 = input1.getAttribute('maxlength')

input1.addEventListener('keyup', function () {
  charCount(this, maxLength1)
})
function charCount(input, maxLength) {
  var countMessage = '超过最大字数' + maxLength + '字'
  var charCount = input.value.length

  if (charCount >= maxLength) {
    layer.msg(countMessage)
  }
}

$(document).on('click', '.addRc', function () {
  // var w = $(layero).find("iframe")[0].contentWindow;//通过该对象可以获取iframe中的变量，调用iframe中的方法
  save()
})

$(document).on('click', '.cancleRc', function () {
  parent.layer.closeAll()
})
$('head').append(
  '<link type="text/css" rel="stylesheet" href="' +
    domainConfig?.portalDomain +
    '/_web/fusionportal/styleSheet/styles.css">'
)

function htmlEncode(html) {
  //1.首先动态创建一个容器标签元素，如DIV
  var temp = document.createElement('div')
  //2.然后将要转换的字符串设置为这个元素的innerText(ie支持)或者textContent(火狐，google支持)
  temp.textContent != undefined ? (temp.textContent = html) : (temp.innerText = html)
  //3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
  var output = temp.innerHTML
  temp = null
  return output
}
/*2.用浏览器内部转换器实现html解码*/
function htmlDecode(text) {
  //1.首先动态创建一个容器标签元素，如DIV
  var temp = document.createElement('div')
  //2.然后将要转换的字符串设置为这个元素的innerHTML(ie，火狐，google都支持)
  temp.innerHTML = text
  //3.最后返回这个元素的innerText(ie支持)或者textContent(火狐，google支持)，即得到经过HTML解码的字符串了。
  var output = temp.innerText || temp.textContent
  temp = null
  return output
}

//点击更多按钮
$('.things-more').on('click', function () {
  $('.things-btn').addClass('z-hide')
  $('.things-info').removeClass('z-hide')
  $('.confirmBox').removeClass('z-hide')
  var iframeHeight = document.body.scrollHeight
  window.parent.postMessage(iframeHeight, '*')
})
//点击完成
$('.things-finish').on('click', function () {
  save()
})

window.onload = function () {
  var iframeHeight = document.body.scrollHeight
  window.parent.postMessage(iframeHeight, '*')
}
