function GetQueryString1(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return decodeURI(r[2])
  return 0
}

var domainUrl = localStorage.getItem('domainUrl')
var portalDomainUrl = localStorage.getItem('portalDomainUrl')

var http2 = portalDomainUrl ? portalDomainUrl : 'https://my.mycc.edu.cn/sopplus'
// var http = domainUrl ? domainUrl : 'https://my.mycc.edu.cn'
var http = ''

function getReasonType() {
  var reason
  $.ajax({
    type: 'get',
    url: http2 + '/mobile/getServiceDomain.rst',
    async: false,
    success: function (data) {
      if (data.result == 1) {
        reason = data.data
      }
    }
  })
  return reason
}
var domainConfig = getReasonType()
if (domainConfig && domainConfig.portalDomain) {
  domain = domainConfig.portalDomain
}
var SERVER = http
var USERSERVER = ''
var _p = 'YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m'
var _urls = {
  //日程
  myCategory: SERVER + '/calendar/mgr/api/category/list.rst?_p=' + _p, //日历分类
  // myCategory:SERVER+"/myCategory.json",//日历分类
  calendarEventView: SERVER + '/_web/_plugs/calendar/api/calendarEventView.rst', //日历事件列表
  // calendarEventView:SERVER+"/calendarEventView.json",//日历事件列表——一周
  // calendarEventView:SERVER+"/calendarEventViewMonth.json",//日历事件列表——一个月

  loadSchedule: SERVER + '/_web/_plugs/calendar/api/loadSchedule.rst', //日历事件详情
  // loadSchedule:SERVER+"/loadSchedule.json",//日历事件详情
  addSchedule: SERVER + '/calendar/mgr/api/create.rst', //增加日程事件
  deleteSchedule: SERVER + '/_web/_plugs/calendar/api/deleteSchedule.rst', //删除日程事件
  // deleteSchedule: SERVER + '/stresult.json',          //删除日程事件
  updateSchedule: SERVER + '/_web/_plugs/calendar/api/schedule/updateSchedule.rst', //更新日程事件
  // updateSchedule: SERVER + '/stresult.json',          //更新日程事件
  //
  seeCreateMB: SERVER + '/_web/_lightapp/calendar/mobile/profile/create.rst', //保存是否可看
  // seeCreateMB:SERVER+"/st.json",//保存是否可看
  addCategory: SERVER + '/_web/_plugs/calendar/api/addCategory.rst', //增加日程分类
  // addCategory: SERVER + '/stresult.json',          //增加日程分类
  updateCategory: SERVER + '/_web/_plugs/calendar/api/updateCategory.rst', //修改日程分类
  // updateCategory: SERVER + '/stresult.json',    //修改日程分类
  deleteCategory: SERVER + '/_web/_plugs/calendar/api/deleteCategory.rst', //删除日程分类
  // deleteCategory: SERVER + '/stresult.json',//删除日程分类
  loadCategory: SERVER + '/_web/_plugs/calendar/api/loadCategory.rst', //查询日程分类详情
  // loadCategory:SERVER+"/loadCategory.json",//查询日程分类详情
  //
  tree: SERVER + '/tree.json', //分类
  users: SERVER + '/users.json', //用户
  searchUsers: SERVER + '/searchUsers.json' //搜索
}

//签名认证
function checkIdentity(paramUrl) {
  var url = http + '/sopplus/mobile/wechatAuth?ret=' + encodeURIComponent(paramUrl)
  var params = {
    isReturn: 'false'
  }
  var returnUrl = ''
  $.ajax({
    url: url,
    type: 'get',
    data: params,
    dataType: 'json',
    async: false,
    timeout: 10000,
    success: function (data) {
      var u = data.data

      returnUrl = u
    },
    error: function (err) {
      // $.poptips("接口暂时无法调用，请稍候访问！")
    }
  })
  return returnUrl
}
