.swiperContentNew {
  position: relative;
}
.swiperContent{
    background:transparent;
    margin-top: 0px;
    height: auto;
   
}
.mySwiper {
  width: 100%;
  height: 138px;
  /* padding: 0 0 20px!important; */
}

.mySwiperSlider {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.mySwiperSlider img {
  display: block!important;
  width: 100%!important;
  height: 100%!important;
  /* object-fit: cover!important; */
}

.mySwiperSlider {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  /* width: 70%!important; */
  /* 一页两张 */
  /* width: calc((100% - 10px)/2)!important; */
  /* 一页三张 */
  /* width: calc((100% - 20px) / 3) !important; */
}

.mySwiperPagination{
    position: absolute;
}
.mySwiperPagination span{
    width: 8px;
    height: 8px;
    background: transparent;
    opacity: 1;
    border: 1px solid #fff;
}
.mySwiperPagination span.swiper-pagination-bullet-active{
    background: #fff;
}


@media screen and (max-width: 300px) {
  body {
    font-size: 14px;
  }
 
}
@media screen and (min-width: 760px) {
  .mySwiper {
    height: 300px !important;
  }
}