*{margin:0;padding:0}
@font-face{font-family:'MyCustomFont';src:url('font/SourceHanSerifCN-Heavy.otf') format('truetype');font-weight:normal;font-style:normal;font-display:swap}
html{-webkit-font-smoothing:antialiased;box-sizing:border-box;text-size-adjust:100%}
body{--colorPrimary:#0044b3;--colorSecond:#FF9A02;background-color: #eef3f7;color:#333333;min-width:280px;}
.flex-evenly{display:flex;justify-content:space-evenly}
.flex-start{display:flex;flex-wrap:wrap;justify-content:start}
.flex-between{display:flex;justify-content:space-between}
.flex-around{display:flex;flex-wrap:wrap;justify-content:space-around}

.ui-footer{height:68px}
.ui-footer-btn{background:#ffffff;box-shadow:0px -5px 10px 0px rgb(0 0 0 / 3%)}
.ui-footer-btn .ui-tiled{}
.ui-footer-btn .ui-tiled li{opacity:1}
.ui-footer-btn .ui-tiled li.active{}
.ui-footer-btn .ui-tiled .ub-img{width:25px;height:25px;margin:0 auto;background-size:100% 100%;margin-bottom:5px}
.ui-footer-btn .ui-tiled .ui-txt-tips{font-size:14px;color:#333}
.ui-footer-btn .ui-tiled li .wx01{background-image:url('../images/menu/i3.png')}
.ui-footer-btn .ui-tiled li.active .wx01{background-image:url('../images/menu/i3a.png')}
.ui-footer-btn .ui-tiled li .wx02{background-image:url('../images/menu/i2.png')}
.ui-footer-btn .ui-tiled li.active .wx02{background-image:url('../images/menu/i2a.png')}
.ui-footer-btn .ui-tiled li.tiled-middle .wx03{width:50px;height:50px;background-image:url('../images/menu/i10.png')}
.ui-footer-btn .ui-tiled li.tiled-middle .ui-txt-tips{display:none}
.ui-footer-btn .ui-tiled li.tiled-middle.active .wx03{background-image:url('../images/menu/i10.png')}
.ui-footer-btn .ui-tiled li .wx04{background-image:url('../images/menu/i5.png')}
.ui-footer-btn .ui-tiled li.active .wx04{background-image:url('../images/menu/i5a.png')}
.ui-footer-btn .ui-tiled li .wx05{background-image:url('../images/menu/i4.png')}
.ui-footer-btn .ui-tiled li.active .wx05{background-image:url('../images/menu/i4a.png')}

.noDate {text-align: center;color: #abaaaa;/* margin-top: 70px; */font-size: 16px;min-height: 200px;display: flex;flex-direction: column;justify-content: center;}
.noDate svg{width:90px;vertical-align: middle;}
.noDate .nodata{line-height: 38px;}
.ui-mask{position: inherit;}
.ui-section,
.ui-section > section{height:100%;} 

.mainContent{width:100%;height: calc(100% - 68px);/* overflow-y: auto; */}
.listContent{width:100%;height: calc(100% - 68px);/* overflow-y: auto; */}
.listContent.my{padding:15px;overflow-y: auto;width: auto;box-sizing: border-box;/* height: calc(100% - 83px); */}
.listContent .mainContent{width:auto;}
.listContent .articleContent,
.listContent .middleContent,
.listContent .serviceContent,
.listContent .taskContent,
.listContent .searchContent{
    width: 100%;
    margin: 0 auto;
    padding: 0px 15px 15px;
    box-sizing: border-box;
    margin-top: -194px;
    position: relative;
    z-index: 2;
    height: calc(100% - 68px);
}
.listContent .middleContent{
    margin-top:0px;
    padding: 10px 0;
    /* height: 100%; */
}
.topContent:not(.my){width:100%;min-height: 260px;padding: 15px 15px;box-sizing:border-box;position:relative;display:flex;flex-direction:column;justify-content:space-between;padding-top: 15px;}
.topContent:not(.my) .topBg{position:absolute;width:100%;object-fit:fill;height:100%;top:0;right:0;left:0;bottom:0;z-index:0;opacity:0.57;mix-blend-mode:soft-light;z-index:1}
.topContent:not(.my):after{content:"";position:absolute;width:100%;height:100%;top:0;right:0;left:0;bottom:0;background: linear-gradient(180deg, #3d6cab 0%, #3d6cab 100%);mix-blend-mode:multiply;z-index: 0;}
.topContent:not(.my) .blurBox{width:100%;height:60px;position:absolute;left:0;bottom:0px;right:0;background: linear-gradient(180deg, rgb(239 241 251 / 0%) 0%, rgb(243 248 250) 100%);z-index:1}

.topContent .topBox,
.topContent .appNav{position:relative;z-index:2}
.topContent .topBox{display:flex;justify-content:space-between;align-items:flex-start}
.topContent .topBox .motto{width: 89%;background:url('../images/logo.png') no-repeat left center;background-size: contain;min-height: 46px;}
.topContent .topBox .motto img{display:none;}
.topContent.my .topBox .motto img{display:block;}
.topContent .topBox .motto .userDept{flex:1;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}
.topContent .topBox .motto img{object-fit:cover;width:100%;height:100%;/* border-radius:100%; */overflow:hidden}
.topContent .topBox .motto img[src=""],
.topContent .topBox .motto img:not([src]){opacity:0}
.topContent .topBox .motto span{color:#fff;font-size: 15px;font-weight:normal}
.topContent .topBox .motto.small span{font-size:18px}
.topContent .topBox .motto .user-box-item .userName {font-size: 18px;font-weight: 700;color: #fff;}

.topContent .topBox .searchBox{display:flex;justify-content:flex-end;margin-left:15px;/* display: none; */background-color: rgb(255 255 255 / 19%);border-radius: 50%;padding: 10px;}
.topContent .topBox .searchBox input{-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;background-color:transparent;vertical-align:middle;border:0;border-bottom:1px solid #ddd;transition-duration:0.5s;color:#ddd;width:0;font-size:14px;display:none}
.topContent .topBox .searchBox img{width: 20px;height: 20px;object-fit:cover;cursor:pointer}
