*{
    padding: 0;
    margin: 0;
}
body{
    background: #f3f8fa;
    overflow: hidden;
}
.scheduleContent{
    width: 100%;
    margin: 0 auto;
    padding: 16px 20px 0;
    box-sizing: border-box;
    margin-top: -260px;
    position: relative;
    z-index: 2;
    height: calc(100% - 81px);
}
.scheduleContent .scheduleContentInfo{
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    border-radius: 10px;
    padding: 20px 0px;
    background: #fff;
    min-height: 200px;
    height: 100%;
}
.scheduleContent .scheduleBgBox{
    position: relative;
    padding: 0 20px;
    box-sizing: border-box;
    min-height: 260px;
}
.scheduleContent .scheduleBgBox .scheduleBg{
    position: absolute;
    right: 0;
    bottom: -50px;
    width: 50%;
    object-fit: cover;
    max-width: 190px;
    z-index: 0;
}
.scheduleContent #scheduleDate{
    width: 100%;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}
.scheduleContent #scheduleDate .calendar-toolbar{
    background: transparent;
    color:#333333;
    margin-bottom: 15px;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-title{
    height: 34px;
    line-height: 34px;
    border: 1px solid #d1d1d1;
    border-radius: 3px;
    margin: 0 48px;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-tool{
    height: 34px;
    line-height: 34px;
    border: 1px solid #d1d1d1;
    border-radius: 3px;
    margin-top: -17px;
    width: 34px;
    text-align: center;
    box-sizing: border-box;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-tool i{
    height: 100%;
    line-height: 100%;
}
.scheduleContent #scheduleDate .calendar-toolbar .dateInfo{
    text-align: center;
    font-size: 15px;
    font-family: MicrosoftYaHei;
}
.scheduleContent #scheduleDate .calendar-toolbar .ui-icon-prev:before,
.scheduleContent #scheduleDate .calendar-toolbar .ui-icon-next:before{
  color: rgba(51,51,51,0.45);
  font-size: 20px;
  vertical-align: middle;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-grid{
    color: #000000;
}
.scheduleContent #scheduleDate .ui-border-l,
.scheduleContent #scheduleDate .ui-border-b,
.scheduleContent #scheduleDate .ui-border-r{
    border: 0;
}
.scheduleContent #scheduleDate .ui-border-l, .ui-border-r, .ui-border-lr,
.scheduleContent #scheduleDate .ui-border-t, .ui-border-b, .ui-border-tb{
    background-image: none;
    font-family: MicrosoftYaHei;
}
.scheduleContent #scheduleDate .arm-calendar-default .grid-curday .grid{
    background: #007CFF;
    border-radius: 50%;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-grid .day-number{
    font-size: 15px;
}
.scheduleContent #scheduleDate .calendar-week-bar.ui-tiled li{
    color: #007CFF;
}
.scheduleContent #scheduleDate .arm-calendar-default .calendar-grid .grid{
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 37px;
}
.scheduleContent #scheduleDate .arm-calendar-default .grid-today .grid{
    border-radius: 50%;
}
.scheduleContent .scheduleInfo{
    margin-top: 10px;
    height: calc(100% - 300px);
    padding: 0 20px;
    box-sizing: border-box;
}
 .actionBox{
    width: 100%;
    box-sizing: border-box;
    display: none;
}
 .actionBox .addBtn{
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    width: 100px;
}
 .actionBox img{
    width: 25px;
    object-fit: cover;
    margin-right: 10px;
}
 .actionBox span{
    font-size: 15px;
    color: #252525;
}


.scheduleContent .scheduleAllList{
    position: relative;
    z-index: 2;
    height: 100%;
}
/* 日程tab项样式 */
.scheduleContent .scheduleAllList .scheduleTab{
    display: flex;
    justify-content: space-between;
    width: 180px;
    margin: 0 auto;
    align-items: center;
    box-sizing: border-box;
    padding: 10px 0;
    font-size: 15px;
    color: #252525;
}
.scheduleContent .scheduleAllList .scheduleTab .schedTabItem {
    position: relative;
}
.scheduleContent .scheduleAllList .scheduleTab .schedTabItem.active:after{
    position: absolute;
    content: '';
    width: 40%;
    height: 4px;
    background: #007CFF;
    border-radius: 2px;
    left: 50%;
    bottom: -5px;
    transform: translateX(-50%);
}
.scheduleContent .scheduleAllList .scheduleList{
    height: 100%;
    display:none;
}
.scheduleContent .scheduleAllList .scheduleList.active{
    display: block;
}
.scheduleContent .scheduleInfo .noData{
    width: 100%;
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    color: #333333;
}
.scheduleContent .scheduleInfo .scheduleList{
    padding:0;
}
.scheduleContent .scheduleInfo .scheduleList.meeting{
   overflow-y: auto;
}
.scheduleContent .scheduleInfo .scheduleList .myScheduleBox{
    height: calc(100% - 12px);
    overflow-y: auto;
}
.scheduleContent .scheduleInfo .scheduleItem{
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 8px 0;
    border-top: 1px solid #EDEDED;
    flex-direction: row;
}
.scheduleContent .scheduleInfo .scheduleItem p{
    word-break: break-all;
}
.scheduleContent .scheduleInfo .scheduleItem .basicInfo{
    width: 80px;
    min-width: 80px;
}
.scheduleContent .scheduleInfo .scheduleItem .basicInfo .time{
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    /* display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; */
    white-space: nowrap;
    
}
.scheduleContent .scheduleInfo .scheduleItem .location{
    width: 60%;
    min-width: 80px;
    font-size: 14px;
    margin: 0 5px;
    color: #252525;
}
.scheduleContent .scheduleInfo .scheduleItem .location .address{
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.scheduleContent .scheduleInfo .scheduleItem .title{
    padding: 0 5px;
    box-sizing: border-box;
    font-size: 14px;
    color: #252525;
     display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-height: 23px;
    /* display: flex;
    align-items: center; */
}
.scheduleContent .scheduleInfo .scheduleItem .delItem{
    padding: 3px;
    box-sizing: border-box;
    font-size: 10px;
    width: 30px;
    min-width: 30px;
    background: #E25051;
    color: #fff;
    border-radius: 4px;
    text-align: center;
}
.scheduleContent .scheduleInfo .scheduleItem .delItem.hideDel{
    display: none;
}

/* 日程有数据时的标记点 */
.scheduleContent .calendar-cur-view .hasData:after{
    position: absolute;
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #007CFF;
    overflow: hidden;
    left: 50%;
    transform: translateX(-50%);
    bottom: 4px;
}
.scheduleContent .calendar-cur-view .grid-curday.hasData:after{
    position: absolute;
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #fff;
    overflow: hidden;
    left: 50%;
    transform: translateX(-50%);
    bottom: 4px;
}
/* 日程新样式 */
.scheduleContent .scheduleInfo .meeting .scheduleItem .title{
    color:#007cff;
    width: 40%;
    line-height: 22px;
    min-width: 65px;
}
.scheduleContent .scheduleAllList .scheduleList .myScheduleTab{
    display: flex;
    font-size: 15px;
}
.scheduleContent .scheduleAllList .scheduleList .myScheduleTab .myScheduleTabItem{
    color: #464646;
  }
.scheduleContent .scheduleAllList .scheduleList .myScheduleTab .myScheduleTabItem.active{
    color: #007cff;
  }
.scheduleContent .scheduleAllList .scheduleList .myScheduleTab .myScheduleTabItem{
    margin-right: 20px;
    padding: 8px 0;
  }
  .scheduleContent .scheduleAllList .mySchedule .scheduleTabItem {
    display: none;
  }
  .scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.active {
    display: block;
  }
  .scheduleContent .scheduleAllList .mySchedule .personal .course{
    width: calc(100% - 90px);
    min-width: 85px;
    font-size: 14px;
    margin-right: 10px;
    color: #252525;
  }
  .scheduleContent .scheduleAllList .mySchedule .personal .timeBox{
    width: 85px;
    min-width: 80px;
    height: auto;
  }
  .scheduleContent .scheduleAllList .mySchedule .personal .timeBox p{
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    white-space: nowrap;
  }
.actionBox{
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;
  }
 .actionBox div{
    width: 100px;
    background: #f3f8fa;
    border-radius: 3px;
    text-align: center;
    padding: 5px 10px;
    font-size: 13px;
    cursor: pointer;
    color: #007cff;
  }
 .actionBox .cancelBtn{
    display: none;
}
.scheduleContent .scheduleAllList .mySchedule .conference .scheduleItem div{
   /* display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    box-sizing: border-box;
    justify-content: flex-start;
    word-break: break-all;*/

     /* display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: 3.0em; 
      line-height: 1.5em;  */
}
.scheduleContent .scheduleAllList .mySchedule .conference .scheduleItem p{
    font-size: 14px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .course{
    width: 50%;
    min-width: 85px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .timeBox{
    width: 85px;
    min-width: 80px;
    margin: 0 10px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .location{
    width: calc(55% - 100px);
    min-width: 60px;
}
.scheduleContent .scheduleAllList .mySchedule .conference .location .address{
    color: #007cff;
}

.scheduleContent .scheduleAllList .mySchedule .ddSchedule .scheduleItem div{

     /* display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: 3.0em; 
      line-height: 1.5em;  */
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .scheduleItem p{
    font-size: 14px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .course{
    /**width: 50%;
    min-width: 85px;**/
	width: calc(100% - 100px);
    margin-right: 10px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .timeBox{
    width: 85px;
    min-width: 80px;
    margin: 0 10px;
}
.scheduleContent .scheduleAllList .mySchedule .ddSchedule .location{
    width: calc(55% - 100px);
    min-width: 60px;
}
  .scheduleContent .scheduleAllList .mySchedule .ddSchedule .location .address{
    color: #007cff;
  }

 /* 日程添加iframe层样式 */
 .iframe-schedule .ui-modal-cnt{
    width: 90%;
    max-width: 350px;
    border-radius: 20px;
  }
  .iframe-schedule .ui-modal-cnt header{
    background: #f9fafc;
  }
  .iframe-schedule .ui-modal-ft{
    display: none;
  }
  .iframe-schedule .ui-modal-cnt .ui-modal-bd{
    max-height: 100%;
    border-bottom: 0px solid transparent!important;
  }
  .iframe-schedule .ui-modal-cnt .ui-modal-bd iframe{
    /* height: 80vh;
    max-height: 470px; */
  }
  .iframe-schedule i.ui-modal-close{
    position: absolute;
    top: 7px;
    right: 7px;
    width: 28px;
    height: 32px;
  }
  .iframe-schedule i.ui-modal-close:before{
    top: 0px;
    right: 0px;
  }

  .arm-calendar-default .grid-today .grid:before{
    display: none;
  }

  /* 日程详情弹窗 */
.ui-page.calDetail .ui-modal-cnt{
    width: 80%!important;
    min-height: auto!important;
    height: auto !important;
    max-height: 100%!important;
    max-width: 80%!important;
  }
  .ui-page.calDetail .ui-modal-cnt header{
    background: #f9fafc;
  }
  .ui-page.calDetails .ui-modal-cnt{
    width: 80%!important;
    height: 40%!important;
    max-width: 80%!important;
  }
  .ui-page.calDetail .ui-modal-cnt .ui-modal-bd,.ui-page.calDetails .ui-modal-cnt .ui-modal-bd{
    top: 44px!important;
    padding:5px 20px;
  }
  .ui-page.calDetail .ui-modal-cnt .ui-modal-bd p,.ui-page.calDetails .ui-modal-cnt .ui-modal-bd p{
    padding: 8px 0;
  }
  .ui-page.calDetail .editCal,.ui-page.calDetails .editCal{
    width: 100px;
      background: #f3f8fa;
      border-radius: 3px;
      text-align: center;
      padding: 5px 10px;
      font-size: 13px;
      cursor: pointer;
      color: #007cff;
      /* margin: 0 0 0 auto;
      margin-top: 50px; */
      margin: 5px auto 8px;
  }
  /* .editCal {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
} */
.calendar-content {
    /* padding-bottom: 50px !important; */
}
@media (max-width: 340px){
    .scheduleContent #scheduleDate .arm-calendar-default .calendar-title{
        height: 30px;
        line-height: 30px;
        margin: 0 38px;
    }
    .scheduleContent #scheduleDate .arm-calendar-default .calendar-tool{
        height: 30px;
        line-height: 30px;
        width: 30px;
        margin-top: -16px;
    }
    .scheduleContent #scheduleDate .arm-calendar-default .calendar-grid .grid{
      min-height: 30px;
        max-width: 30px;
    }
}

.schedule-left {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
}
.schedule-top {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.schedule-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.schedule-info {
    width: calc(100% - 30px);
    display: flex;
    justify-content:space-between;
}
.schedule-del {
    font-size: 10px;
    white-space: nowrap;
    padding: 3px 5px;
    background: #d31133;
    box-sizing: border-box;
    color: #fff;
    border-radius: 5px;
    margin-right: 5px;
    display: none;
}
.schedule-location {
    width: 60%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.schedule-location span {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-size: 12px;
}
.schedule-time-text{
    font-size: 12px;
}
.schedule-time, .schedule-location{
    display: flex;
    align-items: center;
}
.schedule-time{
}
.schedule-time svg, .schedule-location svg {
    margin-top: -2px;
}
.schedule-name {
    background: rgb(0, 128, 193);
    border-radius: 6px;
    color: rgb(255, 255, 255);
    width: 16px;
    height: 16px;
    font-size: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    box-sizing: border-box;
}
.schedule-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 12px;
}
.delCalDialog .ui-modal-bd>div{
    text-align: center;
    padding: 15px 0;
  }
  .delCalDialog  .ui-modal-cnt {
    width: 90%;
    max-width: 350px;
    border-radius: 20px;
  }
  .delCalDialog  .ui-modal-cnt header {
    background: #f9fafc;
  }
  .schedule-svg{
    width: 14px;
    margin-right: 3px;
  }
  .scheduleContent .scheduleInfo .scheduleItem.nolocation .schedule-left{
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    flex-direction: row;
  }
  .scheduleContent .scheduleInfo .scheduleItem.nolocation .schedule-location{
    display: none;
  }
  .scheduleContent .scheduleInfo .scheduleItem.nolocation .schedule-info {
    width: 100px;
    display: flex;
    justify-content: flex-end;
    text-align: right;
}
