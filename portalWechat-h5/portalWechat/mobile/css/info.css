
.ysf-box {
    /* display: -webkit-box !important; */
    display: box !important;
    /* position: relative; */
}

.ysf-box-f1 {
    position: relative;
    -webkit-box-flex: 1;
    box-flex: 1;
}

.ba-c {
    display: -webkit-box !important;
    display: box !important;
    position: relative;
    -webkit-box-align: center;
    box-align: center;
}

.bp-c {
    display: -webkit-box !important;
    display: box !important;
    position: relative;
    -webkit-box-pack: center;
    box-pack: center;
}

.bo-v {
    -webkit-box-orient: vertical;
    box-orient: vertical;
}

.ub-img {
    -webkit-background-size: contain;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.bg-content {
    background-color: #FFFFFF;
    margin: 0 auto;
    width: 98%;
}

.bg-body {
    background-color: #edebeb;
}
.bg-sel{
    background-color: #af251b;
    color: #FFFFFF;
}
.bg-tab{
    background-color: #b8312a;
    color: #FFFFFF;
}
.hei {
    height: 100%;
}
.bg-head{
    background-color: #FFFFFF;
}
.bg-headbg {
    background-image: url("../images/info21.png");
    background-size: cover;
    height: 20%;
    /*width: 100%;*/
}
.hid{
    display: none;
}
/*����*/
.fts21 {
    font-size: 21px;
    color: #1f1515;
}
.fts13 {
    font-size: 13px;
}
.fts14 {
    font-size: 15px;
}
.fts15{
    font-size: 15px;
}
.fts16{
    font-size: 16px;
}
.fts17 {
    font-size: 17px !important;
    margin-bottom: 5px;
}
.fts18{
    font-size: 18px;
}
.ftc01 {
    color: #807474;
}
.ftc02{
    color: #af251b;
}
.ftc03{
    color: #a5a5a5;
}
.ftc04{
    color: #1f1515;
}
.ftc05{
    color: black;
}
.ftc06{
    color: #666666;
}
.ftc07{
    color: #af251b;
}
.ftc08{
    color: #4c4c4c;
}
.ftc09{
    color: #6485bd;
}
.tx-c{
    text-align: center;
}
/*�߾�*/
.pad01 {
    padding: 0 15px;
}

.pad02 {
    padding: 15px 0 10px 0;
}

.pad03 {
    padding: 20px 15px;
}
.pad04{
    padding: 10px 15px 0 15px;
}
.pad05{
    padding-left: 20px;
}
.pad06{
    padding: 15px;
}
.pad07{
    padding: 0 8px;
}
.pad08{
    padding: 18px 0 16px 0;
}
.pad09{
    padding-bottom: 15px;
    /*padding-bottom: 5px;*/
}
.pad10{
    padding-right: 10px;
}
.pad11{
    padding-left: 15px;
}
.pad12{
    padding: 15px 15px 15px 0;
    /*padding: 0px 15px 0px 0;*/
}
.pad13{
    padding: 0 10px;
}
.pad14{
    padding: 10px;
}
.pad15{
    padding: 0px 15px 0px 0;
}
.pad16{
    padding-bottom: 5px;
    display: flex !important;
    justify-content: space-between;
    align-items: center;
}

.mar01{
    margin-top: 15px;
}
.ov-h{
    overflow: hidden;
}
.flo-l{
    float: left;
}

.touxiang {
   /* width: 100px;*/
    height: 100px;
    /*border-radius: 100px;*/
    background-color: #c5c5c5;
    border: 2px solid #FFFFFF;
}

.sex {
    height: 24px;
    width: 24px;
}
.countday{
    height: 20px;
    padding: 5px 15px;
    border-radius: 20px;
    background-color: #FFFFFF;
    border: 1px solid #e0ddd7;
    font-size: 12px;
    color: #534d4d;
    position: absolute;
    bottom: 24px;
    left: 50%;
    margin-left: -30px;
}
.pos-tx {
    position: relative;
    bottom: 30px;
}

.tx-r {
    text-align: right;
}

.border-b {
    border-bottom: 1px solid #e0ddd7;
}
.border-r{
    border-right: 1px solid #e0ddd7;
}
.border-t{
    border-top: 1px solid #e0ddd7;
}
.wid50{
    width: 50%;
}
.wid65{
    width: 65%;
}

.fts-email {
    background-image: url("../images/info02.png");
    width: 24px;
    height: 16px;
    margin-right: 10px;
}

.fts-dept {
    background-image: url("../images/info03.png");
    width: 24px;
    height: 20px;
    margin-right: 10px;
}
.fts-wifi{
    background-image: url("../images/info04.png");
    width: 24px;
    height: 17px;
    margin-right: 10px;
}
.fts-study{
    background-image: url("../images/info05.png");
    width: 24px;
    height: 19px;
    margin-right: 10px;
}
.fts-job{
    background-image: url("../images/info06.png");
    width: 24px;
    height: 22px;
    margin-right: 10px;
}
.fts-star{
    background-image: url("../images/info07.png");
    width: 24px;
    height: 26px;
    margin-right: 10px;
}
.fts-phone{
    background-image: url("../images/info10.png");
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
.fts-update{
    background-image: url("../images/info12.png");
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
.fts-college{
    background-image: url("../images/info13.png");
    width: 24px;
    height: 19px;
    margin-right: 10px;
}
.fts-dorm{
    background-image: url("../images/info14.png");
    width: 24px;
    height: 23px;
    margin-right: 10px;
}
.fts-class{
    background-image: url("../images/info15.png");
    width: 24px;
    height: 20px;
    margin-right: 10px;
}
.fts-coming{
    background-image: url("../images/info16.png");
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
.fts-busy{
    background-image: url("../images/info17.png");
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
.fts-profession{
    background-image: url("../images/info18.png");
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
.fts-more{
    background-image: url("../images/info19.png");
    width: 5px;
    height: 18px;
    margin-right: 10px;
}
.fts-time{
    background-image: url("../images/info20.png");
    width: 14px;
    height: 14px;
    margin-right: 10px;
}
.fts-motion01{
    background-image: url("../images/motion01.png");
    width: 30px;
    height: 27px;
    margin-right: 12px;
}
.fts-motion02{
    background-image: url("../images/motion02.png");
    width: 30px;
    height: 27px;
    margin-right: 10px;
}
.fts-motion03{
    background-image: url("../images/motion03.png");
    width: 27px;
    height: 27px;
    margin-right: 10px;
}
.fts-motion04{
    background-image: url("../images/motion04.png");
    width: 30px;
    height: 27px;
    margin-right: 10px;
}
.fts-show{
    background-image: url("../images/show.png");
    width: 13px;
    height: 8px;
}
.fts-hide{
    background-image: url("../images/hide.png");
    width: 13px;
    height: 8px;
}
.fts-selected{
    background-image: url("../images/motion05.png");
    width: 25px;
    height: 15px;
    position: relative;
    right: -60px;
    top: -57px;
}
.ui-form-item input, .ui-form-item textarea {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: none;
    border: 0;
    background: 0 0;
    padding-left: 0;
}
.btn-search{
    background-color: #004386 ;
    border-color: #004386;
    background-image: -webkit-gradient(linear,left top,left bottom,color-stop(.5,#004386 ),to(#004386));
    color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.cicle{
    height: 8px;
    width: 8px;
    border-radius: 8px;
    background-color: #ed573f;
}
.theme{
    background-color: #88c057;
    padding: 0 15px;
    border-radius: 15px;
    color: #FFFFFF;
}
.pos-head{
    position: absolute;
    top: 0;
    left: 15px;
}
.wh-arrow{
    width: 15px;
    height: 25px;
}
.hw-sel{
    width: 14px;
    height: 15px;
}
.pos-sel{
    position: absolute;
    top: 0;
    right: 15px;
}

.ui-page .ui-modal-cnt {
    width: 82%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    border-radius: 0;
}
.ui-modal-cnt {
    width: 86%;
    max-width: 320px;
    max-height: 100%;
    -webkit-background-clip: padding-box;
    pointer-events: auto;
    position: relative;
    font-size: 16px;
    margin: 0;
    background: #fff;
    overflow: hidden;
    border-radius: 6px;
    /*left: 36px;*/
}


.seltype{
    min-width: 85px;
    padding: 12px 0;
    border-radius: 5px;
    text-align: center;
    margin: 0 8px 10px 0;
}
.ui-iframe .ui-modal-bd, .ui-page .ui-modal-bd {
    -webkit-overflow-scrolling: touch;
    overflow: auto;
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0;
    margin: 0;
    padding: 0;
    border-bottom: none;
    border-top: none;
    max-height: 100%;
}
.type-noselect{
    background-color: #f0f0f0;
}
.type-selected{
    background-color: #ffeeeb;
    color: #af251b;
}
.pos-jiao{
    /*position: relative;*/
}
.ui-tab-nav li.current {
    color: #1c5197;
    /* border-bottom: 2px #1c5197 solid; */
    border-bottom: none !important;
}

.business{
    /*width: 15px;*/
    height: 18px;
    border-radius: 15px;
    background-color: #1c5197;
    color: #FFFFFF;
    font-size: 10px;
    position: absolute;
    bottom: 10px;
    right: -15px;
    min-width: 18px;
    line-height: 18px;
    text-align: center;
}
.bg-search{
    /* background-color: #e4e4e4; */
}
.ui-searchbar-wrap {
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    background-color: #f9f9f9;
    height: 45px;
}

.lh17{
    line-height: 17px!important;
}

/*΢����ҳ*/
.wx-pad01{
    padding: 15px 18px;
}
.wx-pad02{
    padding: 0 10px 10px 0;
}
.wx-pad03{
    padding: 10px;
}
.wx-pad04{
    padding: 20px;
}
.wx-pad05{
    padding: 11px 20px;
}
.wx-pad06{
    padding: 0 18px;
}
.wx-pad07{
    padding: 15px 0;
}
.wx-mar01{
    margin: 10px 0;
}
.bg-human{
    background-color: #4f96d4;
    color: #FFFFFF;
}
.bg-teaching{
    background-color: #ddb860;
    color: #FFFFFF;
}
.bg-logistics{
    background-color: #ee756c;
    color: #FFFFFF;
}
.bg-info{
    background-color: #76aa7a;
    color: #FFFFFF;
}
.bg-swzx{
    background-color: #d6554c;
    color: #FFFFFF;
    border-radius: 5px;
}
.bg-swts{
    background-color: #ddb860;
    color: #FFFFFF;
    border-radius: 5px;
}
.bg-swjy{
    background-color: #76aa7a;
    color: #FFFFFF;
    border-radius: 5px;
}
.bg-gzsb{
    background-color: #4f97d4;
    color: #FFFFFF;
    border-radius: 5px;
}
.wid80{
    width: 80%;
}
.wx01{

    width: 24px;
    height: 20px;
}
.wx02{

    width: 21px;
    height: 20px;
}
.wx03{

    width: 25px;
    height: 20px;
}
.wx04{

    width: 23px;
    height: 20px;
}
.wx05{
    background-image: url("../wc_img/wx05.png");
    width: 25px;
    height: 25px;
}
.wx06{
    background-image: url("../wc_img/wx06.png");
    width: 28px;
    height: 25px;
}
.wx07{
    background-image: url("../wc_img/wx07.png");
    width: 29px;
    height: 25px;
}
.wx08{
    background-image: url("../wc_img/wx08.png");
    width: 23px;
    height: 25px;
}
.wx09{
    background-image: url("../wc_img/wx09.png");
    width: 10px;
    height: 10px;
    margin-right: 10px;
}
.wx09_1{
    background-image: url("../wc_img/wx09_1.png");
    width: 10px;
    height: 10px;
    margin-right: 10px;
}
.wx10{
    background-image: url("../wc_img/wx10.png");
    width: 31px;
    height: 22px;
}
.wx11{
    background-image: url("../wc_img/wx11.png");
    width: 25px;
    height: 22px;
}
.wx12{
    background-image: url("../wc_img/wx12.png");
    width: 26px;
    height: 26px;
}
.wx13{
    background-image: url("../wc_img/wx13.png");
    width: 25px;
    height: 26px;
}
.wx14{
    background-image: url("../wc_img/wx14.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx15{
    background-image: url("../wc_img/wx15.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx16{
    background-image: url("../wc_img/wx16.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx17{
    background-image: url("../wc_img/wx17.png");
    width: 33px;
    height: 32px;
    margin-bottom: 10px;
}
.wx18{
    background-image: url("../wc_img/wx18.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx19{
    background-image: url("../wc_img/wx19.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx20{
    background-image: url("../wc_img/wx20.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx21{
    background-image: url("../wc_img/wx21.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx22{
    background-image: url("../wc_img/wx22.png");
    width: 34px;
    height: 32px;
    margin-bottom: 10px;
}
.wx23{
    background-image: url("../wc_img/wx23.png");
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}
.wx24{
    background-image: url("../wc_img/wx24.png");
    width: 24px;
    height: 32px;
    margin-bottom: 10px;
}
.wx25{
    background-image: url("../wc_img/wx25.png");
    width: 30px;
    height: 32px;
    margin-bottom: 10px;
}
.wx26{
    background-image: url("../wc_img/wx26.png");
    width: 29px;
    height: 29px;
    margin-bottom: 10px;
}
.wx27{
    background-image: url("../wc_img/wx27.png");
    width: 32px;
    height: 29px;
    margin-bottom: 10px;
}
.wx28{
    background-image: url("../wc_img/wx28.png");
    width: 31px;
    height: 29px;
    margin-bottom: 10px;
}

.wx29{
    background-image: url("../wc_img/wx29.png");
    width:17px;
    height: 17px;
    margin-right:10px;
}
.wx30{
    background-image: url("../wc_img/wx30.png");
    width:16px;
    height: 19px;
    margin-right:10px;
}
.wx31{
    background-image: url("../wc_img/wx31.png");
    width:17px;
    height: 15px;
    margin-right:10px;
}
.wx32{
    background-image: url("../wc_img/wx32.png");
    width:17px;
    height: 16px;
    margin-right:10px;
}
.wx33{
    background-image: url("../wc_img/wx33.png");
    width:17px;
    height: 16px;
    margin-right:10px;
}
.wx-padyx{
    padding: 15px 8px 15px 18px;
}

.ui-list>li {
     margin-left: 0px;
}
