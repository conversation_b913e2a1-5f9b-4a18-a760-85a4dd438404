.tab_list {
  width: 100%;
  height: 48px;
}

.tab_list .nav {
  width: 50px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  display: inline-block;
  font-size: 17px;
  color: #848a92;
}

.tab_list .current {
  color: #000;
  border-bottom: 4px solid #1e97ff;
}

.tab_con {
  padding: 5% 0;
}

.item {
  display: none;
}

.ui-searchbar-wrap {
  background-color: #f6f7fc !important;
  border-radius: 20px;
  margin: 0% 3%;
}
.ui-searchbar {
  /* height: 35px !important; */
  background-color: #f6f7fc !important;
}

.demo-desc {
  font-size: 18px;
  margin-left: 6%;
  position: relative;
}

.titleBg {
  /* width: 109px; */
  height: 10px;
  background-color: #caf1f1;
  position: absolute;
  bottom: 1px;
  z-index: -1;
  left: -7px;
  border-radius: 15px;
}

.demo-block {
  padding: 5px 0px;
}

.appIcon {
  /* width: 25%;
    margin: 15px 0px;   */
  text-align: center;
  margin: 10px 0px;
}

.appIcon img {
  width: 48px;
}

.appItem {
  /* background: pink; */
  /* width: 25%; */
  text-align: center;
  margin: 10px 0px;
}

.appList {
  margin-top: 20px;
}

.appItem img {
  width: 48px;
}

.apartment {
  background: #fff;
  padding: 10px;
  margin-top: 10px;
  border-radius: 15px 15px 0px 0px;
}

.ui-searchbar-text {
  margin-right: 234px;
}

.apTitle div {
  width: 85%;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.apTitle img {
  width: 15px;
  display: inline-block;
  margin-left: 25px;
}

.rotate {
  -webkit-transform: rotate(180deg);
}
