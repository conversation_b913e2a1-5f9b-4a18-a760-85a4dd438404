@charset "UTF-8";
.clear {
	width: 100%;
	height: 0;
	line-height: 0;
	font-size: 0;
	overflow: hidden;
	clear: both;
	display: block;
	_display: inline;
}

.none {
	display: none;
}

.hidden {
	visibility: hidden;
}

.clearfix:after {
	clear: both;
	content: ".";
	display: block;
	height: 0;
	visibility: hidden;
}

.clearfix {
	display: block;
	*zoom: 1;
}
body{
	background: #fff;
	padding: 0;
	margin: 0;
    min-width: 280px;
    overflow: hidden;
}
.searchContent{
	width: 100%;
	margin: 0 auto;
	padding: 16px 20px;
	box-sizing: border-box;
    margin-top: -260px;
    position: relative;
    z-index: 2;
}
.searchContent .searchBox{
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 15px;
}
.searchContent .searchBox .searchBtn{
	width: 40px;
	text-align: center;
}
.searchContent .searchBox .inputBox{
	width: calc(100% - 40px);
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    
    box-sizing: border-box;
	padding: 8px 15px;
    border-radius: 12px;
    background: rgba(245, 247, 255,0.3);
}
.searchContent .searchBox .inputBox .svgImg{
	display: flex;
	justify-content: center;
	align-items: center;
}
.searchContent .searchBox .inputBox .svgImg svg{
	width: 16px;
	height: 16px;
	vertical-align: middle;
}
.searchContent .searchBox .inputBox .svgImg.searchIcon{
	margin-right: 8px;
}
.searchContent .searchBox .inputBox .svgImg.searchIcon path{
	fill: #fff
	
}
.searchContent .searchBox .inputBox .svgImg.cancelIcon{
	padding: 0px 5px;
	box-sizing: border-box;
	position: absolute;
	right: 5px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
	height: 30px;
}
.searchContent .searchBox .inputBox .svgImg.cancelIcon path{
	/* fill: rgba(0, 0, 0, 0.54); */
	fill: #fff;
}
.searchContent .searchBox .inputBox input{
	height: 22px;
	line-height: 22px;
	background: transparent;
	outline: 0;
	border: 0;
	width: calc(100% - 40px);
	font-size: 15px;
	color: #fff;
	/* padding: 4px 10px; */
}
.searchContent .searchBox .inputBox input::placeholder{
	color: #fff;
}


.searchContent .searchBox .inputBox .svgImg.cancelIcon.hideBtn{
    display: none;
}
.searchContent .searchBox .searchBtn{
	font-size: 14px;
    text-transform: uppercase;
	color: #fff;
    width: 40px;
    margin: 0px;
    text-decoration: none;
	white-space: nowrap;
	box-sizing: border-box;
}

.searchContent .mainTabContent{
	border-radius: 12px;
	box-shadow: rgba(0, 33, 123, 0.1) 0px 3px 12px 0px;
	margin: initial;
	position: static;
	overflow: hidden;
	background: #fff;
	position: relative;
	z-index: 1;
	height: calc(100% - 68px);
}
.searchContent .mainTabContent .searchTabs{
	position: relative;
    display: flex;
    flex: 1 1 auto;
    white-space: nowrap;
    scrollbar-width: none;
    overflow: auto hidden;
	background: linear-gradient(rgb(232, 238, 255) 0%, rgb(255, 255, 255) 100%);
}
.searchContent .mainTabContent .searchTabs .tabItem{
	display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    background-color: transparent;
    margin: 0px;
    vertical-align: middle;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.25;
    min-width: 90px;
    position: relative;
    min-height: 48px;
    flex-shrink: 0;
    overflow: hidden;
    white-space: normal;
    text-align: center;
    flex-direction: column;
    color: rgba(0, 0, 0, 0.45);
	flex: 1 1 0%;
    padding: 10px 0px 5px !important;
}
.searchContent .mainTabContent .searchTabs .tabItem.active{
	color: rgb(47, 101, 174);
	border-radius: 12px 12px 0px 0px;
    background: rgb(255, 255, 255);
	position: relative;
}
.searchContent .mainTabContent .searchTabs .tabItem.active:after{
	content: "";
    position: absolute;
    left: 50%;
    bottom: 0px;
    width: 24px;
    height: 4px;
    border-radius: 2px;
    background: rgb(255, 154, 2);
    transform: translateX(-50%);
}
.searchContent .mainTabContent .searchInfo{
	/* overflow: auto; */
	overflow: hidden;
	padding-left: 16px;
	padding-bottom: initial;
	padding-top: initial;
	padding-right: 16px;
	/* height: 100%; */
}
.searchContent .mainTabContent .searchInfo .zx .item{
	display: flex;
    flex-flow: column;
    -webkit-box-align: stretch;
    align-items: stretch;
    -webkit-box-pack: start;
    justify-content: flex-start;
    padding-top: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgb(234, 234, 234);
}
.searchContent .mainTabContent .searchInfo .zx .item .title{
	color: rgb(0,0,0);
	font-size: 16px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
.searchContent .mainTabContent .searchInfo .zx .item .date{
	color: rgb(157, 158, 159);
    font-size: 0.875em;
    word-break: break-all;
    margin-top: 6px;
    margin-bottom: 6px;
	font-size: 14px;
}
.searchContent .mainTabContent .searchInfo .zx .item .summary{
	color: rgb(157, 158, 159);
    font-size: 14px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
.searchContent .mainTabContent .apps{
	display: flex;
    flex-flow: wrap;
    align-items: flex-start;
    -webkit-box-pack: start;
    justify-content: flex-start;
    padding-left: 0px;
    padding-top: 16px;
    padding-right: 0px;
}
.searchContent .mainTabContent .searchInfo .apps .item{
	min-height: 96px;
    width: 25%;
    padding:8px 5px;
   margin-bottom:10px;
   box-sizing: border-box;
   text-align:center;
}
.searchContent .mainTabContent .searchInfo .apps .item img{
    width: 40px;
    height: 40px;
	margin: 0 auto;
}
.searchContent .mainTabContent .searchInfo .apps .item .svgImg{
    width: 40px;
    height: 40px;
    margin: 0 auto;
    padding: 8px;
    border-radius: 8px;
    background-color: var(--colorPrimary);
}
.searchContent .mainTabContent .searchInfo .apps .item .svgImg svg{
    width: 100%;
    height: 100%;
}
.searchContent .mainTabContent .searchInfo .apps .item .svgImg svg path,
.searchContent .mainTabContent .searchInfo .apps .item .svgImg svg rect,
.searchContent .mainTabContent .searchInfo .apps .item .svgImg svg polygon,
.searchContent .mainTabContent .searchInfo .apps .item .svgImg svg circle{
  fill: #fff;
  stroke: transparent;
}
.searchContent .mainTabContent .searchInfo .apps .item .name{
	margin-top: 10px;
	white-space: unset;
    text-overflow: ellipsis ;
    word-break: break-all ;
    overflow: hidden ;
    display: -webkit-box ;
    -webkit-box-orient: vertical ;
    -webkit-line-clamp: 2 ;
	font-size: 14px;
    color: rgb(35, 35, 35);
}
.searchContent .mainTabContent .searchInfoContent{
    max-height: 100%;
    overflow-y: auto;
    position: relative;
	display: none;
}
.searchContent .mainTabContent .searchInfoContent.active{
	display: block;
}
.searchContent .mainTabContent .searchInfoContent.apps.active{
	display: flex;
}
.searchContent .mainTabContent .searchInfoContent.apps{
    padding-bottom: 20px;
    box-sizing: border-box;
}
.searchContent .mainTabContent .searchInfoContent.apps .layui-flow-more{
    width: 100%;
    height: 20px;
    
}
.searchContent .mainTabContent .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
}
.searchContent .mainTabContent .nodataBox .noDataImg{
    width: 80px;
    height: 60px;
}
.searchContent .mainTabContent .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}
