body{overflow: hidden;}
.articleContent{
    width: 100%;
    margin: 0 auto;
    padding: 16px 20px 0;
    box-sizing: border-box;
    margin-top: -210px;
    position: relative;
    z-index: 2;
    height: calc(100% - 68px);
}
.articleContent .articleTab{
    width: calc(100% - 16px);
    margin: 0 auto;
    /* overflow: hidden; */
    padding-left: 8px;
    box-sizing: border-box;
    padding-right: 8px;
    overflow-x: auto;
}
.articleContent .articleTab::-webkit-scrollbar{display: none;}
.articleContent .articleTab{scrollbar-width: none;}
.articleContent .articleTab{-ms-overflow-style: none;}
.articleContent .articleTab .articleTabBox{
    white-space: nowrap;
}
.articleContent .articleTab .tabItem{
    display: inline-block;
    padding: 10px 10px;
    color: #fff;
    font-size: 16px;
    position: relative;
    
}
.articleContent .articleTab .tabItem.active{
    background: #fff;
    border-radius: 12px 12px 0 0;
    color: #373737;
    font-size: 18px;
}
.articleContent .articleTab .tabItem.active:before{
    content: "";
    position: absolute;
    width: 9px;
    height: 9px;
    bottom: 0;
    left: -9px;
    background: radial-gradient(circle at 0px 0px, transparent 9px, #fff 9px);
}
.articleContent .articleTab .tabItem.active:after{
    content: "";
    position: absolute;
    right: -9px;
    bottom: 0px;
    width: 9px;
    height: 9px;
    background: radial-gradient(circle at 9px 0px, transparent 9px, #fff 9px);
}
.articleContent .articleTab .tabItem span{
    position: relative;
}
.articleContent .articleTab .tabItem.active span:after{
    position: absolute;
    content: '';
    height: 4px;
    width: 35%;
    background: #007CFF;
    left: 50%;
    transform: translateX(-50%);
    bottom: -10px;
    border-radius: 2px;
}
.articleContent .articleInfo{
    width: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 10px;
    min-height: 300px;
    position: relative;
    box-sizing: border-box;
    height: 100%;
}
.articleContent .subItemTab{
    /* width: calc(100% - 30px); */
    margin: 0 auto;
    overflow: hidden;
    box-sizing: border-box;
    overflow-x: auto;
    white-space: nowrap;
    /* padding-bottom: 10px; */
}
.articleContent .subItemTab::-webkit-scrollbar{display: none;}
.articleContent .subItemTab{scrollbar-width: none;}
.articleContent .subItemTab{-ms-overflow-style: none;}
.articleContent .subItemTab .subItem {
    display: inline-block;
    padding: 5px 10px;
    box-sizing: border-box;
    border-radius: 15px;
    color: #666;
    font-size: 16px;
}
.articleContent .subItemTab .subItem.active{
    background: #0967ac;
    color: #fff;
}
.articleContent .articleInfo .articleList{
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    height: calc(100% - 30px);
    overflow-y: auto;
    z-index: 1;
}
.articleContent .articleInfo .articleInfoBg{
    position: absolute;
    width: 35%;
    object-fit: cover;
    bottom: 20px;
    right: 0;
    max-width: 300px;
    z-index: 0;
}
.articleContent .articleInfo .articleList .noData{
    width: 100%;
    text-align: center;
    padding: 10px 0;
}
.articleContent .articleInfo .articleList .articleItem{
    padding: 20px 0;
    border-bottom: 1px solid #DFE5ED;
}
.articleContent .articleInfo .articleList .articleItem:first-child img{
    width: 100%;
    object-fit: cover;
    border-radius: 10px;
    max-height: 220px;
    margin-bottom: 12px;
}
.articleContent .articleInfo .articleList .articleItem:last-child{
    border: 0;
}
.articleContent .articleInfo .articleList .articleItem .title{
    color: #263238;
    font-size: 16px;
    margin: 0px 0  16px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    max-height: 3em;
    line-height: 1.5em;
    font-weight: bold;
    word-break: break-all;
}
.articleContent .articleInfo .articleList .articleItem:first-child .title{
    margin-bottom: 5px;
}
.articleContent .articleInfo .articleList .articleItem .date{
    color: #007CFF;
    font-size: 15px;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.articleContent .articleInfo .articleList .articleItem.itemFlex{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.articleContent .articleInfo .articleList .articleItem.itemFlex img{
    width: 140px;
    object-fit: cover;
    height: 100px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 10px;
}
.articleContent .articleInfo .articleList .articleItem.itemFlex .rightInfo{
    width: calc(100% - 150px);
    box-sizing: border-box;
}
.articleContent .articleInfo .articleList .articleItem.itemFlex .rightInfo.fullWidth{
    width: 100%;
}
.articleContent .articleInfo .articleList .articleItem.itemFlex .rightInfo .title{
      display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
}

/* 分页样式 */
#articlePages{
    text-align: center;
    margin-top: 10px;
}
#articlePages .layui-laypage a,#articlePages .layui-laypage span{
background: #f8f8f8;
padding: 0 12px;
}
#articlePages .layui-laypage a,#articlePages .layui-laypage button,#articlePages .layui-laypage input,
#articlePages .layui-laypage select,#articlePages .layui-laypage span{
    border: 1px solid #d3d3d3;
}

.articleContent .articleInfo .articleList .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
}
.articleContent .articleInfo .articleList .nodataBox .noDataImg{
    width: 80px;
    height: 60px;
}
.articleContent .articleInfo .articleList .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}
@media (max-width: 400px){
    .articleContent .articleTab .tabItem {
        padding: 10px 8px;
        font-size: 15px;
    }
    .articleContent .articleTab .tabItem.active{
        font-size: 16px;
    }
    .articleContent .subItemTab .subItem,
    .articleContent .articleInfo .articleList .articleItem .title{
        font-size: 15px;
        /* margin-bottom: 10px; */
    }
    .articleContent .articleInfo .articleList .articleItem .date{
        font-size: 14px;
    }
    .articleContent .articleInfo .articleList .articleItem.itemFlex img{
        width: 35%;
        max-height: 90px;
    }
    .articleContent .articleInfo .articleList .articleItem.itemFlex .rightInfo{
        width: calc(65% - 10px);
    }

}

@media (max-width: 340px){
    .articleContent .articleTab .tabItem {
        font-size: 14px;
    }
    .articleContent .articleTab .tabItem.active{
        font-size: 15px;
    }
    .articleContent .articleInfo .articleList .articleItem.itemFlex img{
        max-height: 80px;
    }
}