*{
    padding: 0;
    margin: 0;
}
body{
    overflow: hidden;
}
.taskContent{
    width: 100%;
    margin: 0 auto;
    padding: 16px 20px;
    box-sizing: border-box;
    margin-top: -260px;
    position: relative;
    z-index: 2;
    height: calc(100% - 81px);
}
.taskContent .taskSearchBox{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.taskCategory{
    margin: 0;
    position: relative;
    width: 120px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 12px;
    text-align: left;
    border: 0;
    color: transparent;
    /* overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; */
    height: 46px;
    line-height: 46px;
    padding-left: 10px;
    overflow: hidden;
}
.layui-btn-container{
    overflow: hidden;
}
.layui-btn-container .ui-select:after{
    content: '';
    position: absolute;
    right: 10px;
    top: calc(50% + 8px);
    transition: all .3s;
    -webkit-transition: all .3s;
    padding: 0;
    transform: translateY(-50%);
    border: 8px solid;
    border-color: #fff transparent transparent;
}
.layui-btn-container .layui-btn.taskCategory .layui-icon.layui-edge{
    position: absolute;
    right: 10px;
    top: calc(50% + 4px);
    border-width: 7px;
    border-top-color: #fff;
    border-top-style: solid;
    transition: all .3s;
    -webkit-transition: all .3s;
    padding: 0;
    transform: translateY(-50%);
}
.layui-btn-container input{
    background: transparent;
    border: none;
    position: absolute;
    /* top: 15px; */
    /* z-index: 1; */
    box-sizing: border-box;
    width: 80px;
    overflow: hidden !important;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 46px;
    color: #ffffff;
    left: 30px;
}
.taskContent .searchBox{
    display: flex;
    align-items: center;
    height: 46px;
    line-height: 46px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 10px;
    width: calc(100% - 130px);
    padding: 0 10px;
    box-sizing: border-box;
}
.taskContent .searchBox  .svgImg{
	display: flex;
	justify-content: center;
	align-items: center;
}
.taskContent .searchBox  .svgImg svg{
	width: 16px;
	height: 16px;
	vertical-align: middle;
}
.taskContent .searchBox  .svgImg svg path{
    fill: #fff;
}
.taskContent .searchBox  .svgImg.searchIcon{
	margin-right: 8px;
}
.taskContent .searchBox input{
    outline: 0;
    border: 0;
    background: transparent;
    color: #fff;
    width: calc(100% - 5px);
    line-height: 20px;
    height: 20px;
    font-size: 15px;
}
.taskContent .searchBox input::placeholder{
    color: #fff;
}
.taskContent .taskInfo{
    margin-top: 20px;
    filter: drop-shadow(0px 2px 6px rgba(113, 143, 174, 0.1529));
    height: calc(100% - 70px);
}
.taskContent .taskInfo .taskTab{
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
  
}
/* .taskContent .taskInfo .taskTab::-webkit-scrollbar{display: none;}
.taskContent .taskInfo .taskTab{scrollbar-width: none;}
.taskContent .taskInfo .taskTab{-ms-overflow-style: none;} */
.taskContent .taskInfo .taskTab .taskTabItem{
    display: inline-block;
    padding: 10px 5px;
    font-size: 16px;
    position: relative;
    color: #fff;
    flex: 1;
    text-align: center;
}
.taskContent .taskInfo .taskTab .taskTabItem.active{
    background: #fff;
    border-radius: 12px 12px 0 0;
    color: #373737;
    font-size: 17px;
}
.taskContent .taskInfo .taskTab .taskTabItem.active:before{
    content: "";
    position: absolute;
    width: 9px;
    height: 9px;
    bottom: 0;
    left: -9px;
    background: radial-gradient(circle at 0px 0px, transparent 9px, #fff 9px);
}
.taskContent .taskInfo .taskTab .taskTabItem:first-child.active:before{
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    bottom: -12px;
    left: 0px;
    background: radial-gradient(circle at 12px 12px, transparent 12px, #fff 12px);
}
.taskContent .taskInfo .taskTab .taskTabItem.active:after{
    content: "";
    position: absolute;
    right: -9px;
    bottom: 0px;
    width: 9px;
    height: 9px;
    background: radial-gradient(circle at 9px 0px, transparent 9px, #fff 9px);
}
.taskContent .taskInfo .taskTab .taskTabItem:last-child.active:after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: -12px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 0px 12px, transparent 12px, #fff 12px);
}
.taskContent .taskInfo .taskInfoContent{
    min-height: 200px;
    background: #fff;
    border-radius: 12px;
    padding: 10px 10px 20px 10px;
    box-sizing: border-box;
    position: relative;
    height: calc(100% - 44px);
}

.taskContent .taskInfo .taskInfoContent .taskInfoBg{
    position: absolute;
    width: 35%;
    object-fit: cover;
    bottom: 0;
    right: 0;
    max-width: 384px;
    z-index:0;
}
.taskContent .taskInfo .taskList{
   /* width: 100%;
   position: relative;
   height: 100%;
   overflow-y: auto; */
   z-index: 1;
}
.taskContent .taskInfo .taskList .taskItem{
    padding: 15px 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    position: relative;
}
/* .taskContent .taskInfo .taskList .nodata{
    padding: 10px 0;
    text-align: center;
    font-size: 14px;
    color: #373737;
} */
.taskContent .taskInfo .taskList .taskItem:nth-child(5n+1){
    /* background: #EFF0FA; */
    background: rgba(239, 240, 250, 0.5);
    border-left: 4px solid #6572F7;
}
.taskContent .taskInfo .taskList .taskItem:nth-child(5n+2){
    /* background: #FFEFE7; */
    background:rgba(255,239,231,0.5);
    border-left: 4px solid #FF915A;
}
.taskContent .taskInfo .taskList .taskItem:nth-child(5n+3){
    background: rgba(137,208,248,0.13);
    border-left: 4px solid #559EFA;
}
.taskContent .taskInfo .taskList .taskItem:nth-child(5n+4){
    background: rgba(254,117,102,0.06);
    border-left: 4px solid #FE7566;
}
.taskContent .taskInfo .taskList .taskItem:nth-child(5n+5){
    /* background: #EEF8F7; */
    background: rgba(238,248,247,0.5);
    border-left: 4px solid #42E6D6;
}
.taskContent .taskInfo .taskList .taskItemInfo{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    /* 两行 */
    flex-wrap: wrap;
}
.taskContent .taskInfo .taskList .title{
    font-size: 15px;
    color: #252525;
    width: calc(100% - 30px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: bold;
}
.taskContent .taskInfo .taskList .taskItemInfo span{
    vertical-align: middle;
}
.taskContent .taskInfo .taskList .taskItemInfo .from,
.taskContent .taskInfo .taskList .taskItemInfo .creater,
.taskContent .taskInfo .taskList .taskItemInfo .createTime{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 11px;
    color: #656565;
}
.taskContent .taskInfo .taskList .taskItemInfo .from{
    min-width: 70px;
    margin-right: 3px;
    flex: 1;
}
.taskContent .taskInfo .taskList .taskItemInfo .createTime{
    /* width: 38%; */
    width: 50%;
    min-width: 153px;
}
.taskContent .taskInfo .taskList .taskItemInfo .creater{
    width: 135px;
    min-width: 70px;
}
.taskContent .taskInfo .taskList .taskItemInfo.todoInfo .createTime{
    margin-top: 5px;
    width: 100%;
}
.taskContent .taskInfo .taskList .taskItemInfo .taskItemInfo-form{
    display: flex;
    justify-content: space-around;
    flex-direction: row;
    align-items: center;
    width: 100%;
}
.taskContent .taskInfo .taskList .taskItemInfo .from span:last-child,
.taskContent .taskInfo .taskList .taskItemInfo .creater span:last-child,
.taskContent .taskInfo .taskList .taskItemInfo .createTime span:last-child
{
    
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.taskContent .taskInfo .taskList .taskItemInfo .from span:last-child{
    max-width: calc(100% - 33px);
}
.taskContent .taskInfo .taskList .taskItemInfo .creater span:last-child{
    max-width: calc(100% - 44px);
}
.taskContent .taskInfo .taskList .taskItemInfo .createTime span:last-child{
    max-width: calc(100% - 55px);
}
.taskContent .taskInfo .taskMore{
    width: 100%;
    padding: 0px 0;
    text-align: center;
    color: #007CFF;
    font-size: 16px;
}

/* 下拉框样式 */
.layui-dropdown .layui-menu{
    max-height: calc(100vh - 180px);
    overflow-y: auto;
}
.layui-dropdown .layui-menu::-webkit-scrollbar{display: none;}
.layui-dropdown .layui-menu{scrollbar-width: none;}
.layui-dropdown .layui-menu{-ms-overflow-style: none;}

.taskList .layui-flow-more{
    font-size: 13px;
}
.taskList .layui-flow-more a cite{
    background-color:transparent;
    color: #007CFF;
}
@media (max-width: 360px){
    .taskContent .taskInfo .taskTab .taskTabItem{
        font-size: 14px;
    }
    .taskContent .taskInfo .taskTab .taskTabItem.active{
        font-size: 15px;
    }
  
  }
  @media (max-width:340px){
    .taskContent .taskInfo .taskTab .taskTabItem{
        font-size: 13px;
    }
    .taskContent .taskInfo .taskTab .taskTabItem.active{
        font-size: 14px;
    }
    .taskContent .taskInfo .taskList .taskItemInfo{
        flex-direction: column;
        align-items: flex-start;
    }
    .taskContent .taskInfo .taskList .taskItemInfo .from,
    .taskContent .taskInfo .taskList .taskItemInfo .creater,
    .taskContent .taskInfo .taskList .taskItemInfo .createTime{
       width: 100%;
    }
  }
  @media (max-width:310px){
    .taskContent .taskInfo .taskTab .taskTabItem.active{
        font-size: 13px;
    }
    .taskContent .taskInfo .taskTab .taskTabItem{
        padding: 10px 3px
    }
  }

  .taskContent .taskInfo .nodataBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
}
.taskContent .taskInfo .nodataBox .noDataImg{
    width: 80px;
    height: 60px;
}
.taskContent .taskInfo .nodata{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    width: 100%;
    padding: 5px 0;
    text-align: center;
}
.taskItem .taskItemInfo .creater.hasName{
    display: none !important;
}
.taskList{
    padding-top: 6px;
}
.taskItem-img{
    position: absolute;
    right: 0;
    top: -6px;
    width: 45px;
    display: none;
}

 .ui-list > li.draft-item{display:flex;justify-content:space-between;align-items:center;padding-right:10px}
.draft-tile{flex:1}
.bg-body{background:#fff !important}
.ui-list > li{margin-left:0px}
li{cursor:pointer}
span{cursor:pointer}
.ui-icon-search:before{content:'\e60c';font-size:42px;top:1px;color:#b6b6b6;left:3px;position:relative}
.ui-searchbar-wrap{padding:15px;background:#1c5197 !important}
.ui-searchbar.ui-border-radius{border-radius:15px;height:40px}
@media screen and (max-width:320px){.ui-container .content .demo-block .business{bottom:10px;right:-8px}
}@media screen and (max-width:280px){.ui-container .content .demo-block .business{bottom:12px;right:-4px}
}.title-circulation{color:rgb(123,175,234)}
.pad15{padding:0px 15px 0px 0;margin:5px 0}
.ftc05{color:#9f9f9f !important;margin:2px 0}
i.ui-icon-search{display:flex;justify-content:center;align-items:center}
.ui-searchbar-cancel{color:#fff !important}
.business-db.business.ba-c.bp-c.hid1{background-color:#d32f2f}
.ui-searchbar input{display:flex;justify-content:center;align-items:center}
.ui-list .box-list{padding-bottom:5px;display:flex !important;justify-content:space-between;align-items:flex-start;flex-direction:column}
.bg-body .ui-searchbar{display:flex;position:relative;align-items:center;flex:1;-webkit-box-flex:unset;-webkit-box-pack:unset;-webkit-box-align:unset}
.bg-body .ui-searchbar-wrap{display:flex;align-items:center;-webkit-box-pack:unset;-webkit-box-align:unset}
.bg-body .ui-searchbar-input{height:40px}
.bg-body .ui-searchbar input{border:0;background:0 0;color:#000;width:100%;height:40px;line-height:40px;box-sizing:border-box;padding:0}
.ui-tab .ysf-box div{display:inline-block;padding:10px 5px;font-size:16px;position:relative;color:#fff;flex:1;text-align:center}
.ui-tab-nav{background-color:transparent}
.ui-tab-nav li.current{background-color:#ffffff;background:#fff;border-radius:12px 12px 0 0;color:#373737;font-size:17px}
.ui-tab .ui-tab-nav .current .ysf-box div{color:#373737;font-size:17px;border-radius:12px 12px 0 0}
.taskInfoContent .ui-container{width:calc(100% - 20px);left:10px;right:10px;top:10px;bottom:10px;overflow:auto;z-index:0;position:absolute}
.ui-tab-nav li.current:after{content:'';position:absolute;right:-9px;bottom:0px;width:9px;height:9px;background:radial-gradient(circle at 9px 0px,transparent 9px,#fff 9px)}
.ui-tab-nav li.current::before{content:'';position:absolute;width:9px;height:9px;bottom:0;left:-9px;background:radial-gradient(circle at 0px 0px,transparent 9px,#fff 9px)}
.ui-tab-nav li:first-child.current:before{content:'';position:absolute;width:12px;height:12px;bottom:-12px;left:0px;background:radial-gradient(circle at 12px 12px,transparent 12px,#fff 12px)}
.ui-tab-nav li:last-child.current:after{content:'';position:absolute;right:0px;bottom:-12px;width:12px;height:12px;background:radial-gradient(circle at 0px 12px,transparent 12px,#fff 12px)}
.taskInfo .ui-tab-list{height:45px;margin-bottom: -1px;}
.taskContent .ui-tab-content{margin-top:0}
.ui-tab-nav{overflow-x:auto;overflow-y:hidden}
.taskContent .taskInfo .first.taskInfoContent{border-radius:0 12px 12px 12px}
.taskContent .taskInfo .last.taskInfoContent{border-radius:12px 0 12px 12px}
.taskInfo .ui-tab-nav li{min-width:50px}