body {
    background: #f1f1f1;
}
    .mainContent{
        width: 100%;
        height: 100%;
    box-sizing: border-box;
    }
    .mainContent .msgItem{
        display: flex;
        justify-content: space-between;
        padding: 20px;
        box-sizing: border-box;
        background: #fff;
        align-items: center;
        position: relative;
    }
    .mainContent .msgItem:after{
        position: absolute;
        content: "";
        width: calc(100% - 40px);
        left: 50%;
        transform: translateX(-50%);
        height: 1px;
        background: #eee;
        bottom: 0;
    }
    .mainContent .msgItem:last-child:after{
  height: 0;
    }
    .mainContent .msgItem .msgLeft{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        max-width: calc(100% - 85px);
    }
    .mainContent .msgItem .msgIcon{
        min-width: 50px;
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 50%;
        margin-right: 10px;
        background: linear-gradient(180deg, #4791e7 0%, #173279 100%);
    }
    .mainContent .msgItem .msgIcon img{
        object-fit: cover;
        width: 100%;
    }
    .mainContent .msgItem .msgInfo{
        max-width: calc(100% - 60px);
    }
    .mainContent .msgItem .msgInfo div{
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .mainContent .msgItem .msgInfo .title{
        font-size: 16px;
    }
    .mainContent .msgItem .msgInfo .tips{
        font-size: 14px;
        color: #999;
    }
    .mainContent .msgItem .msgRight{
        color: #999;
        font-size: 14px;
        max-width: 80px;
    }