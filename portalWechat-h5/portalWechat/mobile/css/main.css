@charset "UTF-8";

body {
  margin: 0;
  padding: 0;
  background-color: #fff;
  /*height: 500px;*/
  /*width: 500px;*/
  overflow: scroll !important;
  position: relative;
}

#nav2 {
  height: 45px;
}

.ui-block {
  width: 100%;
  margin-bottom: 8px;
  /* background-color: #ffffff; */
  box-sizing: border-box;
}

.ui-node-flex {
  display: flex;
  /*flex-direction: row;*/
  justify-content: space-between;
  align-items: center;
  color: #333333;
  padding: 15px 0px;
}

.ui-node-logout {
  width: 90px;
  text-align: right;
  font-size: 15px;
}

.ui-node-content {
  flex: 1;
  padding-left: 10px;
}

.ui-logo {
  /*margin: 25px 13px;*/
  /* border-radius: 5px;
    background: url("../images/bg.jpg") no-repeat center center;
    background-size: 100% 170px; */
  min-height: 180px;
}

.ui-node-icon {
  width: 40px;
}

.ui-node-oper {
  text-align: center;
}

.ui-node-oper p {
  font-size: 12px;
  margin-top: 9px;
}

.ui-node-font14 {
  font-size: 14px;
}

.ui-node-font15 {
  font-size: 15px;
}

.ui-node-font12 {
  font-size: 12px;
}

.ui-node-font18 {
  font-size: 18px;
}

.ui-node-oper img {
  width: 28px;
  height: 28px;
}

.ui-node-wraper {
  padding: 0px 15px;
}

.ui-news-flex {
  display: flex;
  padding: 14px 0px;
  border-bottom: 1px solid #eee;
}

.ui-news-time {
  font-size: 11px;
  color: #929292;
  margin-top: 13px;
}

.ui-news-img {
  width: 113px;
  padding-left: 14px;
}

.ui-news-imgs {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.ui-news-imgs-inner {
  flex: 1;
  padding: 8px 5px;
}

.ui-news-imgs-inner img {
  width: 100%;
  height: 100%;
}

.layout-flex {
  display: -webkit-box;
  /* OLD - iOS 6-, Safari 3.1-6 */
  display: -moz-box;
  /* OLD - Firefox 19- (buggy but mostly works) */
  display: -ms-flexbox;
  /* TWEENER - IE 10 */
  display: -webkit-flex;
  /* NEW - Chrome */
  display: flex;
  /* NEW, Spec - Opera 12.1, Firefox 20+ */
  /*justify-content: space-between;*/
  -webkit-flex-wrap: wrap;
  -webkit-box-lines: multiple;
  -moz-flex-wrap: wrap;
  flex-wrap: wrap;

  padding-bottom: 10px;
}

.flex-item {
  width: 25%;
  text-align: center;
  margin-bottom: 10px;
  margin-top: 20px;
}

.flex-item-icon {
  padding-left: 20px;
  padding-right: 20px;
}

.flex-item-icon>img {
  width: 33px;
  height: 33px;
}

.item-icon {
  width: 100%;
  /* height: 45px; */
}

.flex-item p {
  margin-top: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
}

.ui-node-ee {
  position: relative;
  margin-top: 10px;
  padding-left: 9px;
  /* border-left: 2px solid #ce2828; */
}

.ui-node-line {
  width: 3px;
  height: 18px;
  position: absolute;
  top: 2px;
  left: 0;
  background-color: #4da9ea;
}

.ui-node-wrapmarg {
  margin: 0 14px;
}

.ui-tab-nav li {
  font-size: 16px;
  margin: 0px 14px;
}

.ui-node-card {
  padding: 25px 13px;
  margin-bottom: 0px;
  padding-bottom: 10px;
}

.appList,
.todoCenter {
  width: 95%;
  margin: 0 auto;
  /* position: relative;
    top: -50px; */
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
}

.appList {
  margin-top: 10px;
}

.mao>.ui-tab-content>li {
  display: block !important;
}

.mao {
  overflow: scroll;
}

.ui-node-toFixed {
  position: fixed;
  top: 0;
  z-index: 99;
}

.main_collection {
  padding-top: 0px;
  padding-bottom: 0px;
  width: 100%;
}

.layout-flex {
  padding-top: 0px;
  padding-bottom: 0px;
}

/* .edit,.cancelEdit,.more {
    float: right;
} */
.main_collection .more {
  width: 20%;
}

/* 应用导航栏 */
#nav_app,
#todoTab {
  margin: 0 auto 10px;
  width: 90%;
  position: relative;
}

.clearfix:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
}

.moveContent .appList #nav_app .card_List,
#todoTab ul {
  height: 40px;
  display: flex;
  /* width: 96%; */
  width: 90%;
  justify-content: space-between;
}

.card_List li,
#todoTab li {
  color: #bebebe;
  line-height: 40px;
  width: auto;
  /* float: left; */
  /* margin-right: 8%; */
}

.card_List li.current:before {
  content: " ";
  width: 100%;
  height: 3px;
  background: #000000;
  position: absolute;
  top: 34px;
}

#todoTab li.current:before {
  content: " ";
  width: 10%;
  height: 3px;
  background: #000000;
  position: absolute;
  top: 34px;
}

.card_List li.current,
#todoTab li.current {
  color: #1e1e1e;
  font-weight: bold;
}

.card_List span {
  line-height: 40px;
  position: absolute;
  right: 0;
  color: gray;
  font-size: 14px;
}

#nav_app .appListMore,
#todoTab .todoCenterMore {
  width: 30px;
  position: absolute;
  top: 7px;
  right: -15px;
  text-align: center;
}

#nav_app .appListMore img,
#process .processListMore img {
  object-fit: cover;
}

.card_content>div {
  display: grid;
  /* grid-template-columns: 25% 25% 25% 25%; */
  grid-template-columns: 33.3% 33.3% 33.3%;
  /* grid-template-rows: repeat(2,1fr); */
  text-align: center;
  width: 100%;
  margin: 0 auto;
  grid-row-gap: 18px;
}

.card_content ul li {
  list-style: none;
}

.myApps img,
.popularApps img,
.historyApps img,
.recApps img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

/* .appInfo{
     
  } */
.appInfoLine {
  position: relative;
}

.appInfoLine:after {
  content: "";
  position: absolute;
  right: 0px;
  top: 20%;
  bottom: 20%;
  background-color: #f0f0f0;
  width: 1px;
}

.appInfoLine:nth-child(3n):after {
  width: 0px;
}

.appName {
  font-size: 12px;
  margin-top: 10px;
  width: 90%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 8px auto 0;
  font-weight: bold;
}

/* 轮播图展示 */
.ui-slide {
  width: 93% !important;
  height: 130px !important;
  max-width: 100% !important;
  margin: 26px auto 10px !important;
}

.ui-fullpage-dot .cur,
.ui-lattice-grid,
.ui-slide-dot .cur {
  background-color: #000 !important;
  border: 1px solid #000 !important;
}

.ui-slide .ui-slide-item {
  overflow: hidden;
}

.ui-slide .ui-slide-content,
.ui-slide .ui-slide-item,
.ui-slide .ui-slide-item img {
  height: 95% !important;
}

.ui-slide .ui-slide-dot {
  bottom: 0px !important;
  right: 50% !important;
  transform: translate(50%);
}

.ui-fullpage-dot span,
.ui-slide-dot span {
  width: 3px !important;
  height: 3px !important;
  background: #cacaca;
  border: 1px solid #cacaca !important;
}

.ui-slide .ui-slide-item img:last-child {
  left: 69% !important;
  margin-left: 5px;
}

.midImgSlide img {
  width: 69% !important;
  border-radius: 5px;
}

.midImg {
  width: 93%;
  display: flex;
  margin: -40px auto 10px;
  height: 80px;
}

.midImg img {
  flex: 1;
  overflow: hidden;
}

.midImg .dangzheng {
  margin-right: 5px;
}

/* 流程 */
.moveContent #process {
  width: 95%;
  background-color: #fff;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
  display: none;
}

.processList {
  margin: 0px auto 10px;
  width: 90%;
  line-height: 40px;
  position: relative;
}

.moveContent #process .processListMore {
  width: 5px;
  position: absolute;
  top: -2px;
  right: -11px;
}

.processName li.current:before {
  content: " ";
  width: 42%;
  height: 3px;
  background: #000000;
  position: absolute;
  top: 34px;
}

.processName {
  /* position: relative; */
  display: flex;
  justify-content: space-between;
  width: 88%;
}

.processName li {
  color: gray;
  line-height: 40px;
  width: auto;
  /* float: left; */
  /* margin-right: 10%; */
}

.processName li.current {
  color: #000;
  font-weight: bold;
}

/* .processName span {
    line-height: 40px;
    position: absolute;
    right: 0;
    color: gray;
    bottom: -2px;
    font-size: 14px;
  } */
.moveContent #process .processListInfo {
  width: 97%;
  background: #f7f8fa;
  margin: 0 auto;
  border-radius: 6px;
  padding: 10px 20px;
  box-sizing: border-box;
}

.moveContent #process .processListInfo .processContent .processDetailInfo {
  display: flex !important;
  margin: 10px auto;
}

.moveContent #process .processListInfo .processContent .processDetailInfo .processTime {
  width: 30%;
  font-size: 12px;
}

.moveContent #process .processListInfo .processContent .processDetailInfo .processTitle {
  width: 70%;
  font-size: 16px;
}

.moveContent #process .processListInfo .processContent .processDetailInfo .processTime .timeInfo {
  font-weight: bold;
}

.moveContent #process .processListInfo .processContent .processDetailInfo .processTime .dateInfo {
  color: #929395;
}

#process .processListInfo .processContent .processDetailInfo .processTitle .processTitleName {
  font-weight: bold;
  margin-top: -7px;
}

.moveContent #process .processListInfo .processContent .processDetailInfo .processTitle .processTitleTips {
  color: #6e6e70;
  font-size: 14px;
}

#oLi li {
  position: relative;
  /* flex: 1; */
}

.num {
  position: absolute;
  top: 0px;
  left: 100%;
  height: 16px;
  width: 16px;
  background: red;
  line-height: 16px;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  text-align: center;
}

.processName div {
  display: none;
}

/* 我的课表 */
#myTimetable {
  /* width: 95%; */
  background-color: #fff;
  margin: 10px auto;
  border-radius: 10px;
  overflow: hidden;
}
 #myTimetable.none{
      display: none;
}
.timetableName {
  width: 95%;
  margin: 20px auto 10px;
}

.timetableName .timeableMore {
  position: absolute;
  top: 0px;
  right: -15px;
  width: 30px;
  text-align: center;
}

.timetableName .timetableTitle {
  font-weight: bold;
  width: 90%;
  margin: 0 auto;
  position: relative;
}

.timetableName .more {
  font-size: 14px;
  color: gray;
}

#myTimetable.none {
  display: none;
}

#myTimetable .timeNav {
  margin-top: 15px;
  height: 90px;
  position: relative;
  background-color: #f7f8fa;
}

#myTimetable .timeNav .timeNavInfo {
  display: flex;
  width: 100%;
  background: #fff;
  justify-content: space-around;
  /* padding: 15px 0 15px 10px; */
  position: absolute;
  box-sizing: content-box;
  /* border-radius: 0 0 15px 0; */
  height: 70px;
  top: -1px;
  align-items: center;
}

#myTimetable .timeNav .timeNavInfo .timeInfo {
  padding: 10px 4px;
  border-radius: 20px;
  text-align: center;
  color: #bcbcbc;
}

#myTimetable .timeNav .timeNavInfo .timeInfo:hover {
  cursor: pointer;
}

#myTimetable .timeNav .timeNavInfo .timeInfo .timeName {
  margin-bottom: 10px;
}

#myTimetable .timeNav .timeNavInfo .current {
  background-color: #702c07;
  color: #fff;
}

#myTimetable .timeNav .timeNavRadius {
  width: calc(6% - 10px);
  height: 100%;
  background: #f7f8fa;
  position: absolute;
  right: 0;
  border-radius: 15px 0 0 0;
  top: -10px;
}

/* .timeableNav{
    width: 90%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 15% 85%;
    grid-column-gap: 25px;
  } */
/* .timeableNav div{
      display: inline-block;
  } */
/* .timeableNav .week{
      color: red;
      text-align: end;
      white-space: nowrap;
      font-weight: 600;
  } */
/* .timeableDay div{
    border: 1px solid red;
    padding: 0 5px;
    color: red;
  } */
/* .timeableDay .current{
    background: red;
    color: #fff;
  } */
/* .timeableDay .tomorrow{
      margin-left: -5px;
  } */
.timetableList {
  /* width: 95%; */
  margin: 0 auto;
  margin-bottom: 5px;
  background-color: #f7f8fa;
}

.timetableList .timetableListInfo {
  width: 95%;
  margin: 0px auto;
  padding: 0 0 10px;
}

.timetableList .timetableListInfo .timetableListInfoTitle {
  font-weight: bold;
  margin: 0px 0 15px 4%;
}

.timetableList .timetableListInfo .timetableContent {
  display: none;
}

.timetableList .timetableListInfo .timetableContent.active {
  display: block;
}

.timetableList .timetableListInfo .timetableListDetail {
  display: flex;
  /* margin-bottom: 30px; */
  margin: 10px 0px;
  padding: 10px;
}

.timetableList .timetableListInfo .currentClass {
  padding: 10px;
  border-radius: 15px;
  background: #fff;
}

.timetableList .timetableListInfo .tipsAlert .timetableNum {
  background-color: #fff;
}

.timetableList .timetableListInfo .tipsAlert .timetableDetailInfo {
  background-color: #fff;
  padding: 5px 8px 10px 15px;
  border-radius: 10px;
}

.timetableList .timetableListInfo .timetableListDetail .timetableNum {
  width: 5%;
  text-align: center;
  margin-right: 3%;
  border-radius: 10px;
  color: #9c9c9c;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo {
  width: 90%;
  display: flex;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .timetableMsg {
  width: 90%;
  font-size: 14px;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .timetableDetailTime {
  font-size: 14px;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .subjectName {
  /* font-weight: bold; */
  margin-bottom: 5px;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .subjectLocation {
  font-size: 12px;
  color: #9c9c9c;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .timetableMsg div:last-child {
  color: #9c9c9c;
}

/* .timetableList .timetableListInfo .timetableListDetail .timetableDetailTime{
    font-weight: bold;
  } */

.timeableInfo {
  display: grid;
  grid-template-columns: 15% 85%;
  grid-column-gap: 15px;
  grid-template-areas:
    "time subject"
    "time subject_";
  padding-top: 15px;
}

.timeableInfo .time {
  grid-area: time;
}

.timeableInfo .subject {
  margin-left: 15px;
}

/* 课程表休息日 */
#myTimetable .timetableList .timetable_rest {
  color: gray;
  display: flex;
  align-items: center;
  margin: 10px 0;
}

#myTimetable .timetableList .timetable_rest img {
  width: 30px;
  margin-right: 5px;
}

.subject .subjectTitle {
  grid-area: subject;
  font-size: 16px;
}

.subject .subjectInfo {
  grid-area: subject_;
  font-size: 14px;
  margin-top: 1px;
  color: gray;
}

.subject div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.timeableInfo .time {
  position: relative;
  text-align: end;
  font-size: 14px;
}

.timeableInfo .time::before {
  content: "";
  position: absolute;
  right: -14px;
  background-color: red;
  width: 1px;
}

.todayTimeable>div:nth-child(1) .time::before,
.tomorrowTimeable>div:nth-child(1) .time::before {
  bottom: -15px;
  height: calc(50% + 15px);
}

.todayTimeable>div:nth-child(2) .time::before,
.tomorrowTimeable>div:nth-child(2) .time::before {
  top: 0;
  height: calc(100% + 15px);
}

.todayTimeable>div:nth-child(3) .time::before,
.tomorrowTimeable>div:nth-child(3) .time::before {
  top: 0;
  height: 50%;
}

.timeableInfo .time::after {
  content: "";
  position: absolute;
  right: -18px;
  background-color: red;
  width: 9px;
  height: 9px;
  border-radius: 9px;
}

/* 资讯热点 */
#information {
  width: 95%;
  background-color: #fff;
  margin: 10px auto;
  border-radius: 10px;
  overflow: hidden;
}

.informationName {
  width: 90%;
  margin: 0px auto 20px;
  position: relative;
  /* display: flex;
    justify-content: space-between; */
}

.informationName .informationTitle {
  font-weight: bold;
}

.informationName .zxmore {
  /* font-size: 14px;
      color: gray; */
  width: 30px;
  position: absolute;
  right: -15px;
  top: 7px;
  text-align: center;
}

.informationTag {
  width: 98%;
  margin: 0 auto;
  white-space: nowrap;
  overflow-x: auto;
}

.informationTag::-webkit-scrollbar {
  display: none;
  width: 0 !important;
}

.informationTag {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}

.informationTag div {
  display: inline-block;
  padding: 6px 13px;
  margin: 0 3px;
  font-size: 14px;
  border-radius: 3px;
  background: #f3f1f2;
  color: #151a16;
  font-weight: bold;
}

.informationTag .current {
  background: #702c07;
  color: #fff;
}

.schoolInfo {
  width: 90%;
  margin: 12px auto;
  padding-bottom: 12px;
  /* border-bottom: 1px solid rgb(213, 213, 213); */
  position: relative;
}

/* .schoolInfo:nth-child(1),.schoolInfo:nth-child(2){
    border-bottom: 1px solid rgb(213,213,213);
  } */
.schoolInfo:before {
  content: " ";
  background: #000000;
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  left: -12px;
  top: 7px;
}

.schoolInfo .infoTitle {
  /* white-space: nowrap; */
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  font-size: 14px;
  font-weight: bold;
}

.schoolInfo .infoDate {
  font-size: 14px;
  margin-top: 5px;
  color: #9d9d9d;
}

.infoDate img {
  width: 14px;
  margin-top: -3px;
  margin-right: 5px;
}

/* 简报 */
#report {
  width: 95%;
  height: 300px;
  background-color: #fff;
  margin: 10px auto;
  border-radius: 10px;
  overflow: hidden;
}

.reportName {
  width: 90%;
  margin: 20px auto 10px;
  display: flex;
  justify-content: space-between;
}

.reportName .reportTitle {
  font-weight: bold;
}

.reportName .more {
  font-size: 14px;
  color: gray;
}

.reportNav,
.reportAddress {
  width: 90%;
  margin: 0 auto;
}

.reportNav div {
  display: inline-block;
  font-size: 14px;
}

.reportNav .period {
  margin-right: 20px;
  color: red;
}

.reportNav .reportTitle {
  background-color: red;
  color: #fff;
  padding: 1px 8px;
  font-size: 14px;
}

.reportAddress {
  margin-top: 5px;
  font-size: 14px;
  color: rgb(243, 217, 142);
}

.reportList {
  width: 90%;
  margin: 0 auto;
}

.reportInfo {
  display: grid;
  grid-template-columns: 15% 85%;
  grid-column-gap: 5px;
  grid-template-areas:
    "time subject"
    "time subject_";
  padding-top: 10px;
}

.reportInfo .time {
  grid-area: time;
}

.reportInfo .subject {
  margin-left: 15px;
}

.reportList .subject .subjectTitle {
  grid-area: subject;
  font-size: 18px;
}

.reportList .subject .subjectInfo {
  grid-area: subject_;
  font-size: 14px;
  margin-top: 1px;
  color: gray;
}

.reportList .subject div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.reportInfo .time {
  position: relative;
  text-align: end;
}

.reportInfo .time::before {
  content: "";
  position: absolute;
  right: -8px;
  background-color: red;
  width: 1px;
}

.reportList>div:nth-child(1) .time::before {
  bottom: -15px;
  height: calc(50% + 15px);
}

.reportList>div:nth-child(2) .time::before {
  top: 0;
  height: calc(100% + 15px);
}

.reportList>div:nth-child(3) .time::before {
  top: 0;
  height: 50%;
}

.reportInfo .time::after {
  content: "";
  position: absolute;
  right: -12px;
  background-color: red;
  width: 9px;
  height: 9px;
  border-radius: 9px;
}

/* 底部导航 */
#footer {
  width: 100%;
  background-color: #fff;
  margin: 10px auto;
  overflow: hidden;
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: sticky;
  bottom: 0;
  font-size: 14px;
}

.footerInfo img {
  width: 28px;
  filter: grayscale(100%);
}

#footer .current {
  color: red;
}

#footer .current img {
  filter: grayscale(0);
}

/*测试*/
.ui-tab-nav li.current {
  color: none;
  border-bottom: none !important;
}

.ui-tab-nav li {
  line-height: 45px;
  min-width: 70px;
  box-flex: 1;
  -webkit-box-flex: 1;
  text-align: center;
  color: #777;
  box-sizing: border-box;
  border-bottom: 2px solid transparent;
  width: 100%;
}

.ui-tab-nav,
.ui-tab-nav li {
  height: 45px;
  /* -webkit-box-sizing: border-box; */
}

li {
  list-style: none;
}

.bg {
  background: url("../images/bg.png") no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 160px;
}

.search {
  position: relative;
  display: none;
}

.search>input {
  width: calc(100% - 130px);
  border: none;
  border-radius: 15px;
  padding: 5px 15px;
  font-size: 16px;
  line-height: 23px;
  padding-left: 35px;
  padding-right: 40px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

.search>input::-webkit-input-placeholder {
  color: #fff;
}

.search>input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #fff;
}

.search>input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #fff;
}

.search>input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #fff;
}

.search_icon {
  /* float: right;  */
  width: 40px;
  /* height: 25px; */
  /* margin: 15px 0; */
  margin-left: -96px;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.3);
  /* background: #b1bee5; */
  position: absolute;
  right: 4px;
}

.search-icon {
  width: 20px;
  position: absolute;
  top: 6px;
  left: 9px;
}

.yuyin-icon {
  width: 13px;
  position: absolute;
  top: 6px;
  right: 70px;
}

.swiper-container {
  width: 100%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  /*height: 170px;*/
  padding-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.swiper-wrapper {
  -webkit-tap-highlight-color: #fff;
}

.swiper-container-horizontal>.swiper-pagination-bullets,
.swiper-pagination-custom,
.swiper-pagination-fraction {
  bottom: -6px;
  left: 0;
  width: 100%;
}

#myCollection .swiper-slide .layout-flex {
  width: 20%;
  float: left;
  position: relative;
}

.edit {
  margin-left: 30px;
}

.cancalEdit {
  margin-left: 30px;
}

.add_app {
  float: left;
  width: 20%;
}

.isp-service-item-del-fav {
  background-image: url(../images/s-ico-del.png);
  width: 20px;
  height: 20px;
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 99;
  cursor: pointer;
  display: none;
}

.main_collection .flex-item {
  width: 100%;
}

#myCollection .swiper-pagination-bullets {
  left: 0;
  width: 100%;
}

#myCollection .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 4px;
}

.ui-node-card {
	background: url(../images/bg2.jpg) rgba(0, 0, 0, 0) no-repeat;
    background-size: 100% 100%;
    height: 247px;
    /* border-radius: 0 0 15px 15px; */
    background-size: cover;
    background-blend-mode: multiply;
}

#nav {
  margin: 0px 14px;
}

#nav .ui-tab-nav li:first-child {
  margin-left: 0px;
}

#nav .ui-tab-nav li:last-child {
  margin-right: 30px;
}

.person_icon {
  width: 50px;
  height: 50px;
  float: left;
  margin-right: 10px;
  border: 1px solid #fff;
  border-radius: 50%;
}

.person_icon>img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.person_lists {
  float: left;
  width: calc(100% - 180px);
  color: #fff;
  font-family: "微软雅黑";
  white-space: nowrap;
  /* margin-top: 10px;*/
}

.person_list1 {
  font-size: 20px;
  font-weight: bolder;
}

.person_list2 {
  font-size: 12px;
  margin-top: 3px;
  color: #fff;
  /*color: #cebeb5;*/
}

.person_list2 img {
  width: 12px;
  position: relative;
  top: -1px;
}

.person_list2>span {
  margin-right: 5px;
}

.person_list3 {
  font-size: 12px;
  margin-top: 3px;
   color: #fff;
  /*color: #cebeb5;*/
}

.person_list3>span {
  margin-right: 5px;
}

#frontTab {
  margin-top: 15px;
  color: #fff;
}

.swiperContent {
  background: #fff;
  margin-top: -20px;
  border-radius: 15px 15px 0 0;
  height: auto;
  border: 1px solid #fff;
}

.main_collection .more {
  width: 20%;
  float: left;
}

.sortbar {
  /* background-color: rgba(0, 0, 0 , 74%); */
  background-color: #3d3d3d;
  color: white;
  filter: Alpha(opacity=50);
  line-height: 2;
  font-size: 14px;
  width: 74px;
  text-align: center;
  border-radius: 5px;
  position: absolute;
  zoom: 1;
  z-index: 9;
  display: none;
}

.down {
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  border-width: 6px;
  border-style: solid dashed dashed dashed;
  border-color: #3d3d3d transparent transparent transparent;
  position: absolute;
  left: 42%;
}

.sortbar .pp {
  padding: 1px;
}

#myCollection img {
  pointer-events: none;
  /* 禁止长按图片保存 */
}

.moveContent #nav {
  background-color: #e7e7e7;
  display: box;
  display: -webkit-box;
  font-size: 16px;
  box-sizing: border-box;
  border-radius: 5px;
}

.moveContent .ui-tab-nav {
  background-color: #e7e7e7;
  display: box;
  display: -webkit-box;
  font-size: 16px;
  box-sizing: border-box;
}

.moveContent .ui-tab-nav li.current {
  color: #325dbb;
  border-bottom: 2px #00a5e0 solid;
}

.jdj {
  background: #fff url(../images/bg2.jpg) no-repeat;
  background-size: 100% 100%;
  border-radius: 0 0 15px 15px;
}

/* 模块待开发样式 */
/* #process .processInfo .pendingCode{
    color: gray;
    display: flex;
    align-items: center;
    margin-left: 5%;
    margin-bottom: 10px;
}
#process .processInfo .pendingCode img{
    width: 30px;
    margin-right: 5px;
} */

.moveContent {
  min-width: 280px;
}

.appList .card_content {
  /* margin-bottom:5px; */
  margin: 15px auto;
}

/* 我的收藏应用弹框 */
.favoriteAppsModal {
  position: fixed;
  top: 30%;
  right: 0;
  left: 0;
  margin: auto;
  z-index: 2;
  width: 90%;
  background: #fff;
  padding: 15px 0px;
  border-radius: 10px;
  max-height: 260px;
  overflow-y: auto;
}

.favoriteAppsModalInfo {
  display: grid;
  grid-template-columns: 33.3% 33.3% 33.3%;
  text-align: center;
  width: 100%;
  margin: 0 auto;
  grid-row-gap: 18px;
}

#favoriteAppsModal img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.person_icon:hover,
.ui-slide .ui-slide-item img,
#oLi li,
.appInfo,
.timeableMore,
.timeInfo,
.informationName .more,
.columnName,
.schoolInfo {
  cursor: pointer;
}

#information .informationName .informationTitle,
#information .informationName .noticeAnnouncement {
  display: inline-block;
}

#information .informationTitle {
  position: relative;
  height: 40px;
  line-height: 40px;
}

#information .informationTitle:before {
  content: " ";
  width: 100%;
  height: 3px;
  background: #000000;
  position: absolute;
  top: 34px;
}

#information .informationName .noticeAnnouncement {
  margin-left: 20px;
  position: relative;
  width: auto;
  color: #bebebe;
}

#information .informationName .noticeAnnouncement img {
  width: 24px;
  object-fit: cover;
  margin-top: -3px;
}

/* 下拉刷新 */
.moveContent .reloadStatus {
  height: 50px;
  line-height: 50px;
}

.moveContent .reloadStatus .reloadTips {
  width: 50%;
  margin: 0 auto;
  text-align: center;
}

.moveContent .reloadStatus .reloadTips img {
  vertical-align: middle;
  height: 20px;
  object-fit: contain;
}

.moveContent .reloadStatus .reloadTips span {
  vertical-align: middle;
}

/* 个人画像权限弹框 */
.tipsAlert {
  position: absolute;
  top: 190px;
  right: 0;
  left: 0;
  margin: auto;
  font-size: 14px;
  padding: 6px;
  border-radius: 5px;
  background: #0e0e0e;
  z-index: 999;
  background-image: url(../images/card_bg.png);
  width: 221px;
  height: 130px;
  text-align: center;
  line-height: 130px;
  color: #fff;
  background-size: cover;
  display: none;
}

.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .subjectName,
.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .subjectLocation,
.timetableList .timetableListInfo .timetableListDetail .timetableDetailInfo .subjectTips {
  word-break: break-all;
}

#nav_app .appListMore img,
#todoTab .todoCenterMore img,
#process .processListMore img,
.timetableName .timeableMore img,
.informationName .more img,
.informationName .zxmore img {
  height: 16px;
  object-fit: contain;
}

/* 首页按功能分类应用 */
.appFunctional {
  width: 100%;
  background-color: #f6f6f6;
  padding: 10px 0;
}

.appFunctional .appFunctionalContent {
  width: 100%;
  padding: 0 2.5%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.appFunctional .appFunctionalContent .functionItem {
  position: relative;
}

.appFunctional .appFunctionalContent .functionItem:after {
  position: absolute;
  content: "";
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 1px;
  background: #d0d0d0;
  bottom: 0;
}

.appFunctional .appFunctionalContent .functionItem:last-child:after {
  width: 0;
  height: 0;
}

.appFunctional .appFunctionalContent .functionTitle {
  line-height: 45px;
  width: 100%;
  display: inline-block;
  margin-top: 10px;
  padding-left: 5%;
  box-sizing: border-box;
  color: #1e1e1e;
  font-weight: 600;
}

.appFunctional .appFunctionalContent .functionalInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  background: #fff;
  /* border-radius: 0px 0px 15px 15px; */
  overflow: hidden;
}

.appFunctional .appFunctionalContent .functionalInfo li {
  width: 20%;
  text-align: center;
  position: relative;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: inherit;
  padding: 10px 0;
  font-size: 14px;
}

.appFunctional .appFunctionalContent .functionalInfo div.hideMoreCateAppBtn,
.appFunctional .appFunctionalContent .functionalInfo div.moreCateAppBtn {
  align-items: inherit;
  width: 20%;
  text-align: center;
  position: relative;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  padding: 10px 0;
  font-size: 14px;
}

.appFunctional .appFunctionalContent .functionalInfo div.hideMoreCateApp,
.appFunctional .appFunctionalContent .functionalInfo li.hideMoreCateApp {
  display: none;
}

.appFunctional .appFunctionalContent .functionalInfo div.hideMoreCateAppBtn img,
.appFunctional .appFunctionalContent .functionalInfo div.moreCateAppBtn img {
  width: 25px;
  height: 25px;
  object-fit: cover;
  margin-top: 5px;
}

.appFunctional .appFunctionalContent .functionalInfo div.hideMoreCateAppBtn p,
.appFunctional .appFunctionalContent .functionalInfo div.moreCateAppBtn p {
  background: #fff;
  padding-top: 15px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  word-break: break-all;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 12px;
  color: #818181;
  /* font-weight: 600; */
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.appFunctional .appFunctionalContent .functionalInfo li div img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.appFunctional .appFunctionalContent .functionalInfo li div p {
  background: #fff;
  padding-top: 10px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  word-break: break-all;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 12px;
  color: #818181;
  /* font-weight: 600; */
  padding-left: 5px !important;
  padding-right: 5px !important;
}

/* 功能应用 弹窗 蒙版 */
.popWindow_app {
  width: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.3);
  background-size: 100%;
}

.portalAppsModal {
  position: fixed;
  top: 30%;
  right: 0;
  left: 0;
  margin: auto;
  z-index: 2;
  width: 90%;
  background: #fff;
  padding: 15px 0px;
  border-radius: 10px;
  max-height: 260px;
  overflow-y: auto;
}

.portalAppsModalInfo {
  display: grid;
  grid-template-columns: 33.3% 33.3% 33.3%;
  text-align: center;
  width: 100%;
  margin: 0 auto;
  grid-row-gap: 18px;
}

#portalAppsModal .appName {
  font-size: 12px;
  margin-top: 10px;
  width: 90%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 8px auto 0;
  font-weight: bold;
}

#portalAppsModal img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

@media screen and (max-width: 320px) {
  .card_List li {
    margin-right: 4%;
  }

  .processName li {
    margin-right: 6%;
  }
}

#todoCon {
  margin: 0 auto;
  width: 90%;
  margin-top: 20px;
}

#todoCon .item .top div,
#todoCon .item .bottom div {
  display: inline-block;
  vertical-align: middle;
}

#todoCon .item .time,
#todoCon .item .date {
  width: 40%;
}

#todoCon .item .top {
  font-size: 16px;
  font-weight: bold;
}

#todoCon .item .bottom {
  font-size: 14px;
  color: #999999;
  margin-top: 15px;
}

#todoCon .todo {
  background-color: #f7f8fa;
  border-radius: 12px;
  padding: 0px 20px;
  overflow: hidden;
}

#todoCon .item {
  margin: 35px 0px;
}

.ellipsis{
  width: 58%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pos-jiao {
    position: relative;
    text-align: center;
}
.business {
    width: 17px;
    height: 17px;
    border-radius: 15px;
    background-color: #702c07;
    color: #FFFFFF;
    font-size: 12px;
    position: absolute;
    bottom: 15px;
    right: -16px;
    line-height: 17px;
}
/**弹框**/
.layui-layer-btn {
    text-align: center !important;
}
.layui-layer-btn .layui-layer-btn0 {
    border-color: #ccc !important;
    background-color: #FFF !important;
    color: #818181 !important;
}
/**.layui-layer-title {
    padding: 0 20px 0 20px !important;
	text-align: center !important;
}**/
.popup_div{
	background: url(../images/beijing.png) no-repeat;
	background-size: cover;
	-webkit-background-size: cover;
	-o-background-size: cover;
	background-position: center 0;
}
.popup{
	font-family: "微软雅黑";
	font-size: 14px;
	text-indent: 2em ;
	padding: 15px 15px 20px 15px;
}
.popup_title{
	line-height:32px;
}