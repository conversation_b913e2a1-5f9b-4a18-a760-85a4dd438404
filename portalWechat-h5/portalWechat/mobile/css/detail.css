body{
    overflow: hidden;
}
.containBox{
    min-width: 280px;
    width: 100%;
    height: 100%;
    /* padding: 20px; */
    box-sizing: border-box;
    /* background: #F3F8FA; */
    overflow-y: auto;
}
.containBox .appTitle{
    font-size: 20px;
    padding: 10px 0;
    color: #43464d;
    word-break: break-all;
    font-weight: 700;
}
.containBox .platform,.containBox .platform .platformList{
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    color: #7A7A7A;
}
.containBox .platform .platformList{
    width: calc(100% - 60px);
    flex-wrap: wrap;
}
.containBox .platform>span{
    width: 60px;
}
.containBox .platform>span,
.containBox .platform .platformList span{
    margin-right: 8px;
}
.containBox .platform .platformList span:last-child{
    margin-right: 0;
}
.containBox .description{
    margin: 10px auto 15px;
    background-color: #fff;
    border-radius: 10px;
    overflow-x: auto;
    padding: 10px;
    box-sizing: border-box;
    width: 100%;
}
.containBox .description img{
    width: 100%;
    object-fit: cover;
}
.containBox .wybl{
    background: #2f65ae;
    padding: 10px 0;
    text-align: center;
    color: #fff;
    border-radius: 5px;
    box-sizing: border-box;
}
.containBox .detailInfo{
    margin-top: 15px;
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
}
.containBox .detailInfo .detailItem{
    padding: 8px 5px;
    line-height: 28px;
    box-sizing: border-box;
    border-bottom: 1px solid #f1f1f1;
}
.containBox .detailInfo .detailItem span:first-child{
    color: #9a9a9a;
}
.containBox .detailInfo .detailItem span:last-child{
    color: #4f4f4f;
    word-break: break-all;
}