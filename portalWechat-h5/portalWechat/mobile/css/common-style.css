@charset 'UTF-8';

* {
  margin: 0;
  padding: 0;
}

html {
  margin: 0;
  padding: 0;
  font-size: 62.5%;
}
body {
  font-size: 1.5rem;
  font-family: "Microsoft yahei", arial, sans-serif;
  color: #232323;
}
button,
optgroup,
option,
select,
textarea {
  font-size: 1.6rem;
}
h4 {
  font-size: 1.8rem;
}
p,
label {
  font-size: 1.5rem;
}
.ui-mylist-colorblack {
  color: #232323;
}

.ui-mylist-colorgray {
  color: #9a9a9a;
}

.ui-mylist-colorblue {
  color: #0992f2;
}
.ui-mylist-colorblackgray {
  color: #666666;
}
.ui-mylist-font20 {
  font-size: 2rem;
}
.ui-mylist-font18 {
  font-size: 1.8rem;
}
.ui-mylist-font16 {
  font-size: 1.6rem;
}
.ui-mylist-font15 {
  font-size: 15px;
}
.ui-mylist-font14 {
  font-size: 1.4rem;
}
.ui-mylist-flex {
  display: -webkit-box;
  width: 100%;
}
.ui-mylist-subflex {
  -webkit-box-flex: 1;
  padding: 5px;
  text-align: center;
}

.ui-mylist-flow {
  position: relative;
  height: auto;
  background-color: #fff;
  width: 100%;
}
.ui-mylist-inlineblock {
  display: inline-block;
}
.ui-mylist-star {
  height: 15px;
  width: 15px;
  display: inline-block;
  background: url("../img/star.png") no-repeat center center;
  background-size: 15px 15px;
}

.ui-mylist-star2 {
  height: 15px;
  width: 15px;
  display: inline-block;
  background: url("../img/star3.png") no-repeat center center;
  background-size: 15px 15px;
}

.ui-mylist-flow img {
  width: 100%;
  height: auto;
}

#line {
  position: absolute;
  top: 50%;
  left: 0.15%;
  height: 1px;
  width: calc(calc(25% - 5px) * 15%);
  background-color: #0079ff;
}
.ui-mylist-flow div {
  padding: 0;
  width: 14.2857%;
}
.ui-mylist-line-green {
  height: 1px;
  background-color: #46ab45;
}
.ui-mylist-line-gray {
  height: 1px;
  background-color: #bfbfbf;
}

.ui-tab-nav,
.ui-tab-nav li {
  height: 50px;
}

/* .ui-tab-nav li {
  line-height: 50px;
  color: #232323;
} */

.ui-mylist-btn {
  width: 100%;
  height: 55px;
  line-height: 55px;
  background-color: #2e8bea;
  color: #fff;
  font-size: 18px;
}
.ui-mylist-btn-radius {
  width: 100%;
  height: 55px;
  line-height: 55px;
  background-color: #2e8bea;
  color: #fff;
  border-radius: 5px;
  font-size: 18px;
}

.ui-mylist-ellipsis {
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.menu-barstyle {
  -webkit-box-flex: 1;
  padding: 5px;
  text-align: center;
  font-size: 1.4rem;
}

.menu-bottom {
  position: absolute;
  bottom: 0;
  display: -webkit-box;
  width: 100%;
  z-index: 99;
  background-color: #fff;
  padding-top: 10px;
  padding-bottom: 10px;
}

.lineB > div {
  display: inline-block;
}

.verticalM > div {
  vertical-align: middle;
}

.oneEllip {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: start;
}

.flex-between {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}