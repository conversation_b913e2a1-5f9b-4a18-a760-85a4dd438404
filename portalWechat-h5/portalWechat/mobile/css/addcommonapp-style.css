@charset 'UTF-8';

body,html {
    margin: 0;
    padding: 0;
}
/*body{
    font:14px/20px "Microsoft yahei",arial,sans-serif;
}*/
body {
    font-family: "Helvetica Neue",Helvetica,STHeiTi,sans-serif;
    /*line-height: 1.5;*/
    font-size: 12px !important;
    color: #000;
    /*-webkit-text-size-adjust: 100%;
    outline: 0;
    overflow: auto;*/
}
.ui-tab-content>li {
    display: block;
}
.ui-lattice-grid img {
    width: 60%;
    height: 60%;
}
.ui-tab-nav li.current {
    color: #262626;
    border-bottom: 2px #0992F2 solid;
}
.ui-mycollect-msgnum {
    height: 25px;line-height: 25px;font-size: 13px;display: inline-block;border-radius: 25px;background-color: #D23E3E;min-width: 6px;
    margin-left: 5px;color: #fff;padding: 0px 10px;
}
.ui-mycollect-addframe {
    margin-top: 10px;
}
.ui-list-img:after {
    content: "\e601";
    color: #18b4ed;
    right: -5px;
    top: -5px;
    z-index: 9;
    width: 26px;
    height: 26px;
    background: #fff;
    border-radius: 13px;
    line-height: 26px;
    text-indent: -3px;
}

.addApp img:after{
    content:"123"
}

.ui-mylist-block{
    text-align: center;
    margin: 10px 5px;
    width: calc(25% - 10px);
}

.ui-icon-p {
    /*display: inline-block;*/
    margin:0 auto;
    width: 55%;
    position: relative;
    /*background: url(../img/temp-1.png) center center;*/
    background-size: 100% auto;
}

.ui-icon-p img {
    width: 48px;
    height: 48px;
}
.ui-icon-p:after{
    content: "";
    background: url("") center 0;
}
.ui-last-p{
    color: #000 !important;
    /* font-size: 12px !important; */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 72px;
    margin: 0 auto;
    padding-top: 10px;
    word-break: break-all;
}
p.change:after {
    content: "";
    width: 25px;
    height: 25px;
    position: absolute;
    top: -10px;
    right: -10px;
    color: #4DDAD5;
    font-size: 30px;;
    background: url(../images/xz.png) center 0;
    background-size: 25px 25px;
}
.ui-mylist-btn{
    width: 100%;background-color: #2E8BEA;height: 50px;color: #fff;font-size: 16px;
}

@media (min-width: 320px) and (max-width: 370px) {
    .ui-last-p {
        width: 48px !important;
  }
}