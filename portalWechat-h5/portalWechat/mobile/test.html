<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备检测中...</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#0FC6C2',
                        accent: '#FF7D00',
                        neutral: '#F5F7FA',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .animate-pulse-slow {
                animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
        }
    </style>
</head>
<body class="font-inter bg-gradient-to-br from-neutral to-white min-h-screen flex flex-col items-center justify-center p-4">

    <script>
 // 基于设备类型和客户端的智能跳转脚本
    (function() {
        
        // 获取用户代理字符串
        const userAgent = navigator.userAgent.toLowerCase();
        
        // 判断是否在钉钉客户端中
        const isDingTalk = userAgent.includes('dingtalk');
        
        // 判断设备类型
        const isMobile = /mobile|android|iphone|ipad|ipod/.test(userAgent);
        const isTablet = /tablet|ipad/.test(userAgent) && !/mobile/.test(userAgent);
        const isIpad = userAgent.includes('ipad') || (userAgent.includes('macintosh') && navigator.maxTouchPoints > 1);
        
        // 判断操作系统
        const isHarmonyOS = userAgent.includes('harmonyos') || userAgent.includes('huawei');
        
        // 构建基础URL（不包含查询参数和哈希）
        const baseUrl = window.location.origin + window.location.pathname.split('/').slice(0, -1).join('/') + '/';
        
        // 根据不同情况进行跳转
        let targetUrl = '';
        
        
        let mobileUrl = "https://my.mycc.edu.cn/sopplus/_web/customized/DTalkPortal/ding/mobile/index.jsp?_p=YXM9MiZ0PTEmcD0xJm09TiY_";
        
        //钉钉客户端，需要走认证跳转；
        if (isDingTalk) {
            if (isIpad) {
                targetUrl = "https://sso.mycc.edu.cn/mcauth/openoauth/dd/authRedirect?service=https%3A%2F%2Fmy.mycc.edu.cn";
            } else if (isMobile) {
                targetUrl = "https://sso.mycc.edu.cn/mcauth/openoauth/dd/authRedirect?service=https%3A%2F%2Fmy.mycc.edu.cn%2Fsopplus%2F_web%2Fcustomized%2FDTalkPortal%2Fding%2Fmobile%2FindexNew.jsp%3F_p%3DYXM9MiZ0PTEmcD0xJm09TiY_";
            } else {
            	targetUrl = "https://sso.mycc.edu.cn/mcauth/openoauth/dd/authRedirect?service=https%3A%2F%2Fmy.mycc.edu.cn";
            }
        } else {
            if (isIpad || isTablet) {
                targetUrl = 'https://my.mycc.edu.cn';
            } else if (isMobile || isHarmonyOS) {
                targetUrl = "https://my.mycc.edu.cn/sopplus/_web/customized/DTalkPortal/ding/mobile/index.jsp?_p=YXM9MiZ0PTEmcD0xJm09TiY_";
            } else {
                targetUrl = "https://my.mycc.edu.cn";
            }
        }
               
        // 执行跳转
        console.log('正在跳转到:', targetUrl);
        window.location.href = targetUrl;
    })();    
    </script>
</body>
</html>    