<%@ page contentType="text/html;charset=UTF-8" language="java"%>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta name="format-detection" content="telephone=no" />
    <title>绵阳城市学院</title>
    <link rel="stylesheet" href="./css/common.css" />
    <link rel="stylesheet" href="css/swiper-bundle.min.css" />
    <link rel="stylesheet" href="css/index_swiper.css" />
    <link rel="stylesheet" href="./css/index.css?v=1.0.11" />
    
    <!-- 日程相关 -->
    <link rel="stylesheet" href="../../_libs/calendar/css/arm.css" />
    <link rel="stylesheet" href="../../_libs/calendar/css/arm.animation.css" />
    <link rel="stylesheet" href="../../_libs/calendar/css/arm.calendar.css" />
    <link rel="stylesheet" href="../../_libs/calendar/css/demo.css" />
    <link rel="stylesheet" href="css/mytimeTable.css" />
<!-- <script src="js/vconsole.min.js"></script> -->
	<style>
        /* body, html {
			position: relative;
		} */
    body, html{
      height: auto;
      overflow: auto;
    }
		.ui-page.calDetail .ui-modal-cnt{
		  border-radius: 20px;
		}
		.ui-modal.show{
		  backdrop-filter: blur(2px) !important
		}
		.draggable-button {
			width: 56px;
			height: 56px;
			position: fixed;
			bottom: 50px;
			right: 50px;
			z-index: 10;
			cursor: pointer;
			touch-action: none; /* Prevent default touch behaviors */
		}
		.iframe-schedule .ui-modal-cnt .ui-modal-bd iframe{
		  min-height: 240px;
		  height: 100%;
		}
		.calendar-text{
			display: block;
			width: 70px;
			text-align: right;
		 }
	   .calendar-cons p {
			display: flex;
			align-items: flex-start;
		}
		.calendar-val{
			flex: 1;
		}
	</style>
  </head>
  <!-- <style>
    #__vconsole .vc-switch{z-index: 999999999;}
    #__vconsole .vc-panel {z-index: 9999999999;}
    </style> -->
  <body>
    <div class="mainContent">
      <!-- 头部内容 -->
      <div class="topContent">
        <div class="topBox">
          <div class="motto">
            <img src="./images/logo.png" alt="">
            <!-- <span>博学</span>
            <span>笃行</span>
            <span>严谨</span>
            <span>创新</span> -->
          </div>
          <div class="searchBox">
            <input type="text">
            <img src="./images/searchIcon.png" alt="">
          </div>
        </div>
        <div class="appNav">
          <!-- <div class="appItem" data-type="task">
            <div class="imgBox">
              <img src="./images/taskIcon.png" alt="">
              <span class="count taskNum"></span>
            </div>
            <div class="title">我的待办</div>
          </div>
          <div class="appItem" data-type="mail">
            <div class="imgBox">
              <img src="./images/mailIcon.png" alt="">
              <span class="count mailNum"></span>
            </div>
            <div class="title">我的邮箱</div>
          </div>
          <div class="appItem" data-type="qrCode">
            <img src="./images/qrCodeIcon.png" alt="">
            <div class="title">入校码</div>
          </div>
          <div class="appItem" data-type="library">
            <img src="./images/libraryIcon.png" alt="">
            <div class="title">数字阅览室</div>
          </div> -->
        </div>
        <img class="topBg" src="" alt="">
        <!-- 背景图底部效果 -->
        <div class="blurBox">

        </div>
      </div>
      <div class="middleContent">
          <!-- 轮播图部分 -->
          <div class="swiperContent">
            <div class="swiper mySwiper">
                <div class="swiper-wrapper">
                    <!-- <div class="swiper-slide mySwiperSlider"><img data-articleid="" data-url="" src="./images/swiper1.png"></div> -->
                </div>
                <div class="swiper-pagination mySwiperPagination"></div>
              </div>
        </div>
        <!-- 一周会议-我的日程 -->
        <div class="scheduleModule">
          <img src="./images/cartonIcon.png" alt="" class="cartonIcon">
          <div class="moreBtn scheduleMore" data-type="schedule">更多</div>
          <div class="oneWeek">
            <div class="tabBox">
              <div class="tab-list">
                <!-- <div class="tab-item timetableTab not-selected hideTab" data-type="meeting">
                  <div>我的课表</div>
                </div>
                <div class="tab-item myScheduleTab tab-selected " data-type="mySchedule">
                  <div>
                    <span>我的日程</span>
                    <span class="scheduleNum">0</span>
                  </div>
                </div> -->
              </div>
            </div>
            <!-- <div class="shadowLine"></div> -->
            <div class="weekInfo">
              <div class="mySchedule-container">
                <div class="mySchedule-content">
                    <!-- 日程日期部分 -->
                    <div id="scheduleDate"></div>
                    <!-- 日程内容区域 -->
                    <div class="scheduleInfoContent">
                      <!-- 我的课表 -->
                      <div class="contentBox meeting ">
                        <div class="content">
                          <div class="dayInfo Sun"></div>
                          <div class="dayInfo Mon"></div>
                          <div class="dayInfo Tue"></div>
                          <div class="dayInfo Wed"></div>
                          <div class="dayInfo Thu"></div>
                          <div class="dayInfo Fri"></div>
                          <div class="dayInfo Sat"></div>
                      </div>
                      </div>
                      <!-- 我的日程 -->
                      <div class="contentBox mySchedule">
                        <div class="content">
                          <div class="myScheduleTab">
                            <div class="myScheduleTabItem active" data-type="personal">个人日历</div>
                            <div class="myScheduleTabItem" data-type="conference">我的会议</div>
                            <div class="myScheduleTabItem" data-type="ddSchedule">钉钉日程</div>
                          </div>
                          <div class="myScheduleBox">
                              <!-- 我的日程 -->
                              <div class="scheduleItem personal active">
                                <div class="scheduleList">

                                </div>
                                  <div class="calButton">
                                    <div class="addCal">添加</div>
                                    <div class="delCal">删除</div>
                                    <div class="cancelDel">取消删除</div>
                                </div>
                              </div>
                            <!-- 我的会议 -->
                            <div class="scheduleItem conference">

                            </div>
                            <!-- 钉钉日程 -->
                            <div class="scheduleItem ddSchedule">
                              
                            </div>
                          </div>
                        </div>
                    </div>
                    <!-- 背景图 -->
                    <img src="./images/serviceCenterBg.png" alt="" class="scheduleBg">
                    </div>
                </div>
            </div>
  
            </div>
          </div>
        </div>

        <!-- 服务大厅、最近使用 -->
        <div class="appModule">
          <div class="moduleTitleBox">
            <div class="titleTab">
              <div class="tabItem tabItem_0 active" data-type="fwdt">
                <span>服务大厅</span>
                <img src="./images/titleIcon.png" alt="">
              </div>
              <div class="tabItem tabItem_0" data-type="recentlyUsedApps">
                <span>最近使用</span>
                <img src="./images/titleIcon.png" alt="">
              </div>
            </div>
            <div class="moreBtn moreBtn1" data-type="app">更多</div>

          </div>
          <div class="appList">
            <div class="appListInfo fwdt current">
			
            </div>
            <div class="appListInfo recentlyUsedApps">
			
            </div>
            <img src="./images/pandaIcon.png" alt="" class="appContentBg">
          </div>
        </div>
		
		  <!-- 钉钉应用、我的收藏 -->
        <div class="appModule">
          <div class="moduleTitleBox">
            <div class="titleTab">
              <div class="tabItem tabItem_1 active" data-type="dingtalkApps">
                <span>钉钉应用</span>
                <img src="./images/titleIcon.png" alt="">
              </div>
              <div class="tabItem tabItem_1" data-type="myFavourite">
                <span>我的收藏</span>
                <img src="./images/titleIcon.png" alt="">
              </div>
            </div>
            <div class="moreBtn moreBtn2" data-type="ddApps">更多</div>

          </div>
          <div class="appList">
            <div class="appListInfo dingtalkApps current">
			
            </div>
            <div class="appListInfo myFavourite">
			
            </div>
            <img src="./images/pandaIcon.png" alt="" class="appContentBg">
          </div>
        </div>

        <!-- 特色主题 -->
        <div class="specialTopic">
          <div class="moduleTitleBox">
            <div class="titleTab">
              <div class="tabItem active">
                <span>特色主题</span>
                <img src="./images/titleIcon.png" alt="">
              </div>
            </div>
            <div class="moreBtn" data-type="topic">更多</div>

          </div>
          <div class="topicContent">
            <!-- <div class="topicInfo">
              <div class="topicItem topItem">
                <div class="itemInfo">
                  <div class="title">通知公告</div>
                  <div class="detailBox">
                   <span>查看内容</span>
                   <img src="./images/detailIcon.png" alt="">
                  </div>
                </div>
               <img class="itemBg" src="./images/topItem.png" alt="">
              </div>
              <div class="topicItem bottomItem">
                <div class="itemInfo">
                  <div class="title">官网资讯</div>
                  <div class="detailBox">
                   <span>查看内容</span>
                   <img src="./images/detailIcon.png" alt="">
                  </div>
                </div>
               <img class="itemBg" src="./images/bottomItem.png" alt="">
              </div>
              <div class="topicItem rightItem">
                <div class="itemInfo">
                  <div class="title">学术讲座</div>
                  <div class="detailBox">
                   <span>查看内容</span>
                   <img src="./images/detailIcon.png" alt="">
                  </div>
                </div>
               <img class="itemBg" src="./images/rightItem.png" alt="">
              </div>
            </div> -->
          </div>
        
        </div>
        
      </div>
   
    </div>
   
      <!-- <section id="dialog">
        <div class="demo-item">
          <div class="demo-block">
            <div class="ui-modal">
                <div class="ui-modal-cnt">
                    <div class="ui-modal-bd" id="demotext">
                        <div class="imgBox">
                          <img src="./images/swiper1.png" alt="">
                        </div>
                    </div>
                </div>        
            </div>	
          </div>
         
        
        </div>
      </section> -->
 
   <div id="draggable-button" class="draggable-button"><img src="./images/map.svg" alt=""></div>
  </body>
</html>

<!-- <script src="./js/jquery.min.js"></script> -->
<!-- 日程相关 所需js-->
<script src="../../_libs/calendar/lib/zepto.min.js"></script>
<script src="../../_libs/calendar/js/arm.js"></script>
<script src="./js/swiper-bundle.min.js"></script>
<!-- <script src="./js/dingtalk.open-3.0.42.js"></script> -->
<!-- <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.0.72/dingtalk.open.js"></script> -->
<script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.22/dingtalk.open.js"></script>
<script src="./js/utiles.js"></script>
<script src="./js/index_url.js"></script>
<!-- <script src="./js/index.js?v=1.0.25"></script> -->


<script>
  // 初始化日程日期
  A.use(
      [
        "arm.calendar",
        "arm.modal",
        "arm.touch",
        "arm.tpl",
        "arm.mask",
        "arm.paging"
      ],
      function () {
        var calendar = new $.calendar("#scheduleDate");
        //可设置日历开始时间
        // calendar.setCurday(curDate);
      
      }
    );
</script>
<script>
  //点击托拽按钮
  draggable('draggable-button')

  function draggable(el){
      document.addEventListener('DOMContentLoaded', (event) => {
          const draggableButton = document.getElementById(el);
          let isDragging = false;
          let startX, startY, initialX, initialY;

          draggableButton.addEventListener('touchstart', (e) => {
              const touch = e.touches[0];
              startX = touch.clientX;
              startY = touch.clientY;

              // 记录按钮的初始位置
              const rect = draggableButton.getBoundingClientRect();
              initialX = rect.left;
              initialY = rect.top;

              isDragging = true;
              draggableButton.style.cursor = 'grabbing';
          });

          document.addEventListener('touchmove', (e) => {
              if (!isDragging) return;

              const touch = e.touches[0];
              let changeX = touch.clientX - startX;
              let changeY = touch.clientY - startY;

              // 计算新的位置
              let newLeft = initialX + changeX;
              let newTop = initialY + changeY;

              // 获取视口的宽度和高度
              const viewportWidth = window.innerWidth;
              const viewportHeight = window.innerHeight;

              // 获取按钮的宽度和高度
              const buttonWidth = draggableButton.offsetWidth;
              const buttonHeight = draggableButton.offsetHeight;

              // 进行边界检查
              if (newLeft < 0) {
                  newLeft = 0;
              } else if (newLeft + buttonWidth > viewportWidth) {
                  newLeft = viewportWidth - buttonWidth;
              }

              if (newTop < 0) {
                  newTop = 0;
              } else if (newTop + buttonHeight > viewportHeight) {
                  newTop = viewportHeight - buttonHeight;
              }

              // 更新按钮的位置
              draggableButton.style.left = newLeft+'px';
              draggableButton.style.top = newTop+'px';
          });

          document.addEventListener('touchend', () => {
              isDragging = false;
              draggableButton.style.cursor = 'grab';
          });

          // 可选：处理触摸被取消的情况
          document.addEventListener('touchcancel', () => {
              isDragging = false;
              draggableButton.style.cursor = 'grab';
          });
      });
  }
</script>
<script>
  //钉钉环境下拉刷新
// dd.ready(function(){
  
//   alert(1111)
// })
// dd.enablePullDownRefresh({
//     success: () => {
//       alert("reload")
//       window.location.reload()
//     },
//     fail: () => {alert('fail')},
//     complete: () => {alert('complete')},
//   });
  dd.ui.pullToRefresh.enable({
    onSuccess: function() {
        // 成功处理逻辑
        alert("reload")
    },
    onFail: function() {
        // 失败处理逻辑
        alert("onFail")
    }
});

</script>
