<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta name="format-detection" content="telephone=no" />
    <title>日程中心</title>
    <link rel="stylesheet" href="../_libs/calendar/css/arm.css" />
    <link rel="stylesheet" href="../_libs/calendar/css/arm.animation.css" />
    <link rel="stylesheet" href="../_libs/calendar/css/arm.calendar.css" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/scheduleCenter.css" />
    <style>
      .ui-page.calDetail .ui-modal-cnt {
        border-radius: 10px;
      }
      .ui-modal.show {
        backdrop-filter: blur(2px) !important;
      }
      .iframe-schedule .ui-modal-cnt .ui-modal-bd iframe {
        min-height: 400px;
        height: 100%;
      }
      .calendar-text {
        display: block;
        width: 70px;
        text-align: right;
      }

      .calendar-cons p {
        display: flex;
        align-items: flex-start;
      }

      .calendar-val {
        flex: 1;
      }
    </style>
  </head>
  <body class="bg-body">
    <section class="ui-section">
      <div class="listContent">
        <div class="topContent">
          <div class="topBox">
            <div class="motto">
              <img src="images/logo.png" alt="" />
            </div>
          </div>
          <img class="topBg" src="images/topbg.png" alt="" />
          <div class="blurBox"></div>
        </div>
        <div class="scheduleContent">
          <div class="scheduleContentInfo">
            <div class="scheduleBgBox">
              <div id="scheduleDate"></div>
              <img class="scheduleBg" src="images/serviceCenterBg.png" alt="" />
            </div>

            <div class="scheduleInfo">
              <div class="scheduleAllList">
                <div class="scheduleList meeting active">
                  <!-- <div class="scheduleItem">
                            <div class="basicInfo">
                                <div class="time">13:00-15:00</div>
                            </div>
                            <div class="location">
                              <div class="address">行政楼606会议室</div>
                          </div>
                            <div class="title">画法几何与阴影透视</div>
                        </div> -->
                  <!-- <div class="noData">当前日期没有相关事件</div> -->
                </div>
                <div class="scheduleList mySchedule">
                  <div class="myScheduleTab">
                    <!-- <div class="myScheduleTabItem active" data-type="all">全部</div>
                    <div class="myScheduleTabItem" data-type="personal">个人日历</div>
                    <div class="myScheduleTabItem" data-type="conference">我的会议</div>
                    <div class="myScheduleTabItem" data-type="ddSchedule">钉钉日程</div> -->
                  </div>
                  <div class="myScheduleBox">
                    <!-- 全部 -->
                    <div class="scheduleTabItem all active">
                      <div class="scheduleAllList"></div>
                      <!-- 操作区域 -->
                      <div class="actionBox">
                        <div class="addBtn">添加</div>
                        <div class="delBtnItem delCalBtn">删除个人日程</div>
                        <div class="delBtnItem cancelBtn">取消删除</div>
                      </div>
                    </div>
                    <!-- 我的日程 -->
                    <div class="scheduleTabItem personal">
                      <div class="scheduleInfoList"></div>
                      <!-- 操作区域 -->
                      <div class="actionBox">
                        <div class="addBtn">添加</div>
                        <div class="delBtnItem delCalBtn">删除个人日程</div>
                        <div class="delBtnItem cancelBtn">取消删除</div>
                      </div>
                    </div>
                    <!-- 我的会议 -->
                    <div class="scheduleTabItem conference"></div>
                    <!-- 钉钉日程 -->
                    <div class="scheduleTabItem ddSchedule"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <footer class="ui-footer ui-footer-btn">
      <ul class="ui-tiled">
        <li data-href="index.html">
          <div class="ysf-box">
            <div class="ub-img wx01"></div>
            <p class="ui-txt-tips">首页</p>
          </div>
        </li>
        <li data-href="articleCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx02"></div>
            <p class="ui-txt-tips">资讯</p>
          </div>
        </li>
        <li data-href="search.html" class="tiled-middle">
          <div class="ysf-box">
            <div class="ui-tiled-middle">
              <div class="ub-img wx03"></div>
            </div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="serviceCenter.html">
          <div class="ysf-box">
            <div class="ub-img wx04"></div>
            <p class="ui-txt-tips">应用</p>
          </div>
        </li>
        <li data-href="my.html">
          <div class="ysf-box">
            <div class="ub-img wx05"></div>
            <p class="ui-txt-tips">我的</p>
          </div>
        </li>
      </ul>
    </footer>
  </body>
  <!-- 日程相关js -->
  <script src="../_libs/calendar/lib/zepto.min.js"></script>
  <script src="../_libs/calendar/js/arm.js"></script>
  <script src="js/utiles.js"></script>
  <script src="js/index_url.js"></script>
  <script src="js/scheduleCenter.js"></script>

  <script>
    // 初始化日程日期
    A.use(['arm.calendarMore', 'arm.modal', 'arm.touch', 'arm.tpl', 'arm.mask', 'arm.paging'], function () {
      var calendar = new $.calendar('#scheduleDate')
      $('.ui-footer>ul>li').on('click', function () {
        var url = $(this).attr('data-href')
        window.location.replace(url)
      })
    })
  </script>
</html>
