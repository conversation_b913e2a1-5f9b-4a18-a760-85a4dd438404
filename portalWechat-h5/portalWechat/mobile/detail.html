<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta name="format-detection" content="telephone=no"/>
    <title>详情</title>
    <script src="../_libs/arm/js/arm.js"></script>
    <link rel="stylesheet" href="../_libs/arm/css/arm.css">
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/detail.css">
    <script type="text/javascript">
        app.config({
            iPortal: false,
            debug:false
        })
    </script>
<body>

      <div class="listContent my">
<div class="containBox">
    <div class="appTitle"></div>
    <!-- 暂不需要 -->
    <!-- <div class="platform">
        <span>支持平台</span>
        <div class="platformList">
            <span>微信</span>
            <span>pc</span>
            <span>钉钉</span>
        </div>
    </div> -->
    <div class="description"></div>
    <div class="wybl">我要办理</div>
    <div class="detailInfo">
        <div class="detailItem">
            <span>申请人员范围：</span>
            <span class="applyUserScope"></span>
        </div>
        <div class="detailItem">
            <span>负责单位：</span>
            <span class="responsibleDept"></span>
        </div>
        <div class="detailItem">
            <span>联系人：</span>
            <span class="contactUser"></span>
        </div>
        <div class="detailItem">
            <span>联系电话：</span>
            <span class="contactPhone"></span>
        </div>
        <div class="detailItem">
            <span>办理时间：</span>
            <span class="handleTime"></span>
        </div>
        <div class="detailItem">
            <span>办理地点：</span>
            <span class="handleAddress"></span>
        </div>
    </div>
</div>
</div>
<script src="js/utiles.js"></script>
<script src="js/index_url.js"></script>
<script src="js/detail.js"></script>
<script>
$('.ui-footer>ul>li').on('click', function () {
    var url = $(this).attr('data-href')
    window.location.replace(url)
})
</script>
</body>
</html>
