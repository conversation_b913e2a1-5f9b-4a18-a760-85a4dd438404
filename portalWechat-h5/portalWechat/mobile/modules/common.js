/**
 * Created by Administrator on 2017/11/16.
 */

function getData(url,type,d,cb){
    $.ajax({
        url:url,
        type:type,
        data:d,
        dataType:'jsonp',
        timeout:10000,
        success:function(d){
            cb(d);
        },
        error:function(err){
            $.poptips("接口暂时无法调用，请稍候访问！")
        }
    })
}
var baseurl = '';
var U = {
    checkidentity: '/mobile/wechatAuth?ret=',
    //事务待换接口
    targeturl_dbsx: baseurl +'/_web/_apps/taskcenter/query/api/taskcenter/todotask.rst?_p=YXM9MSZwPTEmbT1OJg__', //待办事务
    targeturl_lcgz: baseurl +'/_web/_apps/taskcenter/query/api/taskcenter/processTrack.rst?_p=YXM9MSZwPTEmbT1OJg__',//流程跟踪
    targeturl_ybsx: baseurl +'/_web/_apps/taskcenter/query/api/taskcenter/donetask.rst?_p=YXM9MSZwPTEmbT1OJg__', //已办事务
    targeturl_bjlc: baseurl +'/_web/_apps/taskcenter/query/api/taskcenter/processDone.rst?_p=YXM9MSZwPTEmbT1OJg__' //办结流程
}