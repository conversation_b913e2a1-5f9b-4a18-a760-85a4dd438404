<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <title>绑定邮箱</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="../_libs/calendar/css/arm.css" />
    <link rel="stylesheet" href="css/addSchedule/select2.min.css" />
    <link rel="stylesheet" href="css/createMail.css" />
  </head>
  <body>
    <div class="things-cons">
      <div class="things-info">
        <p class="things-info-title"><span class="red">* </span>邮箱：</p>
        <div class="things-info-cons things-rili-title">
          <input
            id="createMailName"
            class="content"
            type="text"
            placeholder="请输入邮箱地址"
          />
          <input
            disabled
            id="createMailDomain"
            class="content"
            type="text"
          />
        </div>
      </div>
      <div class="things-info">
        <p class="things-info-title"><span class="red">* </span>验证码：</p>
        <div class="things-info-cons">
            <input id="contentInput" class="content" type="text"  />
            <span class="createMailDialog-sender">点击发送验证码</span>
        </div>
      </div>
      <div class="confirmBox">
        <div class="yesBtn">确认</div>
        <div class="cancelBtn">取消</div>
      </div>
    </div>
    <script src="js/jquery.min.js"></script>
    <script src="../_libs/calendar/lib/zepto.min.js"></script>
    <script src="../_libs/calendar/js/arm.js"></script>
  
    <script src="js/utiles.js"></script>
    <script src="js/index_url.js"></script>
    <script src="js/createMail.js"></script>
    <script>
    A.use(['arm.modal', 'arm.touch', 'arm.tpl','../../js/select2'], function() {
      // $('#createMailDomain').select2({
      //     minimumResultsForSearch: -1
      // })
    })

    //添加确认按钮
    $(document).on('click', '.things-cons .confirmBox .yesBtn', function (event) {
      save()
    })
    //添加取消按钮
    $(document).on('click', '.things-cons .confirmBox .cancelBtn', function (event) {
      // window.parent.dia.iframe[0].close();
      // 用postMessage
      window.parent.postMessage('closeSchedIframe', '*')
    })
</script>
  </body>
</html>

