<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="UTF-8">
    <title>添加修改日历——事件</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="../_libs/css/arm.css">
    <link rel="stylesheet" href="css/addSchedule/bootstrap.min.css">
    <link rel="stylesheet" href="css/addSchedule/flat/flat.css">
    <link rel="stylesheet" href="css/addSchedule/select2.min.css">
    <link rel="stylesheet" href="css/addSchedule/daterangepicker.css">
    <link rel="stylesheet" href="css/addSchedule/skin/layer.css">
    <link rel="stylesheet" href="css/addSchedule/calendar.css">
    <style>
       .things-cons   .things-info:nth-child(5)   .things-info-cons  .name-duration{margin:0 13px 0 5px}
	      .bootstrap-select .dropdown-menu {
            width: 100%;
        }

        .bootstrap-select .dropdown-menu li a span {
            white-space: normal;
        }
        .rolldate-container .rolldate-mask{
            background: #fff!important;
        }
        /* #__vconsole .vc-switch{z-index: 999999999;}
    #__vconsole .vc-panel {z-index: 9999999999;} */
    </style>
</head>

<body>
    <div class="things-cons">
        <div class="things-info">
            <p class="things-info-title"><span class="red">* </span>日历：</p>
            <div class="things-info-cons things-rili-title"><select class="title" id="title"></select></div>
        </div>
        <div class="things-info">
            <p class="things-info-title"><span class="red">* </span>事件内容：</p>
            <div class="things-info-cons"><input type="text" id = "contentInput" maxlength="50" class="content"></div>
        </div>
        <div class="things-info">
            <p class="things-info-title"><span class="red">* </span>开始时间：</p>
            <div class="things-info-cons">
                <div class="input-group date form_time" id="beginDatePa" data-date=""
                    data-date-format="yyyy-mm-dd HH:mm" data-link-field="dtp_input4" data-link-format="hh:ii" data-type="start">
                    <!-- <input class="form-control" id="beginDate" size="10" type="text" value="" readonly> -->
                    <input readonly class="form-control" type="text" id="beginDate">
                    <span class="input-group-addon"><!--span class="glyphicon glyphicon-calendar"--><img src="images/rili.png" style="width: 22px;height: 22px;"></span></span>
                </div>
            </div>
        </div>
        <div class="things-info">
            <p class="things-info-title"><span class="red">*</span>结束时间：</p>
         
            <div class="things-info-cons">
                <div class="input-group date form_time" id="endDatePa" data-date="" data-date-format="yyyy-mm-dd HH:mm"
                    data-link-field="dtp_input4" data-link-format="hh:ii" data-type="end">
                    <!-- <input class="form-control" id="endDate" size="10" type="text" value="" readonly> -->
                    <input readonly class="form-control" type="text" id="endDate">
                   
                    <span class="input-group-addon"> <!--span class="glyphicon glyphicon-calendar"--><img src="images/rili.png" style="width: 22px;height: 22px;"></span></span>
                </div>
            </div>
        </div>
        <div class="things-info">
            <p class="things-info-title"><span class="red">*</span>时间段：</p>
            <div class="things-info-cons" style=" display: flex;flex-wrap: wrap;">
		
				<div>	
                <input type="radio" name="duration" value="1" checked class="time-duration"><span
                    class="name-duration">全天</span>
				</div>
				<div>		
                <input type="radio" name="duration" value="2" class="time-duration"><span
                    class="name-duration">上午</span>
				</div>
				<div>		
                <input type="radio" name="duration" value="3" class="time-duration"><span
                    class="name-duration">下午</span>
				</div>
				<div>		
                <input type="radio" name="duration" value="4" class="time-duration"><span
                    class="name-duration">晚上</span>
				</div>
				<div>
                <input type="radio" name="duration" value="-1"  class="time-duration"><span
                    class="name-duration">自定义&nbsp&nbsp&nbsp</span>
				</div>
            </div>
        </div>
        <div class="things-info">
            <p class="things-info-title">备注：</p>
            <div class="things-info-cons">
                <textarea name="content" class="remark" id="content" cols="30" rows="4" maxlength="100"
                    placeholder="100字以内"></textarea>
            </div>
        </div>
        <div class="confirmBox">
            <div class="yesBtn">确认</div>
            <div class="cancelBtn">取消</div>
        </div>
    </div>
    <script src="js/jquery.min.js"></script>
    <script src="../_libs/calendar/lib/zepto.min.js"></script>
    <script src="../_libs/calendar/js/arm.js"></script>
    <script src="css/addSchedule/bootstrap.min.js"></script>
    <!-- date-range-picker -->
    <script src="css/addSchedule/moment-with-locales.js"></script>
    <!-- rolldate -->
    <script src="css/addSchedule/rolldate.js"></script>
    <script src="css/addSchedule/icheck.min.js"></script>
    <script src="css/addSchedule/layer.js"></script>
    <script src="css/addSchedule/urls.js"></script>
    <script src="css/addSchedule/utiles.js"></script>
    <script src="css/addSchedule/thingsNew.js"></script>
</body>


</html>
<script>
A.use(['arm.modal', 'arm.touch', 'arm.tpl','../../js/select2'], function() {
    $('#title').select2({
        minimumResultsForSearch: -1
    })
})
//添加确认按钮
$(document).on("click", ".things-cons .confirmBox .yesBtn", function (event) {
    save();
});
//添加取消按钮
$(document).on("click", ".things-cons .confirmBox .cancelBtn", function (event) {
    // window.parent.dia.iframe[0].close();
    // 用postMessage  
    window.parent.postMessage('closeSchedIframe', '*');
});
</script>