(function(root,factory){"object"==typeof exports&&"object"==typeof module?module.exports=factory():"function"==typeof define&&define.amd?define([],factory):"object"==typeof exports?exports.dd=factory():root.dd=factory()})(this,function(){return function(modules){function __webpack_require__(moduleId){if(installedModules[moduleId])return installedModules[moduleId].exports;var module=installedModules[moduleId]={i:moduleId,l:!1,exports:{}};return modules[moduleId].call(module.exports,module,module.exports,__webpack_require__),module.l=!0,module.exports}var installedModules={};return __webpack_require__.m=modules,__webpack_require__.c=installedModules,__webpack_require__.i=function(value){return value},__webpack_require__.d=function(exports,name,getter){__webpack_require__.o(exports,name)||Object.defineProperty(exports,name,{configurable:!1,enumerable:!0,get:getter})},__webpack_require__.n=function(module){var getter=module&&module.__esModule?function(){return module.default}:function(){return module};return __webpack_require__.d(getter,"a",getter),getter},__webpack_require__.o=function(object,property){return Object.prototype.hasOwnProperty.call(object,property)},__webpack_require__.p="",__webpack_require__(__webpack_require__.s=1154)}([function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.ddSdk=void 0;var env_1=__webpack_require__(4),env_2=__webpack_require__(4);Object.defineProperty(exports,"ENV_ENUM",{enumerable:!0,get:function(){return env_2.ENV_ENUM}}),Object.defineProperty(exports,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return env_2.ENV_ENUM_SUB}});var sdk_1=__webpack_require__(3);__webpack_require__(467),exports.ddSdk=new sdk_1.Sdk(env_1.getENV())},function(module,exports,__webpack_require__){"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getNetWorkTypeResultDeal=exports.scanParamsDeal=exports.removeStorageParamsDeal=exports.getStorageParamsDeal=exports.setStorageParamsDeal=exports.genBizStoreParamsDealFn=exports.genBoolResultDealFn=exports.forceChangeParamsDealFn=exports.genDefaultParamsDealFn=exports.addDefaultCorpIdParamsDeal=exports.addWatchParamsDeal=void 0,exports.addWatchParamsDeal=function(params){var finalParams=Object.assign({},params);return finalParams.watch=!0,finalParams},exports.addDefaultCorpIdParamsDeal=function(params){var finalParams=Object.assign({},params);return finalParams.corpId="corpId",finalParams},exports.genDefaultParamsDealFn=function(defaultParams){var defaultParamsObj=Object.assign({},defaultParams);return function(params){return Object.assign({},defaultParamsObj,params)}},exports.forceChangeParamsDealFn=function(forceParams){var forceParamsObj=Object.assign({},forceParams);return function(params){return Object.assign(params,forceParamsObj)}},exports.genBoolResultDealFn=function(boolKeyList){return function(params){var finalParams=Object.assign({},params);return boolKeyList.forEach(function(key){void 0!==finalParams[key]&&(finalParams[key]=!!finalParams[key])}),finalParams}},exports.genBizStoreParamsDealFn=function(params){var finalParams=Object.assign({},params);return"string"!=typeof finalParams.params?(finalParams.params=JSON.stringify(finalParams),finalParams):finalParams},exports.setStorageParamsDeal=function(params){return{name:params.key,value:params.data}},exports.getStorageParamsDeal=function(params){return{name:params.key}},exports.removeStorageParamsDeal=function(params){return{name:params.key}},exports.scanParamsDeal=function(params){return"qr"===params.type?__assign(__assign({},params),{type:"qrCode"}):"bar"===params.type?__assign(__assign({},params),{type:"barCode"}):__assign(__assign({},params),{type:"all"})},exports.getNetWorkTypeResultDeal=function(result){return"none"!==result.result&&"unknown"!==result.result?{netWorkAvailable:!0,netWorkType:result.result}:{newWorkAvailable:!1}}},function(module,exports,__webpack_require__){"use strict";var dd=__webpack_require__(205);__webpack_require__(478),module.exports=dd},function(module,exports,__webpack_require__){"use strict";function getTargetApiConfigVS(apiConfig,env){var targetVersion=apiConfig&&apiConfig.vs;return"object"==typeof targetVersion&&env.platformSub?targetVersion[env.platformSub]:"string"==typeof targetVersion?targetVersion:void 0}var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Sdk=exports.getTargetApiConfigVS=exports.LogLevel=exports.APP_TYPE=exports.isFunction=exports.compareVersion=exports.ENV_ENUM_SUB=exports.ENV_ENUM=void 0;var sdkLib_1=__webpack_require__(14);Object.defineProperty(exports,"APP_TYPE",{enumerable:!0,get:function(){return sdkLib_1.APP_TYPE}}),Object.defineProperty(exports,"LogLevel",{enumerable:!0,get:function(){return sdkLib_1.LogLevel}}),Object.defineProperty(exports,"isFunction",{enumerable:!0,get:function(){return sdkLib_1.isFunction}}),Object.defineProperty(exports,"compareVersion",{enumerable:!0,get:function(){return sdkLib_1.compareVersion}}),Object.defineProperty(exports,"ENV_ENUM",{enumerable:!0,get:function(){return sdkLib_1.ENV_ENUM}}),Object.defineProperty(exports,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return sdkLib_1.ENV_ENUM_SUB}});var middlewares_1=__webpack_require__(474),log_1=__webpack_require__(13),ApiMapping=__webpack_require__(5);exports.getTargetApiConfigVS=getTargetApiConfigVS;var Sdk=function(){function Sdk(env){var _this=this;this.configJsApiList=[],this.hadConfig=!1,this.devConfig={debug:!1},this.invokeAPIConfigMapByMethod={},this.p={},this.config$=new Promise(function(resolve,reject){_this.p.reject=reject,_this.p.resolve=resolve}),this.apiHandler=new middlewares_1.ApiHandler,this.platformConfigMap={},this.isBridgeDrity=!0,this.getExportSdk=function(){return _this.exportSdk},this.setAPI=function(method,config){_this.invokeAPIConfigMapByMethod[method]=Object.assign(_this.invokeAPIConfigMapByMethod[method]||{},config)},this.setPlatform=function(core){_this.isBridgeDrity=!0,_this.platformConfigMap[core.platform]=_this.withDefaultEvent(core),core.platform===_this.env.platform&&core.bridgeInit().catch(function(err){log_1.formatLog(log_1.diagnosticMessageMap.auto_bridge_init_error,null===err||void 0===err?void 0:err.toString())})},this.getPlatformConfigMap=function(){return _this.platformConfigMap},this.deleteApiConfig=function(method,platform){var invokeAPIConfig=_this.invokeAPIConfigMapByMethod[method];invokeAPIConfig&&delete invokeAPIConfig[platform]},this.invokeAPI=function(method,params,isAuthApi){return void 0===params&&(params={}),void 0===isAuthApi&&(isAuthApi=!0),_this.apiHandler.start({method:method,params:params,isAuthApi:isAuthApi})},this.withDefaultEvent=function(core){var event=Object.assign({on:function(){return log_1.formatLog(log_1.diagnosticMessageMap.not_support_event_on)},off:function(){return log_1.formatLog(log_1.diagnosticMessageMap.not_support_event_off)}},core.event);return __assign(__assign({},core),{event:event})},this.env=env,this.bridgeInitFn=function(){if(_this.bridgeInitFnPromise&&!_this.isBridgeDrity)return _this.bridgeInitFnPromise;_this.isBridgeDrity=!1;var platformCore=_this.platformConfigMap[env.platform];if(platformCore)_this.bridgeInitFnPromise=platformCore.bridgeInit().catch(function(err){return log_1.formatLog(log_1.diagnosticMessageMap.JsBridge_init_fail),Promise.reject(err)});else{var errMsg=log_1.formatLog(log_1.diagnosticMessageMap.not_support_env,env.platform);_this.bridgeInitFnPromise=Promise.reject(new Error(errMsg))}return _this.bridgeInitFnPromise};var devConfig=function(devConfigParams){void 0===devConfigParams&&(devConfigParams={}),_this.devConfig=Object.assign(_this.devConfig,devConfigParams),devConfigParams.extraPlatform&&_this.setPlatform(devConfigParams.extraPlatform)};this.exportSdk={config:function(configParams){void 0===configParams&&(configParams={});var isOnlyDevConfig=!0;Object.keys(configParams).forEach(function(key){-1===["debug","usePromise"].indexOf(key)&&(isOnlyDevConfig=!1)}),isOnlyDevConfig?(log_1.formatLog(log_1.diagnosticMessageMap.config_debug_deprecated),devConfig(configParams)):_this.hadConfig?log_1.formatLog(log_1.diagnosticMessageMap.repeat_config):(configParams.jsApiList&&(_this.configJsApiList=configParams.jsApiList.map(function(apiName){return ApiMapping[apiName]?ApiMapping[apiName]:apiName})),_this.hadConfig=!0,_this.bridgeInitFn().then(function(JSBridge){var platformCore=_this.platformConfigMap[env.platform],inConfigParams=configParams;platformCore.authParamsDeal&&(inConfigParams=platformCore.authParamsDeal(inConfigParams)),JSBridge(platformCore.authMethod,inConfigParams).then(function(res){_this.isReady=!0,_this.p.resolve(res)}).catch(function(err){_this.isReady=!1,_this.p.reject(err)})},function(err){log_1.formatLog(log_1.diagnosticMessageMap.JsBridge_init_fail_dd_config),_this.p.reject(err)}))},devConfig:devConfig,ready:function(callback){!1===_this.hadConfig?(log_1.formatLog(log_1.diagnosticMessageMap.dd_config_wrap_deprecated),_this.bridgeInitFn().then(function(){callback()})):_this.config$.then(function(res){callback()})},error:function(callback){_this.config$.catch(function(res){callback(res)})},on:function(type,handler){_this.bridgeInitFn().then(function(){var _a;null===(_a=_this.platformConfigMap[env.platform].event)||void 0===_a||_a.on(type,handler)})},off:function(type,handler){_this.bridgeInitFn().then(function(){var _a;null===(_a=_this.platformConfigMap[env.platform].event)||void 0===_a||_a.off(type,handler)})},env:env,checkJsApi:function(params){void 0===params&&(params={});var res={};return params.jsApiList&&params.jsApiList.forEach(function(method){var trueMethod=ApiMapping[method]||method,invokeAPIConfig=_this.invokeAPIConfigMapByMethod[trueMethod];if(invokeAPIConfig){var apiConfig=invokeAPIConfig[env.platform],targetVersion=getTargetApiConfigVS(apiConfig,env);targetVersion&&env.version&&sdkLib_1.compareVersion(env.version,targetVersion)&&(res[method]=!0)}res[method]||(res[method]=!1)}),Promise.resolve(res)},_invoke:function(method,params){return void 0===params&&(params={}),_this.invokeAPI(method,params,!1)}},this.initApiMiddleware()}return Sdk.prototype.useApiMiddleware=function(fn){if(!sdkLib_1.isFunction(fn))throw TypeError("middleware must be a function");this.apiHandler.use(fn)},Sdk.prototype.initApiMiddleware=function(){this.apiHandler.use(middlewares_1.bridge.bind(this)),this.apiHandler.use(middlewares_1.retry.bind(this)),this.apiHandler.use(middlewares_1.dealParamsAndResult.bind(this)),this.apiHandler.use(middlewares_1.checkConfig.bind(this)),this.apiHandler.use(middlewares_1.initBridge.bind(this)),this.apiHandler.use(middlewares_1.hookBeforeAndAfter.bind(this))},Sdk}();exports.Sdk=Sdk},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getENV=exports.getUA=void 0;var sdk_1=__webpack_require__(3),sdk_2=__webpack_require__(3);Object.defineProperty(exports,"ENV_ENUM",{enumerable:!0,get:function(){return sdk_2.ENV_ENUM}}),Object.defineProperty(exports,"APP_TYPE",{enumerable:!0,get:function(){return sdk_2.APP_TYPE}}),Object.defineProperty(exports,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return sdk_2.ENV_ENUM_SUB}});var EDdWeexEnv,dingtalk_javascript_env_1=__webpack_require__(464),getTopBridge=function(){try{if("undefined"!=typeof window&&void 0!==window.top){return window.top.__dingtalk_jsapi_top_platfrom_config__}}catch(_err){return}};(function(EDdWeexEnv){EDdWeexEnv.singlePage="singlePage",EDdWeexEnv.miniApp="miniApp",EDdWeexEnv.miniWidget="miniWidget"})(EDdWeexEnv||(EDdWeexEnv={})),exports.getUA=function(){var ua="";try{"undefined"!=typeof navigator&&(ua=navigator&&(navigator.userAgent||navigator.swuserAgent)||"")}catch(e){ua=""}return ua},exports.getENV=function(){var _a,_b,ua=exports.getUA(),isInIOSEquipment=/iPhone|iPad|iPod|iOS/i.test(ua),isInAndroidEquipment=/Android/i.test(ua),isInHarmonyEquipment=/OpenHarmony/i.test(ua)&&/ArkWeb/i.test(ua),isDingTalkRuntime=/DingTalk/i.test(ua),isWebviewInMiniAppRuntime=/dd-web/i.test(ua),isInNuva="object"==typeof nuva,isHadMiniAppBridge="object"==typeof dd&&"function"==typeof dd.dtBridge,isGdtEnvironment=/TaurusApp/.test(ua),isMpaasRuntime=isGdtEnvironment&&!isDingTalkRuntime,isGdtStandardRuntime=isGdtEnvironment&&isDingTalkRuntime,isGdtMiniApp=isMpaasRuntime&&"undefined"!=typeof my&&null!==my&&void 0!==my.alert,isGdtPcWin=isGdtEnvironment&&/dingtalk-win/.test(ua),isGdtIos=!isGdtPcWin&&isMpaasRuntime&&isInIOSEquipment,isGdtAndroid=!isGdtPcWin&&isMpaasRuntime&&isInAndroidEquipment,isGdtStandardIos=!isGdtPcWin&&isGdtStandardRuntime&&isInIOSEquipment,isGdtStandardAndroid=!isGdtPcWin&&isGdtStandardRuntime&&isInAndroidEquipment,isLooseIOS=isHadMiniAppBridge&&isInIOSEquipment||isInNuva&&isInIOSEquipment,inMobileDingtalk=isDingTalkRuntime||dingtalk_javascript_env_1.default.isDingTalk,isIOS=isInIOSEquipment&&inMobileDingtalk||dingtalk_javascript_env_1.default.isWeexiOS||isLooseIOS,isAndroid=isInAndroidEquipment&&inMobileDingtalk||dingtalk_javascript_env_1.default.isWeexAndroid,isMiniApp=isHadMiniAppBridge,isWebviewInMiniApp=isWebviewInMiniAppRuntime,isHarmony=isInHarmonyEquipment&&inMobileDingtalk,appType=sdk_1.APP_TYPE.WEB;if(isGdtMiniApp)appType=sdk_1.APP_TYPE.MINI_APP;else if(isWebviewInMiniApp)appType=sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP;else if(isMiniApp)appType=sdk_1.APP_TYPE.MINI_APP;else if(dingtalk_javascript_env_1.default.isWeexiOS||dingtalk_javascript_env_1.default.isWeexAndroid)try{var ddWeexEnv=weex.config.ddWeexEnv;appType=ddWeexEnv===EDdWeexEnv.miniWidget?sdk_1.APP_TYPE.WEEX_WIDGET:sdk_1.APP_TYPE.WEEX}catch(error){appType=sdk_1.APP_TYPE.WEEX}var containerId,language="*",matches=ua.match(/AliApp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===matches&&(matches=ua.match(/DingTalk\/([a-zA-Z0-9.-]+)/));var version;matches&&matches[1]&&(version=matches[1]);var frameName="";"undefined"!=typeof name&&(frameName=name);var topBridge=getTopBridge();try{topBridge&&"undefined"!=typeof window&&void 0!==window.top&&window.top!==window&&(frameName=top.name)}catch(e){}if(frameName)try{var frameConf=JSON.parse(frameName);frameConf.hostVersion&&(version=frameConf.hostVersion),language=frameConf.language||navigator.language||"*",containerId=frameConf.containerId}catch(e){}var isPC=!!containerId||"undefined"!=typeof window&&(null===(_b=null===(_a=null===window||void 0===window?void 0:window.dingtalk)||void 0===_a?void 0:_a.platform)||void 0===_b?void 0:_b.invokeAPI);isPC&&!version&&(matches=ua.match(/DingTalk\(([a-zA-Z0-9\.-]+)\)/))&&matches[1]&&(version=matches[1]);var platform,platformSub=sdk_1.ENV_ENUM_SUB.noSub;if(isGdtPcWin?(platform=sdk_1.ENV_ENUM.gdtPc,platformSub=sdk_1.ENV_ENUM_SUB.win):platform=isGdtIos?sdk_1.ENV_ENUM.gdtIos:isGdtAndroid?sdk_1.ENV_ENUM.gdtAndroid:isGdtStandardIos?sdk_1.ENV_ENUM.gdtStandardIos:isGdtStandardAndroid?sdk_1.ENV_ENUM.gdtStandardAndroid:isIOS?sdk_1.ENV_ENUM.ios:isAndroid&&!isHarmony?sdk_1.ENV_ENUM.android:isHarmony?sdk_1.ENV_ENUM.harmony:isPC?sdk_1.ENV_ENUM.pc:topBridge&&topBridge.platform?topBridge.platform:sdk_1.ENV_ENUM.notInDingTalk,platform===sdk_1.ENV_ENUM.pc){platformSub=ua.indexOf("Macintosh; Intel Mac OS")>-1?sdk_1.ENV_ENUM_SUB.mac:sdk_1.ENV_ENUM_SUB.win}return{platform:platform,platformSub:platformSub,version:version,appType:appType,language:language}}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default={datePicker:"biz.util.datetimepicker",chooseOneDayInCalendar:"biz.calendar.chooseOneDay",disableWebViewBounce:"ui.webViewBounce.disable",openPageInSlidePanelForPC:"biz.util.openSlidePanel",disablePullDownRefresh:"ui.pullToRefresh.disable",compressImage:"biz.util.compressImage",openPageInWorkBenchForPC:"biz.util.invokeWorkbench",chooseHalfDayInCalendar:"biz.calendar.chooseHalfDay",dateRangePicker:"biz.calendar.chooseInterval",stopPullDownRefresh:"ui.pullToRefresh.stop",chooseDateRangeInCalendar:"biz.calendar.chooseInterval",stopRecord:"device.audio.stopRecord",navigateToPage:"biz.navigation.navigateToPage",chooseDateTime:"biz.calendar.chooseDateTime",enablePullDownRefresh:"ui.pullToRefresh.enable",downloadAudio:"device.audio.download",openLink:"biz.util.openLink",openMicroApp:"biz.microApp.openApp",timePicker:"biz.util.timepicker",openPageInMicroApp:"biz.util.open",previewImage:"biz.util.previewImage",openPageInModalForPC:"biz.util.openModal",enableWebViewBounce:"ui.webViewBounce.enable",generateImageFromCode:"biz.util.generateImageFromCode",navigateBackPage:"biz.navigation.navigateBackPage",openLocation:"biz.map.view",playAduio:"device.audio.play",chooseImage:"biz.util.chooseImage",onRecordEnd:"device.audio.onRecordEnd",stopAudio:"device.audio.stop",vibrate:"device.notification.vibrate",onPlayAudioEnd:"device.audio.onPlayEnd",getStorage:"util.domainStorage.getItem",searchMap:"biz.map.search",stopLocating:"device.geolocation.stop",resumeAudio:"device.audio.resume",getLocatingStatus:"device.geolocation.status",startLocating:"device.geolocation.start",startRecord:"device.audio.startRecord",setStorage:"util.domainStorage.setItem",openLocalFile:"biz.util.openLocalFile",share:"biz.util.share",writeNFC:"device.nfc.nfcWrite",choosePhonebook:"biz.contact.chooseMobileContacts",pauseAduio:"device.audio.pause",getDeviceUUID:"device.base.getUUID",getSystemSettings:"device.base.openSystemSetting",customChooseUsers:"biz.customContact.multipleChoose",removeStorage:"util.domainStorage.removeItem",setClipboard:"biz.clipboardData.setData",scan:"biz.util.scan",decrypt:"biz.util.decrypt",getWifiHotspotStatus:"device.base.getInterface",readNFC:"device.nfc.nfcRead",showCallMenu:"biz.telephone.showCallMenu",getLocation:"device.geolocation.get",locateInMap:"biz.map.locate",clearShake:"device.accelerometer.clearShake",chooseStaffForPC:"biz.contact.choose",uploadAttachmentToDingTalk:"biz.util.uploadAttachment",rotateScreenView:"device.screen.rotateView",isLocalFileExist:"biz.util.isLocalFileExist",openChatByChatId:"biz.chat.toConversation",createGroupChat:"biz.contact.createGroup",watchShake:"device.accelerometer.watchShake",scanCard:"biz.util.scanCard",createDing:"biz.ding.create",openChatByUserId:"biz.chat.openSingleChat",chooseUserFromList:"biz.customContact.choose",exclusiveLiveCheck:"biz.ATMBle.exclusiveLiveCheck",chooseExternalUsers:"biz.contact.externalComplexPicker",saveFileToDingTalk:"biz.cspace.saveFile",resetScreenView:"device.screen.resetView",getCloudCallList:"biz.conference.getCloudCallList",translateVoice:"device.audio.translateVoice",complexChoose:"biz.contact.complexPicker",editExternalUser:"biz.contact.externalEditForm",checkBizCall:"biz.telephone.checkBizCall",getWifiStatus:"device.base.getWifiStatus",chooseDepartments:"biz.contact.departmentsPicker",getSystemInfo:"device.base.getPhoneInfo",getNetworkType:"device.connection.getNetworkType",makeVideoConfCall:"biz.conference.videoConfCall",createDingForPC:"biz.ding.post",encrypt:"biz.util.encrypt",quickCallList:"biz.telephone.quickCallList",getUserExclusiveInfo:"biz.realm.getUserExclusiveInfo",getAuthCode:"runtime.permission.requestAuthCode",previewImagesInDingTalkBatch:"biz.cspace.previewDentryImages",chooseChat:"biz.chat.chooseConversationByCorpId",getCloudCallInfo:"biz.conference.getCloudCallInfo",previewFileInDingTalk:"biz.cspace.preview",openChatByConversationId:"biz.chat.toConversationByOpenConversationId",chooseDingTalkDir:"biz.cspace.chooseSpaceDir",getOperateAuthCode:"runtime.permission.requestOperateAuthCode",isInTabWindow:"biz.tabwindow.isTab",createLiveClassRoom:"biz.live.startClassRoom",callUsers:"biz.telephone.call",makeCloudCall:"biz.conference.createCloudCall",ExternalChannelPublish:"biz.channel.externalChannelPublish",nfcReadCardNumber:"device.nfc.nfcReadCardNumber",liveChooseConversationAndUser:"biz.live.chooseConversationAndUser",getAuthCodeV2:"runtime.permission.requestAuthCodeV2",queryUserProfile:"biz.conference.queryUserProfile",requestMoneySubmmitOrder:"biz.requestMoney.startSubmittingOrder",requestAuthCode:"runtime.permission.requestAuthCodeV2",liveShare:"biz.live.share",chooseFile:"biz.file.chooseFile",setColorScheme:"internal.theme.setColorScheme",chooseConversation:"biz.chat.chooseConversation",saveVideoToPhotosAlbum:"biz.util.saveVideoToPhotosAlbum",setLanguage:"internal.setting.setLanguage",changeAppIcon:"internal.setting.changeAppIcon",editPicture:"biz.util.editPicture","biz.resource.getInfo":"biz.resource.getInfo","biz.resource.reportPerf":"biz.resource.reportPerf",openDocument:"biz.util.openDocument",getImageInfo:"biz.util.getImageInfo",previewMedia:"biz.util.previewMedia",popGesture:"biz.navigation.popGesture",startAdvertising:"biz.realm.startAdvertising",stopAdvertising:"biz.realm.stopAdvertising",getAdvertisingStatus:"biz.realm.getAdvertisingStatus",chooseMedia:"biz.util.chooseMedia",cropImage:"biz.util.cropImage",saveImageToPhotosAlbum:"biz.util.saveImageToPhotosAlbum",setGestures:"biz.navigation.gestures",getAuthInfo:"runtime.permission.getAuthInfo",getBackgroundFetchData:"biz.resource.getBackgroundFetchData",getBackgroundFetchDataWithID:"biz.resource.getBackgroundFetchDataWithID",getThirdAppConfCustomData:"biz.conference.getThirdAppConfCustomData",getThirdAppUserCustomData:"biz.conference.getThirdAppUserCustomData",getDeviceId:"device.base.getDeviceId",onBLEPeripheralCharacteristicReadRequest:"biz.realm.onBLEPeripheralCharacteristicReadRequest",onBLEPeripheralCharacteristicWriteRequest:"biz.realm.onBLEPeripheralCharacteristicWriteRequest",createBLEPeripheralServer:"biz.realm.createBLEPeripheralServer",writeBLEPeripheralCharacteristicValue:"biz.realm.writeBLEPeripheralCharacteristicValue",onBLEPeripheralConnectionStateChanged:"biz.realm.onBLEPeripheralConnectionStateChanged",translate:"biz.i18n.translate",subscribe:"biz.notify.subscribe",getTranslateStatus:"biz.i18n.getTranslateStatus",notifyTranslateEvent:"biz.i18n.notifyTranslateEvent"}},function(module,exports,__webpack_require__){"use strict";var dd=__webpack_require__(205);__webpack_require__(207),__webpack_require__(209),__webpack_require__(208),module.exports=dd},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var eappBridge=function(method,params){return new Promise(function(resolve,reject){dd.dtBridge({m:method,args:params,onSuccess:function(result){"function"==typeof params.success?params.success(result):"function"==typeof params.onSuccess&&params.onSuccess(result),resolve(result)},onFail:function(err){"function"==typeof params.fail?params.fail(err):"function"==typeof params.onFail&&params.onFail(err),reject(err)}})})};exports.default=eappBridge},function(module,exports,__webpack_require__){(function(t,e){module.exports=e()})(0,function(){return function(t){function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,e),o.l=!0,o.exports}var n={};return e.m=t,e.c=n,e.i=function(t){return t},e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:r})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=721)}({199:function(t,e,n){"use strict";var r=n(201);t.exports=r},201:function(t,e,n){"use strict";var r=n(203),o=n(204),i=n(202),u=n(205),c=new i,a=!1,s="",f=null,l={},p=/{.*}/;try{var h=window.name.match(p);if(h&&h[0])var l=JSON.parse(h[0])}catch(t){l={}}l.hostOrigin&&".dingtalk.com"===l.hostOrigin.split(":")[1].slice(0-".dingtalk.com".length)&&l.containerId&&(a=!0,s=l.hostOrigin,f=l.containerId);var d={},v=new Promise(function(t,e){d._resolve=t,d._reject=e}),y={},_=null;window.top!==window?(_=window.top,d._resolve()):"object"==typeof dingtalk&&"object"==typeof dingtalk.platform&&"function"==typeof dingtalk.platform.invokeAPI&&(_=window,d._resolve()),y[u.SYS_INIT]=function(t){_=t.frameWindow,d._resolve(),t.respond({})},window.addEventListener("message",function(t){var e=t.data,n=t.origin;if(n===s)if("response"===e.type&&e.msgId){var r=e.msgId,i=c.getMsyById(r);i&&i.methodName!==u.SYS_EVENT&&i.receiveResponse(e.body,!e.success)}else if("event"===e.type&&e.msgId){var r=e.msgId,i=c.getMsyById(r);i&&i.receiveEvent(e.eventName,e.body)}else if("request"===e.type&&e.msgId){var i=new o(t.source,n,e);y[i.methodName]&&y[i.methodName](i)}}),e.invokeAPI=function(t,e){var n=new r(f,t,e);return a&&v.then(function(){_&&_.postMessage(n.getPayload(),s),c.addPending(n)}),n};var b=null;e.addEventListener=function(t,n){b||(b=e.invokeAPI(u.SYS_EVENT,{})),b.addEventListener(t,n)},e.removeEventListener=function(t,e){b&&b.removeEventListener(t,e)}},202:function(t,e,n){"use strict";var r=function(){this.pendingMsgs={}};r.prototype.addPending=function(t){this.pendingMsgs[t.id]=t;var e=function(){delete this.pendingMsgs[t.id],t.removeEventListener("_finish",e)}.bind(this);t.addEventListener("_finish",e)},r.prototype.getMsyById=function(t){return this.pendingMsgs[t]},t.exports=r},203:function(t,e,n){"use strict";var r=n(716),o=n(715),i=0,u=Math.floor(1e3*Math.random()),c=function(){return 1e3*(1e3*u+Math.floor(1e3*Math.random()))+ ++i%1e3},a={code:408,reason:"timeout"},s={TIMEOUT:"_timeout",FINISH:"_finish"},f={timeout:-1},l=function(t,e,n,r){this.id=c(),this.methodName=e,this.containerId=t,this.option=o({},f,r);var n=n||{};this._p={},this.result=new Promise(function(t,e){this._p._resolve=t,this._p._reject=e}.bind(this)),this.callbacks={},this.plainMsg=this._handleMsg(n),this._eventsHandle={},this._timeoutTimer=null,this._initTimeout(),this.isFinish=!1};l.prototype._initTimeout=function(){this._clearTimeout(),this.option.timeout>0&&(this._timeoutTimer=setTimeout(function(){this.receiveEvent(s.TIMEOUT),this.receiveResponse(a,!0)}.bind(this),this.option.timeout))},l.prototype._clearTimeout=function(){clearTimeout(this._timeoutTimer)},l.prototype._handleMsg=function(t){var e={};return Object.keys(t).forEach(function(n){var o=t[n];"function"==typeof o&&"on"===n.slice(0,2)?this.callbacks[n]=o:e[n]=r(o)}.bind(this)),e},l.prototype.getPayload=function(){return{msgId:this.id,containerId:this.containerId,methodName:this.methodName,body:this.plainMsg,type:"request"}},l.prototype.receiveEvent=function(t,e){if(this.isFinish&&t!==s.FINISH)return!1;t!==s.FINISH&&t!==s.TIMEOUT&&this._initTimeout(),Array.isArray(this._eventsHandle[t])&&this._eventsHandle[t].forEach(function(t){try{t(e)}catch(t){console.error(e)}});var n="on"+t.charAt(0).toUpperCase()+t.slice(1);return this.callbacks[n]&&this.callbacks[n](e),!0},l.prototype.addEventListener=function(t,e){if(!t||"function"!=typeof e)throw"eventName is null or handle is not a function, addEventListener fail";Array.isArray(this._eventsHandle[t])||(this._eventsHandle[t]=[]),this._eventsHandle[t].push(e)},l.prototype.removeEventListener=function(t,e){if(!t||!e)throw"eventName is null or handle is null, invoke removeEventListener fail";if(Array.isArray(this._eventsHandle[t])){var n=this._eventsHandle[t].indexOf(e);-1!==n&&this._eventsHandle[t].splice(n,1)}},l.prototype.receiveResponse=function(t,e){if(!0===this.isFinish)return!1;this._clearTimeout();var e=!!e;return e?this._p._reject(t):this._p._resolve(t),setTimeout(function(){this.receiveEvent(s.FINISH)}.bind(this),0),this.isFinish=!0,!0},t.exports=l},204:function(t,e,n){"use strict";var r=function(t,e,n){if(this._msgId=n.msgId,this.frameWindow=t,this.methodName=n.methodName,this.clientOrigin=e,this.containerId=n.containerId,this.params=n.body,!this._msgId)throw"msgId not exist";if(!this.frameWindow)throw"frameWindow not exist";if(!this.methodName)throw"methodName not exits";if(!this.clientOrigin)throw"clientOrigin not exist";this.hasResponded=!1};r.prototype.respond=function(t,e){var e=!!e;if(!0!==this.hasResponded){var n={type:"response",success:!e,body:t,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin),this.hasResponded=!0}},r.prototype.emit=function(t,e){var n={type:"event",eventName:t,body:e,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin)},t.exports=r},205:function(t,e,n){"use strict";t.exports={SYS_EVENT:"SYS_openAPIContainerInitEvent",SYS_INIT:"SYS_openAPIContainerInit"}},4:function(t,e){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},714:function(t,e,n){(function(t,n){function r(t,e){return t.set(e[0],e[1]),t}function o(t,e){return t.add(e),t}function i(t,e){for(var n=-1,r=t.length;++n<r&&!1!==e(t[n],n,t););return t}function u(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}function c(t,e,n,r){var o=-1,i=t.length;for(r&&i&&(n=t[++o]);++o<i;)n=e(n,t[o],o,t);return n}function a(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}function s(t){return t&&t.Object===Object?t:null}function f(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function l(t){var e=-1,n=Array(t.size);return t.forEach(function(t,r){n[++e]=[r,t]}),n}function p(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}function h(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function d(){this.__data__=ke?ke(null):{}}function v(t){return this.has(t)&&delete this.__data__[t]}function y(t){var e=this.__data__;if(ke){var n=e[t];return n===St?void 0:n}return ye.call(e,t)?e[t]:void 0}function _(t){var e=this.__data__;return ke?void 0!==e[t]:ye.call(e,t)}function b(t,e){return this.__data__[t]=ke&&void 0===e?St:e,this}function g(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function m(){this.__data__=[]}function j(t){var e=this.__data__,n=W(e,t);return!(n<0||(n==e.length-1?e.pop():xe.call(e,n,1),0))}function w(t){var e=this.__data__,n=W(e,t);return n<0?void 0:e[n][1]}function I(t){return W(this.__data__,t)>-1}function O(t,e){var n=this.__data__,r=W(n,t);return r<0?n.push([t,e]):n[r][1]=e,this}function x(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function A(){this.__data__={hash:new h,map:new(Me||g),string:new h}}function E(t){return rt(this,t).delete(t)}function S(t){return rt(this,t).get(t)}function M(t){return rt(this,t).has(t)}function N(t,e){return rt(this,t).set(t,e),this}function P(t){this.__data__=new g(t)}function T(){this.__data__=new g}function k(t){return this.__data__.delete(t)}function F(t){return this.__data__.get(t)}function H(t){return this.__data__.has(t)}function L(t,e){var n=this.__data__;return n instanceof g&&n.__data__.length==Et&&(n=this.__data__=new x(n.__data__)),n.set(t,e),this}function $(t,e,n){var r=t[e];ye.call(t,e)&&yt(r,n)&&(void 0!==n||e in t)||(t[e]=n)}function W(t,e){for(var n=t.length;n--;)if(yt(t[n][0],e))return n;return-1}function U(t,e){return t&&tt(e,At(e),t)}function R(t,e,n,r,o,u,c){var a;if(r&&(a=u?r(t,o,u,c):r(t)),void 0!==a)return a;if(!wt(t))return t;var s=Ye(t);if(s){if(a=at(t),!e)return Z(t,a)}else{var l=ct(t),p=l==kt||l==Ft;if(Ce(t))return D(t,e);if(l==$t||l==Nt||p&&!u){if(f(t))return u?t:{};if(a=st(p?{}:t),!e)return et(t,U(a,t))}else{if(!re[l])return u?t:{};a=ft(t,l,R,e)}}c||(c=new P);var h=c.get(t);if(h)return h;if(c.set(t,a),!s)var d=n?nt(t):At(t);return i(d||t,function(o,i){d&&(i=o,o=t[i]),$(a,i,R(o,e,n,r,i,t,c))}),a}function B(t){return wt(t)?Ie(t):{}}function Y(t,e,n){var r=e(t);return Ye(t)?r:u(r,n(t))}function C(t,e){return ye.call(t,e)||"object"==typeof t&&e in t&&null===it(t)}function V(t){return Ee(Object(t))}function D(t,e){if(e)return t.slice();var n=new t.constructor(t.length);return t.copy(n),n}function G(t){var e=new t.constructor(t.byteLength);return new je(e).set(new je(t)),e}function q(t,e){var n=e?G(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}function z(t,e,n){return c(e?n(l(t),!0):l(t),r,new t.constructor)}function J(t){var e=new t.constructor(t.source,te.exec(t));return e.lastIndex=t.lastIndex,e}function K(t,e,n){return c(e?n(p(t),!0):p(t),o,new t.constructor)}function Q(t){return Re?Object(Re.call(t)):{}}function X(t,e){var n=e?G(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}function Z(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}function tt(t,e,n,r){n||(n={});for(var o=-1,i=e.length;++o<i;){var u=e[o];$(n,u,r?r(n[u],t[u],u,n,t):t[u])}return n}function et(t,e){return tt(t,ut(t),e)}function nt(t){return Y(t,At,ut)}function rt(t,e){var n=t.__data__;return ht(e)?n["string"==typeof e?"string":"hash"]:n.map}function ot(t,e){var n=t[e];return Ot(n)?n:void 0}function it(t){return Ae(Object(t))}function ut(t){return we(Object(t))}function ct(t){return _e.call(t)}function at(t){var e=t.length,n=t.constructor(e);return e&&"string"==typeof t[0]&&ye.call(t,"index")&&(n.index=t.index,n.input=t.input),n}function st(t){return"function"!=typeof t.constructor||dt(t)?{}:B(it(t))}function ft(t,e,n,r){var o=t.constructor;switch(e){case Yt:return G(t);case Pt:case Tt:return new o(+t);case Ct:return q(t,r);case Vt:case Dt:case Gt:case qt:case zt:case Jt:case Kt:case Qt:case Xt:return X(t,r);case Ht:return z(t,r,n);case Lt:case Rt:return new o(t);case Wt:return J(t);case Ut:return K(t,r,n);case Bt:return Q(t)}}function lt(t){var e=t?t.length:void 0;return jt(e)&&(Ye(t)||xt(t)||_t(t))?a(e,String):null}function pt(t,e){return!!(e=null==e?Mt:e)&&("number"==typeof t||ne.test(t))&&t>-1&&t%1==0&&t<e}function ht(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}function dt(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||de)}function vt(t){if(null!=t){try{return ve.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function yt(t,e){return t===e||t!==t&&e!==e}function _t(t){return gt(t)&&ye.call(t,"callee")&&(!Oe.call(t,"callee")||_e.call(t)==Nt)}function bt(t){return null!=t&&jt(Be(t))&&!mt(t)}function gt(t){return It(t)&&bt(t)}function mt(t){var e=wt(t)?_e.call(t):"";return e==kt||e==Ft}function jt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Mt}function wt(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function It(t){return!!t&&"object"==typeof t}function Ot(t){return!!wt(t)&&(mt(t)||f(t)?be:ee).test(vt(t))}function xt(t){return"string"==typeof t||!Ye(t)&&It(t)&&_e.call(t)==Rt}function At(t){var e=dt(t);if(!e&&!bt(t))return V(t);var n=lt(t),r=!!n,o=n||[],i=o.length;for(var u in t)!C(t,u)||r&&("length"==u||pt(u,i))||e&&"constructor"==u||o.push(u);return o}var Et=200,St="__lodash_hash_undefined__",Mt=9007199254740991,Nt="[object Arguments]",Pt="[object Boolean]",Tt="[object Date]",kt="[object Function]",Ft="[object GeneratorFunction]",Ht="[object Map]",Lt="[object Number]",$t="[object Object]",Wt="[object RegExp]",Ut="[object Set]",Rt="[object String]",Bt="[object Symbol]",Yt="[object ArrayBuffer]",Ct="[object DataView]",Vt="[object Float32Array]",Dt="[object Float64Array]",Gt="[object Int8Array]",qt="[object Int16Array]",zt="[object Int32Array]",Jt="[object Uint8Array]",Kt="[object Uint8ClampedArray]",Qt="[object Uint16Array]",Xt="[object Uint32Array]",Zt=/[\\^$.*+?()[\]{}|]/g,te=/\w*$/,ee=/^\[object .+?Constructor\]$/,ne=/^(?:0|[1-9]\d*)$/,re={};re[Nt]=re["[object Array]"]=re[Yt]=re[Ct]=re[Pt]=re[Tt]=re[Vt]=re[Dt]=re[Gt]=re[qt]=re[zt]=re[Ht]=re[Lt]=re[$t]=re[Wt]=re[Ut]=re[Rt]=re[Bt]=re[Jt]=re[Kt]=re[Qt]=re[Xt]=!0,re["[object Error]"]=re[kt]=re["[object WeakMap]"]=!1;var oe={function:!0,object:!0},ie=oe[typeof e]&&e&&!e.nodeType?e:void 0,ue=oe[typeof t]&&t&&!t.nodeType?t:void 0,ce=ue&&ue.exports===ie?ie:void 0,ae=s(ie&&ue&&"object"==typeof n&&n),se=s(oe[typeof self]&&self),fe=s(oe[typeof window]&&window),le=s(oe[typeof this]&&this),pe=ae||fe!==(le&&le.window)&&fe||se||le||Function("return this")(),he=Array.prototype,de=Object.prototype,ve=Function.prototype.toString,ye=de.hasOwnProperty,_e=de.toString,be=RegExp("^"+ve.call(ye).replace(Zt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ge=ce?pe.Buffer:void 0,me=pe.Symbol,je=pe.Uint8Array,we=Object.getOwnPropertySymbols,Ie=Object.create,Oe=de.propertyIsEnumerable,xe=he.splice,Ae=Object.getPrototypeOf,Ee=Object.keys,Se=ot(pe,"DataView"),Me=ot(pe,"Map"),Ne=ot(pe,"Promise"),Pe=ot(pe,"Set"),Te=ot(pe,"WeakMap"),ke=ot(Object,"create"),Fe=vt(Se),He=vt(Me),Le=vt(Ne),$e=vt(Pe),We=vt(Te),Ue=me?me.prototype:void 0,Re=Ue?Ue.valueOf:void 0;h.prototype.clear=d,h.prototype.delete=v,h.prototype.get=y,h.prototype.has=_,h.prototype.set=b,g.prototype.clear=m,g.prototype.delete=j,g.prototype.get=w,g.prototype.has=I,g.prototype.set=O,x.prototype.clear=A,x.prototype.delete=E,x.prototype.get=S,x.prototype.has=M,x.prototype.set=N,P.prototype.clear=T,P.prototype.delete=k,P.prototype.get=F,P.prototype.has=H,P.prototype.set=L;var Be=function(t){return function(e){return null==e?void 0:e.length}}();we||(ut=function(){return[]}),(Se&&ct(new Se(new ArrayBuffer(1)))!=Ct||Me&&ct(new Me)!=Ht||Ne&&"[object Promise]"!=ct(Ne.resolve())||Pe&&ct(new Pe)!=Ut||Te&&"[object WeakMap]"!=ct(new Te))&&(ct=function(t){var e=_e.call(t),n=e==$t?t.constructor:void 0,r=n?vt(n):void 0;if(r)switch(r){case Fe:return Ct;case He:return Ht;case Le:return"[object Promise]";case $e:return Ut;case We:return"[object WeakMap]"}return e});var Ye=Array.isArray,Ce=ge?function(t){return t instanceof ge}:function(t){return function(){return!1}}();t.exports=R}).call(e,n(719)(t),n(4))},715:function(t,e,n){function r(t,e,n){var r=t[e];m.call(t,e)&&a(r,n)&&(void 0!==n||e in t)||(t[e]=n)}function o(t,e,n,o){n||(n={});for(var i=-1,u=e.length;++i<u;){var c=e[i];r(n,c,o?o(n[c],t[c],c,n,t):t[c])}return n}function i(t,e){return!!(e=null==e?v:e)&&("number"==typeof t||b.test(t))&&t>-1&&t%1==0&&t<e}function u(t,e,n){if(!p(n))return!1;var r=typeof e;return!!("number"==r?s(n)&&i(e,n.length):"string"==r&&e in n)&&a(n[e],t)}function c(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||g)}function a(t,e){return t===e||t!==t&&e!==e}function s(t){return null!=t&&l(O(t))&&!f(t)}function f(t){var e=p(t)?j.call(t):"";return e==y||e==_}function l(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=v}function p(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}var h=n(717),d=n(718),v=9007199254740991,y="[object Function]",_="[object GeneratorFunction]",b=/^(?:0|[1-9]\d*)$/,g=Object.prototype,m=g.hasOwnProperty,j=g.toString,w=g.propertyIsEnumerable,I=!w.call({valueOf:1},"valueOf"),O=function(t){return function(e){return null==e?void 0:e.length}}(),x=function(t){return d(function(e,n){var r=-1,o=n.length,i=o>1?n[o-1]:void 0,c=o>2?n[2]:void 0;for(i=t.length>3&&"function"==typeof i?(o--,i):void 0,c&&u(n[0],n[1],c)&&(i=o<3?void 0:i,o=1),e=Object(e);++r<o;){var a=n[r];a&&t(e,a)}return e})}(function(t,e){if(I||c(e)||s(e))return void o(e,h(e),t);for(var n in e)m.call(e,n)&&r(t,n,e[n])});t.exports=x},716:function(t,e,n){function r(t){return o(t,!0,!0)}var o=n(714);t.exports=r},717:function(t,e){function n(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}function r(t,e){var r=x(t)||c(t)?n(t.length,String):[],o=r.length,u=!!o;for(var a in t)!e&&!j.call(t,a)||u&&("length"==a||i(a,o))||r.push(a);return r}function o(t){if(!u(t))return O(t);var e=[];for(var n in Object(t))j.call(t,n)&&"constructor"!=n&&e.push(n);return e}function i(t,e){return!!(e=null==e?v:e)&&("number"==typeof t||g.test(t))&&t>-1&&t%1==0&&t<e}function u(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||m)}function c(t){return s(t)&&j.call(t,"callee")&&(!I.call(t,"callee")||w.call(t)==y)}function a(t){return null!=t&&l(t.length)&&!f(t)}function s(t){return h(t)&&a(t)}function f(t){var e=p(t)?w.call(t):"";return e==_||e==b}function l(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=v}function p(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function h(t){return!!t&&"object"==typeof t}function d(t){return a(t)?r(t):o(t)}var v=9007199254740991,y="[object Arguments]",_="[object Function]",b="[object GeneratorFunction]",g=/^(?:0|[1-9]\d*)$/,m=Object.prototype,j=m.hasOwnProperty,w=m.toString,I=m.propertyIsEnumerable,O=function(t,e){return function(n){return t(e(n))}}(Object.keys,Object),x=Array.isArray;t.exports=d},718:function(t,e){function n(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function r(t,e){return e=I(void 0===e?t.length-1:e,0),function(){for(var r=arguments,o=-1,i=I(r.length-e,0),u=Array(i);++o<i;)u[o]=r[e+o];o=-1;for(var c=Array(e+1);++o<e;)c[o]=r[o];return c[e]=u,n(t,this,c)}}function o(t,e){if("function"!=typeof t)throw new TypeError(l);return e=void 0===e?e:s(e),r(t,e)}function i(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function u(t){return!!t&&"object"==typeof t}function c(t){return"symbol"==typeof t||u(t)&&w.call(t)==v}function a(t){return t?(t=f(t))===p||t===-p?(t<0?-1:1)*h:t===t?t:0:0===t?t:0}function s(t){var e=a(t),n=e%1;return e===e?n?e-n:e:0}function f(t){if("number"==typeof t)return t;if(c(t))return d;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(y,"");var n=b.test(t);return n||g.test(t)?m(t.slice(2),n?2:8):_.test(t)?d:+t}var l="Expected a function",p=1/0,h=1.7976931348623157e308,d=NaN,v="[object Symbol]",y=/^\s+|\s+$/g,_=/^[-+]0x[0-9a-f]+$/i,b=/^0b[01]+$/i,g=/^0o[0-7]+$/i,m=parseInt,j=Object.prototype,w=j.toString,I=Math.max;t.exports=o},719:function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},721:function(t,e,n){t.exports=n(199)}})})},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.off=exports.on=void 0;var NON_BRIDGE_EVENTS=["resume","pause","online","offline","backbutton","goBack","pullToRefresh","message","recycle","restore","drawer","tab","navHelpIcon","navRightButton","navMenu","navTitle","appLinkResponse","internalPageLinkResponse","networkEvent","hostTaskEvent","deviceOrientationChanged","autoCheckIn","deviceFound","hostCheckIn","screenshot","becomeActive","keepAlive","navTitleClick","sharePage","wxNotify","editNoteCommand","updateStyle","qrscanCommonNotify","__message__","dtChannelEvent","livePlayerEventPlay","livePlayerEventPause","livePlayerEventEnded","livePlayerEventError","navActions","attendEvents"],handlerProxyMap=function(){return"undefined"==typeof WeakMap?void 0:new WeakMap}(),getOnHandlerProxy=function(type,handler){if(handlerProxyMap){var handlerProxy=handlerProxyMap.get(handler);return void 0===handlerProxy?(handlerProxy=function(e){var detail=e.detail;if(detail.namespace&&detail.eventName){var eventType=detail.namespace+"."+detail.eventName;handlerProxy&&-1!==handlerProxy.__eventTypeList__.indexOf(eventType)&&handler(detail.data)}},handlerProxy.__eventTypeList__=[type],handlerProxyMap.set(handler,handlerProxy)):-1===handlerProxy.__eventTypeList__.indexOf(type)&&handlerProxy.__eventTypeList__.push(type),handlerProxy}},getOffHandlerProxy=function(type,handler){if(handlerProxyMap){var handlerProxy=handlerProxyMap.get(handler);return handlerProxy&&-1!==handlerProxy.__eventTypeList__.indexOf(type)&&handlerProxy.__eventTypeList__.splice(handlerProxy.__eventTypeList__.indexOf(type),1),handlerProxy&&handlerProxy.__eventTypeList__.length<=1?handlerProxy:void 0}};exports.on=function(type,handler){if(-1!==NON_BRIDGE_EVENTS.indexOf(type))document.addEventListener(type,handler);else{var proxyFunc=getOnHandlerProxy(type,handler);proxyFunc?document.addEventListener("dtBizBridgeEvent",proxyFunc):console.log("bind event : "+type+" need WeakMap support , current environment doesnot")}},exports.off=function(type,handler){if(-1!==NON_BRIDGE_EVENTS.indexOf(type))document.removeEventListener(type,handler);else{var handlerProxy=getOffHandlerProxy(type,handler);handlerProxy&&document.removeEventListener("dtBizBridgeEvent",handlerProxy)}}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var webviewInMiniappBridgeReadyPromise,noop=function(){},webviewInMiniappBridgeInit=function(){return webviewInMiniappBridgeReadyPromise||(webviewInMiniappBridgeReadyPromise=new Promise(function(resolve,reject){window.AlipayJSBridge?resolve():document.addEventListener("AlipayJSBridgeReady",function(){resolve()},!1)})),webviewInMiniappBridgeReadyPromise},webviewInMiniappBridge=function(method,params){return webviewInMiniappBridgeInit().then(function(){return new Promise(function(resolve,reject){var successCallback=params.onSuccess||noop,failCallback=params.onFail||noop;if(delete params.onSuccess,delete params.onFail,AlipayJSBridge){var mArr=method.split("."),actionName=mArr.pop()||"",serviceName=mArr.join(".");AlipayJSBridge.call.apply(null,["webDdExec",{serviceName:serviceName,actionName:actionName,args:params},function(res){var result={},contentStr=res.content;if(contentStr)try{result=JSON.parse(contentStr)}catch(e){console.error("parse dt api result error",contentStr,e)}res.success?(successCallback.apply(null,[result]),resolve(result)):(failCallback.apply(null,[result]),reject(result))}])}else{var err=new Error("Fatal error, cannot find bridge ,current env is WebView in MiniApp");failCallback(err),reject(err)}})})};exports.default=webviewInMiniappBridge},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.androidWeexBridge=exports.iosWeexBridge=exports.requireModule=void 0;exports.requireModule=function(moduleName){return"undefined"!=typeof __weex_require__?__weex_require__("@weex-module/"+moduleName):"undefined"!=typeof weex?weex.requireModule(moduleName):void 0},exports.iosWeexBridge=function(){return Promise.resolve(function(method,params){return new Promise(function(resolve,reject){var bridge=exports.requireModule("nuvajs-exec"),nameArr=method.split("."),action=nameArr.pop(),plugin=nameArr.join(".");bridge.exec({plugin:plugin,action:action,args:params},function(res){res&&"0"===res.errorCode?("function"==typeof params.success?params.success(res.result):"function"==typeof params.onSuccess&&params.onSuccess(res.result),resolve(res.result)):("function"==typeof params.fail?params.fail(res.result):"function"==typeof params.onFail&&params.onFail(res.result),reject(res.result))})})})},exports.androidWeexBridge=function(){return Promise.resolve(function(method,params){return new Promise(function(resolve,reject){var bridge=exports.requireModule("nuvajs-exec"),nameArr=method.split("."),action=nameArr.pop(),plugin=nameArr.join(".");bridge.exec({plugin:plugin,action:action,args:params},function(res){var dataAndroid={};try{if(res&&res.__message__)if("object"==typeof res.__message__)dataAndroid=res.__message__;else try{dataAndroid=JSON.parse(res.__message__)}catch(error){"string"==typeof res.__message__&&(dataAndroid=res.__message__)}}catch(error){}res&&1===parseInt(res.__status__+"",10)?("function"==typeof params.onSuccess&&params.onSuccess(dataAndroid),resolve(dataAndroid)):("function"==typeof params.onFail&&params.onFail(dataAndroid),reject(dataAndroid))})})})}},function(module,exports,__webpack_require__){"use strict";var _this=this;Object.defineProperty(exports,"__esModule",{value:!0}),exports.off=exports.on=void 0;var weex_1=__webpack_require__(11);exports.on=function(type,handler){weex_1.requireModule("globalEvent").addEventListener(type,function(e){var event={preventDefault:function(){throw new Error("does not support preventDefault")},detail:e};handler.call(_this,event)})},exports.off=function(type,handler){weex_1.requireModule("globalEvent").removeEventListener(type,handler)}},function(module,exports,__webpack_require__){"use strict";(function(process){Object.defineProperty(exports,"__esModule",{value:!0}),exports.formatLog=exports.diagnosticMessageMap=exports.LogLevel=void 0;var LogLevel;(function(LogLevel){LogLevel.INFO="INFO",LogLevel.WARN="WARN",LogLevel.ERROR="ERROR"})(LogLevel=exports.LogLevel||(exports.LogLevel={}));var diagLog=function(code,category,message,solution){return void 0===solution&&(solution=void 0),{code:code,category:category,message:message,solution:solution}};exports.diagnosticMessageMap={config_debug_deprecated:diagLog(1010,LogLevel.WARN,"This is a deprecated feature (dd.debug - debug:true), recommend use dd.devConfig"),dd_config_wrap_deprecated:diagLog(1020,LogLevel.WARN,"You don 't use a dd.config, so you don't need to wrap dd.ready, recommend remove dd.ready"),not_support_event_on:diagLog(1030,LogLevel.WARN,"\"event.on\" do not support the current platform ('{0}')"),not_support_event_off:diagLog(1040,LogLevel.WARN,"\"event.off\" do not support the current platform ('{0}')"),repeat_config:diagLog(1040,LogLevel.WARN,"dd.config has been executed, please don't repeat config"),JsBridge_init_fail:diagLog(5010,LogLevel.ERROR,"JsBridge initialization fails, jsapi will not work"),auto_bridge_init_error:diagLog(5020,LogLevel.ERROR,"auto bridgeInit error"),JsBridge_init_fail_dd_config:diagLog(5010,LogLevel.ERROR,'JsBridge initialization failed and "dd.config" failed to call'),not_support_env:diagLog(4040,LogLevel.ERROR,"Do not support the current environment：'{0}'"),call_api_support_platform_error:diagLog(4050,LogLevel.ERROR,"'{0}' do not support the current platform ('{1}')"),call_api_config_platform_error:diagLog(4060,LogLevel.ERROR,"This API method is not configured for the platform ('{0}')"),call_api_on_before_error:diagLog(4060,LogLevel.ERROR,"Call Hook:onBeforeInvokeAPI failed , reason: '{0}'"),call_api_on_after_error:diagLog(4060,LogLevel.ERROR,"Call Hook:onAfterInvokeAPI failed , reason: '{0}'")},exports.formatLog=function(diagnosticMessage){for(var _a,args=[],_i=1;_i<arguments.length;_i++)args[_i-1]=arguments[_i];var msg="[DINGTALK-JSAPI] "+diagnosticMessage.category+" "+diagnosticMessage.code+": "+diagnosticMessage.message.replace(/{(\d)}/g,function(match,$1){return args[$1]||match});return"object"==typeof process&&"production"!==(null===(_a=null===process||void 0===process?void 0:process.env)||void 0===_a?void 0:_a.NODE_ENV)&&console.warn(msg),msg}}).call(exports,__webpack_require__(211))},function(module,exports,__webpack_require__){"use strict";function isFunction(param){return"function"==typeof param}function compareVersion(origin,target){function transform(item){return parseInt(item,10)||0}for(var originVersionArr=origin.split(".").map(transform),targetVersionArr=target.split(".").map(transform),i=0;i<originVersionArr.length;i++){if(void 0===targetVersionArr[i])return!1;if(originVersionArr[i]<targetVersionArr[i])return!1;if(originVersionArr[i]>targetVersionArr[i])return!0}return!0}Object.defineProperty(exports,"__esModule",{value:!0}),exports.LogLevel=exports.APP_TYPE=exports.ENV_ENUM_SUB=exports.ENV_ENUM=exports.ERROR_CODE=exports.compareVersion=exports.isFunction=void 0,exports.isFunction=isFunction,exports.compareVersion=compareVersion;(function(ERROR_CODE){ERROR_CODE.cancel="-1",ERROR_CODE.not_exist="1",ERROR_CODE.no_permission="7"})(exports.ERROR_CODE||(exports.ERROR_CODE={}));(function(ENV_ENUM){ENV_ENUM.pc="pc",ENV_ENUM.android="android",ENV_ENUM.ios="ios",ENV_ENUM.gdtPc="gdtPc",ENV_ENUM.gdtAndroid="gdtAndroid",ENV_ENUM.gdtIos="gdtIos",ENV_ENUM.gdtStandardAndroid="gdtStandardAndroid",ENV_ENUM.gdtStandardIos="gdtStandardIos",ENV_ENUM.notInDingTalk="notInDingTalk",ENV_ENUM.windows="windows",ENV_ENUM.mac="mac",ENV_ENUM.harmony="harmony"})(exports.ENV_ENUM||(exports.ENV_ENUM={}));(function(ENV_ENUM_SUB){ENV_ENUM_SUB.mac="mac",ENV_ENUM_SUB.win="win",ENV_ENUM_SUB.noSub="noSub"})(exports.ENV_ENUM_SUB||(exports.ENV_ENUM_SUB={}));(function(APP_TYPE){APP_TYPE.WEB="WEB",APP_TYPE.MINI_APP="MINI_APP",APP_TYPE.WEEX="WEEX",APP_TYPE.WEBVIEW_IN_MINIAPP="WEBVIEW_IN_MINIAPP",APP_TYPE.WEEX_WIDGET="WEEX_WIDGET"})(exports.APP_TYPE||(exports.APP_TYPE={}));(function(LogLevel){LogLevel[LogLevel.INFO=1]="INFO",LogLevel[LogLevel.WARNING=2]="WARNING",LogLevel[LogLevel.ERROR=3]="ERROR"})(exports.LogLevel||(exports.LogLevel={}))},function(module,exports){var g;g=function(){return this}();try{g=g||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(g=window)}module.exports=g},function(module,exports,__webpack_require__){"use strict";function ExternalChannelPublish$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ExternalChannelPublish$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.channel.externalChannelPublish";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.50"},_a)),exports.ExternalChannelPublish$=ExternalChannelPublish$,exports.default=ExternalChannelPublish$},function(module,exports,__webpack_require__){"use strict";function addPhoneContact$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.addPhoneContact$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.phoneContact.add";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.addPhoneContact$=addPhoneContact$,exports.default=addPhoneContact$},function(module,exports,__webpack_require__){"use strict";function alert$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{message:params.content,title:null===params||void 0===params?void 0:params.title,buttonName:params.buttonText,success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.alert$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.alert",paramsDeal=function(params){return __assign({message:params.content,title:params.title||"",buttonName:params.buttonText||"确定"},params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:paramsDeal},_a)),exports.alert$=alert$,exports.default=alert$},function(module,exports,__webpack_require__){"use strict";function callUsers$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.callUsers$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.telephone.call";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.callUsers$=callUsers$,exports.default=callUsers$},function(module,exports,__webpack_require__){"use strict";function checkAuth$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkAuth$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.checkAuth";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.checkAuth$=checkAuth$,exports.default=checkAuth$},function(module,exports,__webpack_require__){"use strict";function checkBizCall$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkBizCall$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.telephone.checkBizCall";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",resultDeal:function(res){return res?{isSupport:!0}:{isSupport:!1}}},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.checkBizCall$=checkBizCall$,exports.default=checkBizCall$},function(module,exports,__webpack_require__){"use strict";function chooseChat$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseChat$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.chat.chooseConversationByCorpId";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.chooseChat$=chooseChat$,exports.default=chooseChat$},function(module,exports,__webpack_require__){"use strict";function chooseConversation$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseConversation$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.chat.chooseConversation";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.chooseConversation$=chooseConversation$,exports.default=chooseConversation$},function(module,exports,__webpack_require__){"use strict";function chooseDateRangeInCalendar$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDateRangeInCalendar$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.calendar.chooseInterval";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseDateRangeInCalendar$=chooseDateRangeInCalendar$,exports.default=chooseDateRangeInCalendar$},function(module,exports,__webpack_require__){"use strict";function chooseDateTime$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDateTime$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.calendar.chooseDateTime";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseDateTime$=chooseDateTime$,exports.default=chooseDateTime$},function(module,exports,__webpack_require__){"use strict";function chooseDepartments$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDepartments$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.departmentsPicker";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.chooseDepartments$=chooseDepartments$,exports.default=chooseDepartments$},function(module,exports,__webpack_require__){"use strict";function chooseDingTalkDir$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDingTalkDir$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.cspace.chooseSpaceDir";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.chooseDingTalkDir$=chooseDingTalkDir$,exports.default=chooseDingTalkDir$},function(module,exports,__webpack_require__){"use strict";function chooseDistrict$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDistrict$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.chooseRegion";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.chooseDistrict$=chooseDistrict$,exports.default=chooseDistrict$},function(module,exports,__webpack_require__){"use strict";function chooseExternalUsers$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseExternalUsers$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.externalComplexPicker";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.chooseExternalUsers$=chooseExternalUsers$,exports.default=chooseExternalUsers$},function(module,exports,__webpack_require__){"use strict";function chooseFile$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseFile$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.file.chooseFile";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.1.5"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.1.5"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.1.10"},_a)),exports.chooseFile$=chooseFile$,exports.default=chooseFile$},function(module,exports,__webpack_require__){"use strict";function chooseHalfDayInCalendar$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseHalfDayInCalendar$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.calendar.chooseHalfDay";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseHalfDayInCalendar$=chooseHalfDayInCalendar$,exports.default=chooseHalfDayInCalendar$},function(module,exports,__webpack_require__){"use strict";function chooseImage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseImage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.chooseImage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.chooseImage$=chooseImage$,exports.default=chooseImage$},function(module,exports,__webpack_require__){"use strict";function chooseMedia$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseMedia$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.chooseMedia";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.2"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.2"},_a)),exports.chooseMedia$=chooseMedia$,exports.default=chooseMedia$},function(module,exports,__webpack_require__){"use strict";function chooseOneDayInCalendar$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseOneDayInCalendar$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.calendar.chooseOneDay";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseOneDayInCalendar$=chooseOneDayInCalendar$,exports.default=chooseOneDayInCalendar$},function(module,exports,__webpack_require__){"use strict";function choosePhonebook$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.choosePhonebook$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.chooseMobileContacts";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.choosePhonebook$=choosePhonebook$,exports.default=choosePhonebook$},function(module,exports,__webpack_require__){"use strict";function chooseStaffForPC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseStaffForPC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.choose";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.chooseStaffForPC$=chooseStaffForPC$,exports.default=chooseStaffForPC$},function(module,exports,__webpack_require__){"use strict";function chooseUserFromList$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseUserFromList$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.customContact.choose";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.chooseUserFromList$=chooseUserFromList$,exports.default=chooseUserFromList$},function(module,exports,__webpack_require__){"use strict";function clearShake$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.clearShake$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.accelerometer.clearShake";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.clearShake$=clearShake$,exports.default=clearShake$},function(module,exports,__webpack_require__){"use strict";function closeBluetoothAdapter$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.closeBluetoothAdapter$=void 0;var ddSdk_1=__webpack_require__(0),apiName="closeBluetoothAdapter";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.closeBluetoothAdapter$=closeBluetoothAdapter$,exports.default=closeBluetoothAdapter$},function(module,exports,__webpack_require__){"use strict";function closePage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.closePage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.close";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.closePage$=closePage$,exports.default=closePage$},function(module,exports,__webpack_require__){"use strict";function complexChoose$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.complexChoose$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.complexPicker";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.complexChoose$=complexChoose$,exports.default=complexChoose$},function(module,exports,__webpack_require__){"use strict";function compressImage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.compressImage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.compressImage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.compressImage$=compressImage$,exports.default=compressImage$},function(module,exports,__webpack_require__){"use strict";function confirm$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{title:params.title,message:params.content,buttonLabels:[params.cancelButtonText,params.confirmButtonText],success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.confirm$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.confirm",paramsDeal=function(params){var baseParams={title:params.title||"",message:params.content,buttonLabels:[params.cancelButtonText||"取消",params.confirmButtonText||"确定"]};return __assign(__assign({},baseParams),params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.confirm$=confirm$,exports.default=confirm$},function(module,exports,__webpack_require__){"use strict";function connectBLEDevice$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.connectBLEDevice$=void 0;var ddSdk_1=__webpack_require__(0),apiName="connectBLEDevice";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.connectBLEDevice$=connectBLEDevice$,exports.default=connectBLEDevice$},function(module,exports,__webpack_require__){"use strict";function createBLEPeripheralServer$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createBLEPeripheralServer$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.createBLEPeripheralServer";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.6.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.6.10"},_a)),exports.createBLEPeripheralServer$=createBLEPeripheralServer$,exports.default=createBLEPeripheralServer$},function(module,exports,__webpack_require__){"use strict";function createDing$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createDing$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.ding.create";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.createDing$=createDing$,exports.default=createDing$},function(module,exports,__webpack_require__){"use strict";function createDingForPC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createDingForPC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.ding.post";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.createDingForPC$=createDingForPC$,exports.default=createDingForPC$},function(module,exports,__webpack_require__){"use strict";function createGroupChat$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createGroupChat$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.createGroup";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.createGroupChat$=createGroupChat$,exports.default=createGroupChat$},function(module,exports,__webpack_require__){"use strict";function createLiveClassRoom$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createLiveClassRoom$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.live.startClassRoom";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.createLiveClassRoom$=createLiveClassRoom$,exports.default=createLiveClassRoom$},function(module,exports,__webpack_require__){"use strict";function cropImage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.cropImage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.cropImage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.2"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.2"},_a)),exports.cropImage$=cropImage$,exports.default=cropImage$},function(module,exports,__webpack_require__){"use strict";function customChooseUsers$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.customChooseUsers$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.customContact.multipleChoose",paramsDeal=apiHelper_1.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:paramsDeal},_a)),exports.customChooseUsers$=customChooseUsers$,exports.default=customChooseUsers$},function(module,exports,__webpack_require__){"use strict";function datePicker$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.datePicker$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.datetimepicker";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.datePicker$=datePicker$,exports.default=datePicker$},function(module,exports,__webpack_require__){"use strict";function dateRangePicker$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.dateRangePicker$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.calendar.chooseInterval";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.dateRangePicker$=dateRangePicker$,exports.default=dateRangePicker$},function(module,exports,__webpack_require__){"use strict";function decrypt$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.decrypt$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.decrypt";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.decrypt$=decrypt$,exports.default=decrypt$},function(module,exports,__webpack_require__){"use strict";function disablePullDownRefresh$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.disablePullDownRefresh$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="ui.pullToRefresh.disable";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.disablePullDownRefresh$=disablePullDownRefresh$,exports.default=disablePullDownRefresh$},function(module,exports,__webpack_require__){"use strict";function disableWebViewBounce$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.disableWebViewBounce$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="ui.webViewBounce.disable";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.disableWebViewBounce$=disableWebViewBounce$,exports.default=disableWebViewBounce$},function(module,exports,__webpack_require__){"use strict";function disconnectBLEDevice$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.disconnectBLEDevice$=void 0;var ddSdk_1=__webpack_require__(0),apiName="disconnectBLEDevice";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.disconnectBLEDevice$=disconnectBLEDevice$,exports.default=disconnectBLEDevice$},function(module,exports,__webpack_require__){"use strict";function downloadAudio$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.downloadAudio$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.download";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.downloadAudio$=downloadAudio$,exports.default=downloadAudio$},function(module,exports,__webpack_require__){"use strict";function downloadFile$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.downloadFile$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.file.downloadFile";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.downloadFile$=downloadFile$,exports.default=downloadFile$},function(module,exports,__webpack_require__){"use strict";function editExternalUser$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.editExternalUser$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.contact.externalEditForm";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.editExternalUser$=editExternalUser$,exports.default=editExternalUser$},function(module,exports,__webpack_require__){"use strict";function editPicture$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.editPicture$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.editPicture";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.1.21"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.1.21"},_a)),exports.editPicture$=editPicture$,exports.default=editPicture$},function(module,exports,__webpack_require__){"use strict";function enablePullDownRefresh$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.enablePullDownRefresh$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="ui.pullToRefresh.enable";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.enablePullDownRefresh$=enablePullDownRefresh$,exports.default=enablePullDownRefresh$},function(module,exports,__webpack_require__){"use strict";function enableWebViewBounce$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.enableWebViewBounce$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="ui.webViewBounce.enable";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.enableWebViewBounce$=enableWebViewBounce$,exports.default=enableWebViewBounce$},function(module,exports,__webpack_require__){"use strict";function encrypt$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.encrypt$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.encrypt";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.encrypt$=encrypt$,exports.default=encrypt$},function(module,exports,__webpack_require__){"use strict";function exclusiveLiveCheck$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.exclusiveLiveCheck$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.ATMBle.exclusiveLiveCheck";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.40"},_a)),exports.exclusiveLiveCheck$=exclusiveLiveCheck$,exports.default=exclusiveLiveCheck$},function(module,exports,__webpack_require__){"use strict";function generateImageFromCode$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.generateImageFromCode$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.generateImageFromCode";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.generateImageFromCode$=generateImageFromCode$,exports.default=generateImageFromCode$},function(module,exports,__webpack_require__){"use strict";function getAdvertisingStatus$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getAdvertisingStatus$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.getAdvertisingStatus";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.0"},_a)),exports.getAdvertisingStatus$=getAdvertisingStatus$,exports.default=getAdvertisingStatus$},function(module,exports,__webpack_require__){"use strict";function getAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="runtime.permission.requestAuthCode";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.getAuthCode$=getAuthCode$,exports.default=getAuthCode$},function(module,exports,__webpack_require__){"use strict";function getAuthCodeV2$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getAuthCodeV2$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="runtime.permission.requestAuthCodeV2";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.45"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.45"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.50"},_a)),exports.getAuthCodeV2$=getAuthCodeV2$,exports.default=getAuthCodeV2$},function(module,exports,__webpack_require__){"use strict";function getAuthInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getAuthInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="runtime.permission.getAuthInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.26"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.26"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.5.26"},_a)),exports.getAuthInfo$=getAuthInfo$,exports.default=getAuthInfo$},function(module,exports,__webpack_require__){"use strict";function getBLEDeviceCharacteristics$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBLEDeviceCharacteristics$=void 0;var ddSdk_1=__webpack_require__(0),apiName="getBLEDeviceCharacteristics";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.getBLEDeviceCharacteristics$=getBLEDeviceCharacteristics$,exports.default=getBLEDeviceCharacteristics$},function(module,exports,__webpack_require__){"use strict";function getBLEDeviceServices$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBLEDeviceServices$=void 0;var ddSdk_1=__webpack_require__(0),apiName="getBLEDeviceServices";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.getBLEDeviceServices$=getBLEDeviceServices$,exports.default=getBLEDeviceServices$},function(module,exports,__webpack_require__){"use strict";function getBatteryInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBatteryInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getBatteryInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.60"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.60"},_a)),exports.getBatteryInfo$=getBatteryInfo$,exports.default=getBatteryInfo$},function(module,exports,__webpack_require__){"use strict";function getBeacons$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBeacons$=void 0;var ddSdk_1=__webpack_require__(0),apiName="getBeacons";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.getBeacons$=getBeacons$,exports.default=getBeacons$},function(module,exports,__webpack_require__){"use strict";function getBluetoothAdapterState$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBluetoothAdapterState$=void 0;var ddSdk_1=__webpack_require__(0),apiName="getBluetoothAdapterState";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.getBluetoothAdapterState$=getBluetoothAdapterState$,exports.default=getBluetoothAdapterState$},function(module,exports,__webpack_require__){"use strict";function getBluetoothDevices$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBluetoothDevices$=void 0;var ddSdk_1=__webpack_require__(0),apiName="getBluetoothDevices";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.getBluetoothDevices$=getBluetoothDevices$,exports.default=getBluetoothDevices$},function(module,exports,__webpack_require__){"use strict";function getCloudCallInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getCloudCallInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.getCloudCallInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.getCloudCallInfo$=getCloudCallInfo$,exports.default=getCloudCallInfo$},function(module,exports,__webpack_require__){"use strict";function getCloudCallList$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getCloudCallList$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.getCloudCallList";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.9"},_a)),exports.getCloudCallList$=getCloudCallList$,exports.default=getCloudCallList$},function(module,exports,__webpack_require__){"use strict";function getDeviceId$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getDeviceId$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getDeviceId";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.30"},_a)),exports.getDeviceId$=getDeviceId$,exports.default=getDeviceId$},function(module,exports,__webpack_require__){"use strict";function getDeviceUUID$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getDeviceUUID$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getUUID";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getDeviceUUID$=getDeviceUUID$,exports.default=getDeviceUUID$},function(module,exports,__webpack_require__){"use strict";function getImageInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getImageInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.getImageInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.2"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.2"},_a)),exports.getImageInfo$=getImageInfo$,exports.default=getImageInfo$},function(module,exports,__webpack_require__){"use strict";function getLocatingStatus$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getLocatingStatus$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.geolocation.status";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getLocatingStatus$=getLocatingStatus$,exports.default=getLocatingStatus$},function(module,exports,__webpack_require__){"use strict";function getLocation$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getLocation$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.geolocation.get";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getLocation$=getLocation$,exports.default=getLocation$},function(module,exports,__webpack_require__){"use strict";function getNetworkType$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getNetworkType$=void 0;var apiHelper_1=__webpack_require__(1),ddSdk_1=__webpack_require__(0),actualCallApiName="device.connection.getNetworkType";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",resultDeal:apiHelper_1.getNetWorkTypeResultDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",resultDeal:apiHelper_1.getNetWorkTypeResultDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.5.60",resultDeal:function(result){return"none"!==result.result&&"unknown"!==result.result?{netWorkAvailable:!0}:{netWorkAvailable:!1}}},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",resultDeal:apiHelper_1.getNetWorkTypeResultDeal},_a)),exports.getNetworkType$=getNetworkType$,exports.default=getNetworkType$},function(module,exports,__webpack_require__){"use strict";function getOperateAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getOperateAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="runtime.permission.requestOperateAuthCode",paramsDeal=function(params){return Object.assign(params,{url:location.href.split("#")[0]})};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0",paramsDeal:paramsDeal},_a)),exports.getOperateAuthCode$=getOperateAuthCode$,exports.default=getOperateAuthCode$},function(module,exports,__webpack_require__){"use strict";function getScreenBrightness$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getScreenBrightness$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.screen.getScreenBrightness";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.getScreenBrightness$=getScreenBrightness$,exports.default=getScreenBrightness$},function(module,exports,__webpack_require__){"use strict";function getStorage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{name:params.key})}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getStorage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="util.domainStorage.getItem";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.getStorage$=getStorage$,exports.default=getStorage$},function(module,exports,__webpack_require__){"use strict";function getSystemInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getSystemInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getPhoneInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getSystemInfo$=getSystemInfo$,exports.default=getSystemInfo$},function(module,exports,__webpack_require__){"use strict";function getSystemSettings$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getSystemSettings$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.openSystemSetting";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.36"},_a)),exports.getSystemSettings$=getSystemSettings$,exports.default=getSystemSettings$},function(module,exports,__webpack_require__){"use strict";function getThirdAppConfCustomData$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getThirdAppConfCustomData$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.getThirdAppConfCustomData";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.6.0"},_a)),exports.getThirdAppConfCustomData$=getThirdAppConfCustomData$,exports.default=getThirdAppConfCustomData$},function(module,exports,__webpack_require__){"use strict";function getThirdAppUserCustomData$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getThirdAppUserCustomData$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.getThirdAppUserCustomData";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.6.0"},_a)),exports.getThirdAppUserCustomData$=getThirdAppUserCustomData$,exports.default=getThirdAppUserCustomData$},function(module,exports,__webpack_require__){"use strict";function getTranslateStatus$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getTranslateStatus$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.i18n.getTranslateStatus";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.5.35"},_a)),exports.getTranslateStatus$=getTranslateStatus$,exports.default=getTranslateStatus$},function(module,exports,__webpack_require__){"use strict";function getUserExclusiveInfo$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getUserExclusiveInfo$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.getUserExclusiveInfo";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.17"},_a)),exports.getUserExclusiveInfo$=getUserExclusiveInfo$,exports.default=getUserExclusiveInfo$},function(module,exports,__webpack_require__){"use strict";function getWifiHotspotStatus$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWifiHotspotStatus$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getInterface";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getWifiHotspotStatus$=getWifiHotspotStatus$,exports.default=getWifiHotspotStatus$},function(module,exports,__webpack_require__){"use strict";function getWifiStatus$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWifiStatus$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.base.getWifiStatus";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.getWifiStatus$=getWifiStatus$,exports.default=getWifiStatus$},function(module,exports,__webpack_require__){"use strict";function goBackPage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.goBackPage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.goBack";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.goBackPage$=goBackPage$,exports.default=goBackPage$},function(module,exports,__webpack_require__){"use strict";function hideLoading$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.hideLoading$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.hidePreloader";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.hideLoading$=hideLoading$,exports.default=hideLoading$},function(module,exports,__webpack_require__){"use strict";function hideToast$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.hideToast$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.hideToast";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.hideToast$=hideToast$,exports.default=hideToast$},function(module,exports,__webpack_require__){"use strict";function isInTabWindow$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isInTabWindow$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.tabwindow.isTab";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.5.10"},_a)),exports.isInTabWindow$=isInTabWindow$,exports.default=isInTabWindow$},function(module,exports,__webpack_require__){"use strict";function isLocalFileExist$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isLocalFileExist$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.isLocalFileExist";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.isLocalFileExist$=isLocalFileExist$,exports.default=isLocalFileExist$},function(module,exports,__webpack_require__){"use strict";function isScreenReaderEnabled$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isScreenReaderEnabled$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.screen.isScreenReaderEnabled";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.60"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.60"},_a)),exports.isScreenReaderEnabled$=isScreenReaderEnabled$,exports.default=isScreenReaderEnabled$},function(module,exports,__webpack_require__){"use strict";function locateInMap$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.locateInMap$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.map.locate";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.locateInMap$=locateInMap$,exports.default=locateInMap$},function(module,exports,__webpack_require__){"use strict";function makeCloudCall$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeCloudCall$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.createCloudCall";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.9"},_a)),exports.makeCloudCall$=makeCloudCall$,exports.default=makeCloudCall$},function(module,exports,__webpack_require__){"use strict";function makeVideoConfCall$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeVideoConfCall$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.conference.videoConfCall";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.makeVideoConfCall$=makeVideoConfCall$,exports.default=makeVideoConfCall$},function(module,exports,__webpack_require__){"use strict";function multiSelect$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.multiSelect$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.multiSelect";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.multiSelect$=multiSelect$,exports.default=multiSelect$},function(module,exports,__webpack_require__){"use strict";function navigateBackPage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.navigateBackPage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.navigateBackPage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.45"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.45"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.navigateBackPage$=navigateBackPage$,exports.default=navigateBackPage$},function(module,exports,__webpack_require__){"use strict";function navigateToPage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.navigateToPage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.navigateToPage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.45"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.45"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.0"},_a)),exports.navigateToPage$=navigateToPage$,exports.default=navigateToPage$},function(module,exports,__webpack_require__){"use strict";function nfcReadCardNumber$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.nfcReadCardNumber$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.nfc.nfcReadCardNumber";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.60"},_a)),exports.nfcReadCardNumber$=nfcReadCardNumber$,exports.default=nfcReadCardNumber$},function(module,exports,__webpack_require__){"use strict";function notifyBLECharacteristicValueChange$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.notifyBLECharacteristicValueChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="notifyBLECharacteristicValueChange";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.notifyBLECharacteristicValueChange$=notifyBLECharacteristicValueChange$,exports.default=notifyBLECharacteristicValueChange$},function(module,exports,__webpack_require__){"use strict";function notifyTranslateEvent$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.notifyTranslateEvent$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.i18n.notifyTranslateEvent";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.5.35"},_a)),exports.notifyTranslateEvent$=notifyTranslateEvent$,exports.default=notifyTranslateEvent$},function(module,exports,__webpack_require__){"use strict";function offBLECharacteristicValueChange$(params){ddSdk_1.ddSdk.getExportSdk().off("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.offBLECharacteristicValueChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="BLECharacteristicValueChange";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.offBLECharacteristicValueChange$=offBLECharacteristicValueChange$,exports.default=offBLECharacteristicValueChange$},function(module,exports,__webpack_require__){"use strict";function offBLEConnectionStateChanged$(params){ddSdk_1.ddSdk.getExportSdk().off("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.offBLEConnectionStateChanged$=void 0;var ddSdk_1=__webpack_require__(0),apiName="BLEConnectionStateChanged";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.offBLEConnectionStateChanged$=offBLEConnectionStateChanged$,exports.default=offBLEConnectionStateChanged$},function(module,exports,__webpack_require__){"use strict";function offBluetoothAdapterStateChange$(params){ddSdk_1.ddSdk.getExportSdk().off("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.offBluetoothAdapterStateChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="bluetoothAdapterStateChange";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.offBluetoothAdapterStateChange$=offBluetoothAdapterStateChange$,exports.default=offBluetoothAdapterStateChange$},function(module,exports,__webpack_require__){"use strict";function offBluetoothDeviceFound$(params){ddSdk_1.ddSdk.getExportSdk().off("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.offBluetoothDeviceFound$=void 0;var ddSdk_1=__webpack_require__(0),apiName="bluetoothDeviceFound";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.offBluetoothDeviceFound$=offBluetoothDeviceFound$,exports.default=offBluetoothDeviceFound$},function(module,exports,__webpack_require__){"use strict";function onBLECharacteristicValueChange$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBLECharacteristicValueChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="BLECharacteristicValueChange";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.onBLECharacteristicValueChange$=onBLECharacteristicValueChange$,exports.default=onBLECharacteristicValueChange$},function(module,exports,__webpack_require__){"use strict";function onBLEConnectionStateChanged$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBLEConnectionStateChanged$=void 0;var ddSdk_1=__webpack_require__(0),apiName="BLEConnectionStateChanged";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.onBLEConnectionStateChanged$=onBLEConnectionStateChanged$,exports.default=onBLEConnectionStateChanged$},function(module,exports,__webpack_require__){"use strict";function onBLEPeripheralCharacteristicReadRequest$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBLEPeripheralCharacteristicReadRequest$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.onBLEPeripheralCharacteristicReadRequest";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.6.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.6.10"},_a)),exports.onBLEPeripheralCharacteristicReadRequest$=onBLEPeripheralCharacteristicReadRequest$,exports.default=onBLEPeripheralCharacteristicReadRequest$},function(module,exports,__webpack_require__){"use strict";function onBLEPeripheralCharacteristicWriteRequest$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBLEPeripheralCharacteristicWriteRequest$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.onBLEPeripheralCharacteristicWriteRequest";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.6.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.6.10"},_a)),exports.onBLEPeripheralCharacteristicWriteRequest$=onBLEPeripheralCharacteristicWriteRequest$,exports.default=onBLEPeripheralCharacteristicWriteRequest$},function(module,exports,__webpack_require__){"use strict";function onBLEPeripheralConnectionStateChanged$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBLEPeripheralConnectionStateChanged$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.onBLEPeripheralConnectionStateChanged";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.6.10"},_a)),exports.onBLEPeripheralConnectionStateChanged$=onBLEPeripheralConnectionStateChanged$,exports.default=onBLEPeripheralConnectionStateChanged$},function(module,exports,__webpack_require__){"use strict";function onBeaconServiceChange$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,function(res){"function"==typeof params.success?params.success(res):"function"==typeof params.onSuccess&&params.onSuccess(res)})}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBeaconServiceChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="beaconServiceChange";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.onBeaconServiceChange$=onBeaconServiceChange$,exports.default=onBeaconServiceChange$},function(module,exports,__webpack_require__){"use strict";function onBeaconUpdate$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,function(res){"function"==typeof params.success?params.success(res):"function"==typeof params.onSuccess&&params.onSuccess(res)})}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBeaconUpdate$=void 0;var ddSdk_1=__webpack_require__(0),apiName="beaconUpdate";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.onBeaconUpdate$=onBeaconUpdate$,exports.default=onBeaconUpdate$},function(module,exports,__webpack_require__){"use strict";function onBluetoothAdapterStateChange$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBluetoothAdapterStateChange$=void 0;var ddSdk_1=__webpack_require__(0),apiName="bluetoothAdapterStateChange";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.onBluetoothAdapterStateChange$=onBluetoothAdapterStateChange$,exports.default=onBluetoothAdapterStateChange$},function(module,exports,__webpack_require__){"use strict";function onBluetoothDeviceFound$(params){ddSdk_1.ddSdk.getExportSdk().on("bizEvent."+apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onBluetoothDeviceFound$=void 0;var ddSdk_1=__webpack_require__(0),apiName="bluetoothDeviceFound";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.10"},_a)),exports.onBluetoothDeviceFound$=onBluetoothDeviceFound$,exports.default=onBluetoothDeviceFound$},function(module,exports,__webpack_require__){"use strict";function onPlayAudioEnd$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onPlayAudioEnd$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.onPlayEnd";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.onPlayAudioEnd$=onPlayAudioEnd$,exports.default=onPlayAudioEnd$},function(module,exports,__webpack_require__){"use strict";function onRecordEnd$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onRecordEnd$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.onRecordEnd";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.onRecordEnd$=onRecordEnd$,exports.default=onRecordEnd$},function(module,exports,__webpack_require__){"use strict";function openBluetoothAdapter$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.openBluetoothAdapter$=void 0;var ddSdk_1=__webpack_require__(0),apiName="openBluetoothAdapter";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.openBluetoothAdapter$=openBluetoothAdapter$,exports.default=openBluetoothAdapter$},function(module,exports,__webpack_require__){"use strict";function openChatByChatId$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openChatByChatId$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.chat.toConversation";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.openChatByChatId$=openChatByChatId$,exports.default=openChatByChatId$},function(module,exports,__webpack_require__){"use strict";function openChatByConversationId$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openChatByConversationId$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.chat.toConversationByOpenConversationId";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openChatByConversationId$=openChatByConversationId$,exports.default=openChatByConversationId$},function(module,exports,__webpack_require__){"use strict";function openChatByUserId$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openChatByUserId$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.chat.openSingleChat";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.openChatByUserId$=openChatByUserId$,exports.default=openChatByUserId$},function(module,exports,__webpack_require__){"use strict";function openDocument$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openDocument$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.openDocument";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.openDocument$=openDocument$,exports.default=openDocument$},function(module,exports,__webpack_require__){"use strict";function openLink$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openLink$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.openLink";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openLink$=openLink$,exports.default=openLink$},function(module,exports,__webpack_require__){"use strict";function openLocalFile$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openLocalFile$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.openLocalFile";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openLocalFile$=openLocalFile$,exports.default=openLocalFile$},function(module,exports,__webpack_require__){"use strict";function openLocation$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openLocation$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.map.view";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.openLocation$=openLocation$,exports.default=openLocation$},function(module,exports,__webpack_require__){"use strict";function openMicroApp$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openMicroApp$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.microApp.openApp";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.openMicroApp$=openMicroApp$,exports.default=openMicroApp$},function(module,exports,__webpack_require__){"use strict";function openPageInMicroApp$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openPageInMicroApp$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.open";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openPageInMicroApp$=openPageInMicroApp$,exports.default=openPageInMicroApp$},function(module,exports,__webpack_require__){"use strict";function openPageInModalForPC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openPageInModalForPC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.openModal";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openPageInModalForPC$=openPageInModalForPC$,exports.default=openPageInModalForPC$},function(module,exports,__webpack_require__){"use strict";function openPageInSlidePanelForPC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openPageInSlidePanelForPC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.openSlidePanel";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.openPageInSlidePanelForPC$=openPageInSlidePanelForPC$,exports.default=openPageInSlidePanelForPC$},function(module,exports,__webpack_require__){"use strict";function openPageInWorkBenchForPC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openPageInWorkBenchForPC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.invokeWorkbench";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.8"},_a)),exports.openPageInWorkBenchForPC$=openPageInWorkBenchForPC$,exports.default=openPageInWorkBenchForPC$},function(module,exports,__webpack_require__){"use strict";function pauseAduio$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.pauseAduio$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.pause";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.pauseAduio$=pauseAduio$,exports.default=pauseAduio$},function(module,exports,__webpack_require__){"use strict";function playAduio$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.playAduio$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.play";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.playAduio$=playAduio$,exports.default=playAduio$},function(module,exports,__webpack_require__){"use strict";function popGesture$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.popGesture$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.popGesture";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a)),exports.popGesture$=popGesture$,exports.default=popGesture$},function(module,exports,__webpack_require__){"use strict";function previewFileInDingTalk$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewFileInDingTalk$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.cspace.preview";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.previewFileInDingTalk$=previewFileInDingTalk$,exports.default=previewFileInDingTalk$},function(module,exports,__webpack_require__){"use strict";function previewImage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewImage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.previewImage";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0",paramsDeal:function(params){return{urls:params.urls,current:"number"==typeof(null===params||void 0===params?void 0:params.current)?params.urls[params.current]:params.current}}},_a)),exports.previewImage$=previewImage$,exports.default=previewImage$},function(module,exports,__webpack_require__){"use strict";function previewImagesInDingTalkBatch$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewImagesInDingTalkBatch$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.cspace.previewDentryImages";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.30"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.30"},_a)),exports.previewImagesInDingTalkBatch$=previewImagesInDingTalkBatch$,exports.default=previewImagesInDingTalkBatch$},function(module,exports,__webpack_require__){"use strict";function previewMedia$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewMedia$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.previewMedia";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.1.21"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.1.21"},_a)),exports.previewMedia$=previewMedia$,exports.default=previewMedia$},function(module,exports,__webpack_require__){"use strict";function prompt$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{message:params.message,title:params.title,defaultText:params.placeholder,buttonLabels:[params.cancelButtonText,params.okButtonText],success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.prompt$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.prompt",paramsDeal=function(params){var baseParams={title:params.title||"",message:params.message,defaultText:params.placeholder,buttonLabels:[params.cancelButtonText||"取消",params.okButtonText||"确定"]};return __assign(__assign({},baseParams),params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:paramsDeal},_a)),exports.prompt$=prompt$,exports.default=prompt$},function(module,exports,__webpack_require__){"use strict";function quickCallList$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.quickCallList$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.telephone.quickCallList";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.quickCallList$=quickCallList$,exports.default=quickCallList$},function(module,exports,__webpack_require__){"use strict";function quitPage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.quitPage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.quit";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.quitPage$=quitPage$,exports.default=quitPage$},function(module,exports,__webpack_require__){"use strict";function readBLECharacteristicValue$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.readBLECharacteristicValue$=void 0;var ddSdk_1=__webpack_require__(0),apiName="readBLECharacteristicValue";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.readBLECharacteristicValue$=readBLECharacteristicValue$,exports.default=readBLECharacteristicValue$},function(module,exports,__webpack_require__){"use strict";function readNFC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.readNFC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.nfc.nfcRead";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.readNFC$=readNFC$,exports.default=readNFC$},function(module,exports,__webpack_require__){"use strict";function removeStorage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{name:params.key})}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.removeStorage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="util.domainStorage.removeItem";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.removeStorage$=removeStorage$,exports.default=removeStorage$},function(module,exports,__webpack_require__){"use strict";function replacePage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.replacePage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.replace";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.replacePage$=replacePage$,exports.default=replacePage$},function(module,exports,__webpack_require__){"use strict";function requestAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="runtime.permission.requestAuthCodeV2";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.45"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.45"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.50"},_a)),exports.requestAuthCode$=requestAuthCode$,exports.default=requestAuthCode$},function(module,exports,__webpack_require__){"use strict";function requestMoneySubmmitOrder$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestMoneySubmmitOrder$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.requestMoney.startSubmittingOrder";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.1.5"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.1.5"},_a)),exports.requestMoneySubmmitOrder$=requestMoneySubmmitOrder$,exports.default=requestMoneySubmmitOrder$},function(module,exports,__webpack_require__){"use strict";function resetScreenView$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.resetScreenView$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.screen.resetView";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.resetScreenView$=resetScreenView$,exports.default=resetScreenView$},function(module,exports,__webpack_require__){"use strict";function resumeAudio$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.resumeAudio$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.resume";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.resumeAudio$=resumeAudio$,exports.default=resumeAudio$},function(module,exports,__webpack_require__){"use strict";function rotateScreenView$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.rotateScreenView$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.screen.rotateView";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.rotateScreenView$=rotateScreenView$,exports.default=rotateScreenView$},function(module,exports,__webpack_require__){"use strict";function rsa$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.rsa$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.data.rsa";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.rsa$=rsa$,exports.default=rsa$},function(module,exports,__webpack_require__){"use strict";function saveFileToDingTalk$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveFileToDingTalk$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.cspace.saveFile";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.saveFileToDingTalk$=saveFileToDingTalk$,exports.default=saveFileToDingTalk$},function(module,exports,__webpack_require__){"use strict";function saveImageToPhotosAlbum$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveImageToPhotosAlbum$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.saveImageToPhotosAlbum";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.saveImageToPhotosAlbum$=saveImageToPhotosAlbum$,exports.default=saveImageToPhotosAlbum$},function(module,exports,__webpack_require__){"use strict";function saveVideoToPhotosAlbum$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveVideoToPhotosAlbum$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.saveVideoToPhotosAlbum";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.1.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.1.20"},_a)),exports.saveVideoToPhotosAlbum$=saveVideoToPhotosAlbum$,exports.default=saveVideoToPhotosAlbum$},function(module,exports,__webpack_require__){"use strict";function scan$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.scan$=void 0;var apiHelper_1=__webpack_require__(1),ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.scan";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:apiHelper_1.scanParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.scanParamsDeal},_a)),exports.scan$=scan$,exports.default=scan$},function(module,exports,__webpack_require__){"use strict";function scanCard$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.scanCard$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.scanCard";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.scanCard$=scanCard$,exports.default=scanCard$},function(module,exports,__webpack_require__){"use strict";function searchMap$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.searchMap$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.map.search";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.searchMap$=searchMap$,exports.default=searchMap$},function(module,exports,__webpack_require__){"use strict";function setClipboard$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setClipboard$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.clipboardData.setData";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.5.60"},_a)),exports.setClipboard$=setClipboard$,exports.default=setClipboard$},function(module,exports,__webpack_require__){"use strict";function setGestures$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setGestures$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.gestures";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.15"},_a)),exports.setGestures$=setGestures$,exports.default=setGestures$},function(module,exports,__webpack_require__){"use strict";function setKeepScreenOn$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setKeepScreenOn$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.setScreenKeepOn";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.26"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.26"},_a)),exports.setKeepScreenOn$=setKeepScreenOn$,exports.default=setKeepScreenOn$},function(module,exports,__webpack_require__){"use strict";function setNavigationIcon$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setNavigationIcon$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.navigation.setIcon",paramsDeal=apiHelper_1.genDefaultParamsDealFn({watch:!0,showIcon:!0,iconIndex:1});ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.setNavigationIcon$=setNavigationIcon$,exports.default=setNavigationIcon$},function(module,exports,__webpack_require__){"use strict";function setNavigationLeft$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setNavigationLeft$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="biz.navigation.setLeft",paramsDeal=apiHelper_1.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a)),exports.setNavigationLeft$=setNavigationLeft$,exports.default=setNavigationLeft$},function(module,exports,__webpack_require__){"use strict";function setNavigationTitle$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setNavigationTitle$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.navigation.setTitle";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.setNavigationTitle$=setNavigationTitle$,exports.default=setNavigationTitle$},function(module,exports,__webpack_require__){"use strict";function setScreenBrightness$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setScreenBrightness$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.screen.setScreenBrightness";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.setScreenBrightness$=setScreenBrightness$,exports.default=setScreenBrightness$},function(module,exports,__webpack_require__){"use strict";function setStorage$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{name:params.key,value:params.data})}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setStorage$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="util.domainStorage.setItem";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.setStorage$=setStorage$,exports.default=setStorage$},function(module,exports,__webpack_require__){"use strict";function share$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.share$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.share";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.share$=share$,exports.default=share$},function(module,exports,__webpack_require__){"use strict";function showActionSheet$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{title:params.title,cancelButton:params.cancelButtonText,otherButtons:params.items,success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.showActionSheet$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.actionSheet",paramsDeal=function(params){var baseParams={title:params.title,cancelButton:params.cancelButtonText,otherButtons:params.items};return __assign(__assign({},baseParams),params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10",paramsDeal:paramsDeal,resultDeal:function(res){return __assign(__assign({},res),{index:res.buttonIndex})}},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10",paramsDeal:paramsDeal,resultDeal:function(res){return __assign(__assign({},res),{index:res.buttonIndex})}},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10",paramsDeal:paramsDeal,resultDeal:function(res){return __assign(__assign({},res),{index:res.buttonIndex})}},_a)),exports.showActionSheet$=showActionSheet$,exports.default=showActionSheet$},function(module,exports,__webpack_require__){"use strict";function showAuthGuide$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showAuthGuide$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.showAuthGuide";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.showAuthGuide$=showAuthGuide$,exports.default=showAuthGuide$},function(module,exports,__webpack_require__){"use strict";function showCallMenu$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showCallMenu$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.telephone.showCallMenu";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.showCallMenu$=showCallMenu$,exports.default=showCallMenu$},function(module,exports,__webpack_require__){"use strict";function showLoading$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{text:params.content,showIcon:!0,success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.showLoading$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.showPreloader",paramsDeal=function(params){return __assign({text:params.content,showIcon:!0},params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.showLoading$=showLoading$,exports.default=showLoading$},function(module,exports,__webpack_require__){"use strict";function showModal$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showModal$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.extendModal";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.showModal$=showModal$,exports.default=showModal$},function(module,exports,__webpack_require__){"use strict";function showSharePanel$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showSharePanel$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.showSharePanel";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.showSharePanel$=showSharePanel$,exports.default=showSharePanel$},function(module,exports,__webpack_require__){"use strict";function showToast$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,{icon:params.type,duration:params.duration?params.duration/1e3:3,text:params.content,success:params.success,fail:params.fail,complete:params.complete})}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.showToast$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.notification.toast",paramsDeal=function(params){return __assign({icon:params.type,duration:params.duration?params.duration/1e3:3,text:params.content||"toast",delay:0},params)};ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10",paramsDeal:function(params){return params.icon&&!params.type&&("success"===params.icon?params.type="success":"error"===params.icon&&(params.type="error")),params}},_a)),exports.showToast$=showToast$,exports.default=showToast$},function(module,exports,__webpack_require__){"use strict";function singleSelect$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.singleSelect$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.chosen";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.singleSelect$=singleSelect$,exports.default=singleSelect$},function(module,exports,__webpack_require__){"use strict";function startAdvertising$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startAdvertising$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.startAdvertising";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.0"},_a)),exports.startAdvertising$=startAdvertising$,exports.default=startAdvertising$},function(module,exports,__webpack_require__){"use strict";function startBeaconDiscovery$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.startBeaconDiscovery$=void 0;var ddSdk_1=__webpack_require__(0),apiName="startBeaconDiscovery";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.startBeaconDiscovery$=startBeaconDiscovery$,exports.default=startBeaconDiscovery$},function(module,exports,__webpack_require__){"use strict";function startBluetoothDevicesDiscovery$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.startBluetoothDevicesDiscovery$=void 0;var ddSdk_1=__webpack_require__(0),apiName="startBluetoothDevicesDiscovery";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.startBluetoothDevicesDiscovery$=startBluetoothDevicesDiscovery$,exports.default=startBluetoothDevicesDiscovery$},function(module,exports,__webpack_require__){"use strict";function startLocating$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startLocating$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.geolocation.start";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.startLocating$=startLocating$,exports.default=startLocating$},function(module,exports,__webpack_require__){"use strict";function startRecord$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startRecord$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.startRecord";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.30"},_a)),exports.startRecord$=startRecord$,exports.default=startRecord$},function(module,exports,__webpack_require__){"use strict";function stopAdvertising$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopAdvertising$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.stopAdvertising";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.0"},_a)),exports.stopAdvertising$=stopAdvertising$,exports.default=stopAdvertising$},function(module,exports,__webpack_require__){"use strict";function stopAudio$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopAudio$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.stop";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.stopAudio$=stopAudio$,exports.default=stopAudio$},function(module,exports,__webpack_require__){"use strict";function stopBeaconDiscovery$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopBeaconDiscovery$=void 0;var ddSdk_1=__webpack_require__(0),apiName="stopBeaconDiscovery";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.stopBeaconDiscovery$=stopBeaconDiscovery$,exports.default=stopBeaconDiscovery$},function(module,exports,__webpack_require__){"use strict";function stopBluetoothDevicesDiscovery$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopBluetoothDevicesDiscovery$=void 0;var ddSdk_1=__webpack_require__(0),apiName="stopBluetoothDevicesDiscovery";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.stopBluetoothDevicesDiscovery$=stopBluetoothDevicesDiscovery$,exports.default=stopBluetoothDevicesDiscovery$},function(module,exports,__webpack_require__){"use strict";function stopLocating$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopLocating$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.geolocation.stop";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.stopLocating$=stopLocating$,exports.default=stopLocating$},function(module,exports,__webpack_require__){"use strict";function stopPullDownRefresh$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopPullDownRefresh$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="ui.pullToRefresh.stop";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.stopPullDownRefresh$=stopPullDownRefresh$,exports.default=stopPullDownRefresh$},function(module,exports,__webpack_require__){"use strict";function stopRecord$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopRecord$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.stopRecord";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.30"},_a)),exports.stopRecord$=stopRecord$,exports.default=stopRecord$},function(module,exports,__webpack_require__){"use strict";function subscribe$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.subscribe$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.notify.subscribe";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.5.35"},_a)),exports.subscribe$=subscribe$,exports.default=subscribe$},function(module,exports,__webpack_require__){"use strict";function timePicker$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.timePicker$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.timepicker";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.timePicker$=timePicker$,exports.default=timePicker$},function(module,exports,__webpack_require__){"use strict";function translate$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.translate$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.i18n.translate";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.5.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.5.35"},_a)),exports.translate$=translate$,exports.default=translate$},function(module,exports,__webpack_require__){"use strict";function translateVoice$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.translateVoice$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.audio.translateVoice";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.translateVoice$=translateVoice$,exports.default=translateVoice$},function(module,exports,__webpack_require__){"use strict";function uploadAttachmentToDingTalk$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadAttachmentToDingTalk$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.uploadAttachment";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.uploadAttachmentToDingTalk$=uploadAttachmentToDingTalk$,exports.default=uploadAttachmentToDingTalk$},function(module,exports,__webpack_require__){"use strict";function uploadFile$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadFile$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.util.uploadFile";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.uploadFile$=uploadFile$,exports.default=uploadFile$},function(module,exports,__webpack_require__){"use strict";function vibrate$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.vibrate$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="device.notification.vibrate",paramsDeal=apiHelper_1.genDefaultParamsDealFn({duration:300});ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:paramsDeal},_a)),exports.vibrate$=vibrate$,exports.default=vibrate$},function(module,exports,__webpack_require__){"use strict";function watchShake$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.watchShake$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),actualCallApiName="device.accelerometer.watchShake";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:function(params){return apiHelper_1.forceChangeParamsDealFn({sensitivity:3.2})(apiHelper_1.addWatchParamsDeal(params))}},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a)),exports.watchShake$=watchShake$,exports.default=watchShake$},function(module,exports,__webpack_require__){"use strict";function writeBLECharacteristicValue$(params){return ddSdk_1.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",__assign({_action:apiName},params))}var _a,__assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},__assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.writeBLECharacteristicValue$=void 0;var ddSdk_1=__webpack_require__(0),apiName="writeBLECharacteristicValue";ddSdk_1.ddSdk.setAPI("runtime.h5nuvabridge.exec",(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.38"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.38"},_a)),exports.writeBLECharacteristicValue$=writeBLECharacteristicValue$,exports.default=writeBLECharacteristicValue$},function(module,exports,__webpack_require__){"use strict";function writeBLEPeripheralCharacteristicValue$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.writeBLEPeripheralCharacteristicValue$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="biz.realm.writeBLEPeripheralCharacteristicValue";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.6.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.6.10"},_a)),exports.writeBLEPeripheralCharacteristicValue$=writeBLEPeripheralCharacteristicValue$,exports.default=writeBLEPeripheralCharacteristicValue$},function(module,exports,__webpack_require__){"use strict";function writeNFC$(params){return ddSdk_1.ddSdk.invokeAPI(actualCallApiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.writeNFC$=void 0;var ddSdk_1=__webpack_require__(0),actualCallApiName="device.nfc.nfcWrite";ddSdk_1.ddSdk.setAPI(actualCallApiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a)),exports.writeNFC$=writeNFC$,exports.default=writeNFC$},function(module,exports,__webpack_require__){"use strict";var ddSdk_1=__webpack_require__(0),otherApi=__webpack_require__(462),core=Object.assign({},otherApi,ddSdk_1.ddSdk.getExportSdk());module.exports=core},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.FRAMEWORK=exports.PLATFORM=exports.RUNTIME=void 0,exports.RUNTIME={WEB:"Web",WEEX:"Weex",UNKNOWN:"Unknown"},exports.PLATFORM={MAC:"Mac",WINDOWS:"Windows",IOS:"iOS",ANDROID:"Android",IPAD:"iPad",BROWSER:"Browser",UNKNOWN:"Unknown"},exports.FRAMEWORK={VUE:"Vue",RAX:"Rax",UNKNOWN:"Unknown"}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.platformConfig=void 0;var ddSdk_1=__webpack_require__(0),env_1=__webpack_require__(4),sdk_1=__webpack_require__(3),eapp_1=__webpack_require__(7),webviewInMiniApp_1=__webpack_require__(10),h5Android_1=__webpack_require__(457),weex_1=__webpack_require__(11),h5Event_1=__webpack_require__(9),weexEvent_1=__webpack_require__(12),ApiMapping=__webpack_require__(5);exports.platformConfig={platform:env_1.ENV_ENUM.android,bridgeInit:function(){var env=env_1.getENV();return env.appType===sdk_1.APP_TYPE.MINI_APP?Promise.resolve(eapp_1.default):env.appType===sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(webviewInMiniApp_1.default):env.appType===sdk_1.APP_TYPE.WEEX?weex_1.androidWeexBridge():h5Android_1.h5AndroidbridgeInit().then(function(){return h5Android_1.default})},authMethod:"runtime.permission.requestJsApis",authParamsDeal:function(params){var finalParams=Object.assign({},params);return params.jsApiList&&(finalParams.jsApiList=params.jsApiList.map(function(apiName){return ApiMapping[apiName]?ApiMapping[apiName]:apiName})),finalParams},event:{on:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.on(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.on(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}},off:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.off(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.off(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}}}},ddSdk_1.ddSdk.setPlatform(exports.platformConfig)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.platformConfig=void 0;var ddSdk_1=__webpack_require__(0),env_1=__webpack_require__(4),sdk_1=__webpack_require__(3),eapp_1=__webpack_require__(7),webviewInMiniApp_1=__webpack_require__(10),h5Harmony_1=__webpack_require__(458),h5Event_1=__webpack_require__(9),weexEvent_1=__webpack_require__(12),ApiMapping=__webpack_require__(5);exports.platformConfig={platform:env_1.ENV_ENUM.harmony,bridgeInit:function(){var env=env_1.getENV();return env.appType===sdk_1.APP_TYPE.MINI_APP?Promise.resolve(eapp_1.default):env.appType===sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(webviewInMiniApp_1.default):h5Harmony_1.h5HarmonyBridgeInit().then(function(){return h5Harmony_1.default})},authMethod:"runtime.permission.requestJsApis",authParamsDeal:function(params){var finalParams=Object.assign({},params);return params.jsApiList&&(finalParams.jsApiList=params.jsApiList.map(function(apiName){return ApiMapping[apiName]?ApiMapping[apiName]:apiName})),finalParams},event:{on:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.on(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.on(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}},off:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.off(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.off(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}}}},ddSdk_1.ddSdk.setPlatform(exports.platformConfig)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.platformConfig=void 0;var ddSdk_1=__webpack_require__(0),env_1=__webpack_require__(4),sdk_1=__webpack_require__(3),eapp_1=__webpack_require__(7),webviewInMiniApp_1=__webpack_require__(10),h5Ios_1=__webpack_require__(459),weex_1=__webpack_require__(11),h5Event_1=__webpack_require__(9),ApiMapping=__webpack_require__(5),weexEvent_1=__webpack_require__(12);exports.platformConfig={platform:env_1.ENV_ENUM.ios,bridgeInit:function(){var env=env_1.getENV();return env.appType===sdk_1.APP_TYPE.MINI_APP?Promise.resolve(eapp_1.default):env.appType===sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(webviewInMiniApp_1.default):env.appType===sdk_1.APP_TYPE.WEEX?weex_1.iosWeexBridge():h5Ios_1.h5IosBridgeInit().then(function(){return h5Ios_1.default})},authMethod:"runtime.permission.requestJsApis",authParamsDeal:function(params){var finalParams=Object.assign({},params);return params.jsApiList&&(finalParams.jsApiList=params.jsApiList.map(function(apiName){return ApiMapping[apiName]?ApiMapping[apiName]:apiName})),finalParams},event:{on:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.on(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.on(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}},off:function(type,listener){var env=env_1.getENV();switch(env.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:h5Event_1.off(type,listener);break;case sdk_1.APP_TYPE.WEEX:weexEvent_1.off(type,listener);break;default:throw new Error("Not support global event in the platfrom: "+env.appType)}}}},ddSdk_1.ddSdk.setPlatform(exports.platformConfig)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isMobile=void 0;var env_1=__webpack_require__(4),dingtalkEnv=env_1.getENV();exports.isMobile=function(){return dingtalkEnv.platform===env_1.ENV_ENUM.ios}()||function(){return dingtalkEnv.platform===env_1.ENV_ENUM.android}()},function(module,exports){function defaultSetTimout(){throw new Error("setTimeout has not been defined")}function defaultClearTimeout(){throw new Error("clearTimeout has not been defined")}function runTimeout(fun){if(cachedSetTimeout===setTimeout)return setTimeout(fun,0);if((cachedSetTimeout===defaultSetTimout||!cachedSetTimeout)&&setTimeout)return cachedSetTimeout=setTimeout,setTimeout(fun,0);try{return cachedSetTimeout(fun,0)}catch(e){try{return cachedSetTimeout.call(null,fun,0)}catch(e){return cachedSetTimeout.call(this,fun,0)}}}function runClearTimeout(marker){if(cachedClearTimeout===clearTimeout)return clearTimeout(marker);if((cachedClearTimeout===defaultClearTimeout||!cachedClearTimeout)&&clearTimeout)return cachedClearTimeout=clearTimeout,clearTimeout(marker);try{return cachedClearTimeout(marker)}catch(e){try{return cachedClearTimeout.call(null,marker)}catch(e){return cachedClearTimeout.call(this,marker)}}}function cleanUpNextTick(){draining&&currentQueue&&(draining=!1,currentQueue.length?queue=currentQueue.concat(queue):queueIndex=-1,queue.length&&drainQueue())}function drainQueue(){if(!draining){var timeout=runTimeout(cleanUpNextTick);draining=!0;for(var len=queue.length;len;){for(currentQueue=queue,queue=[];++queueIndex<len;)currentQueue&&currentQueue[queueIndex].run();queueIndex=-1,len=queue.length}currentQueue=null,draining=!1,runClearTimeout(timeout)}}function Item(fun,array){this.fun=fun,this.array=array}function noop(){}var cachedSetTimeout,cachedClearTimeout,process=module.exports={};(function(){try{cachedSetTimeout="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){cachedSetTimeout=defaultSetTimout}try{cachedClearTimeout="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){cachedClearTimeout=defaultClearTimeout}})();var currentQueue,queue=[],draining=!1,queueIndex=-1;process.nextTick=function(fun){var args=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)args[i-1]=arguments[i];queue.push(new Item(fun,args)),1!==queue.length||draining||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},process.title="browser",process.browser=!0,process.env={},process.argv=[],process.version="",process.versions={},process.on=noop,process.addListener=noop,process.once=noop,process.off=noop,process.removeListener=noop,process.removeAllListeners=noop,process.emit=noop,process.prependListener=noop,process.prependOnceListener=noop,process.listeners=function(name){return[]},process.binding=function(name){throw new Error("process.binding is not supported")},process.cwd=function(){return"/"},process.chdir=function(dir){throw new Error("process.chdir is not supported")},process.umask=function(){return 0}},function(module,exports,__webpack_require__){"use strict";function beaconPicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.beaconPicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.beaconPicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.0.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.0.7"},_a)),exports.beaconPicker$=beaconPicker$,exports.default=beaconPicker$},function(module,exports,__webpack_require__){"use strict";function detectFace$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.detectFace$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.detectFace";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.18"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.18"},_a)),exports.detectFace$=detectFace$,exports.default=detectFace$},function(module,exports,__webpack_require__){"use strict";function detectFaceFullScreen$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.detectFaceFullScreen$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.detectFaceFullScreen";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.18"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.18"},_a)),exports.detectFaceFullScreen$=detectFaceFullScreen$,exports.default=detectFaceFullScreen$},function(module,exports,__webpack_require__){"use strict";function exclusiveLiveCheck$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.exclusiveLiveCheck$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.exclusiveLiveCheck";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.40"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.40"},_a)),exports.exclusiveLiveCheck$=exclusiveLiveCheck$,exports.default=exclusiveLiveCheck$},function(module,exports,__webpack_require__){"use strict";function faceManager$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.faceManager$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.faceManager";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.0.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.0.7"},_a)),exports.faceManager$=faceManager$,exports.default=faceManager$},function(module,exports,__webpack_require__){"use strict";function punchModePicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.punchModePicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ATMBle.punchModePicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.0.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.0.7"},_a)),exports.punchModePicker$=punchModePicker$,exports.default=punchModePicker$},function(module,exports,__webpack_require__){"use strict";function bindAlipay$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.bindAlipay$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.alipay.bindAlipay";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.15"},_a)),exports.bindAlipay$=bindAlipay$,exports.default=bindAlipay$},function(module,exports,__webpack_require__){"use strict";function openAuth$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openAuth$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.alipay.openAuth";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.8"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.8"},_a)),exports.openAuth$=openAuth$,exports.default=openAuth$},function(module,exports,__webpack_require__){"use strict";function pay$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.pay$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.alipay.pay";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.pay$=pay$,exports.default=pay$},function(module,exports,__webpack_require__){"use strict";function getLBSWua$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getLBSWua$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.attend.getLBSWua";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.35"},_a)),exports.getLBSWua$=getLBSWua$,exports.default=getLBSWua$},function(module,exports,__webpack_require__){"use strict";function openAccountPwdLoginPage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openAccountPwdLoginPage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.auth.openAccountPwdLoginPage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.0"},_a)),exports.openAccountPwdLoginPage$=openAccountPwdLoginPage$,exports.default=openAccountPwdLoginPage$},function(module,exports,__webpack_require__){"use strict";function requestAuthInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestAuthInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.auth.requestAuthInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.19"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.19"},_a)),exports.requestAuthInfo$=requestAuthInfo$,exports.default=requestAuthInfo$},function(module,exports,__webpack_require__){"use strict";function chooseDateTime$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseDateTime$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.calendar.chooseDateTime";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseDateTime$=chooseDateTime$,exports.default=chooseDateTime$},function(module,exports,__webpack_require__){"use strict";function chooseHalfDay$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseHalfDay$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.calendar.chooseHalfDay";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseHalfDay$=chooseHalfDay$,exports.default=chooseHalfDay$},function(module,exports,__webpack_require__){"use strict";function chooseInterval$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseInterval$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.calendar.chooseInterval";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseInterval$=chooseInterval$,exports.default=chooseInterval$},function(module,exports,__webpack_require__){"use strict";function chooseOneDay$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseOneDay$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.calendar.chooseOneDay";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:apiHelper_1.addDefaultCorpIdParamsDeal},_a)),exports.chooseOneDay$=chooseOneDay$,exports.default=chooseOneDay$},function(module,exports,__webpack_require__){"use strict";function chooseConversationByCorpId$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseConversationByCorpId$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.chat.chooseConversationByCorpId",paramsDeal=apiHelper_1.genDefaultParamsDealFn({max:50});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.7.11",paramsDeal:paramsDeal},_a)),exports.chooseConversationByCorpId$=chooseConversationByCorpId$,exports.default=chooseConversationByCorpId$},function(module,exports,__webpack_require__){"use strict";function collectSticker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.collectSticker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.collectSticker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.25"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.25"},_a)),exports.collectSticker$=collectSticker$,exports.default=collectSticker$},function(module,exports,__webpack_require__){"use strict";function createSceneGroup$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createSceneGroup$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.createSceneGroup";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.7.17"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.7.17"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.7.17"},_a)),exports.createSceneGroup$=createSceneGroup$,exports.default=createSceneGroup$},function(module,exports,__webpack_require__){"use strict";function getRealmCid$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getRealmCid$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.getRealmCid";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.7.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.7.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.7.12"},_a)),exports.getRealmCid$=getRealmCid$,exports.default=getRealmCid$},function(module,exports,__webpack_require__){"use strict";function locationChatMessage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.locationChatMessage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.locationChatMessage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.6"},_a)),exports.locationChatMessage$=locationChatMessage$,exports.default=locationChatMessage$},function(module,exports,__webpack_require__){"use strict";function openSingleChat$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openSingleChat$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.openSingleChat";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.4.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.4.10"},_a)),exports.openSingleChat$=openSingleChat$,exports.default=openSingleChat$},function(module,exports,__webpack_require__){"use strict";function pickConversation$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.pickConversation$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.pickConversation";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.2"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.2"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.7.9"},_a)),exports.pickConversation$=pickConversation$,exports.default=pickConversation$},function(module,exports,__webpack_require__){"use strict";function sendEmotion$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.sendEmotion$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.sendEmotion";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.12"},_a)),exports.sendEmotion$=sendEmotion$,exports.default=sendEmotion$},function(module,exports,__webpack_require__){"use strict";function toConversation$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.toConversation$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.toConversation";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.toConversation$=toConversation$,exports.default=toConversation$},function(module,exports,__webpack_require__){"use strict";function toConversationByOpenConversationId$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.toConversationByOpenConversationId$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.chat.toConversationByOpenConversationId";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.30"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.30"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.33"},_a)),exports.toConversationByOpenConversationId$=toConversationByOpenConversationId$,exports.default=toConversationByOpenConversationId$},function(module,exports,__webpack_require__){"use strict";function setData$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setData$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.clipboardData.setData";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.1"},_a)),exports.setData$=setData$,exports.default=setData$},function(module,exports,__webpack_require__){"use strict";function createCloudCall$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createCloudCall$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.conference.createCloudCall";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.9"},_a)),exports.createCloudCall$=createCloudCall$,exports.default=createCloudCall$},function(module,exports,__webpack_require__){"use strict";function getCloudCallInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getCloudCallInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.conference.getCloudCallInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.9"},_a)),exports.getCloudCallInfo$=getCloudCallInfo$,exports.default=getCloudCallInfo$},function(module,exports,__webpack_require__){"use strict";function getCloudCallList$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getCloudCallList$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.conference.getCloudCallList";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.9"},_a)),exports.getCloudCallList$=getCloudCallList$,exports.default=getCloudCallList$},function(module,exports,__webpack_require__){"use strict";function videoConfCall$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.videoConfCall$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.conference.videoConfCall";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.0.8"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.0.8"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.28"},_a)),exports.videoConfCall$=videoConfCall$,exports.default=videoConfCall$},function(module,exports,__webpack_require__){"use strict";function choose$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.choose$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.contact.choose",paramsDeal=apiHelper_1.genDefaultParamsDealFn({multiple:!0,startWithDepartmentId:0,users:[]});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.choose$=choose$,exports.default=choose$},function(module,exports,__webpack_require__){"use strict";function chooseMobileContacts$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseMobileContacts$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.chooseMobileContacts";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.1"},_a)),exports.chooseMobileContacts$=chooseMobileContacts$,exports.default=chooseMobileContacts$},function(module,exports,__webpack_require__){"use strict";function complexPicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.complexPicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.complexPicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.3.5"},_a)),exports.complexPicker$=complexPicker$,exports.default=complexPicker$},function(module,exports,__webpack_require__){"use strict";function createGroup$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createGroup$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.createGroup";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.1"},_a)),exports.createGroup$=createGroup$,exports.default=createGroup$},function(module,exports,__webpack_require__){"use strict";function departmentsPicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.departmentsPicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.departmentsPicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.2.5"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.0"},_a)),exports.departmentsPicker$=departmentsPicker$,exports.default=departmentsPicker$},function(module,exports,__webpack_require__){"use strict";function externalComplexPicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.externalComplexPicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.externalComplexPicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.0"},_a)),exports.externalComplexPicker$=externalComplexPicker$,exports.default=externalComplexPicker$},function(module,exports,__webpack_require__){"use strict";function externalEditForm$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.externalEditForm$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.externalEditForm";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.0"},_a)),exports.externalEditForm$=externalEditForm$,exports.default=externalEditForm$},function(module,exports,__webpack_require__){"use strict";function rolesPicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.rolesPicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.rolesPicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.16"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.16"},_a)),exports.rolesPicker$=rolesPicker$,exports.default=rolesPicker$},function(module,exports,__webpack_require__){"use strict";function setRule$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setRule$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.contact.setRule";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.15"},_a)),exports.setRule$=setRule$,exports.default=setRule$},function(module,exports,__webpack_require__){"use strict";function chooseSpaceDir$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseSpaceDir$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.cspace.chooseSpaceDir";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.27"},_a)),exports.chooseSpaceDir$=chooseSpaceDir$,exports.default=chooseSpaceDir$},function(module,exports,__webpack_require__){"use strict";function delete$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.delete$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.cspace.delete";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.21"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.21"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.21"},_a)),exports.delete$=delete$,exports.default=delete$},function(module,exports,__webpack_require__){"use strict";function preview$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.preview$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.cspace.preview";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.0"},_a)),exports.preview$=preview$,exports.default=preview$},function(module,exports,__webpack_require__){"use strict";function previewDentryImages$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewDentryImages$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.cspace.previewDentryImages";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.30"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.30"},_a)),exports.previewDentryImages$=previewDentryImages$,exports.default=previewDentryImages$},function(module,exports,__webpack_require__){"use strict";function saveFile$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveFile$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.cspace.saveFile";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.6"},_a)),exports.saveFile$=saveFile$,exports.default=saveFile$},function(module,exports,__webpack_require__){"use strict";function choose$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.choose$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.customContact.choose",paramsDeal=apiHelper_1.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.5.2",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.5.2",paramsDeal:paramsDeal},_a)),exports.choose$=choose$,exports.default=choose$},function(module,exports,__webpack_require__){"use strict";function multipleChoose$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.multipleChoose$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.customContact.multipleChoose",paramsDeal=apiHelper_1.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.multipleChoose$=multipleChoose$,exports.default=multipleChoose$},function(module,exports,__webpack_require__){"use strict";function rsa$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.rsa$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.data.rsa";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.rsa$=rsa$,exports.default=rsa$},function(module,exports,__webpack_require__){"use strict";function create$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.create$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ding.create";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.1",resultDeal:function(result){return""===result?result={dingCreateResult:!1}:"object"==typeof result&&(result.dingCreateResult=!!result.dingCreateResult),result}},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.1"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.9"},_a)),exports.create$=create$,exports.default=create$},function(module,exports,__webpack_require__){"use strict";function post$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.post$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.ding.post";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.post$=post$,exports.default=post$},function(module,exports,__webpack_require__){"use strict";function finishMiniCourseByRecordId$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.finishMiniCourseByRecordId$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.finishMiniCourseByRecordId";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.15"},_a)),exports.finishMiniCourseByRecordId$=finishMiniCourseByRecordId$,exports.default=finishMiniCourseByRecordId$},function(module,exports,__webpack_require__){"use strict";function getMiniCourseDraftList$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getMiniCourseDraftList$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.getMiniCourseDraftList";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.15"},_a)),exports.getMiniCourseDraftList$=getMiniCourseDraftList$,exports.default=getMiniCourseDraftList$},function(module,exports,__webpack_require__){"use strict";function joinClassroom$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.joinClassroom$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.joinClassroom";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.15"},_a)),exports.joinClassroom$=joinClassroom$,exports.default=joinClassroom$},function(module,exports,__webpack_require__){"use strict";function makeMiniCourse$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeMiniCourse$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.makeMiniCourse";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.15"},_a)),exports.makeMiniCourse$=makeMiniCourse$,exports.default=makeMiniCourse$},function(module,exports,__webpack_require__){"use strict";function newMsgNotificationStatus$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.newMsgNotificationStatus$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.newMsgNotificationStatus";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.20"},_a)),exports.newMsgNotificationStatus$=newMsgNotificationStatus$,exports.default=newMsgNotificationStatus$},function(module,exports,__webpack_require__){"use strict";function startAuth$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startAuth$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.startAuth";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.20"},_a)),exports.startAuth$=startAuth$,exports.default=startAuth$},function(module,exports,__webpack_require__){"use strict";function tokenFaceImg$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.tokenFaceImg$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.edu.tokenFaceImg";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.20"},_a)),exports.tokenFaceImg$=tokenFaceImg$,exports.default=tokenFaceImg$},function(module,exports,__webpack_require__){"use strict";function notifyWeex$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.notifyWeex$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.event.notifyWeex";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.0"},_a)),exports.notifyWeex$=notifyWeex$,exports.default=notifyWeex$},function(module,exports,__webpack_require__){"use strict";function downloadFile$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.downloadFile$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.file.downloadFile";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.15"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.15"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.15"},_a)),exports.downloadFile$=downloadFile$,exports.default=downloadFile$},function(module,exports,__webpack_require__){"use strict";function fetchData$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.fetchData$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.intent.fetchData";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.6"},_a)),exports.fetchData$=fetchData$,exports.default=fetchData$},function(module,exports,__webpack_require__){"use strict";function bind$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.bind$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.bind";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.34"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.34"},_a)),exports.bind$=bind$,exports.default=bind$},function(module,exports,__webpack_require__){"use strict";function bindMeetingRoom$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.bindMeetingRoom$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.bindMeetingRoom";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.34"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.34"},_a)),exports.bindMeetingRoom$=bindMeetingRoom$,exports.default=bindMeetingRoom$},function(module,exports,__webpack_require__){"use strict";function getDeviceProperties$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getDeviceProperties$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.getDeviceProperties";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.42"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.42"},_a)),exports.getDeviceProperties$=getDeviceProperties$,exports.default=getDeviceProperties$},function(module,exports,__webpack_require__){"use strict";function invokeThingService$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.invokeThingService$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.invokeThingService";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.42"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.42"},_a)),exports.invokeThingService$=invokeThingService$,exports.default=invokeThingService$},function(module,exports,__webpack_require__){"use strict";function queryMeetingRoomList$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.queryMeetingRoomList$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.queryMeetingRoomList";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.34"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.34"},_a)),exports.queryMeetingRoomList$=queryMeetingRoomList$,exports.default=queryMeetingRoomList$},function(module,exports,__webpack_require__){"use strict";function setDeviceProperties$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setDeviceProperties$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.setDeviceProperties";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.42"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.42"},_a)),exports.setDeviceProperties$=setDeviceProperties$,exports.default=setDeviceProperties$},function(module,exports,__webpack_require__){"use strict";function unbind$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.unbind$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.iot.unbind";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.34"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.34"},_a)),exports.unbind$=unbind$,exports.default=unbind$},function(module,exports,__webpack_require__){"use strict";function startClassRoom$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startClassRoom$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.live.startClassRoom";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.19"},_a)),exports.startClassRoom$=startClassRoom$,exports.default=startClassRoom$},function(module,exports,__webpack_require__){"use strict";function startUnifiedLive$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startUnifiedLive$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.live.startUnifiedLive";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.18"},_a)),exports.startUnifiedLive$=startUnifiedLive$,exports.default=startUnifiedLive$},function(module,exports,__webpack_require__){"use strict";function locate$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.locate$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.map.locate";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.locate$=locate$,exports.default=locate$},function(module,exports,__webpack_require__){"use strict";function search$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.search$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.map.search",paramsDeal=apiHelper_1.genDefaultParamsDealFn({scope:500});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.search$=search$,exports.default=search$},function(module,exports,__webpack_require__){"use strict";function view$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.view$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.map.view";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.view$=view$,exports.default=view$},function(module,exports,__webpack_require__){"use strict";function compressVideo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.compressVideo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.media.compressVideo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.37"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.37"},_a)),exports.compressVideo$=compressVideo$,exports.default=compressVideo$},function(module,exports,__webpack_require__){"use strict";function openApp$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openApp$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.microApp.openApp";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.6"},_a)),exports.openApp$=openApp$,exports.default=openApp$},function(module,exports,__webpack_require__){"use strict";function close$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.close$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.close";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.3.5"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.close$=close$,exports.default=close$},function(module,exports,__webpack_require__){"use strict";function goBack$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.goBack$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.goBack";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.goBack$=goBack$,exports.default=goBack$},function(module,exports,__webpack_require__){"use strict";function hideBar$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.hideBar$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.hideBar";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.6"},_a)),exports.hideBar$=hideBar$,exports.default=hideBar$},function(module,exports,__webpack_require__){"use strict";function navigateBackPage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.navigateBackPage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.navigateBackPage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.31"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.31"},_a)),exports.navigateBackPage$=navigateBackPage$,exports.default=navigateBackPage$},function(module,exports,__webpack_require__){"use strict";function navigateToMiniProgram$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.navigateToMiniProgram$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.navigateToMiniProgram";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.31"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.31"},_a)),exports.navigateToMiniProgram$=navigateToMiniProgram$,exports.default=navigateToMiniProgram$},function(module,exports,__webpack_require__){"use strict";function navigateToPage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.navigateToPage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.navigateToPage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.31"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.31"},_a)),exports.navigateToPage$=navigateToPage$,exports.default=navigateToPage$},function(module,exports,__webpack_require__){"use strict";function quit$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.quit$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.quit";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.quit$=quit$,exports.default=quit$},function(module,exports,__webpack_require__){"use strict";function replace$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.replace$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.replace";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.4.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.4.6"},_a)),exports.replace$=replace$,exports.default=replace$},function(module,exports,__webpack_require__){"use strict";function setIcon$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setIcon$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.navigation.setIcon",paramsDeal=apiHelper_1.genDefaultParamsDealFn({watch:!0,showIcon:!0,iconIndex:1});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.setIcon$=setIcon$,exports.default=setIcon$},function(module,exports,__webpack_require__){"use strict";function setLeft$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setLeft$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.navigation.setLeft",paramsDeal=apiHelper_1.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.setLeft$=setLeft$,exports.default=setLeft$},function(module,exports,__webpack_require__){"use strict";function setMenu$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setMenu$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.navigation.setMenu";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a)),exports.setMenu$=setMenu$,exports.default=setMenu$},function(module,exports,__webpack_require__){"use strict";function setRight$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setRight$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.navigation.setRight",paramsDeal=apiHelper_1.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.setRight$=setRight$,exports.default=setRight$},function(module,exports,__webpack_require__){"use strict";function setTitle$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setTitle$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.navigation.setTitle";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.setTitle$=setTitle$,exports.default=setTitle$},function(module,exports,__webpack_require__){"use strict";function componentPunchFromPartner$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.componentPunchFromPartner$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.pbp.componentPunchFromPartner";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.10"},_a)),exports.componentPunchFromPartner$=componentPunchFromPartner$,exports.default=componentPunchFromPartner$},function(module,exports,__webpack_require__){"use strict";function startMatchRuleFromPartner$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startMatchRuleFromPartner$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.pbp.startMatchRuleFromPartner";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.10"},_a)),exports.startMatchRuleFromPartner$=startMatchRuleFromPartner$,exports.default=startMatchRuleFromPartner$},function(module,exports,__webpack_require__){"use strict";function stopMatchRuleFromPartner$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopMatchRuleFromPartner$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.pbp.stopMatchRuleFromPartner";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.10"},_a)),exports.stopMatchRuleFromPartner$=stopMatchRuleFromPartner$,exports.default=stopMatchRuleFromPartner$},function(module,exports,__webpack_require__){"use strict";function add$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.add$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.phoneContact.add";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.add$=add$,exports.default=add$},function(module,exports,__webpack_require__){"use strict";function getRealtimeTracingStatus$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getRealtimeTracingStatus$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.getRealtimeTracingStatus";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.13"},_a)),exports.getRealtimeTracingStatus$=getRealtimeTracingStatus$,exports.default=getRealtimeTracingStatus$},function(module,exports,__webpack_require__){"use strict";function getUserExclusiveInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getUserExclusiveInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.getUserExclusiveInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.14"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.14"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.17"},_a)),exports.getUserExclusiveInfo$=getUserExclusiveInfo$,exports.default=getUserExclusiveInfo$},function(module,exports,__webpack_require__){"use strict";function startRealtimeTracing$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startRealtimeTracing$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.startRealtimeTracing";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.13"},_a)),exports.startRealtimeTracing$=startRealtimeTracing$,exports.default=startRealtimeTracing$},function(module,exports,__webpack_require__){"use strict";function stopRealtimeTracing$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopRealtimeTracing$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.stopRealtimeTracing";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.13"},_a)),exports.stopRealtimeTracing$=stopRealtimeTracing$,exports.default=stopRealtimeTracing$},function(module,exports,__webpack_require__){"use strict";function subscribe$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.subscribe$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.subscribe";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.7.18"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.7.18"},_a)),exports.subscribe$=subscribe$,exports.default=subscribe$},function(module,exports,__webpack_require__){"use strict";function unsubscribe$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.unsubscribe$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.realm.unsubscribe";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.7.18"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.7.18"},_a)),exports.unsubscribe$=unsubscribe$,exports.default=unsubscribe$},function(module,exports,__webpack_require__){"use strict";function getInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.resource.getInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.10"},_a)),exports.getInfo$=getInfo$,exports.default=getInfo$},function(module,exports,__webpack_require__){"use strict";function reportDebugMessage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.reportDebugMessage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.resource.reportDebugMessage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.20"},_a)),exports.reportDebugMessage$=reportDebugMessage$,exports.default=reportDebugMessage$},function(module,exports,__webpack_require__){"use strict";function addShortCut$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.addShortCut$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.shortCut.addShortCut";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.7.32"},_a)),exports.addShortCut$=addShortCut$,exports.default=addShortCut$},function(module,exports,__webpack_require__){"use strict";function getHealthAuthorizationStatus$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getHealthAuthorizationStatus$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.sports.getHealthAuthorizationStatus";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.11"},_a)),exports.getHealthAuthorizationStatus$=getHealthAuthorizationStatus$,exports.default=getHealthAuthorizationStatus$},function(module,exports,__webpack_require__){"use strict";function getHealthData$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getHealthData$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.sports.getHealthData";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a)),exports.getHealthData$=getHealthData$,exports.default=getHealthData$},function(module,exports,__webpack_require__){"use strict";function getHealthDeviceData$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getHealthDeviceData$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.sports.getHealthDeviceData";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a)),exports.getHealthDeviceData$=getHealthDeviceData$,exports.default=getHealthDeviceData$},function(module,exports,__webpack_require__){"use strict";function requestHealthAuthorization$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestHealthAuthorization$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.sports.requestHealthAuthorization";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a)),exports.requestHealthAuthorization$=requestHealthAuthorization$,exports.default=requestHealthAuthorization$},function(module,exports,__webpack_require__){"use strict";function closeUnpayOrder$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.closeUnpayOrder$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.store.closeUnpayOrder";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a)),exports.closeUnpayOrder$=closeUnpayOrder$,exports.default=closeUnpayOrder$},function(module,exports,__webpack_require__){"use strict";function createOrder$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.createOrder$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.store.createOrder";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a)),exports.createOrder$=createOrder$,exports.default=createOrder$},function(module,exports,__webpack_require__){"use strict";function getPayUrl$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getPayUrl$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.store.getPayUrl";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a)),exports.getPayUrl$=getPayUrl$,exports.default=getPayUrl$},function(module,exports,__webpack_require__){"use strict";function inquiry$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.inquiry$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.store.inquiry";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:apiHelper_1.genBizStoreParamsDealFn},_a)),exports.inquiry$=inquiry$,exports.default=inquiry$},function(module,exports,__webpack_require__){"use strict";function isTab$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isTab$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.tabwindow.isTab";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.5.10"},_a)),exports.isTab$=isTab$,exports.default=isTab$},function(module,exports,__webpack_require__){"use strict";function call$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.call$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.telephone.call";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.call$=call$,exports.default=call$},function(module,exports,__webpack_require__){"use strict";function checkBizCall$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkBizCall$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.telephone.checkBizCall";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.6"},_a)),exports.checkBizCall$=checkBizCall$,exports.default=checkBizCall$},function(module,exports,__webpack_require__){"use strict";function quickCallList$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.quickCallList$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.telephone.quickCallList";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.6"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.6"},_a)),exports.quickCallList$=quickCallList$,exports.default=quickCallList$},function(module,exports,__webpack_require__){"use strict";function showCallMenu$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showCallMenu$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.telephone.showCallMenu";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.showCallMenu$=showCallMenu$,exports.default=showCallMenu$},function(module,exports,__webpack_require__){"use strict";function checkPassword$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkPassword$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.user.checkPassword";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.8"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.8"},_a)),exports.checkPassword$=checkPassword$,exports.default=checkPassword$},function(module,exports,__webpack_require__){"use strict";function get$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.get$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.user.get";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.get$=get$,exports.default=get$},function(module,exports,__webpack_require__){"use strict";function callComponent$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.callComponent$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.callComponent";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.35"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.35"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.3.35"},_a)),exports.callComponent$=callComponent$,exports.default=callComponent$},function(module,exports,__webpack_require__){"use strict";function checkAuth$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkAuth$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.checkAuth";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.checkAuth$=checkAuth$,exports.default=checkAuth$},function(module,exports,__webpack_require__){"use strict";function chooseImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.chooseImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.1"},_a)),exports.chooseImage$=chooseImage$,exports.default=chooseImage$},function(module,exports,__webpack_require__){"use strict";function chooseRegion$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chooseRegion$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.chooseRegion";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.chooseRegion$=chooseRegion$,exports.default=chooseRegion$},function(module,exports,__webpack_require__){"use strict";function chosen$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.chosen$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.chosen";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.chosen$=chosen$,exports.default=chosen$},function(module,exports,__webpack_require__){"use strict";function clearWebStoreCache$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.clearWebStoreCache$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.clearWebStoreCache";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.22"},_a)),exports.clearWebStoreCache$=clearWebStoreCache$,exports.default=clearWebStoreCache$},function(module,exports,__webpack_require__){"use strict";function closePreviewImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.closePreviewImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.closePreviewImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.19"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.17"},_a)),exports.closePreviewImage$=closePreviewImage$,exports.default=closePreviewImage$},function(module,exports,__webpack_require__){"use strict";function compressImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.compressImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.compressImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.1"},_a)),exports.compressImage$=compressImage$,exports.default=compressImage$},function(module,exports,__webpack_require__){"use strict";function datepicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.datepicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.datepicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.datepicker$=datepicker$,exports.default=datepicker$},function(module,exports,__webpack_require__){"use strict";function datetimepicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.datetimepicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.datetimepicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.datetimepicker$=datetimepicker$,exports.default=datetimepicker$},function(module,exports,__webpack_require__){"use strict";function decrypt$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.decrypt$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.decrypt";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.1"},_a)),exports.decrypt$=decrypt$,exports.default=decrypt$},function(module,exports,__webpack_require__){"use strict";function downloadFile$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.downloadFile$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.downloadFile";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.downloadFile$=downloadFile$,exports.default=downloadFile$},function(module,exports,__webpack_require__){"use strict";function encrypt$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.encrypt$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.encrypt";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.1"},_a)),exports.encrypt$=encrypt$,exports.default=encrypt$},function(module,exports,__webpack_require__){"use strict";function getPerfInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getPerfInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.getPerfInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.14"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.14"},_a)),exports.getPerfInfo$=getPerfInfo$,exports.default=getPerfInfo$},function(module,exports,__webpack_require__){"use strict";function invokeWorkbench$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.invokeWorkbench$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.invokeWorkbench";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.8"},_a)),exports.invokeWorkbench$=invokeWorkbench$,exports.default=invokeWorkbench$},function(module,exports,__webpack_require__){"use strict";function isEnableGPUAcceleration$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isEnableGPUAcceleration$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.isEnableGPUAcceleration";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.22"},_a)),exports.isEnableGPUAcceleration$=isEnableGPUAcceleration$,exports.default=isEnableGPUAcceleration$},function(module,exports,__webpack_require__){"use strict";function isLocalFileExist$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.isLocalFileExist$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.isLocalFileExist";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.isLocalFileExist$=isLocalFileExist$,exports.default=isLocalFileExist$},function(module,exports,__webpack_require__){"use strict";function multiSelect$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.multiSelect$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.multiSelect";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.0.0"},_a)),exports.multiSelect$=multiSelect$,exports.default=multiSelect$},function(module,exports,__webpack_require__){"use strict";function open$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.open$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.open";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.open$=open$,exports.default=open$},function(module,exports,__webpack_require__){"use strict";function openBrowser$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.openBrowser$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.openBrowser";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.openBrowser$=openBrowser$,exports.default=openBrowser$},function(module,exports,__webpack_require__){"use strict";function openDocument$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openDocument$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.openDocument";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.10"},_a)),exports.openDocument$=openDocument$,exports.default=openDocument$},function(module,exports,__webpack_require__){"use strict";function openLink$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openLink$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.util.openLink",paramsDeal=apiHelper_1.genDefaultParamsDealFn({credible:!0,showMenuBar:!0});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.openLink$=openLink$,exports.default=openLink$},function(module,exports,__webpack_require__){"use strict";function openLocalFile$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openLocalFile$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.openLocalFile";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.openLocalFile$=openLocalFile$,exports.default=openLocalFile$},function(module,exports,__webpack_require__){"use strict";function openModal$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openModal$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.openModal";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.openModal$=openModal$,exports.default=openModal$},function(module,exports,__webpack_require__){"use strict";function openSlidePanel$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openSlidePanel$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.openSlidePanel";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a)),exports.openSlidePanel$=openSlidePanel$,exports.default=openSlidePanel$},function(module,exports,__webpack_require__){"use strict";function presentWindow$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.presentWindow$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.presentWindow";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.presentWindow$=presentWindow$,exports.default=presentWindow$},function(module,exports,__webpack_require__){"use strict";function previewImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.previewImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.previewImage$=previewImage$,exports.default=previewImage$},function(module,exports,__webpack_require__){"use strict";function previewVideo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.previewVideo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.previewVideo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.7"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.33"},_a)),exports.previewVideo$=previewVideo$,exports.default=previewVideo$},function(module,exports,__webpack_require__){"use strict";function saveImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.saveImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.1"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.saveImage$=saveImage$,exports.default=saveImage$},function(module,exports,__webpack_require__){"use strict";function saveImageToPhotosAlbum$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.saveImageToPhotosAlbum$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.saveImageToPhotosAlbum";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.saveImageToPhotosAlbum$=saveImageToPhotosAlbum$,exports.default=saveImageToPhotosAlbum$},function(module,exports,__webpack_require__){"use strict";function scan$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.scan$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.util.scan",paramsDeal=apiHelper_1.genDefaultParamsDealFn({type:"qrCode"});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.scan$=scan$,exports.default=scan$},function(module,exports,__webpack_require__){"use strict";function scanCard$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.scanCard$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.scanCard";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.scanCard$=scanCard$,exports.default=scanCard$},function(module,exports,__webpack_require__){"use strict";function setGPUAcceleration$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setGPUAcceleration$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.setGPUAcceleration";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.0.22"},_a)),exports.setGPUAcceleration$=setGPUAcceleration$,exports.default=setGPUAcceleration$},function(module,exports,__webpack_require__){"use strict";function setScreenBrightnessAndKeepOn$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setScreenBrightnessAndKeepOn$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.setScreenBrightnessAndKeepOn";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.37"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.3"},_a)),exports.setScreenBrightnessAndKeepOn$=setScreenBrightnessAndKeepOn$,exports.default=setScreenBrightnessAndKeepOn$},function(module,exports,__webpack_require__){"use strict";function setScreenKeepOn$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setScreenKeepOn$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.setScreenKeepOn";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.26"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.26"},_a)),exports.setScreenKeepOn$=setScreenKeepOn$,exports.default=setScreenKeepOn$},function(module,exports,__webpack_require__){"use strict";function share$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.share$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.util.share",paramsDeal=apiHelper_1.genDefaultParamsDealFn({title:"",buttonName:"确定"});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.37",paramsDeal:paramsDeal},_a)),exports.share$=share$,exports.default=share$},function(module,exports,__webpack_require__){"use strict";function shareImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.shareImage$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.shareImage";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.1"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.1"},_a)),exports.shareImage$=shareImage$,exports.default=shareImage$},function(module,exports,__webpack_require__){"use strict";function showAuthGuide$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showAuthGuide$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.showAuthGuide";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.showAuthGuide$=showAuthGuide$,exports.default=showAuthGuide$},function(module,exports,__webpack_require__){"use strict";function showSharePanel$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showSharePanel$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.showSharePanel";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.10"},_a)),exports.showSharePanel$=showSharePanel$,exports.default=showSharePanel$},function(module,exports,__webpack_require__){"use strict";function startDocSign$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startDocSign$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.startDocSign";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.6.33"},_a)),exports.startDocSign$=startDocSign$,exports.default=startDocSign$},function(module,exports,__webpack_require__){"use strict";function systemShare$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.systemShare$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.systemShare";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.11"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.11"},_a)),exports.systemShare$=systemShare$,exports.default=systemShare$},function(module,exports,__webpack_require__){"use strict";function timepicker$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.timepicker$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.timepicker";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.timepicker$=timepicker$,exports.default=timepicker$},function(module,exports,__webpack_require__){"use strict";function uploadAttachment$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadAttachment$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.uploadAttachment";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.7.0"},_a)),exports.uploadAttachment$=uploadAttachment$,exports.default=uploadAttachment$},function(module,exports,__webpack_require__){"use strict";function uploadFile$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadFile$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.uploadFile";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.28"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.27"},_a)),exports.uploadFile$=uploadFile$,exports.default=uploadFile$},function(module,exports,__webpack_require__){"use strict";function uploadImage$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadImage$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="biz.util.uploadImage",paramsDeal=apiHelper_1.genDefaultParamsDealFn({multiple:!1});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.uploadImage$=uploadImage$,exports.default=uploadImage$},function(module,exports,__webpack_require__){"use strict";function uploadImageFromCamera$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadImageFromCamera$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.uploadImageFromCamera";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.uploadImageFromCamera$=uploadImageFromCamera$,exports.default=uploadImageFromCamera$},function(module,exports,__webpack_require__){"use strict";function ut$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ut$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.util.ut",utParamsObj2Str=function(params){var finalParams=Object.assign({},params),tempValue=finalParams.value,tempStr=[];if(tempValue&&"object"==typeof tempValue){for(var i in tempValue)void 0!==tempValue[i]&&tempStr.push(i+"="+tempValue[i]);tempValue=tempStr.join(",")}return finalParams.value=tempValue||"",finalParams};ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.5.0",paramsDeal:utParamsObj2Str},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(params){var finalParams=Object.assign({},params),tempValue=finalParams.value;return tempValue&&"object"==typeof tempValue&&(tempValue=JSON.stringify(tempValue)),finalParams.value=tempValue,finalParams}},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:utParamsObj2Str},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:utParamsObj2Str},_a)),exports.ut$=ut$,exports.default=ut$},function(module,exports,__webpack_require__){"use strict";function openBindIDCard$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openBindIDCard$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.verify.openBindIDCard";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.21"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.21"},_a)),exports.openBindIDCard$=openBindIDCard$,exports.default=openBindIDCard$},function(module,exports,__webpack_require__){"use strict";function startAuth$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startAuth$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.verify.startAuth";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.5.21"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.21"},_a)),exports.startAuth$=startAuth$,exports.default=startAuth$},function(module,exports,__webpack_require__){"use strict";function makeCall$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeCall$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.voice.makeCall";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.40"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.40"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.40"},_a)),exports.makeCall$=makeCall$,exports.default=makeCall$},function(module,exports,__webpack_require__){"use strict";function getWatermarkInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWatermarkInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.watermarkCamera.getWatermarkInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.25"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.25"},_a)),exports.getWatermarkInfo$=getWatermarkInfo$,exports.default=getWatermarkInfo$},function(module,exports,__webpack_require__){"use strict";function setWatermarkInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setWatermarkInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="biz.watermarkCamera.setWatermarkInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.25"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.25"},_a)),exports.setWatermarkInfo$=setWatermarkInfo$,exports.default=setWatermarkInfo$},function(module,exports,__webpack_require__){"use strict";function requestAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),apiName="channel.permission.requestAuthCode";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.0.0"},_a)),exports.requestAuthCode$=requestAuthCode$,exports.default=requestAuthCode$},function(module,exports,__webpack_require__){"use strict";function clearShake$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.clearShake$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.accelerometer.clearShake";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.clearShake$=clearShake$,exports.default=clearShake$},function(module,exports,__webpack_require__){"use strict";function watchShake$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.watchShake$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.accelerometer.watchShake";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(params){return apiHelper_1.forceChangeParamsDealFn({sensitivity:3.2})(apiHelper_1.addWatchParamsDeal(params))}},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a)),exports.watchShake$=watchShake$,exports.default=watchShake$},function(module,exports,__webpack_require__){"use strict";function download$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.download$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.download";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.download$=download$,exports.default=download$},function(module,exports,__webpack_require__){"use strict";function onPlayEnd$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onPlayEnd$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.onPlayEnd";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.onPlayEnd$=onPlayEnd$,exports.default=onPlayEnd$},function(module,exports,__webpack_require__){"use strict";function onRecordEnd$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.onRecordEnd$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.onRecordEnd";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.onRecordEnd$=onRecordEnd$,exports.default=onRecordEnd$},function(module,exports,__webpack_require__){"use strict";function pause$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.pause$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.pause";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.pause$=pause$,exports.default=pause$},function(module,exports,__webpack_require__){"use strict";function play$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.play$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.play";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.play$=play$,exports.default=play$},function(module,exports,__webpack_require__){"use strict";function resume$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.resume$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.resume";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.resume$=resume$,exports.default=resume$},function(module,exports,__webpack_require__){"use strict";function startRecord$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.startRecord$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.startRecord";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.30"},_a)),exports.startRecord$=startRecord$,exports.default=startRecord$},function(module,exports,__webpack_require__){"use strict";function stop$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stop$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.stop";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.stop$=stop$,exports.default=stop$},function(module,exports,__webpack_require__){"use strict";function stopRecord$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stopRecord$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.stopRecord";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"7.0.30"},_a)),exports.stopRecord$=stopRecord$,exports.default=stopRecord$},function(module,exports,__webpack_require__){"use strict";function translateVoice$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.translateVoice$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.audio.translateVoice";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.8.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.8.0"},_a)),exports.translateVoice$=translateVoice$,exports.default=translateVoice$},function(module,exports,__webpack_require__){"use strict";function getBatteryInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.getBatteryInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getBatteryInfo";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.getBatteryInfo$=getBatteryInfo$,exports.default=getBatteryInfo$},function(module,exports,__webpack_require__){"use strict";function getInterface$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getInterface$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getInterface";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.getInterface$=getInterface$,exports.default=getInterface$},function(module,exports,__webpack_require__){"use strict";function getPhoneInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getPhoneInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getPhoneInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.5.0"},_a)),exports.getPhoneInfo$=getPhoneInfo$,exports.default=getPhoneInfo$},function(module,exports,__webpack_require__){"use strict";function getScanWifiListAsync$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getScanWifiListAsync$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getScanWifiListAsync";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.41"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.3.0"},_a)),exports.getScanWifiListAsync$=getScanWifiListAsync$,exports.default=getScanWifiListAsync$},function(module,exports,__webpack_require__){"use strict";function getUUID$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getUUID$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getUUID";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.7.6"},_a)),exports.getUUID$=getUUID$,exports.default=getUUID$},function(module,exports,__webpack_require__){"use strict";function getWifiStatus$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWifiStatus$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.getWifiStatus";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.11.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.11.0"},_a)),exports.getWifiStatus$=getWifiStatus$,exports.default=getWifiStatus$},function(module,exports,__webpack_require__){"use strict";function openSystemSetting$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.openSystemSetting$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.base.openSystemSetting";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.27"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.15"},_a)),exports.openSystemSetting$=openSystemSetting$,exports.default=openSystemSetting$},function(module,exports,__webpack_require__){"use strict";function getNetworkType$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getNetworkType$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.connection.getNetworkType";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.getNetworkType$=getNetworkType$,exports.default=getNetworkType$},function(module,exports,__webpack_require__){"use strict";function checkPermission$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkPermission$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.geolocation.checkPermission";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.0"},_a)),exports.checkPermission$=checkPermission$,exports.default=checkPermission$},function(module,exports,__webpack_require__){"use strict";function get$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.get$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.geolocation.get";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.get$=get$,exports.default=get$},function(module,exports,__webpack_require__){"use strict";function start$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.start$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.geolocation.start";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.4.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.4.7"},_a)),exports.start$=start$,exports.default=start$},function(module,exports,__webpack_require__){"use strict";function status$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.status$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.geolocation.status";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.4.8"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.4.8"},_a)),exports.status$=status$,exports.default=status$},function(module,exports,__webpack_require__){"use strict";function stop$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stop$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.geolocation.stop";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.4.7"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.4.7"},_a)),exports.stop$=stop$,exports.default=stop$},function(module,exports,__webpack_require__){"use strict";function checkInstalledApps$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkInstalledApps$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.launcher.checkInstalledApps";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.checkInstalledApps$=checkInstalledApps$,exports.default=checkInstalledApps$},function(module,exports,__webpack_require__){"use strict";function launchApp$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.launchApp$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.launcher.launchApp";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.launchApp$=launchApp$,exports.default=launchApp$},function(module,exports,__webpack_require__){"use strict";function nfcRead$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.nfcRead$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.nfc.nfcRead";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.11.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.11.0"},_a)),exports.nfcRead$=nfcRead$,exports.default=nfcRead$},function(module,exports,__webpack_require__){"use strict";function nfcStop$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.nfcStop$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.nfc.nfcStop";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.3.9"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.3.9"},_a)),exports.nfcStop$=nfcStop$,exports.default=nfcStop$},function(module,exports,__webpack_require__){"use strict";function nfcWrite$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.nfcWrite$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.nfc.nfcWrite";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.11.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.11.0"},_a)),exports.nfcWrite$=nfcWrite$,exports.default=nfcWrite$},function(module,exports,__webpack_require__){"use strict";function actionSheet$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.actionSheet$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.notification.actionSheet";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.actionSheet$=actionSheet$,exports.default=actionSheet$},function(module,exports,__webpack_require__){"use strict";function alert$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.alert$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.alert",paramsDeal=apiHelper_1.genDefaultParamsDealFn({title:"",buttonName:"确定"});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0",paramsDeal:paramsDeal},_a)),exports.alert$=alert$,exports.default=alert$},function(module,exports,__webpack_require__){"use strict";function confirm$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.confirm$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.confirm",paramsDeal=apiHelper_1.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.confirm$=confirm$,exports.default=confirm$},function(module,exports,__webpack_require__){"use strict";function extendModal$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.extendModal$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.notification.extendModal";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.5.0"},_a)),exports.extendModal$=extendModal$,exports.default=extendModal$},function(module,exports,__webpack_require__){"use strict";function hidePreloader$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.hidePreloader$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.notification.hidePreloader";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.hidePreloader$=hidePreloader$,exports.default=hidePreloader$},function(module,exports,__webpack_require__){"use strict";function modal$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.modal$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.notification.modal";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.2.5"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.modal$=modal$,exports.default=modal$},function(module,exports,__webpack_require__){"use strict";function prompt$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.prompt$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.prompt",paramsDeal=apiHelper_1.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.7.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.prompt$=prompt$,exports.default=prompt$},function(module,exports,__webpack_require__){"use strict";function showPreloader$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.showPreloader$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.showPreloader",paramsDeal=apiHelper_1.genDefaultParamsDealFn({text:"加载中...",showIcon:!0});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.showPreloader$=showPreloader$,exports.default=showPreloader$},function(module,exports,__webpack_require__){"use strict";function toast$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.toast$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.toast",paramsDeal=apiHelper_1.genDefaultParamsDealFn({text:"toast",duration:3,delay:0});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"2.5.0",paramsDeal:function(params){return params.icon&&!params.type&&("success"===params.icon?params.type="success":"error"===params.icon&&(params.type="error")),params}},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.toast$=toast$,exports.default=toast$},function(module,exports,__webpack_require__){"use strict";function vibrate$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.vibrate$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="device.notification.vibrate",paramsDeal=apiHelper_1.genDefaultParamsDealFn({duration:300});ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:paramsDeal},_a)),exports.vibrate$=vibrate$,exports.default=vibrate$},function(module,exports,__webpack_require__){"use strict";function getScreenBrightness$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.getScreenBrightness$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.getScreenBrightness";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.getScreenBrightness$=getScreenBrightness$,exports.default=getScreenBrightness$},function(module,exports,__webpack_require__){"use strict";function insetAdjust$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.insetAdjust$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.insetAdjust";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.6.18"},_a)),exports.insetAdjust$=insetAdjust$,exports.default=insetAdjust$},function(module,exports,__webpack_require__){"use strict";function isScreenReaderEnabled$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.isScreenReaderEnabled$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.isScreenReaderEnabled";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.isScreenReaderEnabled$=isScreenReaderEnabled$,exports.default=isScreenReaderEnabled$},function(module,exports,__webpack_require__){"use strict";function resetView$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.resetView$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.resetView";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.0.0"},_a)),exports.resetView$=resetView$,exports.default=resetView$},function(module,exports,__webpack_require__){"use strict";function rotateView$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.rotateView$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.rotateView";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"4.0.0"},_a)),exports.rotateView$=rotateView$,exports.default=rotateView$},function(module,exports,__webpack_require__){"use strict";function setScreenBrightness$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.setScreenBrightness$=void 0;var ddSdk_1=__webpack_require__(0),apiName="device.screen.setScreenBrightness";ddSdk_1.ddSdk.setAPI(apiName,{}),exports.setScreenBrightness$=setScreenBrightness$,exports.default=setScreenBrightness$},function(module,exports,__webpack_require__){"use strict";function keepAlive$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.keepAlive$=void 0;var ddSdk_1=__webpack_require__(0),apiName="media.voiceRecorder.keepAlive";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.12"},_a)),exports.keepAlive$=keepAlive$,exports.default=keepAlive$},function(module,exports,__webpack_require__){"use strict";function pause$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.pause$=void 0;var ddSdk_1=__webpack_require__(0),apiName="media.voiceRecorder.pause";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.12"},_a)),exports.pause$=pause$,exports.default=pause$},function(module,exports,__webpack_require__){"use strict";function resume$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.resume$=void 0;var ddSdk_1=__webpack_require__(0),apiName="media.voiceRecorder.resume";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.12"},_a)),exports.resume$=resume$,exports.default=resume$},function(module,exports,__webpack_require__){"use strict";function start$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.start$=void 0;var ddSdk_1=__webpack_require__(0),apiName="media.voiceRecorder.start";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.12"},_a)),exports.start$=start$,exports.default=start$},function(module,exports,__webpack_require__){"use strict";function stop$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stop$=void 0;var ddSdk_1=__webpack_require__(0),apiName="media.voiceRecorder.stop";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.android]={vs:"5.1.12"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"5.1.12"},_a)),exports.stop$=stop$,exports.default=stop$},function(module,exports,__webpack_require__){"use strict";function loginGovNet$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.loginGovNet$=void 0;var ddSdk_1=__webpack_require__(0),apiName="net.bjGovApn.loginGovNet";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.android]={vs:"4.5.16"},_a)),exports.loginGovNet$=loginGovNet$,exports.default=loginGovNet$},function(module,exports,__webpack_require__){"use strict";function exec$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.exec$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.h5nuvabridge.exec";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"7.0.0"},_a)),exports.exec$=exec$,exports.default=exec$},function(module,exports,__webpack_require__){"use strict";function fetch$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.fetch$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.message.fetch";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.fetch$=fetch$,exports.default=fetch$},function(module,exports,__webpack_require__){"use strict";function post$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.post$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.message.post";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.post$=post$,exports.default=post$},function(module,exports,__webpack_require__){"use strict";function getLoadTime$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getLoadTime$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.monitor.getLoadTime";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.0.10"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.0.10"},_a)),exports.getLoadTime$=getLoadTime$,exports.default=getLoadTime$},function(module,exports,__webpack_require__){"use strict";function requestAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.permission.requestAuthCode",paramsDeal=function(params){return Object.assign(params,{url:location.href.split("#")[0]})};ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.0.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.requestAuthCode$=requestAuthCode$,exports.default=requestAuthCode$},function(module,exports,__webpack_require__){"use strict";function requestOperateAuthCode$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.requestOperateAuthCode$=void 0;var ddSdk_1=__webpack_require__(0),apiName="runtime.permission.requestOperateAuthCode",paramsDeal=function(params){return Object.assign(params,{url:location.href.split("#")[0]})};ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"3.3.0",paramsDeal:paramsDeal},_a[ddSdk_1.ENV_ENUM.ios]={vs:"3.3.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"3.3.0"},_a)),exports.requestOperateAuthCode$=requestOperateAuthCode$,exports.default=requestOperateAuthCode$},function(module,exports,__webpack_require__){"use strict";function plain$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.plain$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.input.plain";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.plain$=plain$,exports.default=plain$},function(module,exports,__webpack_require__){"use strict";function addToFloat$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.addToFloat$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.multitask.addToFloat";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.0"},_a)),exports.addToFloat$=addToFloat$,exports.default=addToFloat$},function(module,exports,__webpack_require__){"use strict";function removeFromFloat$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.removeFromFloat$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.multitask.removeFromFloat";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.0"},_a)),exports.removeFromFloat$=removeFromFloat$,exports.default=removeFromFloat$},function(module,exports,__webpack_require__){"use strict";function close$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.close$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.nav.close";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.close$=close$,exports.default=close$},function(module,exports,__webpack_require__){"use strict";function getCurrentId$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getCurrentId$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.nav.getCurrentId";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.getCurrentId$=getCurrentId$,exports.default=getCurrentId$},function(module,exports,__webpack_require__){"use strict";function go$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.go$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.nav.go";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.go$=go$,exports.default=go$},function(module,exports,__webpack_require__){"use strict";function preload$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.preload$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.nav.preload";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.preload$=preload$,exports.default=preload$},function(module,exports,__webpack_require__){"use strict";function recycle$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.recycle$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.nav.recycle";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.6.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.6.0"},_a)),exports.recycle$=recycle$,exports.default=recycle$},function(module,exports,__webpack_require__){"use strict";function setColors$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setColors$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.progressBar.setColors";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.setColors$=setColors$,exports.default=setColors$},function(module,exports,__webpack_require__){"use strict";function disable$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.disable$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.pullToRefresh.disable";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.disable$=disable$,exports.default=disable$},function(module,exports,__webpack_require__){"use strict";function enable$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.enable$=void 0;var ddSdk_1=__webpack_require__(0),apiHelper_1=__webpack_require__(1),apiName="ui.pullToRefresh.enable";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:apiHelper_1.addWatchParamsDeal},_a)),exports.enable$=enable$,exports.default=enable$},function(module,exports,__webpack_require__){"use strict";function stop$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.stop$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.pullToRefresh.stop";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.stop$=stop$,exports.default=stop$},function(module,exports,__webpack_require__){"use strict";function disable$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.disable$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.webViewBounce.disable";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.disable$=disable$,exports.default=disable$},function(module,exports,__webpack_require__){"use strict";function enable$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.enable$=void 0;var ddSdk_1=__webpack_require__(0),apiName="ui.webViewBounce.enable";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.4.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.4.0"},_a)),exports.enable$=enable$,exports.default=enable$},function(module,exports,__webpack_require__){"use strict";function getItem$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getItem$=void 0;var ddSdk_1=__webpack_require__(0),apiName="util.domainStorage.getItem";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.29"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.getItem$=getItem$,exports.default=getItem$},function(module,exports,__webpack_require__){"use strict";function getStorageInfo$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getStorageInfo$=void 0;var ddSdk_1=__webpack_require__(0),apiName="util.domainStorage.getStorageInfo";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.5.30"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.5.30"},_a)),exports.getStorageInfo$=getStorageInfo$,exports.default=getStorageInfo$},function(module,exports,__webpack_require__){"use strict";function removeItem$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.removeItem$=void 0;var ddSdk_1=__webpack_require__(0),apiName="util.domainStorage.removeItem";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.29"},_a)),exports.removeItem$=removeItem$,exports.default=removeItem$},function(module,exports,__webpack_require__){"use strict";function setItem$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.setItem$=void 0;var ddSdk_1=__webpack_require__(0),apiName="util.domainStorage.setItem";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.android]={vs:"2.9.0"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"4.6.9"},_a[ddSdk_1.ENV_ENUM.harmony]={vs:"7.0.0"},_a)),exports.setItem$=setItem$,exports.default=setItem$},function(module,exports,__webpack_require__){"use strict";function getData$(params){return ddSdk_1.ddSdk.invokeAPI(apiName,params)}var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.getData$=void 0;var ddSdk_1=__webpack_require__(0),apiName="util.openTemporary.getData";ddSdk_1.ddSdk.setAPI(apiName,(_a={},_a[ddSdk_1.ENV_ENUM.ios]={vs:"6.3.20"},_a[ddSdk_1.ENV_ENUM.android]={vs:"6.3.20"},_a[ddSdk_1.ENV_ENUM.pc]={vs:"6.3.30"},_a)),exports.getData$=getData$,exports.default=getData$},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5AndroidbridgeInit=void 0;var h5BridgeReadyPromise;exports.h5AndroidbridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(resolve,reject){var run=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),resolve({})}catch(e){reject(e)}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?run():(document.addEventListener("runtimeready",function(){run()},!1),document.addEventListener("runtimefailed",function(e){var detail=e&&e.detail||{errorCode:"2",errorMessage:"unknown nuvajs bootstrap error"};reject(detail)},!1))})),h5BridgeReadyPromise};var h5AndroidBridge=function(method,params){return h5BridgeReadyPromise||(h5BridgeReadyPromise=exports.h5AndroidbridgeInit()),h5BridgeReadyPromise.then(function(){return new Promise(function(resolve,reject){var arr=method.split("."),suff=arr.pop()||"",pre=arr.join("."),success=function(data){"function"==typeof params.success?params.success(data):"function"==typeof params.onSuccess&&params.onSuccess(data),resolve(data)},fail=function(err){"function"==typeof params.fail?params.fail(err):"function"==typeof params.onFail&&params.onFail(err),reject(err)};"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid(success,fail,pre,suff,params)})})};exports.default=h5AndroidBridge},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5HarmonyBridgeInit=void 0;var h5BridgeReadyPromise;exports.h5HarmonyBridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(resolve,reject){if("undefined"!=typeof DingTalkJSBridge){try{DingTalkJSBridge.init(function(data,responseCallback){})}catch(e){return reject()}return resolve({})}document.addEventListener("DingTalkJSBridgeReady",function(){if("undefined"==typeof DingTalkJSBridge)return reject();try{DingTalkJSBridge.init(function(data,responseCallback){})}catch(e){return reject()}return resolve({})},!1)})),h5BridgeReadyPromise};var h5HarmonyBridge=function(method,params){return h5BridgeReadyPromise||(h5BridgeReadyPromise=exports.h5HarmonyBridgeInit()),h5BridgeReadyPromise.then(function(){return new Promise(function(resolve,reject){var callback=function(data){data.success?(function(data){"function"==typeof params.success?params.success(data):"function"==typeof params.onSuccess&&params.onSuccess(data)}(data.body),resolve(data.body)):(function(err){"function"==typeof params.fail?params.fail(err):"function"==typeof params.onFail&&params.onFail(err)}(data.body),reject(data.body))};"function"==typeof window.DingTalkJSBridge.call&&window.DingTalkJSBridge.call(method,params,callback)})})};exports.default=h5HarmonyBridge},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5IosBridgeInit=void 0;var h5BridgeReadyPromise;exports.h5IosBridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(resolve,reject){if("undefined"!=typeof WebViewJavascriptBridge){try{WebViewJavascriptBridge.init(function(data,responseCallback){})}catch(e){return reject()}return resolve({})}document.addEventListener("WebViewJavascriptBridgeReady",function(){if("undefined"==typeof WebViewJavascriptBridge)return reject();try{WebViewJavascriptBridge.init(function(data,responseCallback){})}catch(e){return reject()}return resolve({})},!1)})),h5BridgeReadyPromise};var h5IosBridge=function(method,params){return h5BridgeReadyPromise||(h5BridgeReadyPromise=exports.h5IosBridgeInit()),h5BridgeReadyPromise.then(function(){var innerParams=Object.assign({},params);return new Promise(function(resolve,reject){if(!0===innerParams.watch){var successHandler_1=innerParams.onSuccess;delete innerParams.onSuccess,"function"==typeof innerParams.success&&(successHandler_1=innerParams.success,delete innerParams.success),"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(method,function(data,responseCallback){"function"==typeof successHandler_1&&successHandler_1.call(null,data),responseCallback&&responseCallback({errorCode:"0",errorMessage:"success"})})}void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler(method,Object.assign({},innerParams),function(response){var data=response||{};"0"===data.errorCode?("function"==typeof innerParams.success?innerParams.success.call(null,data.result):"function"==typeof innerParams.onSuccess&&innerParams.onSuccess.call(null,data.result),resolve(data.result)):("-1"===data.errorCode?"function"==typeof innerParams.cancel?innerParams.cancel.call(null,data,data.errorCode):"function"==typeof innerParams.onCancel&&innerParams.onCancel.call(null,data,data.errorCode):"function"==typeof innerParams.fail?innerParams.fail.call(null,data,data.errorCode):"function"==typeof innerParams.onFail&&innerParams.onFail.call(null,data,data.errorCode),reject(data))})})})};exports.default=h5IosBridge},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5PcBridgeInit=void 0,exports.h5PcBridgeInit=function(){return Promise.resolve(__webpack_require__(8))};var h5PcBridge=function(method,params){return new Promise(function(resolve,reject){return __webpack_require__(8).invokeAPI(method,params).result.then(function(data){return"function"==typeof params.success?params.success.call(null,data):"function"==typeof params.onSuccess&&params.onSuccess.call(null,data),resolve(data)},function(err){return"function"==typeof params.fail?params.fail.call(null,err):"function"==typeof params.onFail&&params.onFail.call(null,err),reject(err)})})};exports.default=h5PcBridge},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.off=exports.on=void 0,exports.on=function(type,handler){__webpack_require__(8).addEventListener(type,handler)},exports.off=function(type,handler){__webpack_require__(8).removeEventListener(type,handler)}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.version=exports.language=exports.compareVersion=exports.other=exports.harmony=exports.pc=exports.android=exports.ios=void 0;var env_1=__webpack_require__(4),ENV=env_1.getENV();exports.ios=ENV.platform===env_1.ENV_ENUM.ios,exports.android=ENV.platform===env_1.ENV_ENUM.android,exports.pc=ENV.platform===env_1.ENV_ENUM.pc,exports.harmony=ENV.platform===env_1.ENV_ENUM.harmony,exports.other=ENV.platform===env_1.ENV_ENUM.notInDingTalk,exports.compareVersion=function(oldVersion,newVersion,containEqual){function transform(item){return parseInt(item,10)||0}if("string"!=typeof oldVersion||"string"!=typeof newVersion)return!1;for(var o,n,oldArray=oldVersion.split("-")[0].split(".").map(transform),newArray=newVersion.split("-")[0].split(".").map(transform);o===n&&newArray.length>0;)o=oldArray.shift(),n=newArray.shift();return containEqual?(n||0)>=(o||0):(n||0)>(o||0)},exports.language=ENV.language,exports.version=ENV.version},function(module,exports,__webpack_require__){"use strict";function environment(runtime,framework,virtualEnv){var isWeb="Web"===virtualEnv.platform,isWeexiOS="iOS"===virtualEnv.platform,isWeexAndroid="android"===virtualEnv.platform,isWeex=isWeexAndroid||isWeexiOS,UA=function(){return isWeb?window.navigator.userAgent.toLowerCase():""}(),PCFrameConf=function(){var tempConf={};if(isWeb){var frameName=window.name;try{var frameConf=JSON.parse(frameName);tempConf.containerId=frameConf.containerId,tempConf.version=frameConf.hostVersion,tempConf.language=frameConf.language||"*"}catch(e){}}return tempConf}(),isDingTalk=function(){return isWeex?"DingTalk"===virtualEnv.appName||"com.alibaba.android.rimet"===virtualEnv.appName:UA.indexOf("dingtalk")>-1||!!PCFrameConf.containerId}(),version=function(){if(isWeb){if(PCFrameConf.version)return PCFrameConf.version;var matches=UA.match(/aliapp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===matches&&(matches=UA.match(/dingtalk\/([a-zA-Z0-9.-]+)/));return matches&&matches[1]||"Unknown"}return virtualEnv.appVersion}(),isPC=!!PCFrameConf.containerId,isWebiOS=/iphone|ipod|ios/.test(UA),isiPad=/ipad/.test(UA),isWebAndroid=UA.indexOf("android")>-1,isDingTalkPCMac=UA.indexOf("mac")>-1&&isPC,isDingTalkPCWindows=UA.indexOf("win")>-1&&isPC,isDingTalkPCWeb=!isDingTalkPCMac&&!isDingTalkPCWindows&&isPC,isDingTalkPC=isPC,platform="";return platform=isDingTalk?isWebiOS||isWeexiOS?constants_1.PLATFORM.IOS:isWebAndroid||isWeexAndroid?constants_1.PLATFORM.ANDROID:isiPad?constants_1.PLATFORM.IPAD:isDingTalkPCMac?constants_1.PLATFORM.MAC:isDingTalkPCWindows?constants_1.PLATFORM.WINDOWS:isDingTalkPCWeb?constants_1.PLATFORM.BROWSER:constants_1.PLATFORM.UNKNOWN:constants_1.PLATFORM.UNKNOWN,{isDingTalk:isDingTalk,isWebiOS:isWebiOS,isWebAndroid:isWebAndroid,isWeexiOS:isWeexiOS,isWeexAndroid:isWeexAndroid,isDingTalkPCMac:isDingTalkPCMac,isDingTalkPCWeb:isDingTalkPCWeb,isDingTalkPCWindows:isDingTalkPCWindows,isDingTalkPC:isDingTalkPC,runtime:runtime,framework:framework,platform:platform,version:version,isWeex:isWeex}}Object.defineProperty(exports,"__esModule",{value:!0});var constants_1=__webpack_require__(206);exports.default=environment},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var whichOneRuntime_1=__webpack_require__(465),environment_1=__webpack_require__(463),constants_1=__webpack_require__(206),_a=whichOneRuntime_1.default().split("."),runtime=_a[0],framework=_a[1],virtualEnv=function(){var containerEnv={};switch(framework){case constants_1.FRAMEWORK.VUE:var config=weex.config,configEnv=config.env;containerEnv.platform=configEnv.platform,constants_1.RUNTIME.WEEX===runtime&&(containerEnv.appVersion=configEnv.appVersion,containerEnv.appName=configEnv.appName);break;case constants_1.FRAMEWORK.RAX:constants_1.RUNTIME.WEEX===runtime&&(containerEnv.platform=navigator.platform,containerEnv.appName=navigator.appName,containerEnv.appVersion=navigator.appVersion);break;case constants_1.FRAMEWORK.UNKNOWN:constants_1.RUNTIME.WEB===runtime&&(containerEnv.platform=constants_1.RUNTIME.WEB),constants_1.RUNTIME.UNKNOWN===runtime&&(containerEnv.platform=constants_1.RUNTIME.UNKNOWN)}return containerEnv}(),env=environment_1.default(runtime,framework,virtualEnv);exports.default=env},function(module,exports,__webpack_require__){"use strict";function snifferMachine(snifferMap,source){for(var j=snifferMap.length,i=0,result=!0;i<j;i++)try{if(!(snifferMap[i]in source)){result=!1;break}}catch(err){result=!1;break}return result}function whichOneRuntime(){return maybeInWebView&&maybeInWeexVueEnv?snifferMachine(snifferWeexVueMap,weex)?"Web.Vue":"Web.Unknown":!maybeInWebView&&maybeInWeexVueEnv?snifferMachine(snifferWeexVueMap,weex)?"Weex.Vue":"Weex.Unknown":maybeInWebView&&maybeInNative&&!maybeInWeexVueEnv?snifferMachine(snifferWeexRaxMap,window)?"Weex.Rax":"Weex.Unknown":maybeInWebView&&snifferMachine(snifferWebViewMap,window)?"Web.Unknown":"Unknown.Unknown"}Object.defineProperty(exports,"__esModule",{value:!0});var maybeInWebView="undefined"!=typeof window,maybeInWeexVueEnv="undefined"!=typeof weex,maybeInNative="undefined"!=typeof callNative,snifferWeexRaxMap=["__weex_config__","__weex_options__","__weex_require__"],snifferWebViewMap=["localStorage","location","navigator","XMLHttpRequest"],snifferWeexVueMap=["config","requireModule","document"];exports.default=whichOneRuntime},function(module,exports,__webpack_require__){"function"!=typeof Promise&&__webpack_require__(488)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),__webpack_require__(466),__webpack_require__(468),__webpack_require__(469)},function(module,exports){"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(target,varArgs){"use strict";if(null==target)throw new TypeError("Cannot convert undefined or null to object");for(var to=Object(target),index=1;index<arguments.length;index++){var nextSource=arguments[index];if(null!=nextSource)for(var nextKey in nextSource)Object.prototype.hasOwnProperty.call(nextSource,nextKey)&&(to[nextKey]=nextSource[nextKey])}return to},writable:!0,configurable:!0})},function(module,exports){Object.keys||(Object.keys=function(o){if(o!==Object(o))throw new TypeError("Object.keys called on a non-object");var p,k=[];for(p in o)Object.prototype.hasOwnProperty.call(o,p)&&k.push(p);return k})},function(module,exports,__webpack_require__){"use strict";function bridge(context){return __awaiter(this,void 0,void 0,function(){var invokeName,method,callParams,JSBridge;return __generator(this,function(_a){return invokeName=context.invokeName,method=context.method,callParams=context.callParams,JSBridge=context.JSBridge,JSBridge?[2,JSBridge(invokeName||method,callParams)]:[2,this.bridgeInitFn().then(function(bridge){return bridge(invokeName||method,callParams)})]})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.bridge=void 0,exports.bridge=bridge},function(module,exports,__webpack_require__){"use strict";function checkConfig(context,next){return __awaiter(this,void 0,void 0,function(){var isAuthApi,method,invokeAPIConfig,apiConfig,errorMessage,errorMessage;return __generator(this,function(_a){return!1===this.devConfig.isAuthApi&&(context.isAuthApi=!1),isAuthApi=context.isAuthApi,method=context.method,invokeAPIConfig=this.invokeAPIConfigMapByMethod[method],invokeAPIConfig||!isAuthApi?(apiConfig=void 0,invokeAPIConfig&&(apiConfig=invokeAPIConfig[this.env.platform]),context.apiConfig=apiConfig,apiConfig||!isAuthApi?[2,next()]:(errorMessage=log_1.formatLog(log_1.diagnosticMessageMap.call_api_support_platform_error,method,this.env.platform),[2,Promise.reject({errorCode:log_1.diagnosticMessageMap.call_api_support_platform_error.code,errorMessage:errorMessage})])):(errorMessage=log_1.formatLog(log_1.diagnosticMessageMap.call_api_config_platform_error,this.env.platform),[2,Promise.reject({errorCode:log_1.diagnosticMessageMap.call_api_config_platform_error.code,errorMessage:errorMessage})])})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.checkConfig=void 0;var log_1=__webpack_require__(13);exports.checkConfig=checkConfig},function(module,exports,__webpack_require__){"use strict";function dealParamsAndResult(context,next){return __awaiter(this,void 0,void 0,function(){var method,params,apiConfig,isForceEnableDealApi,isDisableCurrentApiDeal,callParams,resultDealFn,callParamsOnSuccess_1,callParamsSuccess_1,_this=this;return __generator(this,function(_a){switch(_a.label){case 0:return method=context.method,params=context.params,apiConfig=context.apiConfig,isForceEnableDealApi=this.devConfig.forceEnableDealApiFnMap&&this.devConfig.forceEnableDealApiFnMap[method]&&!0===this.devConfig.forceEnableDealApiFnMap[method](params),isDisableCurrentApiDeal=!isForceEnableDealApi&&(!0===this.devConfig.isDisableDeal||this.devConfig.disbaleDealApiWhiteList&&-1!==this.devConfig.disbaleDealApiWhiteList.indexOf(method)),callParams={},!isDisableCurrentApiDeal&&apiConfig&&apiConfig.paramsDeal&&sdkLib_1.isFunction(apiConfig.paramsDeal)?[4,apiConfig.paramsDeal(params)]:[3,2];case 1:return callParams=_a.sent(),[3,3];case 2:callParams=Object.assign({},params),_a.label=3;case 3:return resultDealFn=function(res){return __awaiter(_this,void 0,void 0,function(){return __generator(this,function(_a){return!isDisableCurrentApiDeal&&apiConfig&&apiConfig.resultDeal&&sdkLib_1.isFunction(apiConfig.resultDeal)?[2,apiConfig.resultDeal(res)]:[2,res]})})},sdkLib_1.isFunction(callParams.onSuccess)&&(callParamsOnSuccess_1=callParams.onSuccess,callParams.onSuccess=function(res){return __awaiter(_this,void 0,void 0,function(){var _a;return __generator(this,function(_b){switch(_b.label){case 0:return _a=callParamsOnSuccess_1,[4,resultDealFn(res)];case 1:return _a.apply(void 0,[_b.sent()]),[2]}})})}),sdkLib_1.isFunction(callParams.success)&&(callParamsSuccess_1=callParams.success,callParams.success=function(res){return __awaiter(_this,void 0,void 0,function(){var _a;return __generator(this,function(_b){switch(_b.label){case 0:return _a=callParamsSuccess_1,[4,resultDealFn(res)];case 1:return _a.apply(void 0,[_b.sent()]),[2]}})})}),Object.assign(context,{callParams:callParams,invokeName:null===apiConfig||void 0===apiConfig?void 0:apiConfig.invokeName}),[2,next().then(resultDealFn)]}})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.dealParamsAndResult=void 0;var sdkLib_1=__webpack_require__(14);exports.dealParamsAndResult=dealParamsAndResult},function(module,exports,__webpack_require__){"use strict";function hookBeforeAndAfter(context,next){return __awaiter(this,void 0,void 0,function(){var method,params,startTime,invokeId,res,err,success,e_1,payload;return __generator(this,function(_a){switch(_a.label){case 0:if(method=context.method,params=context.params,startTime=+new Date,invokeId=startTime+"_"+Math.floor(1e3*Math.random()),this.devConfig.onBeforeInvokeAPI)try{this.devConfig.onBeforeInvokeAPI({invokeId:invokeId,method:method,params:params,startTime:startTime})}catch(e){log_1.formatLog(log_1.diagnosticMessageMap.call_api_on_before_error,e.toString())}success=!0,_a.label=1;case 1:return _a.trys.push([1,3,,4]),[4,next()];case 2:return res=_a.sent(),[3,4];case 3:return e_1=_a.sent(),err=e_1,success=!1,[3,4];case 4:if(payload=success?res:err,this.devConfig.onAfterInvokeAPI)try{this.devConfig.onAfterInvokeAPI({invokeId:invokeId,method:method,params:params,payload:payload,startTime:startTime,duration:+new Date-startTime,isSuccess:success})}catch(e){log_1.formatLog(log_1.diagnosticMessageMap.call_api_on_after_error,e.toString())}return[2,success?Promise.resolve(payload):Promise.reject(payload)]}})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.hookBeforeAndAfter=void 0;var log_1=__webpack_require__(13);exports.hookBeforeAndAfter=hookBeforeAndAfter},function(module,exports,__webpack_require__){"use strict";var __createBinding=this&&this.__createBinding||(Object.create?function(o,m,k,k2){void 0===k2&&(k2=k),Object.defineProperty(o,k2,{enumerable:!0,get:function(){return m[k]}})}:function(o,m,k,k2){void 0===k2&&(k2=k),o[k2]=m[k]}),__exportStar=this&&this.__exportStar||function(m,exports){for(var p in m)"default"===p||exports.hasOwnProperty(p)||__createBinding(exports,m,p)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.ApiHandler=void 0;var ApiHandler=function(){function ApiHandler(){var _this=this;this.middlewares=[],this.use=function(fn){_this.middlewares.push(fn)},this.start=function(context){var fnList=_this.middlewares.slice().reverse(),nextFn=function(i){return i<fnList.length?function(){return fnList[i](context,nextFn(i+1))}:function(){}};return nextFn(0)()}}return ApiHandler}();exports.ApiHandler=ApiHandler,__exportStar(__webpack_require__(470),exports),__exportStar(__webpack_require__(476),exports),__exportStar(__webpack_require__(472),exports),__exportStar(__webpack_require__(471),exports),__exportStar(__webpack_require__(475),exports),__exportStar(__webpack_require__(473),exports),__exportStar(__webpack_require__(477),exports)},function(module,exports,__webpack_require__){"use strict";function initBridge(context,next){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(_a){return[2,this.bridgeInitFn().then(function(JSBridge){return context.JSBridge=JSBridge,next()})]})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.initBridge=void 0,exports.initBridge=initBridge},function(module,exports,__webpack_require__){"use strict";function retry(context,next){return __awaiter(this,void 0,void 0,function(){var err_1,method,isAuthApi,apiConfig,isConfiging,isNoPermissionErr,isCancelErr,targetVersionForSupport,isSupportByStaticData,mobileJudgeIsRetry,pcJudgeIsRetry;return __generator(this,function(_a){switch(_a.label){case 0:return _a.trys.push([0,2,,3]),[4,next()];case 1:return[2,_a.sent()];case 2:return err_1=_a.sent(),method=context.method,isAuthApi=context.isAuthApi,apiConfig=context.apiConfig,isConfiging=this.hadConfig&&void 0===this.isReady&&-1!==this.configJsApiList.indexOf(method),isNoPermissionErr="object"==typeof err_1&&"string"==typeof err_1.errorCode&&err_1.errorCode===sdkLib_1.ERROR_CODE.no_permission,isCancelErr="object"==typeof err_1&&"string"==typeof err_1.errorCode&&err_1.errorCode===sdkLib_1.ERROR_CODE.cancel,targetVersionForSupport=__1.getTargetApiConfigVS(apiConfig,this.env),isSupportByStaticData=targetVersionForSupport&&this.env.version&&sdkLib_1.compareVersion(this.env.version,targetVersionForSupport),mobileJudgeIsRetry=(this.env.platform===sdkLib_1.ENV_ENUM.ios||this.env.platform===sdkLib_1.ENV_ENUM.android)&&isConfiging&&isNoPermissionErr,pcJudgeIsRetry=this.env.platform===sdkLib_1.ENV_ENUM.pc&&isConfiging&&(isSupportByStaticData&&!isCancelErr&&isAuthApi||isNoPermissionErr),mobileJudgeIsRetry||pcJudgeIsRetry?[2,this.config$.then(function(){return next()})]:[2,Promise.reject(err_1)];case 3:return[2]}})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.retry=void 0;var __1=__webpack_require__(3),sdkLib_1=__webpack_require__(14);exports.retry=retry},function(module,exports,__webpack_require__){"use strict";function simpleLogger(context,next){return __awaiter(this,void 0,void 0,function(){var method,params,res,err,success,e_1,payload,level,status;return __generator(this,function(_a){switch(_a.label){case 0:method=context.method,params=context.params,success=!0,_a.label=1;case 1:return _a.trys.push([1,3,,4]),[4,next()];case 2:return res=_a.sent(),[3,4];case 3:return e_1=_a.sent(),err=e_1,success=!1,[3,4];case 4:return payload=success?res:err,level=success?__1.LogLevel.INFO:__1.LogLevel.WARNING,status=success?"success":"fail",[2,success?Promise.resolve(payload):Promise.reject(payload)]}})})}var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator.throw(value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})},__generator=this&&this.__generator||function(thisArg,body){function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");for(;_;)try{if(f=1,y&&(t=2&op[0]?y.return:op[0]?y.throw||((t=y.return)&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;switch(y=0,t&&(op=[2&op[0],t.value]),op[0]){case 0:case 1:t=op;break;case 4:return _.label++,{value:op[1],done:!1};case 5:_.label++,y=op[1],op=[0];continue;case 7:op=_.ops.pop(),_.trys.pop();continue;default:if(t=_.trys,!(t=t.length>0&&t[t.length-1])&&(6===op[0]||2===op[0])){_=0;continue}if(3===op[0]&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(6===op[0]&&_.label<t[1]){_.label=t[1],t=op;break}if(t&&_.label<t[2]){_.label=t[2],_.ops.push(op);break}t[2]&&_.ops.pop(),_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e],y=0}finally{f=t=0}if(5&op[0])throw op[1];return{value:op[0]?op[1]:void 0,done:!0}}var f,y,t,g,_={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return g={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g};Object.defineProperty(exports,"__esModule",{value:!0}),exports.simpleLogger=void 0;var __1=__webpack_require__(3);exports.simpleLogger=simpleLogger},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),__webpack_require__(479),__webpack_require__(207),__webpack_require__(209),__webpack_require__(208)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var ddSdk_1=__webpack_require__(0),env_1=__webpack_require__(4),h5Pc_1=__webpack_require__(460),eapp_1=__webpack_require__(7),sdk_1=__webpack_require__(3),h5PcEvent_1=__webpack_require__(461),ApiMapping=__webpack_require__(5);ddSdk_1.ddSdk.setPlatform({platform:env_1.ENV_ENUM.pc,bridgeInit:function(){switch(env_1.getENV().appType){case sdk_1.APP_TYPE.MINI_APP:return Promise.resolve(eapp_1.default);default:return h5Pc_1.h5PcBridgeInit().then(function(){return h5Pc_1.default})}},authMethod:"config",authParamsDeal:function(params){var finalParams=Object.assign({},params);return params.jsApiList&&(finalParams.jsApiList=params.jsApiList.map(function(apiName){return ApiMapping[apiName]?ApiMapping[apiName]:apiName})),finalParams.url=window.location.href.split("#")[0],finalParams},event:{on:function(type,handler){if(env_1.getENV().appType===sdk_1.APP_TYPE.WEB)return h5PcEvent_1.on(type,handler)},off:function(type,handler){if(env_1.getENV().appType===sdk_1.APP_TYPE.WEB)return h5PcEvent_1.off(type,handler)}}})},function(module,exports,__webpack_require__){"use strict";function addMembers(args){var _a;return mobile_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(_a=null===args||void 0===args?void 0:args.context)||void 0===_a?void 0:_a.corpId)+"#/add-members?params="+encodeURIComponent(JSON.stringify(args)),target:"float",title:"提示",wnId:"addMembers",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.addMembers=void 0,__webpack_require__(2);var mobile_1=__webpack_require__(6);exports.addMembers=addMembers},function(module,exports,__webpack_require__){"use strict";function batchInstallCoolApp(args){var options=Object.assign({},args,{isBatchApi:!0});return mobile_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/"+(utils_1.isMobile?"mob":"index")+".html?scene=addCoolAppToGroup&params="+encodeURIComponent(JSON.stringify(options)),target:utils_1.isMobile?"":"float",title:"选择会话添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.batchInstallCoolApp=void 0,__webpack_require__(2);var mobile_1=__webpack_require__(6),utils_1=__webpack_require__(210);exports.batchInstallCoolApp=batchInstallCoolApp},function(module,exports,__webpack_require__){"use strict";function createGroup(args){var _a;return union_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(_a=null===args||void 0===args?void 0:args.context)||void 0===_a?void 0:_a.corpId)+"#/create-group?params="+encodeURIComponent(JSON.stringify(args)),target:"float",title:"提示",wnId:"createGroup",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.createGroup=void 0,__webpack_require__(2);var union_1=__webpack_require__(2);exports.createGroup=createGroup},function(module,exports,__webpack_require__){"use strict";var __createBinding=this&&this.__createBinding||(Object.create?function(o,m,k,k2){void 0===k2&&(k2=k),Object.defineProperty(o,k2,{enumerable:!0,get:function(){return m[k]}})}:function(o,m,k,k2){void 0===k2&&(k2=k),o[k2]=m[k]}),__exportStar=this&&this.__exportStar||function(m,exports){for(var p in m)"default"===p||exports.hasOwnProperty(p)||__createBinding(exports,m,p)};Object.defineProperty(exports,"__esModule",{value:!0}),__exportStar(__webpack_require__(484),exports),__exportStar(__webpack_require__(485),exports),__exportStar(__webpack_require__(482),exports),__exportStar(__webpack_require__(480),exports),__exportStar(__webpack_require__(486),exports),__exportStar(__webpack_require__(481),exports)},function(module,exports,__webpack_require__){"use strict";function installCoolAppToGroup(args){return mobile_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/"+(utils_1.isMobile?"mob":"index")+".html?scene=addCoolAppToGroup&params="+encodeURIComponent(JSON.stringify(args)),target:utils_1.isMobile?"":"float",title:"选择群添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.installCoolAppToGroup=void 0,__webpack_require__(2);var mobile_1=__webpack_require__(6),utils_1=__webpack_require__(210);exports.installCoolAppToGroup=installCoolAppToGroup},function(module,exports,__webpack_require__){"use strict";function sendMessageToGroup(args){var _a,length=JSON.stringify(args).length;return mobile_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(_a=null===args||void 0===args?void 0:args.context)||void 0===_a?void 0:_a.corpId)+"#/send-message?params="+encodeURIComponent(JSON.stringify({body:args,bodyLengthList:[length]})),target:"float",title:"提示",wnId:"sendMessageToGroup",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.sendMessageToGroup=void 0,__webpack_require__(2);var mobile_1=__webpack_require__(6);exports.sendMessageToGroup=sendMessageToGroup},function(module,exports,__webpack_require__){"use strict";function sendMessageToSingleChat(args){var _a,length=JSON.stringify(args).length;return union_1._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(_a=null===args||void 0===args?void 0:args.context)||void 0===_a?void 0:_a.corpId)+"#/send-message-to-single-chat?params="+encodeURIComponent(JSON.stringify({body:args,bodyLengthList:[length]})),target:"float",title:"提示",wnId:"sendMessageToSingleChat",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.sendMessageToSingleChat=void 0,__webpack_require__(2);var union_1=__webpack_require__(2);exports.sendMessageToSingleChat=sendMessageToSingleChat},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.coolAppSdk=void 0;var coolAppSdk=__webpack_require__(483);exports.coolAppSdk=coolAppSdk},function(module,exports,__webpack_require__){(function(setImmediate,global){(function(global,factory){factory()})(0,function(){"use strict";function noop(){}function bind(fn,thisArg){return function(){fn.apply(thisArg,arguments)}}function Promise(fn){if(!(this instanceof Promise))throw new TypeError("Promises must be constructed via new");if("function"!=typeof fn)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],doResolve(fn,this)}function handle(self,deferred){for(;3===self._state;)self=self._value;if(0===self._state)return void self._deferreds.push(deferred);self._handled=!0,Promise._immediateFn(function(){var cb=1===self._state?deferred.onFulfilled:deferred.onRejected;if(null===cb)return void(1===self._state?resolve:reject)(deferred.promise,self._value);var ret;try{ret=cb(self._value)}catch(e){return void reject(deferred.promise,e)}resolve(deferred.promise,ret)})}function resolve(self,newValue){try{if(newValue===self)throw new TypeError("A promise cannot be resolved with itself.");if(newValue&&("object"==typeof newValue||"function"==typeof newValue)){var then=newValue.then;if(newValue instanceof Promise)return self._state=3,self._value=newValue,void finale(self);if("function"==typeof then)return void doResolve(bind(then,newValue),self)}self._state=1,self._value=newValue,finale(self)}catch(e){reject(self,e)}}function reject(self,newValue){self._state=2,self._value=newValue,finale(self)}function finale(self){2===self._state&&0===self._deferreds.length&&Promise._immediateFn(function(){self._handled||Promise._unhandledRejectionFn(self._value)});for(var i=0,len=self._deferreds.length;i<len;i++)handle(self,self._deferreds[i]);self._deferreds=null}function Handler(onFulfilled,onRejected,promise){this.onFulfilled="function"==typeof onFulfilled?onFulfilled:null,this.onRejected="function"==typeof onRejected?onRejected:null,this.promise=promise}function doResolve(fn,self){var done=!1;try{fn(function(value){done||(done=!0,resolve(self,value))},function(reason){done||(done=!0,reject(self,reason))})}catch(ex){if(done)return;done=!0,reject(self,ex)}}var setTimeoutFunc=setTimeout;Promise.prototype.catch=function(onRejected){return this.then(null,onRejected)},Promise.prototype.then=function(onFulfilled,onRejected){var prom=new this.constructor(noop);return handle(this,new Handler(onFulfilled,onRejected,prom)),prom},Promise.prototype.finally=function(callback){var constructor=this.constructor;return this.then(function(value){return constructor.resolve(callback()).then(function(){return value})},function(reason){return constructor.resolve(callback()).then(function(){return constructor.reject(reason)})})},Promise.all=function(arr){return new Promise(function(resolve,reject){function res(i,val){try{if(val&&("object"==typeof val||"function"==typeof val)){var then=val.then;if("function"==typeof then)return void then.call(val,function(val){res(i,val)},reject)}args[i]=val,0==--remaining&&resolve(args)}catch(ex){reject(ex)}}if(!arr||void 0===arr.length)throw new TypeError("Promise.all accepts an array");var args=Array.prototype.slice.call(arr);if(0===args.length)return resolve([]);for(var remaining=args.length,i=0;i<args.length;i++)res(i,args[i])})},Promise.resolve=function(value){return value&&"object"==typeof value&&value.constructor===Promise?value:new Promise(function(resolve){resolve(value)})},Promise.reject=function(value){return new Promise(function(resolve,reject){reject(value)})},Promise.race=function(values){return new Promise(function(resolve,reject){for(var i=0,len=values.length;i<len;i++)values[i].then(resolve,reject)})},Promise._immediateFn="function"==typeof setImmediate&&function(fn){setImmediate(fn)}||function(fn){setTimeoutFunc(fn,0)},Promise._unhandledRejectionFn=function(err){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",err)};var globalNS=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==global)return global;throw new Error("unable to locate global object")}();globalNS.Promise||(globalNS.Promise=Promise)})}).call(exports,__webpack_require__(490).setImmediate,__webpack_require__(15))},function(module,exports,__webpack_require__){(function(global,process){(function(global,undefined){"use strict";function setImmediate(callback){"function"!=typeof callback&&(callback=new Function(""+callback));for(var args=new Array(arguments.length-1),i=0;i<args.length;i++)args[i]=arguments[i+1];var task={callback:callback,args:args};return tasksByHandle[nextHandle]=task,registerImmediate(nextHandle),nextHandle++}function clearImmediate(handle){delete tasksByHandle[handle]}function run(task){var callback=task.callback,args=task.args;switch(args.length){case 0:callback();break;case 1:callback(args[0]);break;case 2:callback(args[0],args[1]);break;case 3:callback(args[0],args[1],args[2]);break;default:callback.apply(undefined,args)}}function runIfPresent(handle){if(currentlyRunningATask)setTimeout(runIfPresent,0,handle);else{var task=tasksByHandle[handle];if(task){currentlyRunningATask=!0;try{run(task)}finally{clearImmediate(handle),currentlyRunningATask=!1}}}}if(!global.setImmediate){var registerImmediate,nextHandle=1,tasksByHandle={},currentlyRunningATask=!1,doc=global.document,attachTo=Object.getPrototypeOf&&Object.getPrototypeOf(global);attachTo=attachTo&&attachTo.setTimeout?attachTo:global,"[object process]"==={}.toString.call(global.process)?function(){registerImmediate=function(handle){process.nextTick(function(){runIfPresent(handle)})}}():!function(){if(global.postMessage&&!global.importScripts){var postMessageIsAsynchronous=!0,oldOnMessage=global.onmessage;return global.onmessage=function(){postMessageIsAsynchronous=!1},global.postMessage("","*"),global.onmessage=oldOnMessage,postMessageIsAsynchronous}}()?global.MessageChannel?function(){var channel=new MessageChannel;channel.port1.onmessage=function(event){runIfPresent(event.data)},registerImmediate=function(handle){channel.port2.postMessage(handle)}}():doc&&"onreadystatechange"in doc.createElement("script")?function(){var html=doc.documentElement;registerImmediate=function(handle){var script=doc.createElement("script");script.onreadystatechange=function(){runIfPresent(handle),script.onreadystatechange=null,html.removeChild(script),script=null},html.appendChild(script)}}():function(){registerImmediate=function(handle){setTimeout(runIfPresent,0,handle)}}():function(){var messagePrefix="setImmediate$"+Math.random()+"$",onGlobalMessage=function(event){event.source===global&&"string"==typeof event.data&&0===event.data.indexOf(messagePrefix)&&runIfPresent(+event.data.slice(messagePrefix.length))};global.addEventListener?global.addEventListener("message",onGlobalMessage,!1):global.attachEvent("onmessage",onGlobalMessage),registerImmediate=function(handle){global.postMessage(messagePrefix+handle,"*")}}(),attachTo.setImmediate=setImmediate,attachTo.clearImmediate=clearImmediate}})("undefined"==typeof self?void 0===global?this:global:self)}).call(exports,__webpack_require__(15),__webpack_require__(211))},function(module,exports,__webpack_require__){(function(global){function Timeout(id,clearFn){this._id=id,this._clearFn=clearFn}var scope=void 0!==global&&global||"undefined"!=typeof self&&self||window,apply=Function.prototype.apply;exports.setTimeout=function(){return new Timeout(apply.call(setTimeout,scope,arguments),clearTimeout)},exports.setInterval=function(){return new Timeout(apply.call(setInterval,scope,arguments),clearInterval)},exports.clearTimeout=exports.clearInterval=function(timeout){timeout&&timeout.close()},Timeout.prototype.unref=Timeout.prototype.ref=function(){},Timeout.prototype.close=function(){this._clearFn.call(scope,this._id)},exports.enroll=function(item,msecs){clearTimeout(item._idleTimeoutId),item._idleTimeout=msecs},exports.unenroll=function(item){clearTimeout(item._idleTimeoutId),item._idleTimeout=-1},exports._unrefActive=exports.active=function(item){clearTimeout(item._idleTimeoutId);var msecs=item._idleTimeout;msecs>=0&&(item._idleTimeoutId=setTimeout(function(){item._onTimeout&&item._onTimeout()},msecs))},__webpack_require__(489),exports.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==global&&global.setImmediate||this&&this.setImmediate,exports.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==global&&global.clearImmediate||this&&this.clearImmediate}).call(exports,__webpack_require__(15))},,,,,,,,,,,function(module,exports,__webpack_require__){"use strict";var ddWithoutApi=__webpack_require__(2),openApiObj_1=__webpack_require__(1110),plugin=__webpack_require__(487),dd=Object.assign(ddWithoutApi,openApiObj_1.apiObj,{plugin:plugin});module.exports=dd},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.apiObj=void 0;var beaconPicker_1=__webpack_require__(212),detectFace_1=__webpack_require__(213),detectFaceFullScreen_1=__webpack_require__(214),exclusiveLiveCheck_1=__webpack_require__(215),faceManager_1=__webpack_require__(216),punchModePicker_1=__webpack_require__(217),bindAlipay_1=__webpack_require__(218),openAuth_1=__webpack_require__(219),pay_1=__webpack_require__(220),getLBSWua_1=__webpack_require__(221),openAccountPwdLoginPage_1=__webpack_require__(222),requestAuthInfo_1=__webpack_require__(223),chooseDateTime_1=__webpack_require__(224),chooseHalfDay_1=__webpack_require__(225),chooseInterval_1=__webpack_require__(226),chooseOneDay_1=__webpack_require__(227),chooseConversationByCorpId_1=__webpack_require__(228),collectSticker_1=__webpack_require__(229),createSceneGroup_1=__webpack_require__(230),getRealmCid_1=__webpack_require__(231),locationChatMessage_1=__webpack_require__(232),openSingleChat_1=__webpack_require__(233),pickConversation_1=__webpack_require__(234),sendEmotion_1=__webpack_require__(235),toConversation_1=__webpack_require__(236),toConversationByOpenConversationId_1=__webpack_require__(237),setData_1=__webpack_require__(238),createCloudCall_1=__webpack_require__(239),getCloudCallInfo_1=__webpack_require__(240),getCloudCallList_1=__webpack_require__(241),videoConfCall_1=__webpack_require__(242),choose_1=__webpack_require__(243),chooseMobileContacts_1=__webpack_require__(244),complexPicker_1=__webpack_require__(245),createGroup_1=__webpack_require__(246),departmentsPicker_1=__webpack_require__(247),externalComplexPicker_1=__webpack_require__(248),externalEditForm_1=__webpack_require__(249),rolesPicker_1=__webpack_require__(250),setRule_1=__webpack_require__(251),chooseSpaceDir_1=__webpack_require__(252),delete_1=__webpack_require__(253),preview_1=__webpack_require__(254),previewDentryImages_1=__webpack_require__(255),saveFile_1=__webpack_require__(256),choose_2=__webpack_require__(257),multipleChoose_1=__webpack_require__(258),rsa_1=__webpack_require__(259),create_1=__webpack_require__(260),post_1=__webpack_require__(261),finishMiniCourseByRecordId_1=__webpack_require__(262),getMiniCourseDraftList_1=__webpack_require__(263),joinClassroom_1=__webpack_require__(264),makeMiniCourse_1=__webpack_require__(265),newMsgNotificationStatus_1=__webpack_require__(266),startAuth_1=__webpack_require__(267),tokenFaceImg_1=__webpack_require__(268),notifyWeex_1=__webpack_require__(269),downloadFile_1=__webpack_require__(270),fetchData_1=__webpack_require__(271),bind_1=__webpack_require__(272),bindMeetingRoom_1=__webpack_require__(273),getDeviceProperties_1=__webpack_require__(274),invokeThingService_1=__webpack_require__(275),queryMeetingRoomList_1=__webpack_require__(276),setDeviceProperties_1=__webpack_require__(277),unbind_1=__webpack_require__(278),startClassRoom_1=__webpack_require__(279),startUnifiedLive_1=__webpack_require__(280),locate_1=__webpack_require__(281),search_1=__webpack_require__(282),view_1=__webpack_require__(283),compressVideo_1=__webpack_require__(284),openApp_1=__webpack_require__(285),close_1=__webpack_require__(286),goBack_1=__webpack_require__(287),hideBar_1=__webpack_require__(288),navigateBackPage_1=__webpack_require__(289),navigateToMiniProgram_1=__webpack_require__(290),navigateToPage_1=__webpack_require__(291),quit_1=__webpack_require__(292),replace_1=__webpack_require__(293),setIcon_1=__webpack_require__(294),setLeft_1=__webpack_require__(295),setMenu_1=__webpack_require__(296),setRight_1=__webpack_require__(297),setTitle_1=__webpack_require__(298),componentPunchFromPartner_1=__webpack_require__(299),startMatchRuleFromPartner_1=__webpack_require__(300),stopMatchRuleFromPartner_1=__webpack_require__(301),add_1=__webpack_require__(302),getRealtimeTracingStatus_1=__webpack_require__(303),getUserExclusiveInfo_1=__webpack_require__(304),startRealtimeTracing_1=__webpack_require__(305),stopRealtimeTracing_1=__webpack_require__(306),subscribe_1=__webpack_require__(307),unsubscribe_1=__webpack_require__(308),getInfo_1=__webpack_require__(309),reportDebugMessage_1=__webpack_require__(310),addShortCut_1=__webpack_require__(311),getHealthAuthorizationStatus_1=__webpack_require__(312),getHealthData_1=__webpack_require__(313),getHealthDeviceData_1=__webpack_require__(314),requestHealthAuthorization_1=__webpack_require__(315),closeUnpayOrder_1=__webpack_require__(316),createOrder_1=__webpack_require__(317),getPayUrl_1=__webpack_require__(318),inquiry_1=__webpack_require__(319),isTab_1=__webpack_require__(320),call_1=__webpack_require__(321),checkBizCall_1=__webpack_require__(322),quickCallList_1=__webpack_require__(323),showCallMenu_1=__webpack_require__(324),checkPassword_1=__webpack_require__(325),get_1=__webpack_require__(326),callComponent_1=__webpack_require__(327),checkAuth_1=__webpack_require__(328),chooseImage_1=__webpack_require__(329),chooseRegion_1=__webpack_require__(330),chosen_1=__webpack_require__(331),clearWebStoreCache_1=__webpack_require__(332),closePreviewImage_1=__webpack_require__(333),compressImage_1=__webpack_require__(334),datepicker_1=__webpack_require__(335),datetimepicker_1=__webpack_require__(336),decrypt_1=__webpack_require__(337),downloadFile_2=__webpack_require__(338),encrypt_1=__webpack_require__(339),getPerfInfo_1=__webpack_require__(340),invokeWorkbench_1=__webpack_require__(341),isEnableGPUAcceleration_1=__webpack_require__(342),isLocalFileExist_1=__webpack_require__(343),multiSelect_1=__webpack_require__(344),open_1=__webpack_require__(345),openBrowser_1=__webpack_require__(346),openDocument_1=__webpack_require__(347),openLink_1=__webpack_require__(348),openLocalFile_1=__webpack_require__(349),openModal_1=__webpack_require__(350),openSlidePanel_1=__webpack_require__(351),presentWindow_1=__webpack_require__(352),previewImage_1=__webpack_require__(353),previewVideo_1=__webpack_require__(354),saveImage_1=__webpack_require__(355),saveImageToPhotosAlbum_1=__webpack_require__(356),scan_1=__webpack_require__(357),scanCard_1=__webpack_require__(358),setGPUAcceleration_1=__webpack_require__(359),setScreenBrightnessAndKeepOn_1=__webpack_require__(360),setScreenKeepOn_1=__webpack_require__(361),share_1=__webpack_require__(362),shareImage_1=__webpack_require__(363),showAuthGuide_1=__webpack_require__(364),showSharePanel_1=__webpack_require__(365),startDocSign_1=__webpack_require__(366),systemShare_1=__webpack_require__(367),timepicker_1=__webpack_require__(368),uploadAttachment_1=__webpack_require__(369),uploadFile_1=__webpack_require__(370),uploadImage_1=__webpack_require__(371),uploadImageFromCamera_1=__webpack_require__(372),ut_1=__webpack_require__(373),openBindIDCard_1=__webpack_require__(374),startAuth_2=__webpack_require__(375),makeCall_1=__webpack_require__(376),getWatermarkInfo_1=__webpack_require__(377),setWatermarkInfo_1=__webpack_require__(378),requestAuthCode_1=__webpack_require__(379),clearShake_1=__webpack_require__(380),watchShake_1=__webpack_require__(381),download_1=__webpack_require__(382),onPlayEnd_1=__webpack_require__(383),onRecordEnd_1=__webpack_require__(384),pause_1=__webpack_require__(385),play_1=__webpack_require__(386),resume_1=__webpack_require__(387),startRecord_1=__webpack_require__(388),stop_1=__webpack_require__(389),stopRecord_1=__webpack_require__(390),translateVoice_1=__webpack_require__(391),getBatteryInfo_1=__webpack_require__(392),getInterface_1=__webpack_require__(393),getPhoneInfo_1=__webpack_require__(394),getScanWifiListAsync_1=__webpack_require__(395),getUUID_1=__webpack_require__(396),getWifiStatus_1=__webpack_require__(397),openSystemSetting_1=__webpack_require__(398),getNetworkType_1=__webpack_require__(399),checkPermission_1=__webpack_require__(400),get_2=__webpack_require__(401),start_1=__webpack_require__(402),status_1=__webpack_require__(403),stop_2=__webpack_require__(404),checkInstalledApps_1=__webpack_require__(405),launchApp_1=__webpack_require__(406),nfcRead_1=__webpack_require__(407),nfcStop_1=__webpack_require__(408),nfcWrite_1=__webpack_require__(409),actionSheet_1=__webpack_require__(410),alert_1=__webpack_require__(411),confirm_1=__webpack_require__(412),extendModal_1=__webpack_require__(413),hidePreloader_1=__webpack_require__(414),modal_1=__webpack_require__(415),prompt_1=__webpack_require__(416),showPreloader_1=__webpack_require__(417),toast_1=__webpack_require__(418),vibrate_1=__webpack_require__(419),getScreenBrightness_1=__webpack_require__(420),insetAdjust_1=__webpack_require__(421),isScreenReaderEnabled_1=__webpack_require__(422),resetView_1=__webpack_require__(423),rotateView_1=__webpack_require__(424),setScreenBrightness_1=__webpack_require__(425),keepAlive_1=__webpack_require__(426),pause_2=__webpack_require__(427),resume_2=__webpack_require__(428),start_2=__webpack_require__(429),stop_3=__webpack_require__(430),loginGovNet_1=__webpack_require__(431),exec_1=__webpack_require__(432),fetch_1=__webpack_require__(433),post_2=__webpack_require__(434),getLoadTime_1=__webpack_require__(435),requestAuthCode_2=__webpack_require__(436),requestOperateAuthCode_1=__webpack_require__(437),plain_1=__webpack_require__(438),addToFloat_1=__webpack_require__(439),removeFromFloat_1=__webpack_require__(440),close_2=__webpack_require__(441),getCurrentId_1=__webpack_require__(442),go_1=__webpack_require__(443),preload_1=__webpack_require__(444),recycle_1=__webpack_require__(445),setColors_1=__webpack_require__(446),disable_1=__webpack_require__(447),enable_1=__webpack_require__(448),stop_4=__webpack_require__(449),disable_2=__webpack_require__(450),enable_2=__webpack_require__(451),ExternalChannelPublish_1=__webpack_require__(16),ExternalChannelPublish_2=__webpack_require__(16);Object.defineProperty(exports,"ExternalChannelPublish",{enumerable:!0,get:function(){return ExternalChannelPublish_2.ExternalChannelPublish$}});var addPhoneContact_1=__webpack_require__(17),addPhoneContact_2=__webpack_require__(17);Object.defineProperty(exports,"addPhoneContact",{enumerable:!0,get:function(){return addPhoneContact_2.addPhoneContact$}});var alert_2=__webpack_require__(18),alert_3=__webpack_require__(18);Object.defineProperty(exports,"alert",{enumerable:!0,get:function(){return alert_3.alert$}});var callUsers_1=__webpack_require__(19),callUsers_2=__webpack_require__(19);Object.defineProperty(exports,"callUsers",{enumerable:!0,get:function(){return callUsers_2.callUsers$}});var checkAuth_2=__webpack_require__(20),checkAuth_3=__webpack_require__(20);Object.defineProperty(exports,"checkAuth",{enumerable:!0,get:function(){return checkAuth_3.checkAuth$}});var checkBizCall_2=__webpack_require__(21),checkBizCall_3=__webpack_require__(21);Object.defineProperty(exports,"checkBizCall",{enumerable:!0,get:function(){return checkBizCall_3.checkBizCall$}});var chooseChat_1=__webpack_require__(22),chooseChat_2=__webpack_require__(22);Object.defineProperty(exports,"chooseChat",{enumerable:!0,get:function(){return chooseChat_2.chooseChat$}});var chooseConversation_1=__webpack_require__(23),chooseConversation_2=__webpack_require__(23);Object.defineProperty(exports,"chooseConversation",{enumerable:!0,get:function(){return chooseConversation_2.chooseConversation$}});var chooseDateRangeInCalendar_1=__webpack_require__(24),chooseDateRangeInCalendar_2=__webpack_require__(24);Object.defineProperty(exports,"chooseDateRangeInCalendar",{enumerable:!0,get:function(){return chooseDateRangeInCalendar_2.chooseDateRangeInCalendar$}});var chooseDateTime_2=__webpack_require__(25),chooseDateTime_3=__webpack_require__(25);Object.defineProperty(exports,"chooseDateTime",{enumerable:!0,get:function(){return chooseDateTime_3.chooseDateTime$}});var chooseDepartments_1=__webpack_require__(26),chooseDepartments_2=__webpack_require__(26);Object.defineProperty(exports,"chooseDepartments",{enumerable:!0,get:function(){return chooseDepartments_2.chooseDepartments$}});var chooseDingTalkDir_1=__webpack_require__(27),chooseDingTalkDir_2=__webpack_require__(27);Object.defineProperty(exports,"chooseDingTalkDir",{enumerable:!0,get:function(){return chooseDingTalkDir_2.chooseDingTalkDir$}});var chooseDistrict_1=__webpack_require__(28),chooseDistrict_2=__webpack_require__(28);Object.defineProperty(exports,"chooseDistrict",{enumerable:!0,get:function(){return chooseDistrict_2.chooseDistrict$}});var chooseExternalUsers_1=__webpack_require__(29),chooseExternalUsers_2=__webpack_require__(29);Object.defineProperty(exports,"chooseExternalUsers",{enumerable:!0,get:function(){return chooseExternalUsers_2.chooseExternalUsers$}});var chooseFile_1=__webpack_require__(30),chooseFile_2=__webpack_require__(30);Object.defineProperty(exports,"chooseFile",{enumerable:!0,get:function(){return chooseFile_2.chooseFile$}});var chooseHalfDayInCalendar_1=__webpack_require__(31),chooseHalfDayInCalendar_2=__webpack_require__(31);Object.defineProperty(exports,"chooseHalfDayInCalendar",{enumerable:!0,get:function(){return chooseHalfDayInCalendar_2.chooseHalfDayInCalendar$}});var chooseImage_2=__webpack_require__(32),chooseImage_3=__webpack_require__(32);Object.defineProperty(exports,"chooseImage",{enumerable:!0,get:function(){return chooseImage_3.chooseImage$}});var chooseMedia_1=__webpack_require__(33),chooseMedia_2=__webpack_require__(33);Object.defineProperty(exports,"chooseMedia",{enumerable:!0,get:function(){return chooseMedia_2.chooseMedia$}});var chooseOneDayInCalendar_1=__webpack_require__(34),chooseOneDayInCalendar_2=__webpack_require__(34);Object.defineProperty(exports,"chooseOneDayInCalendar",{enumerable:!0,get:function(){return chooseOneDayInCalendar_2.chooseOneDayInCalendar$}});var choosePhonebook_1=__webpack_require__(35),choosePhonebook_2=__webpack_require__(35);Object.defineProperty(exports,"choosePhonebook",{enumerable:!0,get:function(){return choosePhonebook_2.choosePhonebook$}});var chooseStaffForPC_1=__webpack_require__(36),chooseStaffForPC_2=__webpack_require__(36);Object.defineProperty(exports,"chooseStaffForPC",{enumerable:!0,get:function(){return chooseStaffForPC_2.chooseStaffForPC$}});var chooseUserFromList_1=__webpack_require__(37),chooseUserFromList_2=__webpack_require__(37);Object.defineProperty(exports,"chooseUserFromList",{enumerable:!0,get:function(){return chooseUserFromList_2.chooseUserFromList$}});var clearShake_2=__webpack_require__(38),clearShake_3=__webpack_require__(38);Object.defineProperty(exports,"clearShake",{enumerable:!0,get:function(){return clearShake_3.clearShake$}});var closeBluetoothAdapter_1=__webpack_require__(39),closeBluetoothAdapter_2=__webpack_require__(39);Object.defineProperty(exports,"closeBluetoothAdapter",{enumerable:!0,get:function(){return closeBluetoothAdapter_2.closeBluetoothAdapter$}});var closePage_1=__webpack_require__(40),closePage_2=__webpack_require__(40);Object.defineProperty(exports,"closePage",{enumerable:!0,get:function(){return closePage_2.closePage$}});var complexChoose_1=__webpack_require__(41),complexChoose_2=__webpack_require__(41);Object.defineProperty(exports,"complexChoose",{enumerable:!0,get:function(){return complexChoose_2.complexChoose$}});var compressImage_2=__webpack_require__(42),compressImage_3=__webpack_require__(42);Object.defineProperty(exports,"compressImage",{enumerable:!0,get:function(){return compressImage_3.compressImage$}});var confirm_2=__webpack_require__(43),confirm_3=__webpack_require__(43);Object.defineProperty(exports,"confirm",{enumerable:!0,get:function(){return confirm_3.confirm$}});var connectBLEDevice_1=__webpack_require__(44),connectBLEDevice_2=__webpack_require__(44);Object.defineProperty(exports,"connectBLEDevice",{enumerable:!0,get:function(){return connectBLEDevice_2.connectBLEDevice$}});var createBLEPeripheralServer_1=__webpack_require__(45),createBLEPeripheralServer_2=__webpack_require__(45);Object.defineProperty(exports,"createBLEPeripheralServer",{enumerable:!0,get:function(){return createBLEPeripheralServer_2.createBLEPeripheralServer$}});var createDing_1=__webpack_require__(46),createDing_2=__webpack_require__(46);Object.defineProperty(exports,"createDing",{enumerable:!0,get:function(){return createDing_2.createDing$}});var createDingForPC_1=__webpack_require__(47),createDingForPC_2=__webpack_require__(47);Object.defineProperty(exports,"createDingForPC",{enumerable:!0,get:function(){return createDingForPC_2.createDingForPC$}});var createGroupChat_1=__webpack_require__(48),createGroupChat_2=__webpack_require__(48);Object.defineProperty(exports,"createGroupChat",{enumerable:!0,get:function(){return createGroupChat_2.createGroupChat$}});var createLiveClassRoom_1=__webpack_require__(49),createLiveClassRoom_2=__webpack_require__(49);Object.defineProperty(exports,"createLiveClassRoom",{enumerable:!0,get:function(){return createLiveClassRoom_2.createLiveClassRoom$}});var cropImage_1=__webpack_require__(50),cropImage_2=__webpack_require__(50);Object.defineProperty(exports,"cropImage",{enumerable:!0,get:function(){return cropImage_2.cropImage$}});var customChooseUsers_1=__webpack_require__(51),customChooseUsers_2=__webpack_require__(51);Object.defineProperty(exports,"customChooseUsers",{enumerable:!0,get:function(){return customChooseUsers_2.customChooseUsers$}});var datePicker_1=__webpack_require__(52),datePicker_2=__webpack_require__(52);Object.defineProperty(exports,"datePicker",{enumerable:!0,get:function(){return datePicker_2.datePicker$}});var dateRangePicker_1=__webpack_require__(53),dateRangePicker_2=__webpack_require__(53);Object.defineProperty(exports,"dateRangePicker",{enumerable:!0,get:function(){return dateRangePicker_2.dateRangePicker$}});var decrypt_2=__webpack_require__(54),decrypt_3=__webpack_require__(54);Object.defineProperty(exports,"decrypt",{enumerable:!0,get:function(){return decrypt_3.decrypt$}});var disablePullDownRefresh_1=__webpack_require__(55),disablePullDownRefresh_2=__webpack_require__(55);Object.defineProperty(exports,"disablePullDownRefresh",{enumerable:!0,get:function(){return disablePullDownRefresh_2.disablePullDownRefresh$}});var disableWebViewBounce_1=__webpack_require__(56),disableWebViewBounce_2=__webpack_require__(56);Object.defineProperty(exports,"disableWebViewBounce",{enumerable:!0,get:function(){return disableWebViewBounce_2.disableWebViewBounce$}});var disconnectBLEDevice_1=__webpack_require__(57),disconnectBLEDevice_2=__webpack_require__(57);Object.defineProperty(exports,"disconnectBLEDevice",{enumerable:!0,get:function(){return disconnectBLEDevice_2.disconnectBLEDevice$}});var downloadAudio_1=__webpack_require__(58),downloadAudio_2=__webpack_require__(58);Object.defineProperty(exports,"downloadAudio",{enumerable:!0,get:function(){return downloadAudio_2.downloadAudio$}});var downloadFile_3=__webpack_require__(59),downloadFile_4=__webpack_require__(59);Object.defineProperty(exports,"downloadFile",{enumerable:!0,get:function(){return downloadFile_4.downloadFile$}});var editExternalUser_1=__webpack_require__(60),editExternalUser_2=__webpack_require__(60);Object.defineProperty(exports,"editExternalUser",{enumerable:!0,get:function(){return editExternalUser_2.editExternalUser$}});var editPicture_1=__webpack_require__(61),editPicture_2=__webpack_require__(61);Object.defineProperty(exports,"editPicture",{enumerable:!0,get:function(){return editPicture_2.editPicture$}});var enablePullDownRefresh_1=__webpack_require__(62),enablePullDownRefresh_2=__webpack_require__(62);Object.defineProperty(exports,"enablePullDownRefresh",{enumerable:!0,get:function(){return enablePullDownRefresh_2.enablePullDownRefresh$}});var enableWebViewBounce_1=__webpack_require__(63),enableWebViewBounce_2=__webpack_require__(63);Object.defineProperty(exports,"enableWebViewBounce",{enumerable:!0,get:function(){return enableWebViewBounce_2.enableWebViewBounce$}});var encrypt_2=__webpack_require__(64),encrypt_3=__webpack_require__(64);Object.defineProperty(exports,"encrypt",{enumerable:!0,get:function(){return encrypt_3.encrypt$}});var exclusiveLiveCheck_2=__webpack_require__(65),exclusiveLiveCheck_3=__webpack_require__(65);Object.defineProperty(exports,"exclusiveLiveCheck",{enumerable:!0,get:function(){return exclusiveLiveCheck_3.exclusiveLiveCheck$}});var generateImageFromCode_1=__webpack_require__(66),generateImageFromCode_2=__webpack_require__(66);Object.defineProperty(exports,"generateImageFromCode",{enumerable:!0,get:function(){return generateImageFromCode_2.generateImageFromCode$}});var getAdvertisingStatus_1=__webpack_require__(67),getAdvertisingStatus_2=__webpack_require__(67);Object.defineProperty(exports,"getAdvertisingStatus",{enumerable:!0,get:function(){return getAdvertisingStatus_2.getAdvertisingStatus$}});var getAuthCode_1=__webpack_require__(68),getAuthCode_2=__webpack_require__(68);Object.defineProperty(exports,"getAuthCode",{enumerable:!0,get:function(){return getAuthCode_2.getAuthCode$}});var getAuthCodeV2_1=__webpack_require__(69),getAuthCodeV2_2=__webpack_require__(69);Object.defineProperty(exports,"getAuthCodeV2",{enumerable:!0,get:function(){return getAuthCodeV2_2.getAuthCodeV2$}});var getAuthInfo_1=__webpack_require__(70),getAuthInfo_2=__webpack_require__(70);Object.defineProperty(exports,"getAuthInfo",{enumerable:!0,get:function(){return getAuthInfo_2.getAuthInfo$}});var getBLEDeviceCharacteristics_1=__webpack_require__(71),getBLEDeviceCharacteristics_2=__webpack_require__(71);Object.defineProperty(exports,"getBLEDeviceCharacteristics",{enumerable:!0,get:function(){return getBLEDeviceCharacteristics_2.getBLEDeviceCharacteristics$}});var getBLEDeviceServices_1=__webpack_require__(72),getBLEDeviceServices_2=__webpack_require__(72);Object.defineProperty(exports,"getBLEDeviceServices",{enumerable:!0,get:function(){return getBLEDeviceServices_2.getBLEDeviceServices$}});var getBatteryInfo_2=__webpack_require__(73),getBatteryInfo_3=__webpack_require__(73);Object.defineProperty(exports,"getBatteryInfo",{enumerable:!0,get:function(){return getBatteryInfo_3.getBatteryInfo$}});var getBeacons_1=__webpack_require__(74),getBeacons_2=__webpack_require__(74);Object.defineProperty(exports,"getBeacons",{enumerable:!0,get:function(){return getBeacons_2.getBeacons$}});var getBluetoothAdapterState_1=__webpack_require__(75),getBluetoothAdapterState_2=__webpack_require__(75);Object.defineProperty(exports,"getBluetoothAdapterState",{enumerable:!0,get:function(){return getBluetoothAdapterState_2.getBluetoothAdapterState$}});var getBluetoothDevices_1=__webpack_require__(76),getBluetoothDevices_2=__webpack_require__(76);Object.defineProperty(exports,"getBluetoothDevices",{enumerable:!0,get:function(){return getBluetoothDevices_2.getBluetoothDevices$}});var getCloudCallInfo_2=__webpack_require__(77),getCloudCallInfo_3=__webpack_require__(77);Object.defineProperty(exports,"getCloudCallInfo",{enumerable:!0,get:function(){return getCloudCallInfo_3.getCloudCallInfo$}});var getCloudCallList_2=__webpack_require__(78),getCloudCallList_3=__webpack_require__(78);Object.defineProperty(exports,"getCloudCallList",{enumerable:!0,get:function(){return getCloudCallList_3.getCloudCallList$}});var getDeviceId_1=__webpack_require__(79),getDeviceId_2=__webpack_require__(79);Object.defineProperty(exports,"getDeviceId",{enumerable:!0,get:function(){return getDeviceId_2.getDeviceId$}});var getDeviceUUID_1=__webpack_require__(80),getDeviceUUID_2=__webpack_require__(80);Object.defineProperty(exports,"getDeviceUUID",{enumerable:!0,get:function(){return getDeviceUUID_2.getDeviceUUID$}});var getImageInfo_1=__webpack_require__(81),getImageInfo_2=__webpack_require__(81);Object.defineProperty(exports,"getImageInfo",{enumerable:!0,get:function(){return getImageInfo_2.getImageInfo$}});var getLocatingStatus_1=__webpack_require__(82),getLocatingStatus_2=__webpack_require__(82);Object.defineProperty(exports,"getLocatingStatus",{enumerable:!0,get:function(){return getLocatingStatus_2.getLocatingStatus$}});var getLocation_1=__webpack_require__(83),getLocation_2=__webpack_require__(83);Object.defineProperty(exports,"getLocation",{enumerable:!0,get:function(){return getLocation_2.getLocation$}});var getNetworkType_2=__webpack_require__(84),getNetworkType_3=__webpack_require__(84);Object.defineProperty(exports,"getNetworkType",{enumerable:!0,get:function(){return getNetworkType_3.getNetworkType$}});var getOperateAuthCode_1=__webpack_require__(85),getOperateAuthCode_2=__webpack_require__(85);Object.defineProperty(exports,"getOperateAuthCode",{enumerable:!0,get:function(){return getOperateAuthCode_2.getOperateAuthCode$}});var getScreenBrightness_2=__webpack_require__(86),getScreenBrightness_3=__webpack_require__(86);Object.defineProperty(exports,"getScreenBrightness",{enumerable:!0,get:function(){return getScreenBrightness_3.getScreenBrightness$}});var getStorage_1=__webpack_require__(87),getStorage_2=__webpack_require__(87);Object.defineProperty(exports,"getStorage",{enumerable:!0,get:function(){return getStorage_2.getStorage$}});var getSystemInfo_1=__webpack_require__(88),getSystemInfo_2=__webpack_require__(88);Object.defineProperty(exports,"getSystemInfo",{enumerable:!0,get:function(){return getSystemInfo_2.getSystemInfo$}});var getSystemSettings_1=__webpack_require__(89),getSystemSettings_2=__webpack_require__(89);Object.defineProperty(exports,"getSystemSettings",{enumerable:!0,get:function(){return getSystemSettings_2.getSystemSettings$}});var getThirdAppConfCustomData_1=__webpack_require__(90),getThirdAppConfCustomData_2=__webpack_require__(90);Object.defineProperty(exports,"getThirdAppConfCustomData",{enumerable:!0,get:function(){return getThirdAppConfCustomData_2.getThirdAppConfCustomData$}});var getThirdAppUserCustomData_1=__webpack_require__(91),getThirdAppUserCustomData_2=__webpack_require__(91);Object.defineProperty(exports,"getThirdAppUserCustomData",{enumerable:!0,get:function(){return getThirdAppUserCustomData_2.getThirdAppUserCustomData$}});var getTranslateStatus_1=__webpack_require__(92),getTranslateStatus_2=__webpack_require__(92);Object.defineProperty(exports,"getTranslateStatus",{enumerable:!0,get:function(){return getTranslateStatus_2.getTranslateStatus$}});var getUserExclusiveInfo_2=__webpack_require__(93),getUserExclusiveInfo_3=__webpack_require__(93);Object.defineProperty(exports,"getUserExclusiveInfo",{enumerable:!0,get:function(){return getUserExclusiveInfo_3.getUserExclusiveInfo$}});var getWifiHotspotStatus_1=__webpack_require__(94),getWifiHotspotStatus_2=__webpack_require__(94);Object.defineProperty(exports,"getWifiHotspotStatus",{enumerable:!0,get:function(){return getWifiHotspotStatus_2.getWifiHotspotStatus$}});var getWifiStatus_2=__webpack_require__(95),getWifiStatus_3=__webpack_require__(95);Object.defineProperty(exports,"getWifiStatus",{enumerable:!0,get:function(){return getWifiStatus_3.getWifiStatus$}});var goBackPage_1=__webpack_require__(96),goBackPage_2=__webpack_require__(96);Object.defineProperty(exports,"goBackPage",{enumerable:!0,get:function(){return goBackPage_2.goBackPage$}});var hideLoading_1=__webpack_require__(97),hideLoading_2=__webpack_require__(97);Object.defineProperty(exports,"hideLoading",{enumerable:!0,get:function(){return hideLoading_2.hideLoading$}});var hideToast_1=__webpack_require__(98),hideToast_2=__webpack_require__(98);Object.defineProperty(exports,"hideToast",{enumerable:!0,get:function(){return hideToast_2.hideToast$}});var isInTabWindow_1=__webpack_require__(99),isInTabWindow_2=__webpack_require__(99);Object.defineProperty(exports,"isInTabWindow",{enumerable:!0,get:function(){return isInTabWindow_2.isInTabWindow$}});var isLocalFileExist_2=__webpack_require__(100),isLocalFileExist_3=__webpack_require__(100);Object.defineProperty(exports,"isLocalFileExist",{enumerable:!0,get:function(){return isLocalFileExist_3.isLocalFileExist$}});var isScreenReaderEnabled_2=__webpack_require__(101),isScreenReaderEnabled_3=__webpack_require__(101);Object.defineProperty(exports,"isScreenReaderEnabled",{enumerable:!0,get:function(){return isScreenReaderEnabled_3.isScreenReaderEnabled$}});var locateInMap_1=__webpack_require__(102),locateInMap_2=__webpack_require__(102);Object.defineProperty(exports,"locateInMap",{enumerable:!0,get:function(){return locateInMap_2.locateInMap$}});var makeCloudCall_1=__webpack_require__(103),makeCloudCall_2=__webpack_require__(103);Object.defineProperty(exports,"makeCloudCall",{enumerable:!0,get:function(){return makeCloudCall_2.makeCloudCall$}});var makeVideoConfCall_1=__webpack_require__(104),makeVideoConfCall_2=__webpack_require__(104);Object.defineProperty(exports,"makeVideoConfCall",{enumerable:!0,get:function(){return makeVideoConfCall_2.makeVideoConfCall$}});var multiSelect_2=__webpack_require__(105),multiSelect_3=__webpack_require__(105);Object.defineProperty(exports,"multiSelect",{enumerable:!0,get:function(){return multiSelect_3.multiSelect$}});var navigateBackPage_2=__webpack_require__(106),navigateBackPage_3=__webpack_require__(106);Object.defineProperty(exports,"navigateBackPage",{enumerable:!0,get:function(){return navigateBackPage_3.navigateBackPage$}});var navigateToPage_2=__webpack_require__(107),navigateToPage_3=__webpack_require__(107);Object.defineProperty(exports,"navigateToPage",{enumerable:!0,get:function(){return navigateToPage_3.navigateToPage$}});var nfcReadCardNumber_1=__webpack_require__(108),nfcReadCardNumber_2=__webpack_require__(108);Object.defineProperty(exports,"nfcReadCardNumber",{enumerable:!0,get:function(){return nfcReadCardNumber_2.nfcReadCardNumber$}});var notifyBLECharacteristicValueChange_1=__webpack_require__(109),notifyBLECharacteristicValueChange_2=__webpack_require__(109);Object.defineProperty(exports,"notifyBLECharacteristicValueChange",{enumerable:!0,get:function(){return notifyBLECharacteristicValueChange_2.notifyBLECharacteristicValueChange$}});var notifyTranslateEvent_1=__webpack_require__(110),notifyTranslateEvent_2=__webpack_require__(110);Object.defineProperty(exports,"notifyTranslateEvent",{enumerable:!0,get:function(){return notifyTranslateEvent_2.notifyTranslateEvent$}});var offBLECharacteristicValueChange_1=__webpack_require__(111),offBLECharacteristicValueChange_2=__webpack_require__(111);Object.defineProperty(exports,"offBLECharacteristicValueChange",{enumerable:!0,get:function(){return offBLECharacteristicValueChange_2.offBLECharacteristicValueChange$}});var offBLEConnectionStateChanged_1=__webpack_require__(112),offBLEConnectionStateChanged_2=__webpack_require__(112);Object.defineProperty(exports,"offBLEConnectionStateChanged",{enumerable:!0,get:function(){return offBLEConnectionStateChanged_2.offBLEConnectionStateChanged$}});var offBluetoothAdapterStateChange_1=__webpack_require__(113),offBluetoothAdapterStateChange_2=__webpack_require__(113);Object.defineProperty(exports,"offBluetoothAdapterStateChange",{enumerable:!0,get:function(){return offBluetoothAdapterStateChange_2.offBluetoothAdapterStateChange$}});var offBluetoothDeviceFound_1=__webpack_require__(114),offBluetoothDeviceFound_2=__webpack_require__(114);Object.defineProperty(exports,"offBluetoothDeviceFound",{enumerable:!0,get:function(){return offBluetoothDeviceFound_2.offBluetoothDeviceFound$}});var onBLECharacteristicValueChange_1=__webpack_require__(115),onBLECharacteristicValueChange_2=__webpack_require__(115);Object.defineProperty(exports,"onBLECharacteristicValueChange",{enumerable:!0,get:function(){return onBLECharacteristicValueChange_2.onBLECharacteristicValueChange$}});var onBLEConnectionStateChanged_1=__webpack_require__(116),onBLEConnectionStateChanged_2=__webpack_require__(116);Object.defineProperty(exports,"onBLEConnectionStateChanged",{enumerable:!0,get:function(){return onBLEConnectionStateChanged_2.onBLEConnectionStateChanged$}});var onBLEPeripheralCharacteristicReadRequest_1=__webpack_require__(117),onBLEPeripheralCharacteristicReadRequest_2=__webpack_require__(117);Object.defineProperty(exports,"onBLEPeripheralCharacteristicReadRequest",{enumerable:!0,get:function(){return onBLEPeripheralCharacteristicReadRequest_2.onBLEPeripheralCharacteristicReadRequest$}});var onBLEPeripheralCharacteristicWriteRequest_1=__webpack_require__(118),onBLEPeripheralCharacteristicWriteRequest_2=__webpack_require__(118);Object.defineProperty(exports,"onBLEPeripheralCharacteristicWriteRequest",{enumerable:!0,get:function(){return onBLEPeripheralCharacteristicWriteRequest_2.onBLEPeripheralCharacteristicWriteRequest$}});var onBLEPeripheralConnectionStateChanged_1=__webpack_require__(119),onBLEPeripheralConnectionStateChanged_2=__webpack_require__(119);Object.defineProperty(exports,"onBLEPeripheralConnectionStateChanged",{enumerable:!0,get:function(){return onBLEPeripheralConnectionStateChanged_2.onBLEPeripheralConnectionStateChanged$}});var onBeaconServiceChange_1=__webpack_require__(120),onBeaconServiceChange_2=__webpack_require__(120);Object.defineProperty(exports,"onBeaconServiceChange",{enumerable:!0,get:function(){return onBeaconServiceChange_2.onBeaconServiceChange$}});var onBeaconUpdate_1=__webpack_require__(121),onBeaconUpdate_2=__webpack_require__(121);Object.defineProperty(exports,"onBeaconUpdate",{enumerable:!0,get:function(){return onBeaconUpdate_2.onBeaconUpdate$}});var onBluetoothAdapterStateChange_1=__webpack_require__(122),onBluetoothAdapterStateChange_2=__webpack_require__(122);Object.defineProperty(exports,"onBluetoothAdapterStateChange",{enumerable:!0,get:function(){return onBluetoothAdapterStateChange_2.onBluetoothAdapterStateChange$}});var onBluetoothDeviceFound_1=__webpack_require__(123),onBluetoothDeviceFound_2=__webpack_require__(123);Object.defineProperty(exports,"onBluetoothDeviceFound",{enumerable:!0,get:function(){return onBluetoothDeviceFound_2.onBluetoothDeviceFound$}});var onPlayAudioEnd_1=__webpack_require__(124),onPlayAudioEnd_2=__webpack_require__(124);Object.defineProperty(exports,"onPlayAudioEnd",{enumerable:!0,get:function(){return onPlayAudioEnd_2.onPlayAudioEnd$}});var onRecordEnd_2=__webpack_require__(125),onRecordEnd_3=__webpack_require__(125);Object.defineProperty(exports,"onRecordEnd",{enumerable:!0,get:function(){return onRecordEnd_3.onRecordEnd$}});var openBluetoothAdapter_1=__webpack_require__(126),openBluetoothAdapter_2=__webpack_require__(126);Object.defineProperty(exports,"openBluetoothAdapter",{enumerable:!0,get:function(){return openBluetoothAdapter_2.openBluetoothAdapter$}});var openChatByChatId_1=__webpack_require__(127),openChatByChatId_2=__webpack_require__(127);Object.defineProperty(exports,"openChatByChatId",{enumerable:!0,get:function(){return openChatByChatId_2.openChatByChatId$}});var openChatByConversationId_1=__webpack_require__(128),openChatByConversationId_2=__webpack_require__(128);Object.defineProperty(exports,"openChatByConversationId",{enumerable:!0,get:function(){return openChatByConversationId_2.openChatByConversationId$}});var openChatByUserId_1=__webpack_require__(129),openChatByUserId_2=__webpack_require__(129);Object.defineProperty(exports,"openChatByUserId",{enumerable:!0,get:function(){return openChatByUserId_2.openChatByUserId$}});var openDocument_2=__webpack_require__(130),openDocument_3=__webpack_require__(130);Object.defineProperty(exports,"openDocument",{enumerable:!0,get:function(){return openDocument_3.openDocument$}});var openLink_2=__webpack_require__(131),openLink_3=__webpack_require__(131);Object.defineProperty(exports,"openLink",{enumerable:!0,get:function(){return openLink_3.openLink$}});var openLocalFile_2=__webpack_require__(132),openLocalFile_3=__webpack_require__(132);Object.defineProperty(exports,"openLocalFile",{enumerable:!0,get:function(){return openLocalFile_3.openLocalFile$}});var openLocation_1=__webpack_require__(133),openLocation_2=__webpack_require__(133);Object.defineProperty(exports,"openLocation",{enumerable:!0,get:function(){return openLocation_2.openLocation$}});var openMicroApp_1=__webpack_require__(134),openMicroApp_2=__webpack_require__(134);Object.defineProperty(exports,"openMicroApp",{enumerable:!0,get:function(){return openMicroApp_2.openMicroApp$}});var openPageInMicroApp_1=__webpack_require__(135),openPageInMicroApp_2=__webpack_require__(135);Object.defineProperty(exports,"openPageInMicroApp",{enumerable:!0,get:function(){return openPageInMicroApp_2.openPageInMicroApp$}});var openPageInModalForPC_1=__webpack_require__(136),openPageInModalForPC_2=__webpack_require__(136);Object.defineProperty(exports,"openPageInModalForPC",{enumerable:!0,get:function(){return openPageInModalForPC_2.openPageInModalForPC$}});var openPageInSlidePanelForPC_1=__webpack_require__(137),openPageInSlidePanelForPC_2=__webpack_require__(137);Object.defineProperty(exports,"openPageInSlidePanelForPC",{enumerable:!0,get:function(){return openPageInSlidePanelForPC_2.openPageInSlidePanelForPC$}});var openPageInWorkBenchForPC_1=__webpack_require__(138),openPageInWorkBenchForPC_2=__webpack_require__(138);Object.defineProperty(exports,"openPageInWorkBenchForPC",{enumerable:!0,get:function(){return openPageInWorkBenchForPC_2.openPageInWorkBenchForPC$}});var pauseAduio_1=__webpack_require__(139),pauseAduio_2=__webpack_require__(139);Object.defineProperty(exports,"pauseAduio",{enumerable:!0,get:function(){return pauseAduio_2.pauseAduio$}});var playAduio_1=__webpack_require__(140),playAduio_2=__webpack_require__(140);Object.defineProperty(exports,"playAduio",{enumerable:!0,get:function(){return playAduio_2.playAduio$}});var popGesture_1=__webpack_require__(141),popGesture_2=__webpack_require__(141);Object.defineProperty(exports,"popGesture",{enumerable:!0,get:function(){return popGesture_2.popGesture$}});var previewFileInDingTalk_1=__webpack_require__(142),previewFileInDingTalk_2=__webpack_require__(142);Object.defineProperty(exports,"previewFileInDingTalk",{enumerable:!0,get:function(){return previewFileInDingTalk_2.previewFileInDingTalk$}});var previewImage_2=__webpack_require__(143),previewImage_3=__webpack_require__(143);Object.defineProperty(exports,"previewImage",{enumerable:!0,get:function(){return previewImage_3.previewImage$}});var previewImagesInDingTalkBatch_1=__webpack_require__(144),previewImagesInDingTalkBatch_2=__webpack_require__(144);Object.defineProperty(exports,"previewImagesInDingTalkBatch",{enumerable:!0,get:function(){return previewImagesInDingTalkBatch_2.previewImagesInDingTalkBatch$}});var previewMedia_1=__webpack_require__(145),previewMedia_2=__webpack_require__(145);Object.defineProperty(exports,"previewMedia",{enumerable:!0,get:function(){return previewMedia_2.previewMedia$}});var prompt_2=__webpack_require__(146),prompt_3=__webpack_require__(146);Object.defineProperty(exports,"prompt",{enumerable:!0,get:function(){return prompt_3.prompt$}});var quickCallList_2=__webpack_require__(147),quickCallList_3=__webpack_require__(147);Object.defineProperty(exports,"quickCallList",{enumerable:!0,get:function(){return quickCallList_3.quickCallList$}});var quitPage_1=__webpack_require__(148),quitPage_2=__webpack_require__(148);Object.defineProperty(exports,"quitPage",{enumerable:!0,get:function(){return quitPage_2.quitPage$}});var readBLECharacteristicValue_1=__webpack_require__(149),readBLECharacteristicValue_2=__webpack_require__(149);Object.defineProperty(exports,"readBLECharacteristicValue",{enumerable:!0,get:function(){return readBLECharacteristicValue_2.readBLECharacteristicValue$}});var readNFC_1=__webpack_require__(150),readNFC_2=__webpack_require__(150);Object.defineProperty(exports,"readNFC",{enumerable:!0,get:function(){return readNFC_2.readNFC$}});var removeStorage_1=__webpack_require__(151),removeStorage_2=__webpack_require__(151);Object.defineProperty(exports,"removeStorage",{enumerable:!0,get:function(){return removeStorage_2.removeStorage$}});var replacePage_1=__webpack_require__(152),replacePage_2=__webpack_require__(152);Object.defineProperty(exports,"replacePage",{enumerable:!0,get:function(){return replacePage_2.replacePage$}});var requestAuthCode_3=__webpack_require__(153),requestAuthCode_4=__webpack_require__(153);Object.defineProperty(exports,"requestAuthCode",{enumerable:!0,get:function(){return requestAuthCode_4.requestAuthCode$}});var requestMoneySubmmitOrder_1=__webpack_require__(154),requestMoneySubmmitOrder_2=__webpack_require__(154);Object.defineProperty(exports,"requestMoneySubmmitOrder",{enumerable:!0,get:function(){return requestMoneySubmmitOrder_2.requestMoneySubmmitOrder$}});var resetScreenView_1=__webpack_require__(155),resetScreenView_2=__webpack_require__(155);Object.defineProperty(exports,"resetScreenView",{enumerable:!0,get:function(){return resetScreenView_2.resetScreenView$}});var resumeAudio_1=__webpack_require__(156),resumeAudio_2=__webpack_require__(156);Object.defineProperty(exports,"resumeAudio",{enumerable:!0,get:function(){return resumeAudio_2.resumeAudio$}});var rotateScreenView_1=__webpack_require__(157),rotateScreenView_2=__webpack_require__(157);Object.defineProperty(exports,"rotateScreenView",{enumerable:!0,get:function(){return rotateScreenView_2.rotateScreenView$}});var rsa_2=__webpack_require__(158),rsa_3=__webpack_require__(158);Object.defineProperty(exports,"rsa",{enumerable:!0,get:function(){return rsa_3.rsa$}});var saveFileToDingTalk_1=__webpack_require__(159),saveFileToDingTalk_2=__webpack_require__(159);Object.defineProperty(exports,"saveFileToDingTalk",{enumerable:!0,get:function(){return saveFileToDingTalk_2.saveFileToDingTalk$}});var saveImageToPhotosAlbum_2=__webpack_require__(160),saveImageToPhotosAlbum_3=__webpack_require__(160);Object.defineProperty(exports,"saveImageToPhotosAlbum",{enumerable:!0,get:function(){return saveImageToPhotosAlbum_3.saveImageToPhotosAlbum$}});var saveVideoToPhotosAlbum_1=__webpack_require__(161),saveVideoToPhotosAlbum_2=__webpack_require__(161);Object.defineProperty(exports,"saveVideoToPhotosAlbum",{enumerable:!0,get:function(){return saveVideoToPhotosAlbum_2.saveVideoToPhotosAlbum$}});var scan_2=__webpack_require__(162),scan_3=__webpack_require__(162);Object.defineProperty(exports,"scan",{enumerable:!0,get:function(){return scan_3.scan$}});var scanCard_2=__webpack_require__(163),scanCard_3=__webpack_require__(163);Object.defineProperty(exports,"scanCard",{enumerable:!0,get:function(){return scanCard_3.scanCard$}});var searchMap_1=__webpack_require__(164),searchMap_2=__webpack_require__(164);Object.defineProperty(exports,"searchMap",{enumerable:!0,get:function(){return searchMap_2.searchMap$}});var setClipboard_1=__webpack_require__(165),setClipboard_2=__webpack_require__(165);Object.defineProperty(exports,"setClipboard",{enumerable:!0,get:function(){return setClipboard_2.setClipboard$}});var setGestures_1=__webpack_require__(166),setGestures_2=__webpack_require__(166);Object.defineProperty(exports,"setGestures",{enumerable:!0,get:function(){return setGestures_2.setGestures$}});var setKeepScreenOn_1=__webpack_require__(167),setKeepScreenOn_2=__webpack_require__(167);Object.defineProperty(exports,"setKeepScreenOn",{enumerable:!0,get:function(){return setKeepScreenOn_2.setKeepScreenOn$}});var setNavigationIcon_1=__webpack_require__(168),setNavigationIcon_2=__webpack_require__(168);Object.defineProperty(exports,"setNavigationIcon",{enumerable:!0,get:function(){return setNavigationIcon_2.setNavigationIcon$}});var setNavigationLeft_1=__webpack_require__(169),setNavigationLeft_2=__webpack_require__(169);Object.defineProperty(exports,"setNavigationLeft",{enumerable:!0,get:function(){return setNavigationLeft_2.setNavigationLeft$}});var setNavigationTitle_1=__webpack_require__(170),setNavigationTitle_2=__webpack_require__(170);Object.defineProperty(exports,"setNavigationTitle",{enumerable:!0,get:function(){return setNavigationTitle_2.setNavigationTitle$}});var setScreenBrightness_2=__webpack_require__(171),setScreenBrightness_3=__webpack_require__(171);Object.defineProperty(exports,"setScreenBrightness",{enumerable:!0,get:function(){return setScreenBrightness_3.setScreenBrightness$}});var setStorage_1=__webpack_require__(172),setStorage_2=__webpack_require__(172);Object.defineProperty(exports,"setStorage",{enumerable:!0,get:function(){return setStorage_2.setStorage$}});var share_2=__webpack_require__(173),share_3=__webpack_require__(173);Object.defineProperty(exports,"share",{enumerable:!0,get:function(){return share_3.share$}});var showActionSheet_1=__webpack_require__(174),showActionSheet_2=__webpack_require__(174);Object.defineProperty(exports,"showActionSheet",{enumerable:!0,get:function(){return showActionSheet_2.showActionSheet$}});var showAuthGuide_2=__webpack_require__(175),showAuthGuide_3=__webpack_require__(175);Object.defineProperty(exports,"showAuthGuide",{enumerable:!0,get:function(){return showAuthGuide_3.showAuthGuide$}});var showCallMenu_2=__webpack_require__(176),showCallMenu_3=__webpack_require__(176);Object.defineProperty(exports,"showCallMenu",{enumerable:!0,get:function(){return showCallMenu_3.showCallMenu$}});var showLoading_1=__webpack_require__(177),showLoading_2=__webpack_require__(177);Object.defineProperty(exports,"showLoading",{enumerable:!0,get:function(){return showLoading_2.showLoading$}});var showModal_1=__webpack_require__(178),showModal_2=__webpack_require__(178);Object.defineProperty(exports,"showModal",{enumerable:!0,get:function(){return showModal_2.showModal$}});var showSharePanel_2=__webpack_require__(179),showSharePanel_3=__webpack_require__(179);Object.defineProperty(exports,"showSharePanel",{enumerable:!0,get:function(){return showSharePanel_3.showSharePanel$}});var showToast_1=__webpack_require__(180),showToast_2=__webpack_require__(180);Object.defineProperty(exports,"showToast",{enumerable:!0,get:function(){return showToast_2.showToast$}});var singleSelect_1=__webpack_require__(181),singleSelect_2=__webpack_require__(181);Object.defineProperty(exports,"singleSelect",{enumerable:!0,get:function(){return singleSelect_2.singleSelect$}});var startAdvertising_1=__webpack_require__(182),startAdvertising_2=__webpack_require__(182);Object.defineProperty(exports,"startAdvertising",{enumerable:!0,get:function(){return startAdvertising_2.startAdvertising$}});var startBeaconDiscovery_1=__webpack_require__(183),startBeaconDiscovery_2=__webpack_require__(183);Object.defineProperty(exports,"startBeaconDiscovery",{enumerable:!0,get:function(){return startBeaconDiscovery_2.startBeaconDiscovery$}});var startBluetoothDevicesDiscovery_1=__webpack_require__(184),startBluetoothDevicesDiscovery_2=__webpack_require__(184);Object.defineProperty(exports,"startBluetoothDevicesDiscovery",{enumerable:!0,get:function(){return startBluetoothDevicesDiscovery_2.startBluetoothDevicesDiscovery$}});var startLocating_1=__webpack_require__(185),startLocating_2=__webpack_require__(185);Object.defineProperty(exports,"startLocating",{enumerable:!0,get:function(){return startLocating_2.startLocating$}});var startRecord_2=__webpack_require__(186),startRecord_3=__webpack_require__(186);Object.defineProperty(exports,"startRecord",{enumerable:!0,get:function(){return startRecord_3.startRecord$}});var stopAdvertising_1=__webpack_require__(187),stopAdvertising_2=__webpack_require__(187);Object.defineProperty(exports,"stopAdvertising",{enumerable:!0,get:function(){return stopAdvertising_2.stopAdvertising$}});var stopAudio_1=__webpack_require__(188),stopAudio_2=__webpack_require__(188);Object.defineProperty(exports,"stopAudio",{enumerable:!0,get:function(){return stopAudio_2.stopAudio$}});var stopBeaconDiscovery_1=__webpack_require__(189),stopBeaconDiscovery_2=__webpack_require__(189);Object.defineProperty(exports,"stopBeaconDiscovery",{enumerable:!0,get:function(){return stopBeaconDiscovery_2.stopBeaconDiscovery$}});var stopBluetoothDevicesDiscovery_1=__webpack_require__(190),stopBluetoothDevicesDiscovery_2=__webpack_require__(190);Object.defineProperty(exports,"stopBluetoothDevicesDiscovery",{enumerable:!0,get:function(){return stopBluetoothDevicesDiscovery_2.stopBluetoothDevicesDiscovery$}});var stopLocating_1=__webpack_require__(191),stopLocating_2=__webpack_require__(191);Object.defineProperty(exports,"stopLocating",{enumerable:!0,get:function(){return stopLocating_2.stopLocating$}});var stopPullDownRefresh_1=__webpack_require__(192),stopPullDownRefresh_2=__webpack_require__(192);Object.defineProperty(exports,"stopPullDownRefresh",{enumerable:!0,get:function(){return stopPullDownRefresh_2.stopPullDownRefresh$}});var stopRecord_2=__webpack_require__(193),stopRecord_3=__webpack_require__(193);Object.defineProperty(exports,"stopRecord",{enumerable:!0,get:function(){return stopRecord_3.stopRecord$}});var subscribe_2=__webpack_require__(194),subscribe_3=__webpack_require__(194);Object.defineProperty(exports,"subscribe",{enumerable:!0,get:function(){return subscribe_3.subscribe$}});var timePicker_1=__webpack_require__(195),timePicker_2=__webpack_require__(195);Object.defineProperty(exports,"timePicker",{enumerable:!0,get:function(){return timePicker_2.timePicker$}});var translate_1=__webpack_require__(196),translate_2=__webpack_require__(196);Object.defineProperty(exports,"translate",{enumerable:!0,get:function(){return translate_2.translate$}});var translateVoice_2=__webpack_require__(197),translateVoice_3=__webpack_require__(197);Object.defineProperty(exports,"translateVoice",{enumerable:!0,get:function(){return translateVoice_3.translateVoice$}});var uploadAttachmentToDingTalk_1=__webpack_require__(198),uploadAttachmentToDingTalk_2=__webpack_require__(198);Object.defineProperty(exports,"uploadAttachmentToDingTalk",{enumerable:!0,get:function(){return uploadAttachmentToDingTalk_2.uploadAttachmentToDingTalk$}});var uploadFile_2=__webpack_require__(199),uploadFile_3=__webpack_require__(199);Object.defineProperty(exports,"uploadFile",{enumerable:!0,get:function(){return uploadFile_3.uploadFile$}});var vibrate_2=__webpack_require__(200),vibrate_3=__webpack_require__(200);Object.defineProperty(exports,"vibrate",{enumerable:!0,get:function(){return vibrate_3.vibrate$}});var watchShake_2=__webpack_require__(201),watchShake_3=__webpack_require__(201);Object.defineProperty(exports,"watchShake",{enumerable:!0,get:function(){return watchShake_3.watchShake$}});var writeBLECharacteristicValue_1=__webpack_require__(202),writeBLECharacteristicValue_2=__webpack_require__(202);Object.defineProperty(exports,"writeBLECharacteristicValue",{enumerable:!0,get:function(){return writeBLECharacteristicValue_2.writeBLECharacteristicValue$}});var writeBLEPeripheralCharacteristicValue_1=__webpack_require__(203),writeBLEPeripheralCharacteristicValue_2=__webpack_require__(203);Object.defineProperty(exports,"writeBLEPeripheralCharacteristicValue",{enumerable:!0,get:function(){return writeBLEPeripheralCharacteristicValue_2.writeBLEPeripheralCharacteristicValue$}});var writeNFC_1=__webpack_require__(204),writeNFC_2=__webpack_require__(204);Object.defineProperty(exports,"writeNFC",{enumerable:!0,get:function(){return writeNFC_2.writeNFC$}});var getItem_1=__webpack_require__(452),getStorageInfo_1=__webpack_require__(453),removeItem_1=__webpack_require__(454),setItem_1=__webpack_require__(455),getData_1=__webpack_require__(456);exports.apiObj={biz:{ATMBle:{beaconPicker:beaconPicker_1.beaconPicker$,detectFace:detectFace_1.detectFace$,detectFaceFullScreen:detectFaceFullScreen_1.detectFaceFullScreen$,exclusiveLiveCheck:exclusiveLiveCheck_1.exclusiveLiveCheck$,faceManager:faceManager_1.faceManager$,punchModePicker:punchModePicker_1.punchModePicker$},alipay:{bindAlipay:bindAlipay_1.bindAlipay$,openAuth:openAuth_1.openAuth$,pay:pay_1.pay$},attend:{getLBSWua:getLBSWua_1.getLBSWua$},auth:{openAccountPwdLoginPage:openAccountPwdLoginPage_1.openAccountPwdLoginPage$,requestAuthInfo:requestAuthInfo_1.requestAuthInfo$},calendar:{chooseDateTime:chooseDateTime_1.chooseDateTime$,chooseHalfDay:chooseHalfDay_1.chooseHalfDay$,chooseInterval:chooseInterval_1.chooseInterval$,chooseOneDay:chooseOneDay_1.chooseOneDay$},chat:{chooseConversationByCorpId:chooseConversationByCorpId_1.chooseConversationByCorpId$,collectSticker:collectSticker_1.collectSticker$,createSceneGroup:createSceneGroup_1.createSceneGroup$,getRealmCid:getRealmCid_1.getRealmCid$,locationChatMessage:locationChatMessage_1.locationChatMessage$,openSingleChat:openSingleChat_1.openSingleChat$,pickConversation:pickConversation_1.pickConversation$,sendEmotion:sendEmotion_1.sendEmotion$,toConversation:toConversation_1.toConversation$,toConversationByOpenConversationId:toConversationByOpenConversationId_1.toConversationByOpenConversationId$},clipboardData:{setData:setData_1.setData$},conference:{createCloudCall:createCloudCall_1.createCloudCall$,getCloudCallInfo:getCloudCallInfo_1.getCloudCallInfo$,getCloudCallList:getCloudCallList_1.getCloudCallList$,videoConfCall:videoConfCall_1.videoConfCall$},contact:{choose:choose_1.choose$,chooseMobileContacts:chooseMobileContacts_1.chooseMobileContacts$,complexPicker:complexPicker_1.complexPicker$,createGroup:createGroup_1.createGroup$,departmentsPicker:departmentsPicker_1.departmentsPicker$,externalComplexPicker:externalComplexPicker_1.externalComplexPicker$,externalEditForm:externalEditForm_1.externalEditForm$,rolesPicker:rolesPicker_1.rolesPicker$,setRule:setRule_1.setRule$},cspace:{chooseSpaceDir:chooseSpaceDir_1.chooseSpaceDir$,delete:delete_1.delete$,preview:preview_1.preview$,previewDentryImages:previewDentryImages_1.previewDentryImages$,saveFile:saveFile_1.saveFile$},customContact:{choose:choose_2.choose$,multipleChoose:multipleChoose_1.multipleChoose$},data:{rsa:rsa_1.rsa$},ding:{create:create_1.create$,post:post_1.post$},edu:{finishMiniCourseByRecordId:finishMiniCourseByRecordId_1.finishMiniCourseByRecordId$,getMiniCourseDraftList:getMiniCourseDraftList_1.getMiniCourseDraftList$,joinClassroom:joinClassroom_1.joinClassroom$,makeMiniCourse:makeMiniCourse_1.makeMiniCourse$,newMsgNotificationStatus:newMsgNotificationStatus_1.newMsgNotificationStatus$,startAuth:startAuth_1.startAuth$,tokenFaceImg:tokenFaceImg_1.tokenFaceImg$},event:{notifyWeex:notifyWeex_1.notifyWeex$},file:{downloadFile:downloadFile_1.downloadFile$},intent:{fetchData:fetchData_1.fetchData$},iot:{bind:bind_1.bind$,bindMeetingRoom:bindMeetingRoom_1.bindMeetingRoom$,getDeviceProperties:getDeviceProperties_1.getDeviceProperties$,invokeThingService:invokeThingService_1.invokeThingService$,queryMeetingRoomList:queryMeetingRoomList_1.queryMeetingRoomList$,setDeviceProperties:setDeviceProperties_1.setDeviceProperties$,unbind:unbind_1.unbind$},live:{startClassRoom:startClassRoom_1.startClassRoom$,startUnifiedLive:startUnifiedLive_1.startUnifiedLive$},map:{locate:locate_1.locate$,search:search_1.search$,view:view_1.view$},media:{compressVideo:compressVideo_1.compressVideo$},microApp:{openApp:openApp_1.openApp$},navigation:{close:close_1.close$,goBack:goBack_1.goBack$,hideBar:hideBar_1.hideBar$,navigateBackPage:navigateBackPage_1.navigateBackPage$,navigateToMiniProgram:navigateToMiniProgram_1.navigateToMiniProgram$,navigateToPage:navigateToPage_1.navigateToPage$,quit:quit_1.quit$,replace:replace_1.replace$,setIcon:setIcon_1.setIcon$,setLeft:setLeft_1.setLeft$,setMenu:setMenu_1.setMenu$,setRight:setRight_1.setRight$,setTitle:setTitle_1.setTitle$},pbp:{componentPunchFromPartner:componentPunchFromPartner_1.componentPunchFromPartner$,startMatchRuleFromPartner:startMatchRuleFromPartner_1.startMatchRuleFromPartner$,stopMatchRuleFromPartner:stopMatchRuleFromPartner_1.stopMatchRuleFromPartner$},phoneContact:{add:add_1.add$},realm:{getRealtimeTracingStatus:getRealtimeTracingStatus_1.getRealtimeTracingStatus$,getUserExclusiveInfo:getUserExclusiveInfo_1.getUserExclusiveInfo$,startRealtimeTracing:startRealtimeTracing_1.startRealtimeTracing$,stopRealtimeTracing:stopRealtimeTracing_1.stopRealtimeTracing$,subscribe:subscribe_1.subscribe$,unsubscribe:unsubscribe_1.unsubscribe$},resource:{getInfo:getInfo_1.getInfo$,reportDebugMessage:reportDebugMessage_1.reportDebugMessage$},shortCut:{addShortCut:addShortCut_1.addShortCut$},sports:{getHealthAuthorizationStatus:getHealthAuthorizationStatus_1.getHealthAuthorizationStatus$,getHealthData:getHealthData_1.getHealthData$,getHealthDeviceData:getHealthDeviceData_1.getHealthDeviceData$,requestHealthAuthorization:requestHealthAuthorization_1.requestHealthAuthorization$},store:{closeUnpayOrder:closeUnpayOrder_1.closeUnpayOrder$,createOrder:createOrder_1.createOrder$,getPayUrl:getPayUrl_1.getPayUrl$,inquiry:inquiry_1.inquiry$},tabwindow:{isTab:isTab_1.isTab$},telephone:{call:call_1.call$,checkBizCall:checkBizCall_1.checkBizCall$,quickCallList:quickCallList_1.quickCallList$,showCallMenu:showCallMenu_1.showCallMenu$},user:{checkPassword:checkPassword_1.checkPassword$,get:get_1.get$},util:{callComponent:callComponent_1.callComponent$,checkAuth:checkAuth_1.checkAuth$,chooseImage:chooseImage_1.chooseImage$,chooseRegion:chooseRegion_1.chooseRegion$,chosen:chosen_1.chosen$,clearWebStoreCache:clearWebStoreCache_1.clearWebStoreCache$,closePreviewImage:closePreviewImage_1.closePreviewImage$,compressImage:compressImage_1.compressImage$,datepicker:datepicker_1.datepicker$,datetimepicker:datetimepicker_1.datetimepicker$,decrypt:decrypt_1.decrypt$,downloadFile:downloadFile_2.downloadFile$,encrypt:encrypt_1.encrypt$,getPerfInfo:getPerfInfo_1.getPerfInfo$,invokeWorkbench:invokeWorkbench_1.invokeWorkbench$,isEnableGPUAcceleration:isEnableGPUAcceleration_1.isEnableGPUAcceleration$,isLocalFileExist:isLocalFileExist_1.isLocalFileExist$,multiSelect:multiSelect_1.multiSelect$,open:open_1.open$,openBrowser:openBrowser_1.openBrowser$,openDocument:openDocument_1.openDocument$,openLink:openLink_1.openLink$,openLocalFile:openLocalFile_1.openLocalFile$,openModal:openModal_1.openModal$,openSlidePanel:openSlidePanel_1.openSlidePanel$,presentWindow:presentWindow_1.presentWindow$,previewImage:previewImage_1.previewImage$,previewVideo:previewVideo_1.previewVideo$,saveImage:saveImage_1.saveImage$,saveImageToPhotosAlbum:saveImageToPhotosAlbum_1.saveImageToPhotosAlbum$,scan:scan_1.scan$,scanCard:scanCard_1.scanCard$,setGPUAcceleration:setGPUAcceleration_1.setGPUAcceleration$,setScreenBrightnessAndKeepOn:setScreenBrightnessAndKeepOn_1.setScreenBrightnessAndKeepOn$,setScreenKeepOn:setScreenKeepOn_1.setScreenKeepOn$,share:share_1.share$,shareImage:shareImage_1.shareImage$,showAuthGuide:showAuthGuide_1.showAuthGuide$,showSharePanel:showSharePanel_1.showSharePanel$,startDocSign:startDocSign_1.startDocSign$,systemShare:systemShare_1.systemShare$,timepicker:timepicker_1.timepicker$,uploadAttachment:uploadAttachment_1.uploadAttachment$,uploadFile:uploadFile_1.uploadFile$,uploadImage:uploadImage_1.uploadImage$,uploadImageFromCamera:uploadImageFromCamera_1.uploadImageFromCamera$,ut:ut_1.ut$},verify:{openBindIDCard:openBindIDCard_1.openBindIDCard$,startAuth:startAuth_2.startAuth$},voice:{makeCall:makeCall_1.makeCall$},watermarkCamera:{getWatermarkInfo:getWatermarkInfo_1.getWatermarkInfo$,setWatermarkInfo:setWatermarkInfo_1.setWatermarkInfo$}},channel:{permission:{requestAuthCode:requestAuthCode_1.requestAuthCode$}},device:{accelerometer:{clearShake:clearShake_1.clearShake$,watchShake:watchShake_1.watchShake$},audio:{download:download_1.download$,onPlayEnd:onPlayEnd_1.onPlayEnd$,onRecordEnd:onRecordEnd_1.onRecordEnd$,pause:pause_1.pause$,play:play_1.play$,resume:resume_1.resume$,startRecord:startRecord_1.startRecord$,stop:stop_1.stop$,stopRecord:stopRecord_1.stopRecord$,translateVoice:translateVoice_1.translateVoice$},base:{getBatteryInfo:getBatteryInfo_1.getBatteryInfo$,getInterface:getInterface_1.getInterface$,getPhoneInfo:getPhoneInfo_1.getPhoneInfo$,getScanWifiListAsync:getScanWifiListAsync_1.getScanWifiListAsync$,getUUID:getUUID_1.getUUID$,getWifiStatus:getWifiStatus_1.getWifiStatus$,openSystemSetting:openSystemSetting_1.openSystemSetting$},connection:{getNetworkType:getNetworkType_1.getNetworkType$},geolocation:{checkPermission:checkPermission_1.checkPermission$,get:get_2.get$,start:start_1.start$,status:status_1.status$,stop:stop_2.stop$},launcher:{checkInstalledApps:checkInstalledApps_1.checkInstalledApps$,launchApp:launchApp_1.launchApp$},nfc:{nfcRead:nfcRead_1.nfcRead$,nfcStop:nfcStop_1.nfcStop$,nfcWrite:nfcWrite_1.nfcWrite$},notification:{actionSheet:actionSheet_1.actionSheet$,alert:alert_1.alert$,confirm:confirm_1.confirm$,extendModal:extendModal_1.extendModal$,hidePreloader:hidePreloader_1.hidePreloader$,modal:modal_1.modal$,prompt:prompt_1.prompt$,showPreloader:showPreloader_1.showPreloader$,toast:toast_1.toast$,vibrate:vibrate_1.vibrate$},screen:{getScreenBrightness:getScreenBrightness_1.getScreenBrightness$,insetAdjust:insetAdjust_1.insetAdjust$,isScreenReaderEnabled:isScreenReaderEnabled_1.isScreenReaderEnabled$,resetView:resetView_1.resetView$,rotateView:rotateView_1.rotateView$,setScreenBrightness:setScreenBrightness_1.setScreenBrightness$}},media:{voiceRecorder:{keepAlive:keepAlive_1.keepAlive$,pause:pause_2.pause$,resume:resume_2.resume$,start:start_2.start$,stop:stop_3.stop$}},net:{bjGovApn:{loginGovNet:loginGovNet_1.loginGovNet$}},runtime:{h5nuvabridge:{exec:exec_1.exec$},message:{fetch:fetch_1.fetch$,post:post_2.post$},monitor:{getLoadTime:getLoadTime_1.getLoadTime$},permission:{requestAuthCode:requestAuthCode_2.requestAuthCode$,requestOperateAuthCode:requestOperateAuthCode_1.requestOperateAuthCode$}},ui:{input:{plain:plain_1.plain$},multitask:{addToFloat:addToFloat_1.addToFloat$,removeFromFloat:removeFromFloat_1.removeFromFloat$},nav:{close:close_2.close$,getCurrentId:getCurrentId_1.getCurrentId$,go:go_1.go$,preload:preload_1.preload$,recycle:recycle_1.recycle$},progressBar:{setColors:setColors_1.setColors$},pullToRefresh:{disable:disable_1.disable$,enable:enable_1.enable$,stop:stop_4.stop$},webViewBounce:{disable:disable_2.disable$,enable:enable_2.enable$}},ExternalChannelPublish:ExternalChannelPublish_1.ExternalChannelPublish$,addPhoneContact:addPhoneContact_1.addPhoneContact$,alert:alert_2.alert$,callUsers:callUsers_1.callUsers$,checkAuth:checkAuth_2.checkAuth$,checkBizCall:checkBizCall_2.checkBizCall$,chooseChat:chooseChat_1.chooseChat$,chooseConversation:chooseConversation_1.chooseConversation$,chooseDateRangeInCalendar:chooseDateRangeInCalendar_1.chooseDateRangeInCalendar$,chooseDateTime:chooseDateTime_2.chooseDateTime$,chooseDepartments:chooseDepartments_1.chooseDepartments$,chooseDingTalkDir:chooseDingTalkDir_1.chooseDingTalkDir$,chooseDistrict:chooseDistrict_1.chooseDistrict$,chooseExternalUsers:chooseExternalUsers_1.chooseExternalUsers$,chooseFile:chooseFile_1.chooseFile$,chooseHalfDayInCalendar:chooseHalfDayInCalendar_1.chooseHalfDayInCalendar$,chooseImage:chooseImage_2.chooseImage$,chooseMedia:chooseMedia_1.chooseMedia$,chooseOneDayInCalendar:chooseOneDayInCalendar_1.chooseOneDayInCalendar$,choosePhonebook:choosePhonebook_1.choosePhonebook$,chooseStaffForPC:chooseStaffForPC_1.chooseStaffForPC$,chooseUserFromList:chooseUserFromList_1.chooseUserFromList$,clearShake:clearShake_2.clearShake$,closeBluetoothAdapter:closeBluetoothAdapter_1.closeBluetoothAdapter$,closePage:closePage_1.closePage$,complexChoose:complexChoose_1.complexChoose$,compressImage:compressImage_2.compressImage$,confirm:confirm_2.confirm$,connectBLEDevice:connectBLEDevice_1.connectBLEDevice$,createBLEPeripheralServer:createBLEPeripheralServer_1.createBLEPeripheralServer$,createDing:createDing_1.createDing$,createDingForPC:createDingForPC_1.createDingForPC$,createGroupChat:createGroupChat_1.createGroupChat$,createLiveClassRoom:createLiveClassRoom_1.createLiveClassRoom$,cropImage:cropImage_1.cropImage$,customChooseUsers:customChooseUsers_1.customChooseUsers$,datePicker:datePicker_1.datePicker$,dateRangePicker:dateRangePicker_1.dateRangePicker$,decrypt:decrypt_2.decrypt$,disablePullDownRefresh:disablePullDownRefresh_1.disablePullDownRefresh$,disableWebViewBounce:disableWebViewBounce_1.disableWebViewBounce$,disconnectBLEDevice:disconnectBLEDevice_1.disconnectBLEDevice$,downloadAudio:downloadAudio_1.downloadAudio$,downloadFile:downloadFile_3.downloadFile$,editExternalUser:editExternalUser_1.editExternalUser$,editPicture:editPicture_1.editPicture$,enablePullDownRefresh:enablePullDownRefresh_1.enablePullDownRefresh$,enableWebViewBounce:enableWebViewBounce_1.enableWebViewBounce$,encrypt:encrypt_2.encrypt$,exclusiveLiveCheck:exclusiveLiveCheck_2.exclusiveLiveCheck$,generateImageFromCode:generateImageFromCode_1.generateImageFromCode$,getAdvertisingStatus:getAdvertisingStatus_1.getAdvertisingStatus$,getAuthCode:getAuthCode_1.getAuthCode$,getAuthCodeV2:getAuthCodeV2_1.getAuthCodeV2$,getAuthInfo:getAuthInfo_1.getAuthInfo$,getBLEDeviceCharacteristics:getBLEDeviceCharacteristics_1.getBLEDeviceCharacteristics$,getBLEDeviceServices:getBLEDeviceServices_1.getBLEDeviceServices$,getBatteryInfo:getBatteryInfo_2.getBatteryInfo$,getBeacons:getBeacons_1.getBeacons$,getBluetoothAdapterState:getBluetoothAdapterState_1.getBluetoothAdapterState$,getBluetoothDevices:getBluetoothDevices_1.getBluetoothDevices$,getCloudCallInfo:getCloudCallInfo_2.getCloudCallInfo$,getCloudCallList:getCloudCallList_2.getCloudCallList$,getDeviceId:getDeviceId_1.getDeviceId$,getDeviceUUID:getDeviceUUID_1.getDeviceUUID$,getImageInfo:getImageInfo_1.getImageInfo$,getLocatingStatus:getLocatingStatus_1.getLocatingStatus$,getLocation:getLocation_1.getLocation$,getNetworkType:getNetworkType_2.getNetworkType$,getOperateAuthCode:getOperateAuthCode_1.getOperateAuthCode$,getScreenBrightness:getScreenBrightness_2.getScreenBrightness$,getStorage:getStorage_1.getStorage$,getSystemInfo:getSystemInfo_1.getSystemInfo$,getSystemSettings:getSystemSettings_1.getSystemSettings$,getThirdAppConfCustomData:getThirdAppConfCustomData_1.getThirdAppConfCustomData$,getThirdAppUserCustomData:getThirdAppUserCustomData_1.getThirdAppUserCustomData$,getTranslateStatus:getTranslateStatus_1.getTranslateStatus$,getUserExclusiveInfo:getUserExclusiveInfo_2.getUserExclusiveInfo$,getWifiHotspotStatus:getWifiHotspotStatus_1.getWifiHotspotStatus$,getWifiStatus:getWifiStatus_2.getWifiStatus$,goBackPage:goBackPage_1.goBackPage$,hideLoading:hideLoading_1.hideLoading$,hideToast:hideToast_1.hideToast$,isInTabWindow:isInTabWindow_1.isInTabWindow$,isLocalFileExist:isLocalFileExist_2.isLocalFileExist$,isScreenReaderEnabled:isScreenReaderEnabled_2.isScreenReaderEnabled$,locateInMap:locateInMap_1.locateInMap$,makeCloudCall:makeCloudCall_1.makeCloudCall$,makeVideoConfCall:makeVideoConfCall_1.makeVideoConfCall$,multiSelect:multiSelect_2.multiSelect$,navigateBackPage:navigateBackPage_2.navigateBackPage$,navigateToPage:navigateToPage_2.navigateToPage$,nfcReadCardNumber:nfcReadCardNumber_1.nfcReadCardNumber$,notifyBLECharacteristicValueChange:notifyBLECharacteristicValueChange_1.notifyBLECharacteristicValueChange$,notifyTranslateEvent:notifyTranslateEvent_1.notifyTranslateEvent$,offBLECharacteristicValueChange:offBLECharacteristicValueChange_1.offBLECharacteristicValueChange$,offBLEConnectionStateChanged:offBLEConnectionStateChanged_1.offBLEConnectionStateChanged$,offBluetoothAdapterStateChange:offBluetoothAdapterStateChange_1.offBluetoothAdapterStateChange$,offBluetoothDeviceFound:offBluetoothDeviceFound_1.offBluetoothDeviceFound$,onBLECharacteristicValueChange:onBLECharacteristicValueChange_1.onBLECharacteristicValueChange$,onBLEConnectionStateChanged:onBLEConnectionStateChanged_1.onBLEConnectionStateChanged$,onBLEPeripheralCharacteristicReadRequest:onBLEPeripheralCharacteristicReadRequest_1.onBLEPeripheralCharacteristicReadRequest$,onBLEPeripheralCharacteristicWriteRequest:onBLEPeripheralCharacteristicWriteRequest_1.onBLEPeripheralCharacteristicWriteRequest$,onBLEPeripheralConnectionStateChanged:onBLEPeripheralConnectionStateChanged_1.onBLEPeripheralConnectionStateChanged$,onBeaconServiceChange:onBeaconServiceChange_1.onBeaconServiceChange$,onBeaconUpdate:onBeaconUpdate_1.onBeaconUpdate$,onBluetoothAdapterStateChange:onBluetoothAdapterStateChange_1.onBluetoothAdapterStateChange$,onBluetoothDeviceFound:onBluetoothDeviceFound_1.onBluetoothDeviceFound$,onPlayAudioEnd:onPlayAudioEnd_1.onPlayAudioEnd$,onRecordEnd:onRecordEnd_2.onRecordEnd$,openBluetoothAdapter:openBluetoothAdapter_1.openBluetoothAdapter$,openChatByChatId:openChatByChatId_1.openChatByChatId$,openChatByConversationId:openChatByConversationId_1.openChatByConversationId$,openChatByUserId:openChatByUserId_1.openChatByUserId$,openDocument:openDocument_2.openDocument$,openLink:openLink_2.openLink$,openLocalFile:openLocalFile_2.openLocalFile$,openLocation:openLocation_1.openLocation$,openMicroApp:openMicroApp_1.openMicroApp$,openPageInMicroApp:openPageInMicroApp_1.openPageInMicroApp$,openPageInModalForPC:openPageInModalForPC_1.openPageInModalForPC$,openPageInSlidePanelForPC:openPageInSlidePanelForPC_1.openPageInSlidePanelForPC$,openPageInWorkBenchForPC:openPageInWorkBenchForPC_1.openPageInWorkBenchForPC$,pauseAduio:pauseAduio_1.pauseAduio$,playAduio:playAduio_1.playAduio$,popGesture:popGesture_1.popGesture$,previewFileInDingTalk:previewFileInDingTalk_1.previewFileInDingTalk$,previewImage:previewImage_2.previewImage$,previewImagesInDingTalkBatch:previewImagesInDingTalkBatch_1.previewImagesInDingTalkBatch$,previewMedia:previewMedia_1.previewMedia$,prompt:prompt_2.prompt$,quickCallList:quickCallList_2.quickCallList$,quitPage:quitPage_1.quitPage$,readBLECharacteristicValue:readBLECharacteristicValue_1.readBLECharacteristicValue$,readNFC:readNFC_1.readNFC$,removeStorage:removeStorage_1.removeStorage$,replacePage:replacePage_1.replacePage$,requestAuthCode:requestAuthCode_3.requestAuthCode$,requestMoneySubmmitOrder:requestMoneySubmmitOrder_1.requestMoneySubmmitOrder$,resetScreenView:resetScreenView_1.resetScreenView$,resumeAudio:resumeAudio_1.resumeAudio$,rotateScreenView:rotateScreenView_1.rotateScreenView$,rsa:rsa_2.rsa$,saveFileToDingTalk:saveFileToDingTalk_1.saveFileToDingTalk$,saveImageToPhotosAlbum:saveImageToPhotosAlbum_2.saveImageToPhotosAlbum$,saveVideoToPhotosAlbum:saveVideoToPhotosAlbum_1.saveVideoToPhotosAlbum$,scan:scan_2.scan$,scanCard:scanCard_2.scanCard$,searchMap:searchMap_1.searchMap$,setClipboard:setClipboard_1.setClipboard$,setGestures:setGestures_1.setGestures$,setKeepScreenOn:setKeepScreenOn_1.setKeepScreenOn$,setNavigationIcon:setNavigationIcon_1.setNavigationIcon$,setNavigationLeft:setNavigationLeft_1.setNavigationLeft$,setNavigationTitle:setNavigationTitle_1.setNavigationTitle$,setScreenBrightness:setScreenBrightness_2.setScreenBrightness$,setStorage:setStorage_1.setStorage$,share:share_2.share$,showActionSheet:showActionSheet_1.showActionSheet$,showAuthGuide:showAuthGuide_2.showAuthGuide$,showCallMenu:showCallMenu_2.showCallMenu$,showLoading:showLoading_1.showLoading$,showModal:showModal_1.showModal$,showSharePanel:showSharePanel_2.showSharePanel$,showToast:showToast_1.showToast$,singleSelect:singleSelect_1.singleSelect$,startAdvertising:startAdvertising_1.startAdvertising$,startBeaconDiscovery:startBeaconDiscovery_1.startBeaconDiscovery$,startBluetoothDevicesDiscovery:startBluetoothDevicesDiscovery_1.startBluetoothDevicesDiscovery$,startLocating:startLocating_1.startLocating$,startRecord:startRecord_2.startRecord$,stopAdvertising:stopAdvertising_1.stopAdvertising$,stopAudio:stopAudio_1.stopAudio$,stopBeaconDiscovery:stopBeaconDiscovery_1.stopBeaconDiscovery$,stopBluetoothDevicesDiscovery:stopBluetoothDevicesDiscovery_1.stopBluetoothDevicesDiscovery$,stopLocating:stopLocating_1.stopLocating$,stopPullDownRefresh:stopPullDownRefresh_1.stopPullDownRefresh$,stopRecord:stopRecord_2.stopRecord$,subscribe:subscribe_2.subscribe$,timePicker:timePicker_1.timePicker$,translate:translate_1.translate$,translateVoice:translateVoice_2.translateVoice$,uploadAttachmentToDingTalk:uploadAttachmentToDingTalk_1.uploadAttachmentToDingTalk$,uploadFile:uploadFile_2.uploadFile$,vibrate:vibrate_2.vibrate$,watchShake:watchShake_2.watchShake$,writeBLECharacteristicValue:writeBLECharacteristicValue_1.writeBLECharacteristicValue$,writeBLEPeripheralCharacteristicValue:writeBLEPeripheralCharacteristicValue_1.writeBLEPeripheralCharacteristicValue$,writeNFC:writeNFC_1.writeNFC$,util:{domainStorage:{getItem:getItem_1.getItem$,getStorageInfo:getStorageInfo_1.getStorageInfo$,removeItem:removeItem_1.removeItem$,setItem:setItem_1.setItem$},openTemporary:{getData:getData_1.getData$}}}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(module,exports,__webpack_require__){module.exports=__webpack_require__(501)}])});