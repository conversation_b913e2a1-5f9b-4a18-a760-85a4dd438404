$(function () {
  //获取地址栏参数
  
  var keyword = getURLParameter('keyword');
  keyword = keyword?decodeURIComponent(keyword):'';
  if(keyword){
    $('.searchContent .searchBox .inputBox input').val(keyword);
  }
 
  //使用layui 流加载-加载资讯数据--默认加载资讯下
  getZxFlow(keyword);
  //使用layui 流加载-加载应用数据
  // getAppsFlow(keyword);
})
$(document).on("click", " .searchContent .searchBox .inputBox ", function (e){
  e.stopPropagation();
  $('.searchContent .searchBox .inputBox input').focus();
  $('.searchContent .searchBox .inputBox .cancelIcon').removeClass('hideBtn');
})
 //点击搜索框清空按钮
 $(document).on("click", " .searchContent .searchBox .inputBox .cancelIcon", function (e){
  e.stopPropagation();
  $('.searchContent .searchBox .inputBox input').val('');
  getZxFlow();
  //使用layui 流加载-加载应用数据
  getAppsFlow();
})
//点击inputBox 之外
// $(document).on('click', function(event) {
//   var $target = $(event.target);
//   if (!$target.is('.searchContent .searchBox .inputBox') && !$target.closest('.searchContent .searchBox .inputBox').length) {
//       // 点击了inputBox以外的区域，执行你想要的操作
//       $('.searchContent .searchBox .inputBox .cancelIcon').addClass('hideBtn');
//   }
// });

//流加载-加载资讯
function getZxFlow(keyword=''){
  //先清空资讯容器
  $('.searchContent .mainTabContent .searchInfoContent.zx').html('');
  layui.use('flow', function(){
    var flow = layui.flow;
    flow.load({
      elem: '.zx' //指定列表容器
      ,isAuto:true
      ,scrollElem:'.zx'//滚动条所在容器
      ,end:' '
      ,done: function(page, next){
        // console.log(page)
         //到达临界点（默认滚动触发），触发下一页
        var lis = [];
        //以jQuery的Ajax请求为例，请求下一页数据（注意：page是从2开始返回）
       //假设你的列表返回在data集合中
        $.ajax({
        url: index_urls.getSearchUrl,
        data: {
          _method: 'GET',
          keyRange: 'title',
          exact_match: true,
          keyword: keyword,
          page: page,
          size: 10,
          filter: '[{"key":"sys_mode.keyword","value":"article"}]'
        },
        crossDomain: true,
        dataType: "jsonp",
        success: function (res) {
          var result = (res.result)?res.result:{};
          var total = (result.total)?result.total:0;
          var zxPages = Math.ceil(total/10);
          var dataList = (result.data)?result.data:[];
          if(dataList.length>0){
            for(var i=0;i<dataList.length;i++){
              var item = dataList[i];
              var this_sys_content = item.sys_content;
              this_sys_content = (this_sys_content==='null' || this_sys_content==='undefined')?'':this_sys_content;
              lis.push(`
              <div class="item" data-url="${item.mobileArticleUrl}">
              <div class="title">${item.sys_title}</div>
              <div class="date">${timestampToDate(item.sys_createTime)}</div>
              <div class="summary">${this_sys_content}</div>
              </div>
              `);
            }
           
            //执行下一页渲染，第二参数为：满足“加载更多”的条件，即后面仍有分页
            //pages为Ajax返回的总页数，只有当前页小于总页数的情况下，才会继续出现加载更多
            next(lis.join(''), page < zxPages); 
          }else{
            if(page === 1){
              lis.push(`
              <div class="nodataBox">
              <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
              <div class="nodata">暂无数据</div>
              </div>
              `);
              //第一页不需要显示-没有更多了
              $('.layui-flow-more').hide();
            }else{
              $('.layui-flow-more').show();
            }
            next(lis.join(''), page < 1); 
          }
        },
        error(err){
          if(page === 1){
            lis.push(`
            <div class="nodataBox">
            <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
            <div class="nodata">暂无数据</div>
            </div>
            `);
            //第一页不需要显示-没有更多了
            $('.layui-flow-more').hide();
          }else{
            $('.layui-flow-more').show();
          }
          
          next(lis.join(''), page < 1); 
        }
      });
      }
    });
  });
}
//流加载-加载应用
function getAppsFlow(keyword=''){
  //先清空应用容器
  $('.searchContent .mainTabContent .searchInfoContent.apps').html('');
  layui.use('flow', function(){
    var flow = layui.flow;
    flow.load({
      elem: '.apps' //指定列表容器
      ,isAuto:true
      ,scrollElem:'.apps'//滚动条所在容器
      ,end:' '
      ,done: function(page, next){
        // console.log(page)
         //到达临界点（默认滚动触发），触发下一页
        var lis = [];
        //以jQuery的Ajax请求为例，请求下一页数据（注意：page是从2开始返回）
       //假设你的列表返回在data集合中
         $.ajax({
          url: index_urls.getSearchUrl,
          data: {
            _method: 'GET',
            keyRange: 'title',
            exact_match: true,
            keyword: keyword,
            page: page,
            size: 20,
            filter: '[{"key":"sys_mode.keyword","value":"app"}]',
            platform: 4
          },
          crossDomain: true,
          dataType: "jsonp",
          success: async function (res) {
            var result = (res.result)?res.result:{};
            var total = (result.total)?result.total:0;
            var appPages = Math.ceil(total/20);
            var dataList = (result.data)?result.data:[];
            if(dataList.length>0){
              for(var i=0;i<dataList.length;i++){
                //判断图标是否svg
                var item = dataList[i];
                var this_iconUrl = item.iconUrl;
                var this_isSvg = false;  
                if(this_iconUrl.slice(-4) === '.svg'){
                  var this_svgDetail = await imgToSvg(this_iconUrl);  
                  this_isSvg = true;
                }
                // 正常应用需要data-enableservice="${item.enableService}" data-id="${item.serviceVersionId}" data-isallow="${item.isAllow}"
                // data-reason="${item.reason}" data-appurl="${item.appUrl}" data-appname="${item.appKey}"    该地方暂不需要那么多
                if((this_isSvg === true) && this_svgDetail){
                  lis.push(`
                  <div class="item" data-enableservice="${item.enableService}" data-id="${item.serviceVersionId}"
                   data-appurl="${item.appUrl}" data-appname="${item.appKey}">
                          <div class="svgImg">${this_svgDetail}</div>
                          <div class="name">${item.sys_title}</div>
                      </div>
                  `);

                }else{
                  lis.push(`
                  <div class="item" data-enableservice="${item.enableService}" data-id="${item.serviceVersionId}"
                   data-appurl="${item.appUrl}" data-appname="${item.appKey}">
                          <img src="${item.iconUrl}" alt="">
                          <div class="name">${item.sys_title}</div>
                      </div>
                  `);
                }
              }
             
              
              //执行下一页渲染，第二参数为：满足“加载更多”的条件，即后面仍有分页
              //pages为Ajax返回的总页数，只有当前页小于总页数的情况下，才会继续出现加载更多
              next(lis.join(''), page < appPages); 
            }else{
              if(page === 1){
                lis.push(`
                <div class="nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
                `);
                //第一页不需要显示-没有更多了
                $('.layui-flow-more').hide();
              }else{
                $('.layui-flow-more').show();
              }
              next(lis.join(''), page < 1); 
            }
          },
          error(err){
            if(page === 1){
              lis.push(`
              <div class="nodataBox">
              <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
              <div class="nodata">暂无数据</div>
              </div>
              `);
              //第一页不需要显示-没有更多了
              $('.layui-flow-more').hide();
            }else{
              $('.layui-flow-more').show();
            }
            next(lis.join(''), page < 1); 
          }
        });
      }
    });
  });
}

//资讯-应用tab切换
$(document).on("click", " .searchContent .mainTabContent .searchTabs .tabItem", function (e){
    e.stopPropagation();
    $(this).addClass('active').siblings().removeClass('active');
    var this_type = $(this).attr('data-type');
    $('.searchContent .mainTabContent .searchInfoContent.'+this_type).addClass('active').siblings().removeClass('active');
    var keyword = $('.searchContent .searchBox .inputBox input').val();
    if(this_type === 'zx'){
      getZxFlow(keyword);
     }else if(this_type === 'apps'){
      //使用layui 流加载-加载应用数据
      getAppsFlow(keyword);
     }
  })

//点击搜索按钮
$(document).on("click", " .searchContent .searchBox .searchBtn", function (e){
  e.stopPropagation();
 var keyword = $('.searchContent .searchBox .inputBox input').val();
 checkVal(keyword);
 
})

//点击文章跳转
$(document).on("click", " .searchContent .mainTabContent .searchInfo .zx .item", function (e){
  e.stopPropagation();
 var articleUrl = $(this).data('url');
 if(articleUrl){
  commonToUrl(articleUrl,'_blank');
 }
})

//点击应用跳转
$(document).on("click", " .searchContent .mainTabContent .searchInfo .apps .item", function (e){
  e.stopPropagation();
 var this_enableservice = $(this).data('enableservice');
//  var this_reason = $(this).data('reason');
//  var this_isallow = $(this).data('isallow');
 var this_appurl = $(this).data('appurl');
 var this_appname = $(this).data('appname');
 var this_id = $(this).data('id');
 if(this_enableservice == 1){
   //有服务指南页 跳应用详情页
  //  window.open('detail.html?appVersionId='+this_id+'&_t='+new Date().getTime());
  var this_url = commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/detail.html?appVersionId='+this_id+'&_t='+new Date().getTime();
   commonToUrl(this_url,'_blank');
 }else{
  if(this_appurl){
    openModule(this_appname);
    commonToUrl(this_appurl,'_blank');
    // DDOpen(this_appurl);
  }
    //  if(this_isallow == "true"){
    //      openModule(this_appname);
    //      window.open(this_appurl);
    //      // DDOpen(this_appurl);
    //  }else{
    //    this_reason = this_reason?this_reason:'暂无权限';
    //      $.poptips(this_reason);
    //  }
 }
})

//应用统计
function openModule(appName) {
  var screenSize=window.screen.width+"*"+window.screen.height;
  var clientVersion=navigator.platform+"-"+myBrowser()+"-"+navigator.appVersion.substring(0, navigator.appVersion.indexOf("(")-1);
  $.ajax({
      url: index_urls.openModule,
      type: 'get',
      data: {
          appName:appName,
          clientVersion: clientVersion,
          screenSize:screenSize
      },
      dataType: 'json',
      success: function (){
      }
  })
}
  //获取地址栏参数
  function getURLParameter(name) {
    var url = window.location.search.substring(1); // 获取URL中?后的参数字符串
    var paramsArr = url.split('&'); // 将参数字符串按'&'分割成数组
    for (var i = 0; i < paramsArr.length; i++) {
        var param = paramsArr[i].split('='); // 将每个参数按'='分割成键值对数组
        if (param[0] === name) {
            return param[1]; // 返回指定参数名对应的值
        }
    }
    return null; // 如果没有找到则返回null
  }

  //时间转换
  function timestampToDate(timestamp) {
    var date = new Date(parseInt(timestamp)); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y = date.getFullYear() + "-";
    var M =
      (date.getMonth() + 1 < 10
        ? "0" + (date.getMonth() + 1)
        : date.getMonth() + 1) + "-";
    var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
    var h =
      (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
    var m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
    // var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
    return Y + M + D;
  }

   //检查搜索文字
   function checkVal(keyWord){
    keyWord = $.trim(keyWord);
    if(keyWord){
      let re = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;//只能输入汉字、英文字母或数字
      if(re.test(keyWord)){
        //正常搜索
        //当前选中tab项
        var this_type = $('.searchContent .mainTabContent .searchTabs .tabItem.active').data('type');
        if(this_type === 'zx'){
          getZxFlow(keyWord);
        }else if(this_type === 'apps'){
          //使用layui 流加载-加载应用数据
          getAppsFlow(keyWord);
        }
      }else{
        //提示信息
        $.poptips('请输入汉字、英文字母或数字！');
      }
    }else{
      //提示信息
     $("#portal_header .inputTips").html('请输入您要查找的内容').show();
     $.poptips('请输入您要查找的内容');
    }
  }

  $("body").on("keyup", ".searchContent .searchBox .inputBox input", function (e) {
    //筛选框赋值
    if (e.keyCode == 13) {
      //enter键为13
      var keyword = $('.searchContent .searchBox .inputBox input').val();
      checkVal(keyword);
    }
  });