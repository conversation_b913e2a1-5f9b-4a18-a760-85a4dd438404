/**
 * arm.swipeSlide
 * @authors <PERSON> (<EMAIL>)
 * @date    2015-11-30 09:27:05
 * @version 2015-11-30 09:27:05
 */

;(function(win,$, A){
    'use strict';
    var utils = A.utils;
    // 判断IE
    var browser = {
        ie10 : win.navigator.msPointerEnabled,
        ie11 : win.navigator.pointerEnabled
    };
    // 检测
    var support = A.support;

    // 触摸赋值
    var touchEvents = A.touchEvents;

    var sS = function(element, options){
        var me = this;
        me.$el = $(element).css({"visibility": "hidden"});
        me._distance = 50;
        me.allowSlideClick = true;
        me.opts = $.extend({}, {
            slideClass:"ui-slide",                  // 类型
            slideSelector: "",                      // 切换体内容
            index : 0,                              // 轮播初始值
            pagination: true,                       // 页码指示器
            continuousScroll : true,                // 连续滚动
            autoSwipe : true,                       // 自动切换
            speed : 5000,                           // 切换速度
            duration: 300,                          // 动画时间
            distance: 50,                           // 激发距离
            axisX : true,                           // X轴
            transitionType : 'ease',                // 过渡类型
            lazyLoad : false,                       // 图片懒加载
            callback : function(){},                // 回调方法
            swipeTrigger: true
        }, options);
        me.init();
    };

    // 初始化
    sS.prototype.init = function(){
        var me = this;
        
        me.$el.addClass(me.opts.slideClass).css("visibility","hidden");
        if(me.opts.axisX)me.$el.addClass(me.opts.slideClass+"-axisx");
        me.opts.ul = $(me.opts.slideSelector);
        if(me.opts.ul.length<1)
            me.opts.ul = me.$el.children();
        me.opts.li = me.opts.ul.children();
        me.opts.ul.addClass(me.opts.slideClass + "-content");
        me.opts.li.addClass(me.opts.slideClass + "-item");
        me._index = me.opts.index;
        // 轮播数量
        me._liLength = me.opts.li.length;
        me.isScrolling;

        // 显示指示器
        if(me.opts.pagination){
            me.$pagination = $('<div class="'+me.opts.slideClass+'-dot" />');
            me.$pagination.html(function(){
                var html = "";
                for (var i = 0; i < me._liLength; i++) {
                    html += '<span></span>';
                };
                return html;
            });
            me.$el.append(me.$pagination);
        }

        // 回调
        me._callback(true);

        // 如果轮播小于等于1个，跳出
        if(me._liLength <= 1){
            if(me.opts.lazyLoad) fnLazyLoad(me, 0);
            return false;
        }

        // 连续滚动，复制dom
        if(me.opts.continuousScroll) me.opts.ul.prepend(me.opts.li.last().clone()).append(me.opts.li.first().clone());

        // 懒加载图片
        if(me.opts.lazyLoad){
            fnLazyLoad(me, me._index);
            if(me.opts.continuousScroll){
                fnLazyLoad(me, me._index+1);
                fnLazyLoad(me, me._index+2);
                // 如果是第一屏
                if(me._index == 0){
                    fnLazyLoad(me, me._liLength);
                // 如果是最后一屏
                }else if(me._index+1 == me._liLength){
                    fnLazyLoad(me, 1);
                }
            }else{
                fnLazyLoad(me, me._index+1 == me._liLength ? me._liLength-2 : me._index+1);
            }
        }

        // 轮播的宽度
        fnGetSlideDistance();

        if(browser.ie10 || browser.ie11){
            // IE触控
            var action = '';
            if(me.opts.axisX){
                action = 'pan-y';
            }else{
                action = 'none';
            }
            me.$el.css({'-ms-touch-action':action,'touch-action':action});

            // 解决IE滑动触发click
            me.$el.on('click',function(){
                return me.allowSlideClick;
            });
        }

        // 调用轮播
        fnAutoSlide(me);
        me.$el.css({"visibility":"visible"});
        // 绑定触摸
        if(me.opts.swipeTrigger){
            me.$el.on(touchEvents.touchStart,function(e){
                fnTouches(e);
                fnTouchstart(e, me);
            }).on(touchEvents.touchMove,function(e){
                fnTouches(e);
                fnTouchmove(e, me);
            }).on(touchEvents.touchEnd,function(){
                fnTouchend(me);
            });
        }

        if(support.transition){
        	me.opts.ul.on(support.transition.end,function(){
	            fnAutoSlide(me);
	        });
        }

        // 横竖屏、窗口调整
        $(win).on('onorientationchange' in win ? 'orientationchange' : 'resize',function(){
            clearTimeout(me.timer);
            me.timer = setTimeout(fnGetSlideDistance,150);
        });
        
        // 获取轮播宽度
        function fnGetSlideDistance(){
            var $li = me.opts.ul.children();
            me._slideDistance = me.opts.axisX ? me.opts.ul.width() : me.opts.ul.height();
            me._distance = me.opts.distance > me._slideDistance/3 ? me._slideDistance/3 : me.opts.distance;
            // 定位
            fnTransition(me, me.opts.ul, 0);
            fnTranslate(me, me.opts.ul, -me._slideDistance*me._index);
            fnTransition(me, $li, 0);
            var num = me.opts.continuousScroll ? -1 : 0;
            $li.each(function(i){
                fnTranslate(me, $(this), me._slideDistance*(i+num));
            });
        }
    };

    sS.prototype._callback = function(init){
        var me = this;
        if(me.$pagination)
        	me.$pagination.children().removeClass('cur').eq(me._index).addClass('cur');

        if(!init&&me.curIndex===me._index)
        	return;
        me.opts.callback.call(me, me._index, me.curIndex, me._liLength);
        me.curIndex = me._index;
    }

    // css过渡
    function fnTransition(me, dom, num){
        if(dom===me.opts.ul){
            dom[0].style[utils.prefixStyle('transition')] = "all " + num/1000+'s' + " " + me.opts.transitionType;
        }
    }

    // css位移
    function fnTranslate(me, dom, distance){
        if(dom===me.opts.ul){
            var result = me.opts.axisX ? distance+'px,0' : '0,'+distance+'px';
            dom[0].style[utils.prefixStyle('transform')] = 'translate('+result+')';
        }else{
            var result = me.opts.axisX ? {top:0, left:distance} : {top: distance, left:0};
            $(dom[0]).css(result);
        }
    }

    // 懒加载图片
    function fnLazyLoad(me, index){
        var $li = me.opts.ul.children();
        var $thisImg = $li.eq(index).find('[data-src]');
        if($thisImg){
            $thisImg.each(function(i){
                var $this = $(this);
                if($this.is('img')){
                    $this.attr('src',$this.data('src'));
                    $this.removeAttr('data-src');
                }else{
                    $this.css({'background-image':'url('+$this.data('src')+')'});
                    $this.removeAttr('data-src');
                }
            });
        }
    }

    // touches
    function fnTouches(e){
        if(support.touch && !e.touches){
            e.touches = e.originalEvent.touches;
        }
    }

    // touchstart
    function fnTouchstart(e, me){
    	me.active = true;
        me.isScrolling = undefined;
        me._moveDistance = me._moveDistanceIE = 0;
        // 按下时的坐标
        me._startX = support.touch ? e.touches[0].pageX : (e.pageX || e.clientX);
        me._startY = support.touch ? e.touches[0].pageY : (e.pageY || e.clientY);
    }

    // touchmove
    function fnTouchmove(e, me){
    	if(!me.active)return false;
        // 如果自动切换，move的时候清除autoSlide自动轮播方法
        if(me.opts.autoSwipe) fnStopSlide(me);
        me.allowSlideClick = false;
        // 触摸时的坐标
        me._curX = support.touch ? e.touches[0].pageX : (e.pageX || e.clientX);
        me._curY = support.touch ? e.touches[0].pageY : (e.pageY || e.clientY);
        // 触摸时的距离
        me._moveX = me._curX - me._startX;
        me._moveY = me._curY - me._startY;
        // 优化触摸禁止事件
        if(typeof me.isScrolling == 'undefined'){
            if(me.opts.axisX){
                me.isScrolling = !!(Math.abs(me._moveX) >= Math.abs(me._moveY));
            }else{
                me.isScrolling = !!(Math.abs(me._moveY) >= Math.abs(me._moveX));
            }
        }
        
        // 距离
        if(me.isScrolling){
            if (e.preventDefault) e.preventDefault();
            else e.returnValue = false;
            // 触摸时跟手
            fnTransition(me, me.opts.ul, 0);
            me._moveDistance = me._moveDistanceIE = me.opts.axisX ? me._moveX : me._moveY;
        }
        if(!me.opts.continuousScroll && !me.opts.bounce){
            // 如果是第一屏，并且往下滚动，就不让滚动 || 如果是最后一屏，并且往上滚动，就不让滚动
                if(me._index == 0 && me._moveDistance > 0 || (me._index + 1) >= me._liLength && me._moveDistance < 0){
                    
                    me._moveDistance = 0; 

                } 
        }
        // 触摸时跟手滚动
        fnTranslate(me, me.opts.ul, -(me._slideDistance * me._index - me._moveDistance));

    }

    // touchend
    function fnTouchend(me){
    	me.active = false;
        // 优化触摸禁止事件
        if(!me.isScrolling){
            fnAutoSlide(me);
        }

        // 解决IE滑动触发click
        if(browser.ie10 || browser.ie11){
            if(Math.abs(me._moveDistanceIE) < 5){
                me.allowSlideClick = true;
            }
            setTimeout(function(){
                me.allowSlideClick = true;
            },100);
        }

        // 距离小
        if(Math.abs(me._moveDistance) <= me._distance){
            fnSlide(me, '', me.opts.duration);
        // 距离大
        }else{
            // 手指触摸上一屏滚动
            if(me._moveDistance > me._distance){
                if(me._index===0&&me.opts.bounce){
                    fnSlide(me, '', me.opts.duration);
                }else{
                    fnSlide(me, 'prev', me.opts.duration);
                }
                
            // 手指触摸下一屏滚动
            }else if(Math.abs(me._moveDistance) > me._distance){
                if(me._index===me._liLength-1&&me.opts.bounce){
                    fnSlide(me, '', me.opts.duration);
                }else{
                    fnSlide(me, 'next', me.opts.duration);
                }
            }
        }
        me._moveDistance = me._moveDistanceIE = 0;
    }

    // 自动轮播
    function fnAutoSlide(me){
        if(me.opts.autoSwipe){
            fnStopSlide(me);
            me.autoSlide = setInterval(function(){
                fnSlide(me, 'next', me.opts.duration);
            },me.opts.speed);
        }
    }

    // 停止轮播
    function fnStopSlide(me){
        clearInterval(me.autoSlide);
    }

    // 指定轮播
    sS.prototype.goTo = function(i){
        var me = this;
        fnSlide(me, i, me.opts.duration);
    };

    // 轮播方法
    function fnSlide(me, go, num){
        // 判断方向
        if(typeof go === 'number'){
            me._index = go;
            // 加载当前屏、前一屏、后一屏
            if(me.opts.lazyLoad){
                // 因为连续滚动，复制dom，所以要多加1
                if(me.opts.continuousScroll){
                    fnLazyLoad(me, me._index);
                    fnLazyLoad(me, me._index+1);
                    fnLazyLoad(me, me._index+2);
                }else{
                    fnLazyLoad(me, me._index-1);
                    fnLazyLoad(me, me._index);
                    fnLazyLoad(me, me._index+1);
                }
            }
        }else if(go == 'next'){
            me._index++;
            if(me.opts.lazyLoad){
                if(me.opts.continuousScroll){
                    fnLazyLoad(me, me._index+2);
                    // 滑到最后一屏
                    if(me._index+1 == me._liLength){
                        fnLazyLoad(me, 1);
                    // 最后一屏，继续往后滑动
                    }else if(me._index == me._liLength){
                        fnLazyLoad(me, 0);
                    }
                }else{
                    fnLazyLoad(me, me._index+1);
                }
            }
        }else if(go == 'prev'){
            me._index--;
            if(me.opts.lazyLoad){
                if(me.opts.continuousScroll){
                    fnLazyLoad(me, me._index);
                    // 滑到第一屏
                    if(me._index == 0){
                        fnLazyLoad(me, me._liLength);
                    
                    // 第一屏，继续往前滑动
                    }else if(me._index < 0){
                        fnLazyLoad(me, me._liLength-1);
                    }
                }else{
                    fnLazyLoad(me, me._index-1);
                }
            }
        }
        // 如果是连续滚动
        if(me.opts.continuousScroll){
            if(me._index >= me._liLength){
                fnScroll(me, num);
                me._index = 0;
                setTimeout(function(){
                    fnScroll(me, 0);
                    me._callback();
                    return;
                },300);
            }else if(me._index < 0){
                fnScroll(me, num);
                me._index = me._liLength-1;
                setTimeout(function(){
                    fnScroll(me, 0);
                    me._callback();
                    return;
                },300);
            }else{
                fnScroll(me, num);
            }
        }else{
            if(me._index >= me._liLength){
                me._index = 0;
            }else if(me._index < 0){
                me._index = me._liLength-1;
            }
            fnScroll(me, num);
        }
        me._callback();
    }

    // 轮播动作
    function fnScroll(me, num){
        fnTransition(me, me.opts.ul, num);
        fnTranslate(me, me.opts.ul, -me._index*me._slideDistance);
    }

    $.splipper = A.splipper = sS;
    // 注册swipeSlide
    $.fn.swipeSlide = function(options){
        return new sS(this, options);
    };

    // 注册swipeSlide
    A.pt.swipeSlide = function(options){
        return new sS(this.$, options);
    };

})(window, JQUERY, window.arm);

/**
 * arm.fullpage
 * @authors Nat Liu (<EMAIL>)
 * @date    2015-12-07 18:04:36
 * @version 2015-12-07 18:04:36
 */

!function($, A){
    "use strict";
    
    $.fullpage = A.fullpage = function(el, options){
        var el = $(el);
        var options = $.extend(true, {

            slideClass:"ui-fullpage",
            continuousScroll:false,
            autoSwipe:false,
            axisX:true,
            bounce:true,
            bounceEasing:"ease-in"
        }, options||{});
        return new A.splipper(el, options);
    }
}(JQUERY, window.arm)