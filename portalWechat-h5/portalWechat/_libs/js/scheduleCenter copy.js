$(function () {
    //获取地址栏参数
    var this_typeActive = getURLParameter('type');
    if(this_typeActive === 'mySchedule'){
        typeActive = 'mySchedule';
        $('.scheduleContent .scheduleInfo .scheduleList.mySchedule').addClass('active').siblings().removeClass('active');
        $('.scheduleContent .scheduleInfo .actionBox').show();
    }else{
        typeActive = 'meeting';
        $('.scheduleContent .scheduleInfo .actionBox').hide();
    }
    monthData=[];
    scheduleInfo={
      categoryids:[],
      categoryInfo:{},
      hasLoadCalCategory:false,
      monthStart:'',
      monthEnd:''
    }
})

//加载日程分类信息
function getCategoryInfo(monthStart,monthEnd){
  scheduleInfo.hasLoadCalCategory = true;
  var getCalendarCategoryUrl = index_urls.getCalendarCategoryUrl;
  $.ajax({
      url: getCalendarCategoryUrl,
      data: { queryType: 2 },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        var data = res.result.data;
        for (var i = 0; i < data.length; i++) {
          var obj = data[i];
          if (obj.teamCategoryVOS) {
            for (var j = 0; j < obj.teamCategoryVOS.length; j++) {
              var obj2 = obj.teamCategoryVOS[j];
              var this_categoryid = obj.teamCategoryVOS[j].id;
              if (obj2.checked && (this_categoryid !== -2)) {
                scheduleInfo.categoryids.push(obj2.id);
              }
              var this_categoryColor = obj.teamCategoryVOS[j].calendarCateColor;
              scheduleInfo.categoryInfo[this_categoryid] = this_categoryColor;
            }
          }
        }
        //加载各日程分类一周数据
        loadMonthData(monthStart,monthEnd);
      },
      error:function(err){
         //加载各日程分类一周数据
         loadMonthData(monthStart,monthEnd);
      }
    });
}

//加载整月日程
function loadMonthData(monthStart,monthEnd){
    scheduleInfo.monthStart=monthStart;
    scheduleInfo.monthEnd=monthEnd;
    // console.log('monthStart',monthStart);
    // console.log('monthEnd',monthEnd);
    if(typeActive === 'meeting'){
        //我的会议--现改为了课表
        //加载我的课表
        getKbMonth();
    } else{
        //我的日程
        //是否加载过日程分类信息
        var hasLoadCalCategory = scheduleInfo.hasLoadCalCategory;
        var categoryids=scheduleInfo.categoryids;
        if(hasLoadCalCategory === false){
          //未加载--先加载日程分类信息
          getCategoryInfo(monthStart,monthEnd);
        }else{
          //获取后一天日期
          monthEnd=getDateLater(1,monthEnd);
          $.ajax({
              url: index_urls.getCalendarListUrl,
              type: 'get',
              dataType: 'jsonp',
              data: {
                  categoryIds: categoryids.join(),
                  queryType: 2,
                  type: 0,
                  fromDate: new Date(monthStart).toISOString(),
                  endDate: new Date(monthEnd).toISOString()
              },
              success: function (monthInfo) {
                  manageMonthData(monthInfo);
              },
              error: function (err) {
                  var noDataHtml='<div class="noData">当前日期没有相关事件</div>';
                  $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(noDataHtml);
                  $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(noDataHtml);
              }
          })
        }
    }
}

//加载课表整月数据
function getKbMonth(){
  //获取后一天日期
  var monthStart = scheduleInfo.monthStart;
  var monthEnd = scheduleInfo.monthEnd;
  monthEnd=getDateLater(1,monthEnd);
  $.ajax({
    url: index_urls.getCalendarListUrl,
    type: 'get',
    dataType: 'jsonp',
    data: {
      categoryIds: -2,
      queryType: 2,
      type: 0,
      fromDate: new Date(monthStart).toISOString(),
      endDate: new Date(monthEnd).toISOString()
    },
    success: function (monthInfo) {
      manageMonthData(monthInfo);
    },
    error: function (err) {
      $('.scheduleContent .scheduleInfo .scheduleList.meeting ').html('<div class="noData">当前日期没有相关事件</div>');
    }
  })
}

//整月数据处理
function manageMonthData(monthInfo){
    //先清掉之前的数据标记
    $('.calendar-cur-view .calendar-grid').removeClass('hasData');
    //找出有数据的日期
    var hasDataArr = {};
    for(var i=0;i<monthInfo.length;i++){
      var this_events = monthInfo[i].events?monthInfo[i].events:[];
      var this_day = monthInfo[i].day?monthInfo[i].day:'';
      this_day = (this_day.length>10)?this_day.slice(0,10):'';
      if(this_events.length > 0){
        hasDataArr[this_day] = this_events;
      }
    }
    monthData = hasDataArr;
    //当前一屏展示的日期
    var dateNodeArr = $('.calendar-cur-view .calendar-grid');
    // 是否已有当天数据
    var isHasCurData= false;
    for(var j=0;j<dateNodeArr.length;j++){
      var this_node = dateNodeArr[j];
      var this_date = $(this_node).data('date');
      this_date = this_date.replace(/\//g, "-");
      var this_data_info = hasDataArr[this_date];
      if(this_data_info && this_data_info.length >0){
        //有数据的日期做标记
        $(this_node).addClass('hasData');
      }
      //展示选中日期数据
      if($(this_node).hasClass('grid-curday') && (isHasCurData===false)){
        showCurDayInfo(this_date);
        // console.log('this_date',this_date);
        isHasCurData = true;
    }
    }
}


//展示某天日程数据
function showCurDayInfo(curdate,isFirst=false){
    if(typeActive === 'mySchedule'){
      //我的日程-会有删除按钮
      // 删除按钮等回到默认值
      hideDelCalItem();
    }

    curdate = curdate.replace(/\//g, "-");
    if(isFirst === false){
        var this_date_info = monthData[curdate];
        if(this_date_info && this_date_info.length>0){
          renderCurDayInfo(curdate,this_date_info);
        }else{
          var noDataHtml='<div class="noData">当前日期没有相关事件</div>';
          if(typeActive === 'meeting'){
            //暂无数据
            $('.scheduleContent .scheduleInfo .scheduleList.'+typeActive).html(noDataHtml);
          }else if(typeActive === 'mySchedule'){
             //暂无数据
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(noDataHtml);
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(noDataHtml);
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.ddSchedule').html(noDataHtml);
            $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.all .scheduleAllList').html(noDataHtml);
          }
        }
    }
}

//渲染当天的数据
function renderCurDayInfo(curdate,data){
    // console.log(curdate,data);
    //一周会议
    var calendarHtml = '';
    var this_day = curdate;
    this_day = this_day.length>10?this_day.slice(0,10):this_day;
     //全部日历
     var allScheduleHtml = '';
    //个人日历
    var personalHtml = '';
    //会议
    var conferenceHtml = '';
    //钉钉日程
    var ddScheduleHtml = '';
    // for(var i=0;i<data.length;i++){
    //   var this_schedule = data[i].schedule?data[i].schedule:{};
    //   var this_begin = this_schedule.effectiveTime;
    //   var this_begin_arr = this_begin.split(' ');
    //   var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
    //   var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
    //   var this_end = this_schedule.finishTime;;
    //   var this_end_arr = this_end.split(' ');
    //   var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
    //   var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
    //   this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
    //   this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;
    //   //跨天 时间处理
    //   this_begin_date = this_begin_date.replace(/-/g, "/");
    //   this_end_date = this_end_date.replace(/-/g, "/");
    //   this_day_new = this_day.replace(/-/g, "/");
    //   if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
    //       this_begin_time = '00:00';
    //   }
    //   if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
    //       this_end_time = '23:59';
    //   }
    //   var this_location = this_schedule.location?this_schedule.location:'';
    //   var this_tag8 = this_schedule.tag8?this_schedule.tag8:'';
    //   var this_tag3 = this_schedule.tag3?this_schedule.tag3:'';
    //   var this_title = this_schedule.title?this_schedule.title:'';
    //    // 完整开始-结束时间--不含秒
    //   var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    //   var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    //   var this_content = this_schedule.content?this_schedule.content:'';
    //   var this_categoryid = this_schedule?.cateGory?.id;
    //   var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    //   var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    //   let category='';
    //   switch(this_categoryid){
    //     case 0:
    //       // category='kb'
    //       category='mySchedule'
    //     break;
    //     case -4:
    //        category='ddSchedule'
    //       break;
    //     case -9:
    //       category='conference'
    //       break;
    //     case -2:
    //       category='kb'
    //         break;
    //   }
    //   if(typeActive === 'meeting'){
    //       //放到各自容器内
    //       calendarHtml +=`
    //           <div class="scheduleItem" data-category="kb"  data-title="${this_title}" data-tag3="${this_tag3}" data-time="${this_begin_time}-${this_end_time}"
    //           data-location="${this_location}"  data-content="${this_content}" data-tag8="${this_tag8}">
    //                     <div class="location">
    //                       <div class="time">${this_begin_time}-${this_end_time}</div>
    //                       <div class="address">${this_location}</div>
    //                   </div>
    //                     <div class="title">${this_title}</div>
    //                 </div>
    //       `;
    //   } else if(typeActive === 'mySchedule'){
    //       //再判断是个人日历还是会议
    //       var this_cateGory = this_schedule.cateGory?this_schedule.cateGory:{};
    //       var this_cateGoryId=this_cateGory.id;
    //       var this_cateGoryName=this_cateGory.name.substr(0,1);
    //       var color="#999999";
    //       let bgColor=this_cateGory.color;
    //       //我的日程有添加删除按钮
    //       var this_id = this_schedule.id;
    //       let time=this_begin_time+'-'+this_end_time;
    //       if(category==='dingtalkCal'){
    //         time=this_whole_begin+'-'+this_whole_end;
    //       }
    //       let params={
    //         category:category,
    //         this_id:this_id,
    //         this_location:this_location,
    //         this_tag3:this_tag3,
    //         this_title:this_title,
    //         time:time,
    //         createBy:createBy,
    //         participantsStr:participantsStr,
    //         bgColor:bgColor,
    //         this_cateGoryName:this_cateGoryName,
    //         this_cateGoryId:this_cateGoryId,
    //         color:color,
    //         this_begin_time:this_begin_time,
    //         this_end_time:this_end_time
    //       }
    //       allScheduleHtml+=renderFragment(params)
         
    //       // console.log(this_cateGoryId)
    //       if(this_cateGoryId === 0){
    //           //个人日历
    //           personalHtml += `
    //             <div class="scheduleItem" data-category="mySchedule" data-location="${this_location}" data-tag3="${this_tag3}" data-title="${this_title}" data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-id="${this_id}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                   <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                 <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //               <div class="delItem hideDel" data-id="${this_id}">删除</div>
    //             </div>
    //         `;
    //     } else if(this_cateGoryId === -9){
    //         //会议-后期id确定需要改
    //         conferenceHtml +=`
    //             <div class="scheduleItem" data-category="conference" data-title="${this_title}" data-tag3="${this_tag3}" data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-location="${this_location}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                 <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                   <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //               <div class="location">
    //                 <p class="address">${this_location}</p>
    //               </div>
    //             </div>
    //         `;
    //     } else if(this_cateGoryId === -4){ //钉钉日程
    //         var participantsStr = this_schedule.participantsStr ? this_schedule.participantsStr : '';
    //         var createBy = this_schedule.createBy ? this_schedule.createBy : '';
    //         ddScheduleHtml +=`
    //             <div class="scheduleItem" data-category="ddSchedule" data-title="${this_title}" data-createby="${createBy}" data-participantsStr="${participantsStr}"  data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-location="${this_location}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                 <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                   <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //             </div>
    //         `;
    //     }
    //   }
    // }
    allScheduleHtml=renderFragment(data,this_day,'all')
    personalHtml=renderFragment(data,this_day,'0')
    conferenceHtml=renderFragment(data,this_day,'-9')
    ddScheduleHtml=renderFragment(data,this_day,'-4')
    if(typeActive === 'meeting'){
        //课表
        if(calendarHtml === ''){
          calendarHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        $('.scheduleContent .scheduleInfo .scheduleList.'+typeActive).html(calendarHtml);
    } else if(typeActive === 'mySchedule'){
        //全部
        if(allScheduleHtml === ''){
          allScheduleHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //个人日程
        if(personalHtml === ''){
          personalHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //我的会议
        if(conferenceHtml === ''){
          conferenceHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //钉钉日程
        if(ddScheduleHtml === ''){
          ddScheduleHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(personalHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(conferenceHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.ddSchedule').html(ddScheduleHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.all .scheduleAllList').html(allScheduleHtml);
    }
}
function renderFragment(data,this_day,cateGoryId){
  let allScheduleHtml='';
  for(var i=0;i<data.length;i++){
    var this_schedule = data[i].schedule?data[i].schedule:{};
    var this_begin = this_schedule.effectiveTime;
    var this_begin_arr = this_begin.split(' ');
    var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
    var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
    var this_end = this_schedule.finishTime;;
    var this_end_arr = this_end.split(' ');
    var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
    var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
    this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
    this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;
    //跨天 时间处理
    this_begin_date = this_begin_date.replace(/-/g, "/");
    this_end_date = this_end_date.replace(/-/g, "/");
    let this_day_new = this_day.replace(/-/g, "/");
    if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
        this_begin_time = '00:00';
    }
    if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
        this_end_time = '23:59';
    }
    var this_location = this_schedule.location?this_schedule.location:'';
    var this_tag8 = this_schedule.tag8?this_schedule.tag8:'';
    var this_tag3 = this_schedule.tag3?this_schedule.tag3:'';
    var this_title = this_schedule.title?this_schedule.title:'';
     // 完整开始-结束时间--不含秒
    var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    var this_content = this_schedule.content?this_schedule.content:'';
    var this_categoryid = this_schedule?.cateGory?.id;
    var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    let category='';
    switch(this_categoryid){
      case 0:
        // category='kb'
        category='mySchedule'
      break;
      case -4:
         category='ddSchedule'
        break;
      case -9:
        category='conference'
        break;
      case -2:
        category='kb'
          break;
    }
    var this_id = this_schedule.id;
    let time=this_begin_time+'-'+this_end_time;
    if(category==='ddSchedule'){
        time=this_whole_begin+'-'+this_whole_end;
    }
    var this_cateGory = this_schedule.cateGory?this_schedule.cateGory:{};
    var this_cateGoryId=this_cateGory.id;
    var this_cateGoryName=this_cateGory.name.substr(0,1);
    var color="#999999";
    let bgColor=this_cateGory.color;
  
    if(cateGoryId==this_cateGoryId ||cateGoryId=="all"){
    allScheduleHtml +='<div class="schedule-item scheduleItem" data-category="'+category+'" data-id="'+this_id+'" data-location="'+this_location+'" data-tag3="'+this_tag3+'" data-title="'+this_title+'" data-time="'+time+'" data-createby="'+createBy+'" data-participantsStr="'+participantsStr+'">'+
    '<div class="schedule-left">'+
      '<div class="schedule-name" style="background:'+bgColor+'">'+this_cateGoryName+'</div>'+
      '<div class="schedule-title">'+this_title+'</div>'+
      '<div class="schedule-info">'+
    '<div class="schedule-time">'+
        '<div class="schedule-svg"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="14.196044921875" height="13.99932861328125" viewBox="0 0 14.196044921875 13.99932861328125"><g><g><path d="M0.8435726165771484,7.7438933203125C0.8435726165771484,11.1986733203125,3.6442426165771487,13.9993033203125,7.099062616577148,13.9993033203125C10.553872616577149,13.9993033203125,13.354572616577148,11.1986733203125,13.354572616577148,7.7438933203125C13.354572616577148,4.289063320312501,10.553872616577149,1.4884033203125,7.099062616577148,1.4884033203125C3.6442426165771487,1.4884033203125,0.8435726165771484,4.289063320312501,0.8435726165771484,7.7438933203125ZM6.451972616577148,5.5444633203125L6.451972616577148,8.1752933203125C6.451972616577148,8.1855833203125,6.452212616577149,8.1958033203125,6.452682616577149,8.2059733203125C6.468702616577149,8.549153320312499,6.751992616577148,8.822453320312501,7.099062616577148,8.822453320312501L8.997292616577148,8.822453320312501C9.354682616577149,8.822453320312501,9.64441261657715,8.5327133203125,9.64441261657715,8.1752933203125C9.64441261657715,7.8178733203125,9.354682616577149,7.5281933203125,8.997292616577148,7.5281933203125L7.746222616577149,7.5281933203125L7.746222616577149,5.5444633203125C7.746222616577149,5.1870733203125,7.456452616577148,4.897343320312499,7.099062616577148,4.897343320312499C6.7417026165771485,4.897343320312499,6.451972616577148,5.1870733203125,6.451972616577148,5.5444633203125Z" fill-rule="evenodd" fill="'+color+'" fill-opacity="1"></path></g><g><path d="M0.647131,3.99579C0.474661,3.99603,0.309286,3.92719,0.187933,3.80463C-0.0638827,3.55106,-0.0624636,3.14134,0.191137,2.88948C0.191137,2.88948,2.91161,0.187927,2.91161,0.187927C3.1652,-0.0638732,3.57496,-0.0624694,3.82676,0.191132C4.07859,0.444702,4.07715,0.854416,3.82358,1.10628C3.82358,1.10628,1.10309,3.80783,1.10309,3.80783C0.982001,3.92841,0.818014,3.99602,0.647131,3.99579C0.647131,3.99579,0.647131,3.99579,0.647131,3.99579ZM13.549,3.99579C13.3781,3.99603,13.2141,3.92844,13.093,3.80786C13.093,3.80786,10.3725,1.10632,10.3725,1.10632C10.1188,0.854508,10.1174,0.444794,10.3693,0.191162C10.6212,-0.0624694,11.0309,-0.0638426,11.2844,0.187988C11.2844,0.187988,14.0049,2.88953,14.0049,2.88953C14.2586,3.14134,14.26,3.55106,14.0081,3.80469C13.8868,3.92719,13.7214,3.996,13.549,3.99579C13.549,3.99579,13.549,3.99579,13.549,3.99579Z" fill="'+color+'" fill-opacity="1"></path></g></g></svg></div>'+
        '<span class="schedule-time-text">'+this_begin_time+'-'+this_end_time+'</span>'+
    '</div>'+
    '<div class="schedule-location">'+
  '<div class="schedule-svg"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="13.448486328125" height="14" viewBox="0 0 13.448486328125 14"><g><g><path d="M10.01607,13.948582109375C10.08031,13.982242109375,10.15178,13.999802109375,10.22432,13.999752109375C10.29683,13.999802109375,10.36829,13.982242109375,10.43253,13.948582109375C10.50046,13.912762109375,10.55641,13.861142109375,10.59877,13.795102109375L12.75975,10.424302109374999C12.78011,10.392682109375,12.79646,10.358632109375,12.808430000000001,10.322982109375001C13.01183,10.048902109375,13.17075,9.744492109374999,13.27928,9.420972109375C13.39163,9.086212109375,13.44859,8.735582109375,13.44859,8.378862109375C13.44865,8.158892109375,13.42671,7.939452109375,13.383099999999999,7.7238321093749995C13.340620000000001,7.514802109375,13.27769,7.3104721093750005,13.19524,7.113742109375C13.11412,6.920582109375,13.014569999999999,6.735702109375,12.897960000000001,6.561662109375C12.78261,6.389542109375,12.65013,6.2277021093750005,12.50423,6.080612109375C12.358319999999999,5.933548109375,12.19781,5.800033109375,12.02705,5.683731109375C11.85464,5.566330109375,11.671199999999999,5.465958109375,11.47934,5.384064109375C11.284410000000001,5.300980109375,11.081669999999999,5.237564109375,10.87411,5.194686909375C10.6617,5.150894209375,10.44307,5.128662109375,10.22432,5.128662109375C10.00557,5.128662109375,9.78691,5.150894209375,9.57451,5.194686909375C9.36749,5.237381109375,9.16383,5.301117109375,8.96928,5.384064109375C8.77742,5.465958109375,8.59401,5.566330109375,8.42159,5.683731109375C8.25023,5.800552109375,8.090440000000001,5.933441109375,7.944382,6.080612109375C7.798172,6.2281021093749995,7.666321,6.389172109375,7.550659,6.561662109375C7.434052,6.735702109375,7.334488,6.920582109375,7.253372,7.113742109375C7.170914,7.310442109375,7.107986,7.514802109375,7.065506,7.7238321093749995C7.0220337,7.937942109375,7,8.158372109375,7,8.378862109375C7,8.735612109375,7.0569611,9.086232109375,7.169296,9.420972109375C7.277832,9.744522109375,7.436752,10.048902109375,7.640182,10.322982109375001C7.65213,10.358662109375,7.6684719999999995,10.392682109375,7.688828,10.424302109374999L9.84985,13.795102109375C9.89217,13.861142109375,9.9481,13.912762109375,10.01607,13.948582109375ZM8.44539,8.351062109375C8.44539,7.370732109375,9.24339,6.573182109375,10.22432,6.573182109375C11.20522,6.573182109375,12.003250000000001,7.370732109375,12.003250000000001,8.351062109375C12.003250000000001,9.331372109375,11.20525,10.128932109375,10.22432,10.128932109375C9.24339,10.128932109375,8.44539,9.331372109375,8.44539,8.351062109375Z" fill-rule="evenodd" fill="'+color+'" fill-opacity="1"></path></g><g><path d="M9.198165893554688,8.378300781250001C9.198165893554688,8.94504078125,9.657638893554687,9.404480781250001,10.224415893554688,9.404480781250001C10.791155893554688,9.404480781250001,11.250625893554687,8.94504078125,11.250625893554687,8.378300781250001C11.250625893554687,7.81152378125,10.791155893554688,7.35205078125,10.224415893554688,7.35205078125C9.657638893554687,7.35205078125,9.198165893554688,7.81152378125,9.198165893554688,8.378300781250001C9.198165893554688,8.378300781250001,9.198165893554688,8.378300781250001,9.198165893554688,8.378300781250001Z" fill="'+color+'" fill-opacity="1"></path></g><g><g><path d="M4.75983,13.9192C4.86121,13.9724,4.974,14.0001,5.08847,14C5.20293,14.0001,5.31569,13.9724,5.41707,13.9192C5.52428,13.8627,5.61259,13.7812,5.67944,13.677L9.08981,8.35736C9.12192,8.30745,9.14774,8.25372,9.16661,8.19745C9.48763,7.76492,9.73842,7.28452,9.90971,6.77396C10.087,6.24564,10.1769,5.69229,10.1769,5.12932C10.177,4.78217,10.1424,4.43587,10.0735,4.09558C10.0065,3.7657,9.9072,3.44322,9.77705,3.13277C9.64905,2.82794,9.49194,2.53616,9.30792,2.26149C9.12587,1.98985,8.91679,1.73448,8.68654,1.50233C8.45628,1.27023,8.20297,1.05952,7.93349,0.875992C7.66139,0.690704,7.3719,0.532318,7.06911,0.403061C6.76146,0.271957,6.44151,0.17186,6.11397,0.104202C5.77875,0.03508,5.4337,0,5.08847,0C4.74326,0,4.39819,0.03508,4.06297,0.104202C3.73628,0.171585,3.41489,0.272156,3.10783,0.403061C2.80505,0.532318,2.51558,0.690704,2.24347,0.875992C1.97305,1.06033,1.72089,1.27007,1.49039,1.50233C1.25963,1.73509,1.05156,1.9893,0.869019,2.26149C0.684998,2.53616,0.527878,2.82794,0.399872,3.13277C0.26973,3.44321,0.17041,3.7657,0.103394,4.09558C0.0347748,4.4335,0,4.78134,0,5.12933C0,5.69234,0.0898743,6.24568,0.267181,6.77396C0.438461,7.28455,0.68927,7.76494,1.01031,8.19745C1.02916,8.25375,1.05495,8.30746,1.08708,8.35736L4.49751,13.677C4.5643,13.7812,4.65257,13.8627,4.75983,13.9192ZM2.28104,5.08546C2.28104,3.53833,3.54044,2.27968,5.08847,2.27968C6.63649,2.27968,7.89594,3.53833,7.89594,5.08546C7.89594,6.63255,6.63655,7.89122,5.0885,7.89122C3.54044,7.89122,2.28104,6.63255,2.28104,5.08546Z" fill-rule="evenodd" fill="'+color+'" fill-opacity="1"></path></g><g><path d="M3.4648284912109375,5.12839009765625C3.4648284912109375,6.02283009765625,4.1899264912109375,6.74792009765625,5.084368491210937,6.74792009765625C5.978808491210938,6.74792009765625,6.703938491210938,6.02283009765625,6.703938491210938,5.12839009765625C6.703938491210938,4.23394809765625,5.978808491210938,3.50885009765625,5.084368491210937,3.50885009765625C4.1899264912109375,3.50885009765625,3.4648284912109375,4.23394809765625,3.4648284912109375,5.12839009765625C3.4648284912109375,5.12839009765625,3.4648284912109375,5.12839009765625,3.4648284912109375,5.12839009765625Z" fill="'+color+'" fill-opacity="1"></path></g></g></g></svg></div>'+
  '<span>'+this_location+'</span>'+
'</div>'+
'</div>'
      if(this_cateGoryId === 0){
        allScheduleHtml+='<div class="schedule-right">'+
        '<div class="schedule-del delItem" data-id="'+this_id+'">删除</div>'+
        '</div>'
      }
  '</div>';
  }
}
        return allScheduleHtml
}
//日程添加
var dia;
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .addBtn", function (e) {
  e.stopPropagation();
  //默认日期
  var defaultDate  = $('.arm-calendar-days .calendar-cur-view .grid-curday').data('date');
  defaultDate = defaultDate.replace(/\//g, "-");
   dia = $.iframe({
    title:"日程添加",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=add&date="+defaultDate,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });
});
window.onmessage = function(e) {
  var iframeHeight = parseInt(e.data, 10);
  document.getElementById('iframeWrapper').style.height = iframeHeight + 'px';
};
//监听添加页面操作
window.addEventListener('message', function(event) {
  // console.log(event.data); 
  if(event.data === 'reloadSched'){
    dia.iframe[0].close();
    var monthStart=scheduleInfo.monthStart;
    var monthEnd=scheduleInfo.monthEnd;
    loadMonthData(monthStart,monthEnd);
    
  }
    //关闭日程弹窗
    if(event.data === 'closeSchedIframe'){
      dia.iframe[0].close();
    }
}, false);

//日程删除
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .delCalBtn", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.scheduleContent .scheduleInfo .actionBox .cancelBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').removeClass('hideDel');
  //全部分类的时候
  $('.scheduleAllList  .schedule-del').show();
});
//取消日程删除
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .cancelBtn", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.scheduleContent .scheduleInfo .actionBox .delCalBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').addClass('hideDel');
  //全部分类的时候
  $('.scheduleAllList  .schedule-del').hide();
});

//删除单个日程
$(document).on("click", ".delItem", function (e) {
  e.preventDefault();
  e.stopPropagation();
  e.stopImmediatePropagation();
  var id = $(this).data('id');
  var dia = $.dialog({
    modalClassName:'delCalDialog',
    title:'信息提示',
    content:"您确定要删除吗？",
    onOpen: function(e, dialog){
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("dialog:cancel",function(e, dialog){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
      // console.log('取消')
  }).on("dialog:confirm",function(e, dialog){
      // $.poptips({tipLevel:"success", tipIsColor:true, content:"您点击了"+ e.relatedTarget.innerText});
      var urls = index_urls.getDelCalendarUrl+'?selectedIds='+id;
      //删除
      $.ajax({
          url: urls+"&selectedIds="+id,
          type: 'delete',
          dataType: 'json',
          success: function (res) {
              if (res.result.data) {
                $.poptips("删除成功");
                  // 重新加载该周数据
                  var monthStart=scheduleInfo.monthStart;
                  var monthEnd=scheduleInfo.monthEnd;
                  loadMonthData(monthStart,monthEnd);
                  hideDelCalItem();
                  
              } else {
                $.poptips(data.errorMsg);
              }

          }
      })
      // console.log('确认')
  });
});

//日程删除按钮隐藏
function hideDelCalItem(){
  $('.scheduleContent .scheduleInfo .actionBox .cancelBtn').hide();
  $('.scheduleContent .scheduleInfo .actionBox .delCalBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').hide();
}

// 获取几天后日期
function getDateLater(day,date=''){
  var today = date?(new Date(date)):(new Date());
 var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;
 today.setTime(targetday_milliseconds);
 var tYear = today.getFullYear();
 var tMonth = today.getMonth();
 var tDate = today.getDate();
 tMonth = doHandleMonth(tMonth + 1);
tDate = doHandleMonth(tDate);
 return tYear+"/"+tMonth+"/"+tDate;
}

function doHandleMonth(month) {
  var m = month;
  if (month.toString().length == 1) {
    m = "0" + month;
  }
  return m;
}

//获取地址栏参数
function getURLParameter(name) {
  var url = window.location.search.substring(1); // 获取URL中?后的参数字符串
  var paramsArr = url.split('&'); // 将参数字符串按'&'分割成数组
  for (var i = 0; i < paramsArr.length; i++) {
      var param = paramsArr[i].split('='); // 将每个参数按'='分割成键值对数组
      if (param[0] === name) {
          return param[1]; // 返回指定参数名对应的值
      }
  }
  return null; // 如果没有找到则返回null
}

//我的日程--个人日程-会议tab切换
$(document).on("click", ".scheduleContent .scheduleAllList .scheduleList .myScheduleTab .myScheduleTabItem", function (e){
  e.stopPropagation();
  $(this).addClass('active').siblings().removeClass('active');
  var this_type = $(this).data('type');
  $('.myScheduleBox .scheduleTabItem.'+this_type).addClass('active').siblings().removeClass('active');
  if(this_type=="personal"||this_type=="all"){
    $(".cancelBtn").hide()
    $(".delCalBtn").show()
  }

})

//日程详情弹窗
var detailCaldia;
$(document).on("click", ".scheduleContent .scheduleInfo .scheduleItem", function (e){
  e.stopPropagation();
  var detailTime = $(this).data('time');
  detailTime=detailTime?detailTime:'';
  var detailLocation = $(this).data('location');
  detailLocation=detailLocation?detailLocation:'';
  var detailTitle = $(this).data('title');
  detailTitle=detailTitle?detailTitle:'';
  var detailCategory ='';
  var detailContent = $(this).data('content');
  detailContent=detailContent?detailContent:'';
  var remark = $(this).data('tag8');//备注
  remark=remark?remark:'';
  var category = $(this).data('category');
  var calSpanTitle = '日历：';
  var calSpanLocation = '日程地点：';
  var calSpanContent = '备注：';
  var layerHtml = '';
  if(category === 'kb'){
    detailCategory ='课表';
    calSpanLocation = '上课地点：';
    layerHtml +='<div class="calendar-cons">';
    /**layerHtml +='<p class="calendar-category"><span class="calendar-text">'+calSpanTitle+'</span><span class="calendar-val">'+detailCategory+'</span></p>';**/
    layerHtml +='<p class="calendar-title"><span class="calendar-text">课表名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
    if(detailLocation){
      layerHtml +='<p class="calendar-location"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
    }
    
    layerHtml +='<p class="calendar-time"><span class="calendar-text">上课时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
    /*if(detailContent){
      layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanContent+'</span><span class="calendar-val">'+detailContent+'</span></p>';
    }*/

    //教职工:展示上课班级、学生： 展示上课老师
    const _userLb = localStorage.getItem('userLb');
    if(_userLb === "ls"){
        var tag3 = $(this).data('tag3');//上课班级
        tag3 = tag3 ? tag3 : '';
        layerHtml +='<p class="calendar-content"><span class="calendar-text">上课班级：</span><span class="calendar-val">'+tag3+'</span></p>';
    } else if(_userLb === "xs"){
        var skls = '';
        if(detailContent){
            //格式：上课周次:13,15-16;当前周:15;上课星期:2;上课节次:7-8;老师:郑婧;校区:游仙校区;单双周:全
            var _temp = detailContent.split(";");
            console.log("_temp[4]=" + _temp[4]);
            var _skTec = _temp[4] ? _temp[4].split(":") : "";
            skls = _skTec ? _skTec[1] : "";
        }
        layerHtml +='<p class="calendar-content"><span class="calendar-text">上课老师：</span><span class="calendar-val">'+skls+'</span></p>';
    }
    
    layerHtml +='</div>';
  }else if(category === 'mySchedule'){
      detailCategory ='个人日程';
      var scheduleId = $(this).data('id');
      layerHtml +='<div class="calendar-cons">';
      
      // layerHtml +='<p class="calendar-category"><span>'+calSpanTitle+'</span>'+detailCategory+'</p>';
      layerHtml +='<p class="calendar-title"><span class="calendar-text">日程名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
      layerHtml +='<p class="calendar-time"><span class="calendar-text">日程时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
      // layerHtml +='<p class="calendar-location"><span>'+calSpanLocation+'</span>'+detailLocation+'</p>';
      if(detailLocation){
          layerHtml +='<p class="calendar-location"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>'
      }
      if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">日程内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
      }
      if(remark){
          layerHtml +='<p class="calendar-remark"><span class="calendar-text">备注：</span><span class="calendar-val">'+remark+'</span></p>';
      }
    
      layerHtml +='<div class="editCal" data-id="'+scheduleId+'">编辑</div>'
      layerHtml +='</div>';
  }  else if(category === 'conference'){
        detailCategory ='会议';
        var scheduleId = $(this).data('id');
        layerHtml +='<div class="calendar-cons">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">会议名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">会议时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">会议地点：</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">会议内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        layerHtml +='</div>';
    } else if(category === 'ddSchedule'){
        detailCategory ='钉钉日程';
        var participants = $(this).data('participantsstr');
        participants = participants ? participants :'';
        var createby = $(this).data('createby');
        createby = createby ? createby :'';
        var scheduleId = $(this).data('id');

        layerHtml +='<div class="calendar-cons">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">标题：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">地点：</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        if(createby){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">发起人：</span><span class="calendar-val">'+createby+'</span></p>';
        }
        if(participants){
          layerHtml +='<p class="calendar-remark"><span class="calendar-text">参与者：</span><span class="calendar-val">'+participants+'</span></p>';
        }
       
        layerHtml +='</div>';
    }
  
    //let names=getURLParameter('type')=="meeting"?"课程":'个人日程';
    detailCaldia = $.modal({
          modalClassName:'calDetail',
          durationIn:220,
          durationOut:500,
          animateOutClass:"ani-zoomout",
          content: layerHtml,
          // closeOnMask:true,
          title: detailCategory +'详细信息',
          onOpen: function(e, modal){

            // $.poptips({type:"warning",color:true, content:"正在打开"});
          }
        }).on("modal:cancel",function(e, modal){
            // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
        }).on("modal:opened",function(e, modal){
          var detailHeight = $('.calDetail .ui-modal-bd .calendar-cons').height();
          // var calDetailBoxHeight = $('.calDetail .ui-modal-bd').height();
          var calDetailBoxHeight = $('.calDetail').height()*0.45;
          if(category === 'mySchedule'){
            detailHeight = detailHeight+8;
          }
          if(detailHeight < calDetailBoxHeight){
            var detailHeightNew = detailHeight+54;
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":detailHeightNew+'px!important',
              "height":detailHeightNew+'px!important',
              "max-height":detailHeightNew+'px!important'
            });
          }else{
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":'50%!important',
              "height":'50%!important'
            });
          }
          // $.poptips({tipPosition:"top",tipLevel:"success",animateInClass:"ani-fadeinT",animateOutClass:"ani-fadeoutT",tipIsColor:true, content:"完成打开"});
        }).on("modal:close",function(e, modal){
          // $.poptips({tipPosition:"bottom",tipLevel:"warning",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",tipIsColor:false, content:"正在关闭"});
        }).on("modal:closed",function(e, modal){
          // $.alert("已经关闭");
        });
})

//个人日程编辑
$(document).on("click", ".ui-page.calDetail .editCal", function (e){
  e.stopPropagation();
  // 关闭详情弹窗
  detailCaldia.modal[0].close();
  var scheduleId = $(this).data('id');
   dia = $.iframe({
    title:"日程编辑",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=edit&id="+scheduleId,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });

})