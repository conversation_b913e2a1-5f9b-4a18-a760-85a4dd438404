$(function () {
    //获取地址栏参数
    var this_typeActive = getURLParameter('type');
    if(this_typeActive === 'mySchedule'){
        typeActive = 'mySchedule';
        $('.scheduleContent .scheduleInfo .scheduleList.mySchedule').addClass('active').siblings().removeClass('active');
        $('.scheduleContent .scheduleInfo .actionBox').show();
    }else{
        typeActive = 'meeting';
        $('.scheduleContent .scheduleInfo .actionBox').hide();
    }
    monthData=[];
    scheduleInfo={
      categoryids:[],
      categoryInfo:{},
      hasLoadCalCategory:false,
      monthStart:'',
      monthEnd:''
    }
})

//加载日程分类信息
function getCategoryInfo(monthStart,monthEnd){
  scheduleInfo.hasLoadCalCategory = true;
  var getCalendarCategoryUrl = index_urls.getCalendarCategoryUrl;
  $.ajax({
      url: getCalendarCategoryUrl,
      data: { queryType: 2 },
      crossDomain: true,
      dataType: "jsonp",
      success: function (res) {
        var data = res.result.data;
        for (var i = 0; i < data.length; i++) {
          var obj = data[i];
          if (obj.teamCategoryVOS) {
            for (var j = 0; j < obj.teamCategoryVOS.length; j++) {
              var obj2 = obj.teamCategoryVOS[j];
              var this_categoryid = obj.teamCategoryVOS[j].id;
              if (obj2.checked && (this_categoryid !== -2)) {
                scheduleInfo.categoryids.push(obj2.id);
              }
              var this_categoryColor = obj.teamCategoryVOS[j].calendarCateColor;
              scheduleInfo.categoryInfo[this_categoryid] = this_categoryColor;
            }
          }
        }
        //加载各日程分类一周数据
        loadMonthData(monthStart,monthEnd);
      },
      error:function(err){
         //加载各日程分类一周数据
         loadMonthData(monthStart,monthEnd);
      }
    });
}

//加载整月日程
function loadMonthData(monthStart,monthEnd){
    scheduleInfo.monthStart=monthStart;
    scheduleInfo.monthEnd=monthEnd;
    // console.log('monthStart',monthStart);
    // console.log('monthEnd',monthEnd);
    if(typeActive === 'meeting'){
        //我的会议--现改为了课表
        //加载我的课表
        getKbMonth();
    } else{
        //我的日程
        //是否加载过日程分类信息
        var hasLoadCalCategory = scheduleInfo.hasLoadCalCategory;
        var categoryids=scheduleInfo.categoryids;
        if(hasLoadCalCategory === false){
          //未加载--先加载日程分类信息
          getCategoryInfo(monthStart,monthEnd);
        }else{
          //获取后一天日期
          monthEnd=getDateLater(1,monthEnd);
          $.ajax({
              url: index_urls.getCalendarListUrl,
              type: 'get',
              dataType: 'jsonp',
              data: {
                  categoryIds: categoryids.join(),
                  queryType: 2,
                  type: 0,
                  fromDate: new Date(monthStart).toISOString(),
                  endDate: new Date(monthEnd).toISOString()
              },
              success: function (monthInfo) {
                  manageMonthData(monthInfo);
              },
              error: function (err) {
                  var noDataHtml='<div class="noData">当前日期没有相关事件</div>';
                  $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(noDataHtml);
                  $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(noDataHtml);
              }
          })
        }
    }
}

//加载课表整月数据
function getKbMonth(){
  //获取后一天日期
  var monthStart = scheduleInfo.monthStart;
  var monthEnd = scheduleInfo.monthEnd;
  monthEnd=getDateLater(1,monthEnd);
  $.ajax({
    url: index_urls.getCalendarListUrl,
    type: 'get',
    dataType: 'jsonp',
    data: {
      categoryIds: -2,
      queryType: 2,
      type: 0,
      fromDate: new Date(monthStart).toISOString(),
      endDate: new Date(monthEnd).toISOString()
    },
    success: function (monthInfo) {
      manageMonthData(monthInfo);
    },
    error: function (err) {
      $('.scheduleContent .scheduleInfo .scheduleList.meeting ').html('<div class="noData">当前日期没有相关事件</div>');
    }
  })
}

//整月数据处理
function manageMonthData(monthInfo){
    //先清掉之前的数据标记
    $('.calendar-cur-view .calendar-grid').removeClass('hasData');
    //找出有数据的日期
    var hasDataArr = {};
    for(var i=0;i<monthInfo.length;i++){
      var this_events = monthInfo[i].events?monthInfo[i].events:[];
      var this_day = monthInfo[i].day?monthInfo[i].day:'';
      this_day = (this_day.length>10)?this_day.slice(0,10):'';
      if(this_events.length > 0){
        hasDataArr[this_day] = this_events;
      }
    }
    monthData = hasDataArr;
    //当前一屏展示的日期
    var dateNodeArr = $('.calendar-cur-view .calendar-grid');
    // 是否已有当天数据
    var isHasCurData= false;
    for(var j=0;j<dateNodeArr.length;j++){
      var this_node = dateNodeArr[j];
      var this_date = $(this_node).data('date');
      this_date = this_date.replace(/\//g, "-");
      var this_data_info = hasDataArr[this_date];
      if(this_data_info && this_data_info.length >0){
        //有数据的日期做标记
        $(this_node).addClass('hasData');
      }
      //展示选中日期数据
      if($(this_node).hasClass('grid-curday') && (isHasCurData===false)){
        showCurDayInfo(this_date);
        // console.log('this_date',this_date);
        isHasCurData = true;
    }
    }
}


//展示某天日程数据
function showCurDayInfo(curdate,isFirst=false){
    if(typeActive === 'mySchedule'){
      //我的日程-会有删除按钮
      // 删除按钮等回到默认值
      hideDelCalItem();
    }

    curdate = curdate.replace(/\//g, "-");
    if(isFirst === false){
        var this_date_info = monthData[curdate];
        if(this_date_info && this_date_info.length>0){
          renderCurDayInfo(curdate,this_date_info);
        }else{
          var noDataHtml='<div class="noData">当前日期没有相关事件</div>';
          if(typeActive === 'meeting'){
            //暂无数据
            $('.scheduleContent .scheduleInfo .scheduleList.'+typeActive).html(noDataHtml);
          }else if(typeActive === 'mySchedule'){
             //暂无数据
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(noDataHtml);
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(noDataHtml);
             $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.ddSchedule').html(noDataHtml);
            $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.all .scheduleAllList').html(noDataHtml);
          }
        }
    }
}

//渲染当天的数据
function renderCurDayInfo(curdate,data){
    // console.log(curdate,data);
    //一周会议
    var calendarHtml = '';
    var this_day = curdate;
    this_day = this_day.length>10?this_day.slice(0,10):this_day;
     //全部日历
     var allScheduleHtml = '';
    //个人日历
    var personalHtml = '';
    //会议
    var conferenceHtml = '';
    //钉钉日程
    var ddScheduleHtml = '';
    // for(var i=0;i<data.length;i++){
    //   var this_schedule = data[i].schedule?data[i].schedule:{};
    //   var this_begin = this_schedule.effectiveTime;
    //   var this_begin_arr = this_begin.split(' ');
    //   var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
    //   var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
    //   var this_end = this_schedule.finishTime;;
    //   var this_end_arr = this_end.split(' ');
    //   var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
    //   var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
    //   this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
    //   this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;
    //   //跨天 时间处理
    //   this_begin_date = this_begin_date.replace(/-/g, "/");
    //   this_end_date = this_end_date.replace(/-/g, "/");
    //   this_day_new = this_day.replace(/-/g, "/");
    //   if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
    //       this_begin_time = '00:00';
    //   }
    //   if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
    //       this_end_time = '23:59';
    //   }
    //   var this_location = this_schedule.location?this_schedule.location:'';
    //   var this_tag8 = this_schedule.tag8?this_schedule.tag8:'';
    //   var this_tag3 = this_schedule.tag3?this_schedule.tag3:'';
    //   var this_title = this_schedule.title?this_schedule.title:'';
    //    // 完整开始-结束时间--不含秒
    //   var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    //   var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    //   var this_content = this_schedule.content?this_schedule.content:'';
    //   var this_categoryid = this_schedule?.cateGory?.id;
    //   var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    //   var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    //   let category='';
    //   switch(this_categoryid){
    //     case 0:
    //       // category='kb'
    //       category='mySchedule'
    //     break;
    //     case -4:
    //        category='ddSchedule'
    //       break;
    //     case -9:
    //       category='conference'
    //       break;
    //     case -2:
    //       category='kb'
    //         break;
    //   }
    //   if(typeActive === 'meeting'){
    //       //放到各自容器内
    //       calendarHtml +=`
    //           <div class="scheduleItem" data-category="kb"  data-title="${this_title}" data-tag3="${this_tag3}" data-time="${this_begin_time}-${this_end_time}"
    //           data-location="${this_location}"  data-content="${this_content}" data-tag8="${this_tag8}">
    //                     <div class="location">
    //                       <div class="time">${this_begin_time}-${this_end_time}</div>
    //                       <div class="address">${this_location}</div>
    //                   </div>
    //                     <div class="title">${this_title}</div>
    //                 </div>
    //       `;
    //   } else if(typeActive === 'mySchedule'){
    //       //再判断是个人日历还是会议
    //       var this_cateGory = this_schedule.cateGory?this_schedule.cateGory:{};
    //       var this_cateGoryId=this_cateGory.id;
    //       var this_cateGoryName=this_cateGory.name.substr(0,1);
    //       var color="#999999";
    //       let bgColor=this_cateGory.color;
    //       //我的日程有添加删除按钮
    //       var this_id = this_schedule.id;
    //       let time=this_begin_time+'-'+this_end_time;
    //       if(category==='dingtalkCal'){
    //         time=this_whole_begin+'-'+this_whole_end;
    //       }
    //       let params={
    //         category:category,
    //         this_id:this_id,
    //         this_location:this_location,
    //         this_tag3:this_tag3,
    //         this_title:this_title,
    //         time:time,
    //         createBy:createBy,
    //         participantsStr:participantsStr,
    //         bgColor:bgColor,
    //         this_cateGoryName:this_cateGoryName,
    //         this_cateGoryId:this_cateGoryId,
    //         color:color,
    //         this_begin_time:this_begin_time,
    //         this_end_time:this_end_time
    //       }
    //       allScheduleHtml+=renderFragment(params)
         
    //       // console.log(this_cateGoryId)
    //       if(this_cateGoryId === 0){
    //           //个人日历
    //           personalHtml += `
    //             <div class="scheduleItem" data-category="mySchedule" data-location="${this_location}" data-tag3="${this_tag3}" data-title="${this_title}" data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-id="${this_id}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                   <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                 <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //               <div class="delItem hideDel" data-id="${this_id}">删除</div>
    //             </div>
    //         `;
    //     } else if(this_cateGoryId === -9){
    //         //会议-后期id确定需要改
    //         conferenceHtml +=`
    //             <div class="scheduleItem" data-category="conference" data-title="${this_title}" data-tag3="${this_tag3}" data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-location="${this_location}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                 <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                   <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //               <div class="location">
    //                 <p class="address">${this_location}</p>
    //               </div>
    //             </div>
    //         `;
    //     } else if(this_cateGoryId === -4){ //钉钉日程
    //         var participantsStr = this_schedule.participantsStr ? this_schedule.participantsStr : '';
    //         var createBy = this_schedule.createBy ? this_schedule.createBy : '';
    //         ddScheduleHtml +=`
    //             <div class="scheduleItem" data-category="ddSchedule" data-title="${this_title}" data-createby="${createBy}" data-participantsStr="${participantsStr}"  data-time="${this_whole_begin}-${this_whole_end}"
    //             data-content="${this_content}" data-location="${this_location}" data-tag8="${this_tag8}">
    //               <div class="course">
    //                 <p>${this_title}</p>
    //               </div>
    //               <div class="timeBox">
    //                   <p class="time">${this_begin_time}-${this_end_time}</p>
    //               </div>
    //             </div>
    //         `;
    //     }
    //   }
    // }
    allScheduleHtml=renderFragment(data,this_day,'all')
    personalHtml=renderFragment(data,this_day,'0')
    conferenceHtml=renderFragment(data,this_day,'-9')
    ddScheduleHtml=renderFragment(data,this_day,'-4')
    if(typeActive === 'meeting'){
        //课表
        if(calendarHtml === ''){
          calendarHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        $('.scheduleContent .scheduleInfo .scheduleList.'+typeActive).html(calendarHtml);
    } else if(typeActive === 'mySchedule'){
        //全部
        if(allScheduleHtml === ''){
          allScheduleHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //个人日程
        if(personalHtml === ''){
          personalHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //我的会议
        if(conferenceHtml === ''){
          conferenceHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        //钉钉日程
        if(ddScheduleHtml === ''){
          ddScheduleHtml = '<div class="noData">当前日期没有相关事件</div>';
        }
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.personal .scheduleInfoList').html(personalHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.conference').html(conferenceHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.ddSchedule').html(ddScheduleHtml);
        $('.scheduleContent .scheduleAllList .mySchedule .scheduleTabItem.all .scheduleAllList').html(allScheduleHtml);
    }
}
function renderFragment(data,this_day,cateGoryId){
  let allScheduleHtml='';
  for(var i=0;i<data.length;i++){
    let beginTime=data[i].beginTime;
    let endTime=data[i].endTime;
    var this_schedule = data[i].schedule?data[i].schedule:{};
    var this_begin = this_schedule.effectiveTime;
    var this_begin_arr = this_begin.split(' ');
    var this_begin_date = this_begin_arr.length>0?this_begin_arr[0]:'';
    var this_begin_time = this_begin_arr.length>1?this_begin_arr[1]:'';
    var this_end = this_schedule.finishTime;;
    var this_end_arr = this_end.split(' ');
    var this_end_date = this_end_arr.length>0?this_end_arr[0]:'';
    var this_end_time = this_end_arr.length>1?this_end_arr[1]:'';
    this_begin_time = (this_begin_time.length>=8)?this_begin_time.slice(0,-3):this_begin_time;
    this_end_time = (this_end_time.length>=8)?this_end_time.slice(0,-3):this_end_time;

    //跨天 时间处理
    this_begin_date = this_begin_date.replace(/-/g, "/");
    this_end_date = this_end_date.replace(/-/g, "/");
    let this_day_new = this_day.replace(/-/g, "/");
    if(new Date(this_begin_date).getTime()<new Date(this_day_new).getTime()){
        this_begin_time = '00:00';
    }
    if(new Date(this_end_date).getTime()>new Date(this_day_new).getTime()){
        this_end_time = '23:59';
    }
    var this_location = this_schedule.location?this_schedule.location:'';
    var this_tag8 = this_schedule.tag8?this_schedule.tag8:'';
    var this_tag3 = this_schedule.tag3?this_schedule.tag3:'';
    var this_title = this_schedule.title?this_schedule.title:'';
     // 完整开始-结束时间--不含秒
    var this_whole_begin = (this_begin.length>16)?(this_begin.slice(0,-3)):this_begin;
    var this_whole_end = (this_end.length>16)?(this_end.slice(0,-3)):this_end;
    var this_content = this_schedule.content?this_schedule.content:'';
    var this_categoryid = this_schedule?.cateGory?.id;
    var createBy = this_schedule?.createBy ? this_schedule?.createBy : '';
    var participantsStr = this_schedule?.participantsStr ? this_schedule?.participantsStr : '';
    let category='';
    switch(this_categoryid){
      case 0:
        // category='kb'
        category='mySchedule'
      break;
      case -4:
         category='ddSchedule'
        break;
      case -9:
        category='conference'
        break;
      case -2:
        category='kb'
          break;
    }
    var this_id = this_schedule.id;
    let time=this_begin_time+'-'+this_end_time;
    if(category==='ddSchedule'){
        time=this_whole_begin+'-'+this_whole_end;
    }
    var this_cateGory = this_schedule.cateGory?this_schedule.cateGory:{};
    var this_cateGoryId=this_cateGory.id;
    var this_cateGoryName=this_cateGory.name.substr(0,1);
    let bgColor=this_cateGory.color;
  
    if(cateGoryId==this_cateGoryId ||cateGoryId=="all"){
    allScheduleHtml +='<div class="schedule-item scheduleItem" data-category="'+category+'" data-id="'+this_id+'" data-location="'+this_location+'" data-tag8="'+this_tag8+'" data-tag3="'+this_tag3+'" data-title="'+this_title+'" data-time="'+time+'" data-beginTime="'+beginTime+'" data-endTime="'+endTime+'" data-createby="'+createBy+'" data-participantsStr="'+participantsStr+'"   data-content="'+this_content+'">'+
    '<div class="schedule-timeList">'+
      '<div>'+this_begin_time+'</div>'+
      '<div class="schedule-endTime">'+this_end_time+'</div>'+
    '</div>'+
    '<div class="schedule-left">'+
      '<div class="schedule-titleName">'+
      // '<div class="schedule-name" style="background:'+bgColor+'">'+this_cateGoryName+'</div>'+
        '<div class="schedule-icon" style="background:'+bgColor+'"></div>'+
        '<div class="schedule-title">'+this_title+'</div>'+
      '</div>'
      if(this_location){
        allScheduleHtml +='<div class="schedule-location">'+
        '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="11.142669677734375" height="13.000003814697266" viewBox="0 0 11.142669677734375 13.000003814697266"><g><path d="M11.1427,5.48315C11.1427,7.4772,9.48096,9.86415,6.21349,12.7566C5.84686,13.0811,5.29581,13.0811,4.92917,12.7566C1.66145,9.86442,0,7.47722,0,5.48317C0,2.45187,2.49741,0,5.57133,0C8.64526,0,11.1427,2.45187,11.1427,5.48317C11.1427,5.48317,11.1427,5.48315,11.1427,5.48315ZM1.45338,5.48315C1.45338,6.8801,2.80939,8.87947,5.57132,11.3808C8.33324,8.87947,9.68925,6.88008,9.68925,5.48315C9.68927,3.26069,7.84856,1.45339,5.57133,1.45339C3.29411,1.45339,1.4534,3.26044,1.4534,5.48317C1.4534,5.48317,1.45338,5.48315,1.45338,5.48315ZM5.57133,7.02471C4.76865,7.02471,4.11795,6.37402,4.11795,5.57133C4.11795,4.76863,4.76865,4.11793,5.57133,4.11793C6.37401,4.11793,7.02472,4.76863,7.02472,5.57133C7.02472,6.37402,6.37401,7.02471,5.57133,7.02471C5.57133,7.02471,5.57133,7.02471,5.57133,7.02471Z" fill="'+bgColor+'" fill-opacity="1"/></g></svg>'+
        '<span style="color:'+bgColor+'">'+this_location+'</span>'
      }   
allScheduleHtml+='</div>'+
      '</div>'
      if(this_cateGoryId === 0){
        allScheduleHtml+='<div class="schedule-del delItem" data-id="'+this_id+'">删除</div>'
      }
      allScheduleHtml+='</div>'+
  '</div>';
  }
}
        return allScheduleHtml
}
//日程添加
var dia;
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .addBtn", function (e) {
  e.stopPropagation();
  //默认日期
  var defaultDate  = $('.arm-calendar-days .calendar-cur-view .grid-curday').data('date');
  defaultDate = defaultDate.replace(/\//g, "-");
   dia = $.iframe({
    title:"日程添加",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=add&date="+defaultDate,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });
});
window.onmessage = function(e) {
  var iframeHeight = parseInt(e.data, 10);
  document.getElementById('iframeWrapper').style.height = iframeHeight + 'px';
};
//监听添加页面操作
window.addEventListener('message', function(event) {
  // console.log(event.data); 
  if(event.data === 'reloadSched'){
    dia.iframe[0].close();
    var monthStart=scheduleInfo.monthStart;
    var monthEnd=scheduleInfo.monthEnd;
    loadMonthData(monthStart,monthEnd);
    
  }
    //关闭日程弹窗
    if(event.data === 'closeSchedIframe'){
      dia.iframe[0].close();
    }
}, false);

//日程删除
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .delCalBtn", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.scheduleContent .scheduleInfo .actionBox .cancelBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').removeClass('hideDel');
  //全部分类的时候
  $('.scheduleAllList  .schedule-del').show();
});
//取消日程删除
$(document).on("click", " .scheduleContent .scheduleInfo .actionBox .cancelBtn", function (e) {
  e.stopPropagation();
  e.preventDefault();
  $(this).hide();
  $('.scheduleContent .scheduleInfo .actionBox .delCalBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').addClass('hideDel');
  //全部分类的时候
  $('.scheduleAllList  .schedule-del').hide();
});

//删除单个日程
$(document).on("click", ".delItem", function (e) {
  e.preventDefault();
  e.stopPropagation();
  e.stopImmediatePropagation();
  var id = $(this).data('id');
  var dia = $.dialog({
    modalClassName:'delCalDialog',
    title:'信息提示',
    content:"您确定要删除吗？",
    onOpen: function(e, dialog){
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("dialog:cancel",function(e, dialog){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
      // console.log('取消')
  }).on("dialog:confirm",function(e, dialog){
      // $.poptips({tipLevel:"success", tipIsColor:true, content:"您点击了"+ e.relatedTarget.innerText});
      var urls = index_urls.getDelCalendarUrl+'?selectedIds='+id;
      //删除
      $.ajax({
          url: urls+"&selectedIds="+id,
          type: 'delete',
          dataType: 'json',
          success: function (res) {
              if (res.result.data) {
                $.poptips("删除成功");
                  // 重新加载该周数据
                  var monthStart=scheduleInfo.monthStart;
                  var monthEnd=scheduleInfo.monthEnd;
                  loadMonthData(monthStart,monthEnd);
                  hideDelCalItem();
                   detailCaldia.modal[0].close();
              } else {
                $.poptips(data.errorMsg);
              }

          }
      })
      // console.log('确认')
  });
});

//日程删除按钮隐藏
function hideDelCalItem(){
  $('.scheduleContent .scheduleInfo .actionBox .cancelBtn').hide();
  $('.scheduleContent .scheduleInfo .actionBox .delCalBtn').show();
  $('.scheduleContent .scheduleInfo .scheduleItem .delItem').hide();
}

// 获取几天后日期
function getDateLater(day,date=''){
  var today = date?(new Date(date)):(new Date());
 var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;
 today.setTime(targetday_milliseconds);
 var tYear = today.getFullYear();
 var tMonth = today.getMonth();
 var tDate = today.getDate();
 tMonth = doHandleMonth(tMonth + 1);
tDate = doHandleMonth(tDate);
 return tYear+"/"+tMonth+"/"+tDate;
}

function doHandleMonth(month) {
  var m = month;
  if (month.toString().length == 1) {
    m = "0" + month;
  }
  return m;
}

//获取地址栏参数
function getURLParameter(name) {
  var url = window.location.search.substring(1); // 获取URL中?后的参数字符串
  var paramsArr = url.split('&'); // 将参数字符串按'&'分割成数组
  for (var i = 0; i < paramsArr.length; i++) {
      var param = paramsArr[i].split('='); // 将每个参数按'='分割成键值对数组
      if (param[0] === name) {
          return param[1]; // 返回指定参数名对应的值
      }
  }
  return null; // 如果没有找到则返回null
}

//我的日程--个人日程-会议tab切换
$(document).on("click", ".scheduleContent .scheduleAllList .scheduleList .myScheduleTab .myScheduleTabItem", function (e){
  e.stopPropagation();
  $(this).addClass('active').siblings().removeClass('active');
  var this_type = $(this).data('type');
  $('.myScheduleBox .scheduleTabItem.'+this_type).addClass('active').siblings().removeClass('active');
  if(this_type=="personal"||this_type=="all"){
    $(".cancelBtn").hide()
    $(".delCalBtn").show()
  }

})

//日程详情弹窗
var detailCaldia;
$(document).on("click", ".scheduleContent .scheduleInfo .scheduleItem", function (e){
  e.stopPropagation();
  var detailTime = $(this).data('time');
  var beginTime = $(this).data('begintime');
  var endTime = $(this).data('endtime');
  detailTime=detailTime?detailTime:'';
  var detailLocation = $(this).data('location');
  detailLocation=detailLocation?detailLocation:'';
  var detailTitle = $(this).data('title');
  detailTitle=detailTitle?detailTitle:'';
  var detailCategory ='';
  var detailContent = $(this).data('content');
  detailContent=detailContent?detailContent:'';
  var remark = $(this).data('tag8');//备注
  remark=remark?remark:'';
  var category = $(this).data('category');
  var calSpanTitle = '日历：';
  var calSpanLocation = '日程地点：';
  var calSpanContent = '备注：';
  var layerHtml = '';
  if(category === 'kb'){
    detailCategory ='课表';
    calSpanLocation = '上课地点：';
    layerHtml +='<div class="calendar-cons">';
    /**layerHtml +='<p class="calendar-category"><span class="calendar-text">'+calSpanTitle+'</span><span class="calendar-val">'+detailCategory+'</span></p>';**/
    layerHtml +='<p class="calendar-title"><span class="calendar-text">课表名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
    if(detailLocation){
      layerHtml +='<p class="calendar-location"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>';
    }
    
    layerHtml +='<p class="calendar-time"><span class="calendar-text">上课时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
    /*if(detailContent){
      layerHtml +='<p class="calendar-content"><span class="calendar-text">'+calSpanContent+'</span><span class="calendar-val">'+detailContent+'</span></p>';
    }*/

    //教职工:展示上课班级、学生： 展示上课老师
    const _userLb = localStorage.getItem('userLb');
    if(_userLb === "ls"){
        var tag3 = $(this).data('tag3');//上课班级
        tag3 = tag3 ? tag3 : '';
        layerHtml +='<p class="calendar-content"><span class="calendar-text">上课班级：</span><span class="calendar-val">'+tag3+'</span></p>';
    } else if(_userLb === "xs"){
        var skls = '';
        if(detailContent){
            //格式：上课周次:13,15-16;当前周:15;上课星期:2;上课节次:7-8;老师:郑婧;校区:游仙校区;单双周:全
            var _temp = detailContent.split(";");
            console.log("_temp[4]=" + _temp[4]);
            var _skTec = _temp[4] ? _temp[4].split(":") : "";
            skls = _skTec ? _skTec[1] : "";
        }
        layerHtml +='<p class="calendar-content"><span class="calendar-text">上课老师：</span><span class="calendar-val">'+skls+'</span></p>';
    }
    
    layerHtml +='</div>';
  }else if(category === 'mySchedule'){
      detailCategory ='个人日程';
      var scheduleId = $(this).data('id');
      layerHtml +='<div class="calendar-cons">';
      
      // layerHtml +='<p class="calendar-category"><span>'+calSpanTitle+'</span>'+detailCategory+'</p>';
      layerHtml +='<p class="calendar-title"><span class="calendar-text">日程名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
      layerHtml +='<p class="calendar-time"><span class="calendar-text">日程时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
      // layerHtml +='<p class="calendar-location"><span>'+calSpanLocation+'</span>'+detailLocation+'</p>';
      if(detailLocation){
          layerHtml +='<p class="calendar-location"><span class="calendar-text">'+calSpanLocation+'</span><span class="calendar-val">'+detailLocation+'</span></p>'
      }
      if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">日程内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
      }
      if(remark){
          layerHtml +='<p class="calendar-remark"><span class="calendar-text">备注：</span><span class="calendar-val">'+remark+'</span></p>';
      }
    
      layerHtml +='<div class="edit-btn"><div class="editCal" data-id="'+scheduleId+'">编辑</div><div class="delItem delCal" data-id="'+scheduleId+'">删除</div></div>'
      layerHtml +='</div>';
  }  else if(category === 'conference'){
        detailCategory ='会议';
        var scheduleId = $(this).data('id');
        layerHtml +='<div class="calendar-cons calendar-dd">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">会议名称：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">会议时间：</span><span class="calendar-val">'+detailTime+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">会议地点：</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">会议内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        layerHtml +='</div>';
    } else if(category === 'ddSchedule'){
        detailCategory ='钉钉日程';
        var participants = $(this).data('participantsstr');
        participants = participants ? participants :'';
        var createby = $(this).data('createby');
        createby = createby ? createby :'';
        var scheduleId = $(this).data('id');

        layerHtml +='<div class="calendar-cons calendar-dd">';
        layerHtml +='<p class="calendar-title"><span class="calendar-text">日程标题：</span><span class="calendar-val">'+detailTitle+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">开始时间：</span><span class="calendar-val">'+beginTime+'</span></p>';
        layerHtml +='<p class="calendar-time"><span class="calendar-text">结束时间：</span><span class="calendar-val">'+endTime+'</span></p>';
        if(detailLocation){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">日程地点：</span><span class="calendar-val">'+detailLocation+'</span></p>';
        }
        if(detailContent){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">内容：</span><span class="calendar-val">'+detailContent+'</span></p>';
        }
        if(createby){
          layerHtml +='<p class="calendar-content"><span class="calendar-text">发起人：</span><span class="calendar-val">'+createby+'</span></p>';
        }
        if(participants){
          layerHtml +='<p class="calendar-remark"><span class="calendar-text">参与者：</span><span class="calendar-val">'+participants+'</span></p>';
        }
       
        layerHtml +='</div>';
    }
  
    //let names=getURLParameter('type')=="meeting"?"课程":'个人日程';
    detailCaldia = $.modal({
          modalClassName:'calDetail',
          durationIn:220,
          durationOut:500,
          animateOutClass:"ani-zoomout",
          content: layerHtml,
          // closeOnMask:true,
          title: detailCategory +'详细信息',
          onOpen: function(e, modal){

            // $.poptips({type:"warning",color:true, content:"正在打开"});
          }
        }).on("modal:cancel",function(e, modal){
            // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
        }).on("modal:opened",function(e, modal){
          var detailHeight = $('.calDetail .ui-modal-bd .calendar-cons').height();
          // var calDetailBoxHeight = $('.calDetail .ui-modal-bd').height();
          var calDetailBoxHeight = $('.calDetail').height()*0.45;
          if(category === 'mySchedule'){
            detailHeight = detailHeight+8;
          }
          if(detailHeight < calDetailBoxHeight){
            var detailHeightNew = detailHeight+54;
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":detailHeightNew+'px!important',
              "height":detailHeightNew+'px!important',
              "max-height":detailHeightNew+'px!important'
            });
          }else{
            $('.ui-page.calDetail .ui-modal-cnt').css({
              "min-height":'50%!important',
              "height":'50%!important'
            });
          }
          // $.poptips({tipPosition:"top",tipLevel:"success",animateInClass:"ani-fadeinT",animateOutClass:"ani-fadeoutT",tipIsColor:true, content:"完成打开"});
        }).on("modal:close",function(e, modal){
          // $.poptips({tipPosition:"bottom",tipLevel:"warning",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",tipIsColor:false, content:"正在关闭"});
        }).on("modal:closed",function(e, modal){
          // $.alert("已经关闭");
        });
})

//个人日程编辑
$(document).on("click", ".ui-page.calDetail .editCal", function (e){
  e.stopPropagation();
  // 关闭详情弹窗
  detailCaldia.modal[0].close();
  var scheduleId = $(this).data('id');
   dia = $.iframe({
    title:"日程编辑",
    content:"addSched.html?_p=YXM9MSZwPTEmbT1OJg__&type=edit&id="+scheduleId,
    modalClassName:"iframe-schedule",
    onOpen: function(e, iframe){
      var iframe = $('iframe'); // 假设页面中只有一个iframe
      iframe.on('load', function() {
          iframe.attr('id', 'iframeWrapper');
      });
      // $.poptips({type:"warning",color:true, content:"正在打开"});
    }
  }).on("iframe:cancel",function(e, iframe){
      // $.poptips({content:"您点击了"+ e.relatedTarget.innerText});
  }).on("iframe:closed",function(e, iframe){
    // $.poptips({tipPosition:"top",tipLevel:"error",tipIcColor:true,content:"已经关闭",animateInClass:"ani-fadeinT",tipIcColor:true,content:"已经关闭"});
  });

})