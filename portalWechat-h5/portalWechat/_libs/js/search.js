$(function (){
    //获取搜索热榜
    getHotSearch();
})

//获取搜索热榜
function getHotSearch(){
    $.ajax({
        url: index_urls.getHotWordsNew,
        data: {
          limit:8
        },
        crossDomain: true,
        dataType: "jsonp",
        success: function (res) {
          var result = (res.result)?res.result:[];
          var hotWordsStr = '';
          for(var i=0;i<result.length;i++){
            hotWordsStr += `
                  <div class="item">
                  <div class="hotTitle">
                      <span>${i+1}</span>
                      <span class="title">${result[i].word}</span>
                  </div>
              </div>
            `;
          }
          if(hotWordsStr === ''){
            hotWordsStr =`
            <div class="nodataBox">
            <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
            <div class="nodata">暂无数据</div>
            </div>
            `;
          }
          $('.searchContent .mainContent .hotSearch').html(hotWordsStr);
        },
        error(err){
          var hotWordsStr =`
          <div class="nodataBox">
          <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
          <div class="nodata">暂无数据</div>
          </div>
          `;
          $('.searchContent .mainContent .hotSearch').html(hotWordsStr);
        }
      });
}

$(document).on("click", " .searchContent .searchBox .inputBox ", function (e){
    e.stopPropagation();
    $('.searchContent .searchBox .inputBox input').focus();
    $('.searchContent .searchBox .inputBox .cancelIcon').removeClass('hideBtn');
  })
   //点击关闭按钮清空
   $(document).on("click", " .searchContent .searchBox .inputBox .cancelIcon", function (e){
    e.stopPropagation();
    $('.searchContent .searchBox .inputBox input').val('');
  })

     //点击搜索按钮
     $(document).on("click", " .searchContent .searchBox .searchBtn", function (e){
        e.stopPropagation();
       //跳到搜索tab页
       var keyword =  $('.searchContent .searchBox .inputBox input').val();
       checkVal(keyword);
      })

  //点击热词
  $(document).on("click", " .searchContent .mainContent .hotSearch .item", function (e){
    e.stopPropagation();
   //跳到搜索tab页
   var keyword =  $(this).find('.title').html();
   checkVal(keyword);
  })

  //判断关键字
  //检查搜索文字
function checkVal(keyWord){
  keyWord = $.trim(keyWord);
  if(keyWord){
    let re = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;//只能输入汉字、英文字母或数字
    if(re.test(keyWord)){
      //正常
      //跳转-搜索tab页面
      var url = commonUrlTwo+'/_web/customized/DTalkPortal/ding/mobile/searchTabs.html?keyword='+encodeURIComponent(keyWord);
      // window.open(url,'_self');
      // DDOpen(url);
      commonToUrl(url,'_self');
      
    }else{
      //提示信息
      $.poptips('请输入汉字、英文字母或数字！');
    }
  }else{
    //提示信息
   $("#portal_header .inputTips").html('请输入您要查找的内容').show();
   $.poptips('请输入您要查找的内容');
  }
}

$("body").on("keyup", ".searchContent .searchBox .inputBox input", function (e) {
  //筛选框赋值
  if (e.keyCode == 13) {
    //enter键为13
    var keyword =  $('.searchContent .searchBox .inputBox input').val();
    checkVal(keyword);
  }
});

  //点击inputBox 之外
// $(document).on('click', function(event) {
//   var $target = $(event.target);
//   if (!$target.is('.searchContent .searchBox .inputBox') && !$target.closest('.searchContent .searchBox .inputBox').length) {
//       // 点击了inputBox以外的区域，执行你想要的操作
//       $('.searchContent .searchBox .inputBox .cancelIcon').addClass('hideBtn');
//   }
// });