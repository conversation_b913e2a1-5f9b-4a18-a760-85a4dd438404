(function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.dd=t():e.dd=t()})(this,function(){return function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1097)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ddSdk=void 0;var o=n(4),r=n(4);Object.defineProperty(t,"ENV_ENUM",{enumerable:!0,get:function(){return r.ENV_ENUM}}),Object.defineProperty(t,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return r.ENV_ENUM_SUB}});var i=n(2);n(420),t.ddSdk=new i.Sdk(o.getENV())},function(e,t,n){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},o.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.getNetWorkTypeResultDeal=t.scanParamsDeal=t.removeStorageParamsDeal=t.getStorageParamsDeal=t.setStorageParamsDeal=t.genBizStoreParamsDealFn=t.genBoolResultDealFn=t.forceChangeParamsDealFn=t.genDefaultParamsDealFn=t.addDefaultCorpIdParamsDeal=t.addWatchParamsDeal=void 0,t.addWatchParamsDeal=function(e){var t=Object.assign({},e);return t.watch=!0,t},t.addDefaultCorpIdParamsDeal=function(e){var t=Object.assign({},e);return t.corpId="corpId",t},t.genDefaultParamsDealFn=function(e){var t=Object.assign({},e);return function(e){return Object.assign({},t,e)}},t.forceChangeParamsDealFn=function(e){var t=Object.assign({},e);return function(e){return Object.assign(e,t)}},t.genBoolResultDealFn=function(e){return function(t){var n=Object.assign({},t);return e.forEach(function(e){void 0!==n[e]&&(n[e]=!!n[e])}),n}},t.genBizStoreParamsDealFn=function(e){var t=Object.assign({},e);return"string"!=typeof t.params?(t.params=JSON.stringify(t),t):t},t.setStorageParamsDeal=function(e){return{name:e.key,value:e.data}},t.getStorageParamsDeal=function(e){return{name:e.key}},t.removeStorageParamsDeal=function(e){return{name:e.key}},t.scanParamsDeal=function(e){return"qr"===e.type?o(o({},e),{type:"qrCode"}):"bar"===e.type?o(o({},e),{type:"barCode"}):o(o({},e),{type:"all"})},t.getNetWorkTypeResultDeal=function(e){return"none"!==e.result&&"unknown"!==e.result?{netWorkAvailable:!0,netWorkType:e.result}:{newWorkAvailable:!1}}},function(e,t,n){"use strict";function o(e,t){var n=e&&e.vs;return"object"==typeof n&&t.platformSub?n[t.platformSub]:"string"==typeof n?n:void 0}var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.Sdk=t.getTargetApiConfigVS=t.LogLevel=t.APP_TYPE=t.isFunction=t.compareVersion=t.ENV_ENUM_SUB=t.ENV_ENUM=void 0;var i=n(11);Object.defineProperty(t,"APP_TYPE",{enumerable:!0,get:function(){return i.APP_TYPE}}),Object.defineProperty(t,"LogLevel",{enumerable:!0,get:function(){return i.LogLevel}}),Object.defineProperty(t,"isFunction",{enumerable:!0,get:function(){return i.isFunction}}),Object.defineProperty(t,"compareVersion",{enumerable:!0,get:function(){return i.compareVersion}}),Object.defineProperty(t,"ENV_ENUM",{enumerable:!0,get:function(){return i.ENV_ENUM}}),Object.defineProperty(t,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return i.ENV_ENUM_SUB}});var a=n(427),d=n(10),s=n(6);t.getTargetApiConfigVS=o;var u=function(){function e(e){var t=this;this.configJsApiList=[],this.hadConfig=!1,this.devConfig={debug:!1},this.invokeAPIConfigMapByMethod={},this.p={},this.config$=new Promise(function(e,n){t.p.reject=n,t.p.resolve=e}),this.apiHandler=new a.ApiHandler,this.platformConfigMap={},this.isBridgeDrity=!0,this.getExportSdk=function(){return t.exportSdk},this.setAPI=function(e,n){t.invokeAPIConfigMapByMethod[e]=Object.assign(t.invokeAPIConfigMapByMethod[e]||{},n)},this.setPlatform=function(e){t.isBridgeDrity=!0,t.platformConfigMap[e.platform]=t.withDefaultEvent(e),e.platform===t.env.platform&&e.bridgeInit().catch(function(e){d.formatLog(d.diagnosticMessageMap.auto_bridge_init_error,null===e||void 0===e?void 0:e.toString())})},this.getPlatformConfigMap=function(){return t.platformConfigMap},this.deleteApiConfig=function(e,n){var o=t.invokeAPIConfigMapByMethod[e];o&&delete o[n]},this.invokeAPI=function(e,n,o){return void 0===n&&(n={}),void 0===o&&(o=!0),t.apiHandler.start({method:e,params:n,isAuthApi:o})},this.withDefaultEvent=function(e){var t=Object.assign({on:function(){return d.formatLog(d.diagnosticMessageMap.not_support_event_on)},off:function(){return d.formatLog(d.diagnosticMessageMap.not_support_event_off)}},e.event);return r(r({},e),{event:t})},this.env=e,this.bridgeInitFn=function(){if(t.bridgeInitFnPromise&&!t.isBridgeDrity)return t.bridgeInitFnPromise;t.isBridgeDrity=!1;var n=t.platformConfigMap[e.platform];if(n)t.bridgeInitFnPromise=n.bridgeInit().catch(function(e){return d.formatLog(d.diagnosticMessageMap.JsBridge_init_fail),Promise.reject(e)});else{var o=d.formatLog(d.diagnosticMessageMap.not_support_env,e.platform);t.bridgeInitFnPromise=Promise.reject(new Error(o))}return t.bridgeInitFnPromise};var n=function(e){void 0===e&&(e={}),t.devConfig=Object.assign(t.devConfig,e),e.extraPlatform&&t.setPlatform(e.extraPlatform)};this.exportSdk={config:function(o){void 0===o&&(o={});var r=!0;Object.keys(o).forEach(function(e){-1===["debug","usePromise"].indexOf(e)&&(r=!1)}),r?(d.formatLog(d.diagnosticMessageMap.config_debug_deprecated),n(o)):t.hadConfig?d.formatLog(d.diagnosticMessageMap.repeat_config):(o.jsApiList&&(t.configJsApiList=o.jsApiList.map(function(e){return s[e]?s[e]:e})),t.hadConfig=!0,t.bridgeInitFn().then(function(n){var r=t.platformConfigMap[e.platform],i=o;r.authParamsDeal&&(i=r.authParamsDeal(i)),n(r.authMethod,i).then(function(e){t.isReady=!0,t.p.resolve(e)}).catch(function(e){t.isReady=!1,t.p.reject(e)})},function(e){d.formatLog(d.diagnosticMessageMap.JsBridge_init_fail_dd_config),t.p.reject(e)}))},devConfig:n,ready:function(e){!1===t.hadConfig?(d.formatLog(d.diagnosticMessageMap.dd_config_wrap_deprecated),t.bridgeInitFn().then(function(){e()})):t.config$.then(function(t){e()})},error:function(e){t.config$.catch(function(t){e(t)})},on:function(n,o){t.bridgeInitFn().then(function(){var r;null===(r=t.platformConfigMap[e.platform].event)||void 0===r||r.on(n,o)})},off:function(n,o){t.bridgeInitFn().then(function(){var r;null===(r=t.platformConfigMap[e.platform].event)||void 0===r||r.off(n,o)})},env:e,checkJsApi:function(n){void 0===n&&(n={});var r={};return n.jsApiList&&n.jsApiList.forEach(function(n){var a=s[n]||n,d=t.invokeAPIConfigMapByMethod[a];if(d){var u=d[e.platform],c=o(u,e);c&&e.version&&i.compareVersion(e.version,c)&&(r[n]=!0)}r[n]||(r[n]=!1)}),Promise.resolve(r)},_invoke:function(e,n){return void 0===n&&(n={}),t.invokeAPI(e,n,!1)}},this.initApiMiddleware()}return e.prototype.useApiMiddleware=function(e){if(!i.isFunction(e))throw TypeError("middleware must be a function");this.apiHandler.use(e)},e.prototype.initApiMiddleware=function(){this.apiHandler.use(a.bridge.bind(this)),this.apiHandler.use(a.retry.bind(this)),this.apiHandler.use(a.dealParamsAndResult.bind(this)),this.apiHandler.use(a.checkConfig.bind(this)),this.apiHandler.use(a.initBridge.bind(this)),this.apiHandler.use(a.hookBeforeAndAfter.bind(this))},e}();t.Sdk=u},function(e,t,n){"use strict";var o=n(157);n(431),e.exports=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getENV=t.getUA=void 0;var o=n(2),r=n(2);Object.defineProperty(t,"ENV_ENUM",{enumerable:!0,get:function(){return r.ENV_ENUM}}),Object.defineProperty(t,"APP_TYPE",{enumerable:!0,get:function(){return r.APP_TYPE}}),Object.defineProperty(t,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return r.ENV_ENUM_SUB}});var i,a=n(417),d=function(){try{if("undefined"!=typeof window&&void 0!==window.top){return window.top.__dingtalk_jsapi_top_platfrom_config__}}catch(e){return}};(function(e){e.singlePage="singlePage",e.miniApp="miniApp",e.miniWidget="miniWidget"})(i||(i={})),t.getUA=function(){var e="";try{"undefined"!=typeof navigator&&(e=navigator&&(navigator.userAgent||navigator.swuserAgent)||"")}catch(t){e=""}return e},t.getENV=function(){var e,n,r=t.getUA(),s=/iPhone|iPad|iPod|iOS/i.test(r),u=/Android/i.test(r),c=/DingTalk/i.test(r),l=/dd-web/i.test(r),v="object"==typeof nuva,f="object"==typeof dd&&"function"==typeof dd.dtBridge,p=/TaurusApp/.test(r),_=p&&!c,P=p&&c,E=_&&"undefined"!=typeof my&&null!==my&&void 0!==my.alert,N=p&&/dingtalk-win/.test(r),b=!N&&_&&s,g=!N&&_&&u,h=!N&&P&&s,k=!N&&P&&u,M=f&&s||v&&s,m=c||a.default.isDingTalk,I=s&&m||a.default.isWeexiOS||M,y=u&&m||a.default.isWeexAndroid,$=f,S=l,A=o.APP_TYPE.WEB;if(E)A=o.APP_TYPE.MINI_APP;else if(S)A=o.APP_TYPE.WEBVIEW_IN_MINIAPP;else if($)A=o.APP_TYPE.MINI_APP;else if(a.default.isWeexiOS||a.default.isWeexAndroid)try{var U=weex.config.ddWeexEnv;A=U===i.miniWidget?o.APP_TYPE.WEEX_WIDGET:o.APP_TYPE.WEEX}catch(e){A=o.APP_TYPE.WEEX}var V,O="*",j=r.match(/AliApp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===j&&(j=r.match(/DingTalk\/([a-zA-Z0-9.-]+)/));var C;j&&j[1]&&(C=j[1]);var w="";"undefined"!=typeof name&&(w=name);var D=d();try{D&&"undefined"!=typeof window&&void 0!==window.top&&window.top!==window&&(w=top.name)}catch(e){}if(w)try{var T=JSON.parse(w);T.hostVersion&&(C=T.hostVersion),O=T.language||navigator.language||"*",V=T.containerId}catch(e){}var z=!!V||"undefined"!=typeof window&&(null===(n=null===(e=null===window||void 0===window?void 0:window.dingtalk)||void 0===e?void 0:e.platform)||void 0===n?void 0:n.invokeAPI);z&&!C&&(j=r.match(/DingTalk\(([a-zA-Z0-9\.-]+)\)/))&&j[1]&&(C=j[1]);var B,F=o.ENV_ENUM_SUB.noSub;if(N?(B=o.ENV_ENUM.gdtPc,F=o.ENV_ENUM_SUB.win):B=b?o.ENV_ENUM.gdtIos:g?o.ENV_ENUM.gdtAndroid:h?o.ENV_ENUM.gdtStandardIos:k?o.ENV_ENUM.gdtStandardAndroid:I?o.ENV_ENUM.ios:y?o.ENV_ENUM.android:z?o.ENV_ENUM.pc:D&&D.platform?D.platform:o.ENV_ENUM.notInDingTalk,B===o.ENV_ENUM.pc){F=r.indexOf("Macintosh; Intel Mac OS")>-1?o.ENV_ENUM_SUB.mac:o.ENV_ENUM_SUB.win}return{platform:B,platformSub:F,version:C,appType:A,language:O}}},function(e,t,n){"use strict";var o=n(157);n(162),n(163),e.exports=o},function(e,t){e.exports={datePicker:"biz.util.datetimepicker",chooseOneDayInCalendar:"biz.calendar.chooseOneDay",disableWebViewBounce:"ui.webViewBounce.disable",openPageInSlidePanelForPC:"biz.util.openSlidePanel",disablePullDownRefresh:"ui.pullToRefresh.disable",compressImage:"biz.util.compressImage",openPageInWorkBenchForPC:"biz.util.invokeWorkbench",chooseHalfDayInCalendar:"biz.calendar.chooseHalfDay",dateRangePicker:"biz.calendar.chooseInterval",stopPullDownRefresh:"ui.pullToRefresh.stop",chooseDateRangeInCalendar:"biz.calendar.chooseInterval",stopRecord:"device.audio.stopRecord",navigateToPage:"biz.navigation.navigateToPage",chooseDateTime:"biz.calendar.chooseDateTime",enablePullDownRefresh:"ui.pullToRefresh.enable",downloadAudio:"device.audio.download",openLink:"biz.util.openLink",openMicroApp:"biz.microApp.openApp",timePicker:"biz.util.timepicker",openPageInMicroApp:"biz.util.open",previewImage:"biz.util.previewImage",openPageInModalForPC:"biz.util.openModal",enableWebViewBounce:"ui.webViewBounce.enable",generateImageFromCode:"biz.util.generateImageFromCode",navigateBackPage:"biz.navigation.navigateBackPage",openLocation:"biz.map.view",playAduio:"device.audio.play",chooseImage:"biz.util.chooseImage",onRecordEnd:"device.audio.onRecordEnd",stopAudio:"device.audio.stop",vibrate:"device.notification.vibrate",onPlayAudioEnd:"device.audio.onPlayEnd",getStorage:"util.domainStorage.getItem",searchMap:"biz.map.search",stopLocating:"device.geolocation.stop",resumeAudio:"device.audio.resume",getLocatingStatus:"device.geolocation.status",startLocating:"device.geolocation.start",startRecord:"device.audio.startRecord",setStorage:"util.domainStorage.setItem",openLocalFile:"biz.util.openLocalFile",share:"biz.util.share",writeNFC:"device.nfc.nfcWrite",choosePhonebook:"biz.contact.chooseMobileContacts",pauseAduio:"device.audio.pause",getDeviceUUID:"device.base.getUUID",getSystemSettings:"device.base.openSystemSetting",customChooseUsers:"biz.customContact.multipleChoose",removeStorage:"util.domainStorage.removeItem",setClipboard:"biz.clipboardData.setData",scan:"biz.util.scan",decrypt:"biz.util.decrypt",getWifiHotspotStatus:"device.base.getInterface",readNFC:"device.nfc.nfcRead",showCallMenu:"biz.telephone.showCallMenu",getLocation:"device.geolocation.get",locateInMap:"biz.map.locate",clearShake:"device.accelerometer.clearShake",chooseStaffForPC:"biz.contact.choose",uploadAttachmentToDingTalk:"biz.util.uploadAttachment",rotateScreenView:"device.screen.rotateView",isLocalFileExist:"biz.util.isLocalFileExist",openChatByChatId:"biz.chat.toConversation",createGroupChat:"biz.contact.createGroup",watchShake:"device.accelerometer.watchShake",scanCard:"biz.util.scanCard",createDing:"biz.ding.create",openChatByUserId:"biz.chat.openSingleChat",chooseUserFromList:"biz.customContact.choose",exclusiveLiveCheck:"biz.ATMBle.exclusiveLiveCheck",chooseExternalUsers:"biz.contact.externalComplexPicker",saveFileToDingTalk:"biz.cspace.saveFile",resetScreenView:"device.screen.resetView",getCloudCallList:"biz.conference.getCloudCallList",translateVoice:"device.audio.translateVoice",complexChoose:"biz.contact.complexPicker",editExternalUser:"biz.contact.externalEditForm",checkBizCall:"biz.telephone.checkBizCall",getWifiStatus:"device.base.getWifiStatus",chooseDepartments:"biz.contact.departmentsPicker",getSystemInfo:"device.base.getPhoneInfo",getNetworkType:"device.connection.getNetworkType",makeVideoConfCall:"biz.conference.videoConfCall",createDingForPC:"biz.ding.post",encrypt:"biz.util.encrypt",quickCallList:"biz.telephone.quickCallList",getUserExclusiveInfo:"biz.realm.getUserExclusiveInfo",getAuthCode:"runtime.permission.requestAuthCode",previewImagesInDingTalkBatch:"biz.cspace.previewDentryImages",chooseChat:"biz.chat.chooseConversationByCorpId",getCloudCallInfo:"biz.conference.getCloudCallInfo",previewFileInDingTalk:"biz.cspace.preview",openChatByConversationId:"biz.chat.toConversationByOpenConversationId",chooseDingTalkDir:"biz.cspace.chooseSpaceDir",getOperateAuthCode:"runtime.permission.requestOperateAuthCode",isInTabWindow:"biz.tabwindow.isTab",createLiveClassRoom:"biz.live.startClassRoom",callUsers:"biz.telephone.call",makeCloudCall:"biz.conference.createCloudCall",ExternalChannelPublish:"biz.channel.externalChannelPublish",nfcReadCardNumber:"device.nfc.nfcReadCardNumber",liveChooseConversationAndUser:"biz.live.chooseConversationAndUser",getAuthCodeV2:"runtime.permission.requestAuthCodeV2",queryUserProfile:"biz.conference.queryUserProfile",requestMoneySubmmitOrder:"biz.requestMoney.startSubmittingOrder",requestAuthCode:"runtime.permission.requestAuthCodeV2",liveShare:"biz.live.share",chooseFile:"biz.file.chooseFile",setColorScheme:"internal.theme.setColorScheme",chooseConversation:"biz.chat.chooseConversation",saveVideoToPhotosAlbum:"biz.util.saveVideoToPhotosAlbum",setLanguage:"internal.setting.setLanguage",changeAppIcon:"internal.setting.changeAppIcon"}},function(e,t,n){(function(t,n){e.exports=n()})(0,function(){return function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=721)}({199:function(e,t,n){"use strict";var o=n(201);e.exports=o},201:function(e,t,n){"use strict";var o=n(203),r=n(204),i=n(202),a=n(205),d=new i,s=!1,u="",c=null,l={},v=/{.*}/;try{var f=window.name.match(v);if(f&&f[0])var l=JSON.parse(f[0])}catch(e){l={}}l.hostOrigin&&".dingtalk.com"===l.hostOrigin.split(":")[1].slice(0-".dingtalk.com".length)&&l.containerId&&(s=!0,u=l.hostOrigin,c=l.containerId);var p={},_=new Promise(function(e,t){p._resolve=e,p._reject=t}),P={},E=null;window.top!==window?(E=window.top,p._resolve()):"object"==typeof dingtalk&&"object"==typeof dingtalk.platform&&"function"==typeof dingtalk.platform.invokeAPI&&(E=window,p._resolve()),P[a.SYS_INIT]=function(e){E=e.frameWindow,p._resolve(),e.respond({})},window.addEventListener("message",function(e){var t=e.data,n=e.origin;if(n===u)if("response"===t.type&&t.msgId){var o=t.msgId,i=d.getMsyById(o);i&&i.methodName!==a.SYS_EVENT&&i.receiveResponse(t.body,!t.success)}else if("event"===t.type&&t.msgId){var o=t.msgId,i=d.getMsyById(o);i&&i.receiveEvent(t.eventName,t.body)}else if("request"===t.type&&t.msgId){var i=new r(e.source,n,t);P[i.methodName]&&P[i.methodName](i)}}),t.invokeAPI=function(e,t){var n=new o(c,e,t);return s&&_.then(function(){E&&E.postMessage(n.getPayload(),u),d.addPending(n)}),n};var N=null;t.addEventListener=function(e,n){N||(N=t.invokeAPI(a.SYS_EVENT,{})),N.addEventListener(e,n)},t.removeEventListener=function(e,t){N&&N.removeEventListener(e,t)}},202:function(e,t,n){"use strict";var o=function(){this.pendingMsgs={}};o.prototype.addPending=function(e){this.pendingMsgs[e.id]=e;var t=function(){delete this.pendingMsgs[e.id],e.removeEventListener("_finish",t)}.bind(this);e.addEventListener("_finish",t)},o.prototype.getMsyById=function(e){return this.pendingMsgs[e]},e.exports=o},203:function(e,t,n){"use strict";var o=n(716),r=n(715),i=0,a=Math.floor(1e3*Math.random()),d=function(){return 1e3*(1e3*a+Math.floor(1e3*Math.random()))+ ++i%1e3},s={code:408,reason:"timeout"},u={TIMEOUT:"_timeout",FINISH:"_finish"},c={timeout:-1},l=function(e,t,n,o){this.id=d(),this.methodName=t,this.containerId=e,this.option=r({},c,o);var n=n||{};this._p={},this.result=new Promise(function(e,t){this._p._resolve=e,this._p._reject=t}.bind(this)),this.callbacks={},this.plainMsg=this._handleMsg(n),this._eventsHandle={},this._timeoutTimer=null,this._initTimeout(),this.isFinish=!1};l.prototype._initTimeout=function(){this._clearTimeout(),this.option.timeout>0&&(this._timeoutTimer=setTimeout(function(){this.receiveEvent(u.TIMEOUT),this.receiveResponse(s,!0)}.bind(this),this.option.timeout))},l.prototype._clearTimeout=function(){clearTimeout(this._timeoutTimer)},l.prototype._handleMsg=function(e){var t={};return Object.keys(e).forEach(function(n){var r=e[n];"function"==typeof r&&"on"===n.slice(0,2)?this.callbacks[n]=r:t[n]=o(r)}.bind(this)),t},l.prototype.getPayload=function(){return{msgId:this.id,containerId:this.containerId,methodName:this.methodName,body:this.plainMsg,type:"request"}},l.prototype.receiveEvent=function(e,t){if(this.isFinish&&e!==u.FINISH)return!1;e!==u.FINISH&&e!==u.TIMEOUT&&this._initTimeout(),Array.isArray(this._eventsHandle[e])&&this._eventsHandle[e].forEach(function(e){try{e(t)}catch(e){console.error(t)}});var n="on"+e.charAt(0).toUpperCase()+e.slice(1);return this.callbacks[n]&&this.callbacks[n](t),!0},l.prototype.addEventListener=function(e,t){if(!e||"function"!=typeof t)throw"eventName is null or handle is not a function, addEventListener fail";Array.isArray(this._eventsHandle[e])||(this._eventsHandle[e]=[]),this._eventsHandle[e].push(t)},l.prototype.removeEventListener=function(e,t){if(!e||!t)throw"eventName is null or handle is null, invoke removeEventListener fail";if(Array.isArray(this._eventsHandle[e])){var n=this._eventsHandle[e].indexOf(t);-1!==n&&this._eventsHandle[e].splice(n,1)}},l.prototype.receiveResponse=function(e,t){if(!0===this.isFinish)return!1;this._clearTimeout();var t=!!t;return t?this._p._reject(e):this._p._resolve(e),setTimeout(function(){this.receiveEvent(u.FINISH)}.bind(this),0),this.isFinish=!0,!0},e.exports=l},204:function(e,t,n){"use strict";var o=function(e,t,n){if(this._msgId=n.msgId,this.frameWindow=e,this.methodName=n.methodName,this.clientOrigin=t,this.containerId=n.containerId,this.params=n.body,!this._msgId)throw"msgId not exist";if(!this.frameWindow)throw"frameWindow not exist";if(!this.methodName)throw"methodName not exits";if(!this.clientOrigin)throw"clientOrigin not exist";this.hasResponded=!1};o.prototype.respond=function(e,t){var t=!!t;if(!0!==this.hasResponded){var n={type:"response",success:!t,body:e,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin),this.hasResponded=!0}},o.prototype.emit=function(e,t){var n={type:"event",eventName:e,body:t,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin)},e.exports=o},205:function(e,t,n){"use strict";e.exports={SYS_EVENT:"SYS_openAPIContainerInitEvent",SYS_INIT:"SYS_openAPIContainerInit"}},4:function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},714:function(e,t,n){(function(e,n){function o(e,t){return e.set(t[0],t[1]),e}function r(e,t){return e.add(t),e}function i(e,t){for(var n=-1,o=e.length;++n<o&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}function d(e,t,n,o){var r=-1,i=e.length;for(o&&i&&(n=e[++r]);++r<i;)n=t(n,e[r],r,e);return n}function s(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function u(e){return e&&e.Object===Object?e:null}function c(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}function l(e){var t=-1,n=Array(e.size);return e.forEach(function(e,o){n[++t]=[o,e]}),n}function v(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function f(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function p(){this.__data__=jt?jt(null):{}}function _(e){return this.has(e)&&delete this.__data__[e]}function P(e){var t=this.__data__;if(jt){var n=t[e];return n===Se?void 0:n}return Pt.call(t,e)?t[e]:void 0}function E(e){var t=this.__data__;return jt?void 0!==t[e]:Pt.call(t,e)}function N(e,t){return this.__data__[e]=jt&&void 0===t?Se:t,this}function b(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function g(){this.__data__=[]}function h(e){var t=this.__data__,n=z(t,e);return!(n<0||(n==t.length-1?t.pop():It.call(t,n,1),0))}function k(e){var t=this.__data__,n=z(t,e);return n<0?void 0:t[n][1]}function M(e){return z(this.__data__,e)>-1}function m(e,t){var n=this.__data__,o=z(n,e);return o<0?n.push([e,t]):n[o][1]=t,this}function I(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function y(){this.__data__={hash:new f,map:new(At||b),string:new f}}function $(e){return oe(this,e).delete(e)}function S(e){return oe(this,e).get(e)}function A(e){return oe(this,e).has(e)}function U(e,t){return oe(this,e).set(e,t),this}function V(e){this.__data__=new b(e)}function O(){this.__data__=new b}function j(e){return this.__data__.delete(e)}function C(e){return this.__data__.get(e)}function w(e){return this.__data__.has(e)}function D(e,t){var n=this.__data__;return n instanceof b&&n.__data__.length==$e&&(n=this.__data__=new I(n.__data__)),n.set(e,t),this}function T(e,t,n){var o=e[t];Pt.call(e,t)&&Pe(o,n)&&(void 0!==n||t in e)||(e[t]=n)}function z(e,t){for(var n=e.length;n--;)if(Pe(e[n][0],t))return n;return-1}function B(e,t){return e&&ee(t,ye(t),e)}function F(e,t,n,o,r,a,d){var s;if(o&&(s=a?o(e,r,a,d):o(e)),void 0!==s)return s;if(!ke(e))return e;var u=Lt(e);if(u){if(s=se(e),!t)return Q(e,s)}else{var l=de(e),v=l==je||l==Ce;if(xt(e))return q(e,t);if(l==Te||l==Ue||v&&!a){if(c(e))return a?e:{};if(s=ue(v?{}:e),!t)return te(e,B(s,e))}else{if(!ot[l])return a?e:{};s=ce(e,l,F,t)}}d||(d=new V);var f=d.get(e);if(f)return f;if(d.set(e,s),!u)var p=n?ne(e):ye(e);return i(p||e,function(r,i){p&&(i=r,r=e[i]),T(s,i,F(r,t,n,o,i,e,d))}),s}function R(e){return ke(e)?Mt(e):{}}function L(e,t,n){var o=t(e);return Lt(e)?o:a(o,n(e))}function x(e,t){return Pt.call(e,t)||"object"==typeof e&&t in e&&null===ie(e)}function W(e){return $t(Object(e))}function q(e,t){if(t)return e.slice();var n=new e.constructor(e.length);return e.copy(n),n}function H(e){var t=new e.constructor(e.byteLength);return new ht(t).set(new ht(e)),t}function G(e,t){var n=t?H(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function J(e,t,n){return d(t?n(l(e),!0):l(e),o,new e.constructor)}function Y(e){var t=new e.constructor(e.source,et.exec(e));return t.lastIndex=e.lastIndex,t}function K(e,t,n){return d(t?n(v(e),!0):v(e),r,new e.constructor)}function X(e){return Ft?Object(Ft.call(e)):{}}function Z(e,t){var n=t?H(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function Q(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}function ee(e,t,n,o){n||(n={});for(var r=-1,i=t.length;++r<i;){var a=t[r];T(n,a,o?o(n[a],e[a],a,n,e):e[a])}return n}function te(e,t){return ee(e,ae(e),t)}function ne(e){return L(e,ye,ae)}function oe(e,t){var n=e.__data__;return fe(t)?n["string"==typeof t?"string":"hash"]:n.map}function re(e,t){var n=e[t];return me(n)?n:void 0}function ie(e){return yt(Object(e))}function ae(e){return kt(Object(e))}function de(e){return Et.call(e)}function se(e){var t=e.length,n=e.constructor(t);return t&&"string"==typeof e[0]&&Pt.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ue(e){return"function"!=typeof e.constructor||pe(e)?{}:R(ie(e))}function ce(e,t,n,o){var r=e.constructor;switch(t){case Le:return H(e);case Ve:case Oe:return new r(+e);case xe:return G(e,o);case We:case qe:case He:case Ge:case Je:case Ye:case Ke:case Xe:case Ze:return Z(e,o);case we:return J(e,o,n);case De:case Fe:return new r(e);case ze:return Y(e);case Be:return K(e,o,n);case Re:return X(e)}}function le(e){var t=e?e.length:void 0;return he(t)&&(Lt(e)||Ie(e)||Ee(e))?s(t,String):null}function ve(e,t){return!!(t=null==t?Ae:t)&&("number"==typeof e||nt.test(e))&&e>-1&&e%1==0&&e<t}function fe(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function pe(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||pt)}function _e(e){if(null!=e){try{return _t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Pe(e,t){return e===t||e!==e&&t!==t}function Ee(e){return be(e)&&Pt.call(e,"callee")&&(!mt.call(e,"callee")||Et.call(e)==Ue)}function Ne(e){return null!=e&&he(Rt(e))&&!ge(e)}function be(e){return Me(e)&&Ne(e)}function ge(e){var t=ke(e)?Et.call(e):"";return t==je||t==Ce}function he(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Ae}function ke(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function Me(e){return!!e&&"object"==typeof e}function me(e){return!!ke(e)&&(ge(e)||c(e)?Nt:tt).test(_e(e))}function Ie(e){return"string"==typeof e||!Lt(e)&&Me(e)&&Et.call(e)==Fe}function ye(e){var t=pe(e);if(!t&&!Ne(e))return W(e);var n=le(e),o=!!n,r=n||[],i=r.length;for(var a in e)!x(e,a)||o&&("length"==a||ve(a,i))||t&&"constructor"==a||r.push(a);return r}var $e=200,Se="__lodash_hash_undefined__",Ae=9007199254740991,Ue="[object Arguments]",Ve="[object Boolean]",Oe="[object Date]",je="[object Function]",Ce="[object GeneratorFunction]",we="[object Map]",De="[object Number]",Te="[object Object]",ze="[object RegExp]",Be="[object Set]",Fe="[object String]",Re="[object Symbol]",Le="[object ArrayBuffer]",xe="[object DataView]",We="[object Float32Array]",qe="[object Float64Array]",He="[object Int8Array]",Ge="[object Int16Array]",Je="[object Int32Array]",Ye="[object Uint8Array]",Ke="[object Uint8ClampedArray]",Xe="[object Uint16Array]",Ze="[object Uint32Array]",Qe=/[\\^$.*+?()[\]{}|]/g,et=/\w*$/,tt=/^\[object .+?Constructor\]$/,nt=/^(?:0|[1-9]\d*)$/,ot={};ot[Ue]=ot["[object Array]"]=ot[Le]=ot[xe]=ot[Ve]=ot[Oe]=ot[We]=ot[qe]=ot[He]=ot[Ge]=ot[Je]=ot[we]=ot[De]=ot[Te]=ot[ze]=ot[Be]=ot[Fe]=ot[Re]=ot[Ye]=ot[Ke]=ot[Xe]=ot[Ze]=!0,ot["[object Error]"]=ot[je]=ot["[object WeakMap]"]=!1;var rt={function:!0,object:!0},it=rt[typeof t]&&t&&!t.nodeType?t:void 0,at=rt[typeof e]&&e&&!e.nodeType?e:void 0,dt=at&&at.exports===it?it:void 0,st=u(it&&at&&"object"==typeof n&&n),ut=u(rt[typeof self]&&self),ct=u(rt[typeof window]&&window),lt=u(rt[typeof this]&&this),vt=st||ct!==(lt&&lt.window)&&ct||ut||lt||Function("return this")(),ft=Array.prototype,pt=Object.prototype,_t=Function.prototype.toString,Pt=pt.hasOwnProperty,Et=pt.toString,Nt=RegExp("^"+_t.call(Pt).replace(Qe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),bt=dt?vt.Buffer:void 0,gt=vt.Symbol,ht=vt.Uint8Array,kt=Object.getOwnPropertySymbols,Mt=Object.create,mt=pt.propertyIsEnumerable,It=ft.splice,yt=Object.getPrototypeOf,$t=Object.keys,St=re(vt,"DataView"),At=re(vt,"Map"),Ut=re(vt,"Promise"),Vt=re(vt,"Set"),Ot=re(vt,"WeakMap"),jt=re(Object,"create"),Ct=_e(St),wt=_e(At),Dt=_e(Ut),Tt=_e(Vt),zt=_e(Ot),Bt=gt?gt.prototype:void 0,Ft=Bt?Bt.valueOf:void 0;f.prototype.clear=p,f.prototype.delete=_,f.prototype.get=P,f.prototype.has=E,f.prototype.set=N,b.prototype.clear=g,b.prototype.delete=h,b.prototype.get=k,b.prototype.has=M,b.prototype.set=m,I.prototype.clear=y,I.prototype.delete=$,I.prototype.get=S,I.prototype.has=A,I.prototype.set=U,V.prototype.clear=O,V.prototype.delete=j,V.prototype.get=C,V.prototype.has=w,V.prototype.set=D;var Rt=function(e){return function(e){return null==e?void 0:e.length}}();kt||(ae=function(){return[]}),(St&&de(new St(new ArrayBuffer(1)))!=xe||At&&de(new At)!=we||Ut&&"[object Promise]"!=de(Ut.resolve())||Vt&&de(new Vt)!=Be||Ot&&"[object WeakMap]"!=de(new Ot))&&(de=function(e){var t=Et.call(e),n=t==Te?e.constructor:void 0,o=n?_e(n):void 0;if(o)switch(o){case Ct:return xe;case wt:return we;case Dt:return"[object Promise]";case Tt:return Be;case zt:return"[object WeakMap]"}return t});var Lt=Array.isArray,xt=bt?function(e){return e instanceof bt}:function(e){return function(){return!1}}();e.exports=F}).call(t,n(719)(e),n(4))},715:function(e,t,n){function o(e,t,n){var o=e[t];g.call(e,t)&&s(o,n)&&(void 0!==n||t in e)||(e[t]=n)}function r(e,t,n,r){n||(n={});for(var i=-1,a=t.length;++i<a;){var d=t[i];o(n,d,r?r(n[d],e[d],d,n,e):e[d])}return n}function i(e,t){return!!(t=null==t?_:t)&&("number"==typeof e||N.test(e))&&e>-1&&e%1==0&&e<t}function a(e,t,n){if(!v(n))return!1;var o=typeof t;return!!("number"==o?u(n)&&i(t,n.length):"string"==o&&t in n)&&s(n[t],e)}function d(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||b)}function s(e,t){return e===t||e!==e&&t!==t}function u(e){return null!=e&&l(m(e))&&!c(e)}function c(e){var t=v(e)?h.call(e):"";return t==P||t==E}function l(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=_}function v(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var f=n(717),p=n(718),_=9007199254740991,P="[object Function]",E="[object GeneratorFunction]",N=/^(?:0|[1-9]\d*)$/,b=Object.prototype,g=b.hasOwnProperty,h=b.toString,k=b.propertyIsEnumerable,M=!k.call({valueOf:1},"valueOf"),m=function(e){return function(e){return null==e?void 0:e.length}}(),I=function(e){return p(function(t,n){var o=-1,r=n.length,i=r>1?n[r-1]:void 0,d=r>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(r--,i):void 0,d&&a(n[0],n[1],d)&&(i=r<3?void 0:i,r=1),t=Object(t);++o<r;){var s=n[o];s&&e(t,s)}return t})}(function(e,t){if(M||d(t)||u(t))return void r(t,f(t),e);for(var n in t)g.call(t,n)&&o(e,n,t[n])});e.exports=I},716:function(e,t,n){function o(e){return r(e,!0,!0)}var r=n(714);e.exports=o},717:function(e,t){function n(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function o(e,t){var o=I(e)||d(e)?n(e.length,String):[],r=o.length,a=!!r;for(var s in e)!t&&!h.call(e,s)||a&&("length"==s||i(s,r))||o.push(s);return o}function r(e){if(!a(e))return m(e);var t=[];for(var n in Object(e))h.call(e,n)&&"constructor"!=n&&t.push(n);return t}function i(e,t){return!!(t=null==t?_:t)&&("number"==typeof e||b.test(e))&&e>-1&&e%1==0&&e<t}function a(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||g)}function d(e){return u(e)&&h.call(e,"callee")&&(!M.call(e,"callee")||k.call(e)==P)}function s(e){return null!=e&&l(e.length)&&!c(e)}function u(e){return f(e)&&s(e)}function c(e){var t=v(e)?k.call(e):"";return t==E||t==N}function l(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=_}function v(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function f(e){return!!e&&"object"==typeof e}function p(e){return s(e)?o(e):r(e)}var _=9007199254740991,P="[object Arguments]",E="[object Function]",N="[object GeneratorFunction]",b=/^(?:0|[1-9]\d*)$/,g=Object.prototype,h=g.hasOwnProperty,k=g.toString,M=g.propertyIsEnumerable,m=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object),I=Array.isArray;e.exports=p},718:function(e,t){function n(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function o(e,t){return t=M(void 0===t?e.length-1:t,0),function(){for(var o=arguments,r=-1,i=M(o.length-t,0),a=Array(i);++r<i;)a[r]=o[t+r];r=-1;for(var d=Array(t+1);++r<t;)d[r]=o[r];return d[t]=a,n(e,this,d)}}function r(e,t){if("function"!=typeof e)throw new TypeError(l);return t=void 0===t?t:u(t),o(e,t)}function i(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function a(e){return!!e&&"object"==typeof e}function d(e){return"symbol"==typeof e||a(e)&&k.call(e)==_}function s(e){return e?(e=c(e))===v||e===-v?(e<0?-1:1)*f:e===e?e:0:0===e?e:0}function u(e){var t=s(e),n=t%1;return t===t?n?t-n:t:0}function c(e){if("number"==typeof e)return e;if(d(e))return p;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(P,"");var n=N.test(e);return n||b.test(e)?g(e.slice(2),n?2:8):E.test(e)?p:+e}var l="Expected a function",v=1/0,f=1.7976931348623157e308,p=NaN,_="[object Symbol]",P=/^\s+|\s+$/g,E=/^[-+]0x[0-9a-f]+$/i,N=/^0b[01]+$/i,b=/^0o[0-7]+$/i,g=parseInt,h=Object.prototype,k=h.toString,M=Math.max;e.exports=r},719:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},721:function(e,t,n){e.exports=n(199)}})})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e,t){return new Promise(function(n,o){dd.dtBridge({m:e,args:t,onSuccess:function(e){"function"==typeof t.success?t.success(e):"function"==typeof t.onSuccess&&t.onSuccess(e),n(e)},onFail:function(e){"function"==typeof t.fail?t.fail(e):"function"==typeof t.onFail&&t.onFail(e),o(e)}})})};t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.androidWeexBridge=t.iosWeexBridge=t.requireModule=void 0;t.requireModule=function(e){return"undefined"!=typeof __weex_require__?__weex_require__("@weex-module/"+e):"undefined"!=typeof weex?weex.requireModule(e):void 0},t.iosWeexBridge=function(){return Promise.resolve(function(e,n){return new Promise(function(o,r){var i=t.requireModule("nuvajs-exec"),a=e.split("."),d=a.pop(),s=a.join(".");i.exec({plugin:s,action:d,args:n},function(e){e&&"0"===e.errorCode?("function"==typeof n.success?n.success(e.result):"function"==typeof n.onSuccess&&n.onSuccess(e.result),o(e.result)):("function"==typeof n.fail?n.fail(e.result):"function"==typeof n.onFail&&n.onFail(e.result),r(e.result))})})})},t.androidWeexBridge=function(){return Promise.resolve(function(e,n){return new Promise(function(o,r){var i=t.requireModule("nuvajs-exec"),a=e.split("."),d=a.pop(),s=a.join(".");i.exec({plugin:s,action:d,args:n},function(e){var t={};try{if(e&&e.__message__)if("object"==typeof e.__message__)t=e.__message__;else try{t=JSON.parse(e.__message__)}catch(n){"string"==typeof e.__message__&&(t=e.__message__)}}catch(e){}e&&1===parseInt(e.__status__+"",10)?("function"==typeof n.onSuccess&&n.onSuccess(t),o(t)):("function"==typeof n.onFail&&n.onFail(t),r(t))})})})}},function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.formatLog=t.diagnosticMessageMap=t.LogLevel=void 0;var n;(function(e){e.INFO="INFO",e.WARN="WARN",e.ERROR="ERROR"})(n=t.LogLevel||(t.LogLevel={}));var o=function(e,t,n,o){return void 0===o&&(o=void 0),{code:e,category:t,message:n,solution:o}};t.diagnosticMessageMap={config_debug_deprecated:o(1010,n.WARN,"This is a deprecated feature (dd.debug - debug:true), recommend use dd.devConfig"),dd_config_wrap_deprecated:o(1020,n.WARN,"You don 't use a dd.config, so you don't need to wrap dd.ready, recommend remove dd.ready"),not_support_event_on:o(1030,n.WARN,"\"event.on\" do not support the current platform ('{0}')"),not_support_event_off:o(1040,n.WARN,"\"event.off\" do not support the current platform ('{0}')"),repeat_config:o(1040,n.WARN,"dd.config has been executed, please don't repeat config"),JsBridge_init_fail:o(5010,n.ERROR,"JsBridge initialization fails, jsapi will not work"),auto_bridge_init_error:o(5020,n.ERROR,"auto bridgeInit error"),JsBridge_init_fail_dd_config:o(5010,n.ERROR,'JsBridge initialization failed and "dd.config" failed to call'),not_support_env:o(4040,n.ERROR,"Do not support the current environment：'{0}'"),call_api_support_platform_error:o(4050,n.ERROR,"'{0}' do not support the current platform ('{1}')"),call_api_config_platform_error:o(4060,n.ERROR,"This API method is not configured for the platform ('{0}')"),call_api_on_before_error:o(4060,n.ERROR,"Call Hook:onBeforeInvokeAPI failed , reason: '{0}'"),call_api_on_after_error:o(4060,n.ERROR,"Call Hook:onAfterInvokeAPI failed , reason: '{0}'")},t.formatLog=function(t){for(var n,o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];var i="[DINGTALK-JSAPI] "+t.category+" "+t.code+": "+t.message.replace(/{(\d)}/g,function(e,t){return o[t]||e});return"object"==typeof e&&"production"!==(null===(n=null===e||void 0===e?void 0:e.env)||void 0===n?void 0:n.NODE_ENV)&&console.warn(i),i}}).call(t,n(165))},function(e,t,n){"use strict";function o(e){return"function"==typeof e}function r(e,t){function n(e){return parseInt(e,10)||0}for(var o=e.split(".").map(n),r=t.split(".").map(n),i=0;i<o.length;i++){if(void 0===r[i])return!1;if(o[i]<r[i])return!1;if(o[i]>r[i])return!0}return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.LogLevel=t.APP_TYPE=t.ENV_ENUM_SUB=t.ENV_ENUM=t.ERROR_CODE=t.compareVersion=t.isFunction=void 0,t.isFunction=o,t.compareVersion=r;(function(e){e.cancel="-1",e.not_exist="1",e.no_permission="7"})(t.ERROR_CODE||(t.ERROR_CODE={}));(function(e){e.pc="pc",e.android="android",e.ios="ios",e.gdtPc="gdtPc",e.gdtAndroid="gdtAndroid",e.gdtIos="gdtIos",e.gdtStandardAndroid="gdtStandardAndroid",e.gdtStandardIos="gdtStandardIos",e.notInDingTalk="notInDingTalk",e.windows="windows",e.mac="mac"})(t.ENV_ENUM||(t.ENV_ENUM={}));(function(e){e.mac="mac",e.win="win",e.noSub="noSub"})(t.ENV_ENUM_SUB||(t.ENV_ENUM_SUB={}));(function(e){e.WEB="WEB",e.MINI_APP="MINI_APP",e.WEEX="WEEX",e.WEBVIEW_IN_MINIAPP="WEBVIEW_IN_MINIAPP",e.WEEX_WIDGET="WEEX_WIDGET"})(t.APP_TYPE||(t.APP_TYPE={}));(function(e){e[e.INFO=1]="INFO",e[e.WARNING=2]="WARNING",e[e.ERROR=3]="ERROR"})(t.LogLevel||(t.LogLevel={}))},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ExternalChannelPublish$=void 0;var i=n(0),a="biz.channel.externalChannelPublish";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"7.0.50"},r)),t.ExternalChannelPublish$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.addPhoneContact$=void 0;var i=n(0),a="biz.phoneContact.add";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.addPhoneContact$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,{message:e.content,title:null===e||void 0===e?void 0:e.title,buttonName:e.buttonText,success:e.success,fail:e.fail,complete:e.complete})}Object.defineProperty(t,"__esModule",{value:!0}),t.alert$=void 0;var r=n(0),i="device.notification.alert";r.ddSdk.setAPI(i,{}),t.alert$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.callUsers$=void 0;var i=n(0),a="biz.telephone.call";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.callUsers$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkAuth$=void 0;var i=n(0),a="biz.util.checkAuth";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.checkAuth$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkBizCall$=void 0;var i=n(0),a="biz.telephone.checkBizCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",resultDeal:function(e){return e?{isSupport:!0}:{isSupport:!1}}},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.checkBizCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseChat$=void 0;var i=n(0),a="biz.chat.chooseConversationByCorpId";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.chooseChat$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseConversation$=void 0;var i=n(0),a="biz.chat.chooseConversation";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.chooseConversation$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDateRangeInCalendar$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseInterval";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseDateRangeInCalendar$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDateTime$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseDateTime";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseDateTime$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDepartments$=void 0;var i=n(0),a="biz.contact.departmentsPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.chooseDepartments$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDingTalkDir$=void 0;var i=n(0),a="biz.cspace.chooseSpaceDir";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.chooseDingTalkDir$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDistrict$=void 0;var i=n(0),a="biz.util.chooseRegion";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.chooseDistrict$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseExternalUsers$=void 0;var i=n(0),a="biz.contact.externalComplexPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.chooseExternalUsers$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseFile$=void 0;var i=n(0),a="biz.file.chooseFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.1.5"},r[i.ENV_ENUM.android]={vs:"7.1.5"},r[i.ENV_ENUM.pc]={vs:"7.1.10"},r)),t.chooseFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseHalfDayInCalendar$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseHalfDay";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseHalfDayInCalendar$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseImage$=void 0;var i=n(0),a="biz.util.chooseImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.chooseImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseOneDayInCalendar$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseOneDay";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseOneDayInCalendar$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.choosePhonebook$=void 0;var i=n(0),a="biz.contact.chooseMobileContacts";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.choosePhonebook$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseStaffForPC$=void 0;var i=n(0),a="biz.contact.choose";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.chooseStaffForPC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseUserFromList$=void 0;var i=n(0),a="biz.customContact.choose";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.chooseUserFromList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.clearShake$=void 0;var i=n(0),a="device.accelerometer.clearShake";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.clearShake$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.closePage$=void 0;var i=n(0),a="biz.navigation.close";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.closePage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.complexChoose$=void 0;var i=n(0),a="biz.contact.complexPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.complexChoose$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.compressImage$=void 0;var i=n(0),a="biz.util.compressImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.compressImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,{title:e.title,message:e.content,buttonLabels:[e.cancelButtonText,e.confirmButtonText],success:e.success,fail:e.fail,complete:e.complete})}Object.defineProperty(t,"__esModule",{value:!0}),t.confirm$=void 0;var r=n(0),i="device.notification.confirm";r.ddSdk.setAPI(i,{}),t.confirm$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createDing$=void 0;var i=n(0),a="biz.ding.create";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.createDing$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createDingForPC$=void 0;var i=n(0),a="biz.ding.post";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.createDingForPC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createGroupChat$=void 0;var i=n(0),a="biz.contact.createGroup";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.createGroupChat$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createLiveClassRoom$=void 0;var i=n(0),a="biz.live.startClassRoom";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.createLiveClassRoom$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.customChooseUsers$=void 0;var i=n(0),a=n(1),d="biz.customContact.multipleChoose",s=a.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:s},r)),t.customChooseUsers$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.datePicker$=void 0;var i=n(0),a="biz.util.datetimepicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.datePicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.dateRangePicker$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseInterval";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.dateRangePicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.decrypt$=void 0;var i=n(0),a="biz.util.decrypt";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.decrypt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.disablePullDownRefresh$=void 0;var i=n(0),a="ui.pullToRefresh.disable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.disablePullDownRefresh$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.disableWebViewBounce$=void 0;var i=n(0),a="ui.webViewBounce.disable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.disableWebViewBounce$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.downloadAudio$=void 0;var i=n(0),a="device.audio.download";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.downloadAudio$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.downloadFile$=void 0;var i=n(0),a="biz.file.downloadFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.downloadFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.editExternalUser$=void 0;var i=n(0),a="biz.contact.externalEditForm";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.editExternalUser$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.enablePullDownRefresh$=void 0;var i=n(0),a="ui.pullToRefresh.enable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.enablePullDownRefresh$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.enableWebViewBounce$=void 0;var i=n(0),a="ui.webViewBounce.enable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.enableWebViewBounce$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.encrypt$=void 0;var i=n(0),a="biz.util.encrypt";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.encrypt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.exclusiveLiveCheck$=void 0;var i=n(0),a="biz.ATMBle.exclusiveLiveCheck";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.5.40"},r)),t.exclusiveLiveCheck$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.generateImageFromCode$=void 0;var i=n(0),a="biz.util.generateImageFromCode";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.generateImageFromCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getAuthCode$=void 0;var i=n(0),a="runtime.permission.requestAuthCode";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.getAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getAuthCodeV2$=void 0;var i=n(0),a="runtime.permission.requestAuthCodeV2";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.45"},r[i.ENV_ENUM.android]={vs:"7.0.45"},r[i.ENV_ENUM.pc]={vs:"7.0.50"},r)),t.getAuthCodeV2$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getBatteryInfo$=void 0;var i=n(0),a="device.base.getBatteryInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.60"},r[i.ENV_ENUM.android]={vs:"6.5.60"},r)),t.getBatteryInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",i({_action:d},e))}var r,i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.getBeacons$=void 0;var a=n(0),d="getBeacons";a.ddSdk.setAPI("runtime.h5nuvabridge.exec",(r={},r[a.ENV_ENUM.ios]={vs:"4.6.38"},r[a.ENV_ENUM.android]={vs:"4.6.38"},r)),t.getBeacons$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getCloudCallInfo$=void 0;var i=n(0),a="biz.conference.getCloudCallInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.getCloudCallInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getCloudCallList$=void 0;var i=n(0),a="biz.conference.getCloudCallList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.9"},r)),t.getCloudCallList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getDeviceUUID$=void 0;var i=n(0),a="device.base.getUUID";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getDeviceUUID$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getLocatingStatus$=void 0;var i=n(0),a="device.geolocation.status";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getLocatingStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getLocation$=void 0;var i=n(0),a="device.geolocation.get";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getLocation$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getNetworkType$=void 0;var i=n(1),a=n(0),d="device.connection.getNetworkType";a.ddSdk.setAPI(d,(r={},r[a.ENV_ENUM.ios]={vs:"6.0.0",resultDeal:i.getNetWorkTypeResultDeal},r[a.ENV_ENUM.android]={vs:"6.0.0",resultDeal:i.getNetWorkTypeResultDeal},r[a.ENV_ENUM.pc]={vs:"6.5.60",resultDeal:function(e){return"none"!==e.result&&"unknown"!==e.result?{netWorkAvailable:!0}:{netWorkAvailable:!1}}},r)),t.getNetworkType$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getOperateAuthCode$=void 0;var i=n(0),a="runtime.permission.requestOperateAuthCode";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.getOperateAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getScreenBrightness$=void 0;var i=n(0),a="device.screen.getScreenBrightness";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.getScreenBrightness$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{name:e.key})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getStorage$=void 0;var i=n(0),a="util.domainStorage.getItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.getStorage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getSystemInfo$=void 0;var i=n(0),a="device.base.getPhoneInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getSystemInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getSystemSettings$=void 0;var i=n(0),a="device.base.openSystemSetting";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.15"},r[i.ENV_ENUM.android]={vs:"4.6.36"},r)),t.getSystemSettings$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getUserExclusiveInfo$=void 0;var i=n(0),a="biz.realm.getUserExclusiveInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.15"},r[i.ENV_ENUM.android]={vs:"6.0.15"},r[i.ENV_ENUM.pc]={vs:"6.0.17"},r)),t.getUserExclusiveInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getWifiHotspotStatus$=void 0;var i=n(0),a="device.base.getInterface";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getWifiHotspotStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getWifiStatus$=void 0;var i=n(0),a="device.base.getWifiStatus";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.getWifiStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.goBackPage$=void 0;var i=n(0),a="biz.navigation.goBack";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.goBackPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.hideLoading$=void 0;var i=n(0),a="device.notification.hidePreloader";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.hideLoading$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.hideToast$=void 0;var i=n(0),a="device.notification.hideToast";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.hideToast$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isInTabWindow$=void 0;var i=n(0),a="biz.tabwindow.isTab";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.5.10"},r)),t.isInTabWindow$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isLocalFileExist$=void 0;var i=n(0),a="biz.util.isLocalFileExist";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.isLocalFileExist$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isScreenReaderEnabled$=void 0;var i=n(0),a="device.screen.isScreenReaderEnabled";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.60"},r[i.ENV_ENUM.android]={vs:"6.5.60"},r)),t.isScreenReaderEnabled$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.locateInMap$=void 0;var i=n(0),a="biz.map.locate";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.locateInMap$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.makeCloudCall$=void 0;var i=n(0),a="biz.conference.createCloudCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.9"},r)),t.makeCloudCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.makeVideoConfCall$=void 0;var i=n(0),a="biz.conference.videoConfCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.makeVideoConfCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.multiSelect$=void 0;var i=n(0),a="biz.util.multiSelect";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.multiSelect$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.navigateBackPage$=void 0;var i=n(0),a="biz.navigation.navigateBackPage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.45"},r[i.ENV_ENUM.android]={vs:"6.5.45"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.navigateBackPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.navigateToPage$=void 0;var i=n(0),a="biz.navigation.navigateToPage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.45"},r[i.ENV_ENUM.android]={vs:"6.5.45"},r[i.ENV_ENUM.pc]={vs:"7.0.0"},r)),t.navigateToPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.nfcReadCardNumber$=void 0;var i=n(0),a="device.nfc.nfcReadCardNumber";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"7.0.60"},r)),t.nfcReadCardNumber$=o,t.default=o},function(e,t,n){"use strict";function o(e){i.ddSdk.getExportSdk().on("bizEvent."+a,function(t){"function"==typeof e.success?e.success(t):"function"==typeof e.onSuccess&&e.onSuccess(t)})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onBeaconServiceChange$=void 0;var i=n(0),a="beaconServiceChange";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.38"},r[i.ENV_ENUM.android]={vs:"4.6.38"},r)),t.onBeaconServiceChange$=o,t.default=o},function(e,t,n){"use strict";function o(e){i.ddSdk.getExportSdk().on("bizEvent."+a,function(t){"function"==typeof e.success?e.success(t):"function"==typeof e.onSuccess&&e.onSuccess(t)})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onBeaconUpdate$=void 0;var i=n(0),a="beaconUpdate";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.38"},r[i.ENV_ENUM.android]={vs:"4.6.38"},r)),t.onBeaconUpdate$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onPlayAudioEnd$=void 0;var i=n(0),a="device.audio.onPlayEnd";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.onPlayAudioEnd$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onRecordEnd$=void 0;var i=n(0),a="device.audio.onRecordEnd";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.onRecordEnd$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openChatByChatId$=void 0;var i=n(0),a="biz.chat.toConversation";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.openChatByChatId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openChatByConversationId$=void 0;var i=n(0),a="biz.chat.toConversationByOpenConversationId";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openChatByConversationId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openChatByUserId$=void 0;var i=n(0),a="biz.chat.openSingleChat";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.openChatByUserId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openDocument$=void 0;var i=n(0),a="biz.util.openDocument";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.openDocument$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openLink$=void 0;var i=n(0),a="biz.util.openLink";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openLink$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openLocalFile$=void 0;var i=n(0),a="biz.util.openLocalFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openLocalFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openLocation$=void 0;var i=n(0),a="biz.map.view";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.openLocation$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openMicroApp$=void 0;var i=n(0),a="biz.microApp.openApp";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.openMicroApp$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openPageInMicroApp$=void 0;var i=n(0),a="biz.util.open";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openPageInMicroApp$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openPageInModalForPC$=void 0;var i=n(0),a="biz.util.openModal";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openPageInModalForPC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openPageInSlidePanelForPC$=void 0;var i=n(0),a="biz.util.openSlidePanel";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.openPageInSlidePanelForPC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openPageInWorkBenchForPC$=void 0;var i=n(0),a="biz.util.invokeWorkbench";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.8"},r)),t.openPageInWorkBenchForPC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.pauseAduio$=void 0;var i=n(0),a="device.audio.pause";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.pauseAduio$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.playAduio$=void 0;var i=n(0),a="device.audio.play";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.playAduio$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.previewFileInDingTalk$=void 0;var i=n(0),a="biz.cspace.preview";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.previewFileInDingTalk$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI(d,e)}var r,i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.previewImage$=void 0;var a=n(0),d="biz.util.previewImage";a.ddSdk.setAPI(d,(r={},r[a.ENV_ENUM.ios]={vs:"6.0.0"},r[a.ENV_ENUM.android]={vs:"6.0.0"},r[a.ENV_ENUM.pc]={vs:"6.0.0",paramsDeal:function(e){return i(i({},e),{current:e.urls[e.current]})}},r)),t.previewImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.previewImagesInDingTalkBatch$=void 0;var i=n(0),a="biz.cspace.previewDentryImages";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.30"},r[i.ENV_ENUM.android]={vs:"6.3.30"},r)),t.previewImagesInDingTalkBatch$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{message:e.message,title:e.title,defaultText:e.placeholder,buttonLabels:[e.cancelButtonText,e.okButtonText],success:e.success,fail:e.fail,complete:e.complete})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.prompt$=void 0;var i=n(0),a="device.notification.prompt";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.prompt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.quickCallList$=void 0;var i=n(0),a="biz.telephone.quickCallList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.quickCallList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.quitPage$=void 0;var i=n(0),a="biz.navigation.quit";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.quitPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.readNFC$=void 0;var i=n(0),a="device.nfc.nfcRead";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.readNFC$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{name:e.key})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.removeStorage$=void 0;var i=n(0),a="util.domainStorage.removeItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.removeStorage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.replacePage$=void 0;var i=n(0),a="biz.navigation.replace";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.replacePage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestAuthCode$=void 0;var i=n(0),a="runtime.permission.requestAuthCodeV2";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.45"},r[i.ENV_ENUM.android]={vs:"7.0.45"},r[i.ENV_ENUM.pc]={vs:"7.0.50"},r)),t.requestAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestMoneySubmmitOrder$=void 0;var i=n(0),a="biz.requestMoney.startSubmittingOrder";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.1.5"},r[i.ENV_ENUM.android]={vs:"7.1.5"},r)),t.requestMoneySubmmitOrder$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.resetScreenView$=void 0;var i=n(0),a="device.screen.resetView";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.resetScreenView$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.resumeAudio$=void 0;var i=n(0),a="device.audio.resume";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.resumeAudio$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.rotateScreenView$=void 0;var i=n(0),a="device.screen.rotateView";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.rotateScreenView$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.rsa$=void 0;var i=n(0),a="biz.data.rsa";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.rsa$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.saveFileToDingTalk$=void 0;var i=n(0),a="biz.cspace.saveFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.saveFileToDingTalk$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.saveVideoToPhotosAlbum$=void 0;var i=n(0),a="biz.util.saveVideoToPhotosAlbum";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.1.20"},r[i.ENV_ENUM.android]={vs:"7.1.20"},r)),t.saveVideoToPhotosAlbum$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.scan$=void 0;var i=n(1),a=n(0),d="biz.util.scan";a.ddSdk.setAPI(d,(r={},r[a.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:i.scanParamsDeal},r[a.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:i.scanParamsDeal},r)),t.scan$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.scanCard$=void 0;var i=n(0),a="biz.util.scanCard";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.scanCard$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.searchMap$=void 0;var i=n(0),a="biz.map.search";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.searchMap$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setClipboard$=void 0;var i=n(0),a="biz.clipboardData.setData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.5.60"},r)),t.setClipboard$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setKeepScreenOn$=void 0;var i=n(0),a="biz.util.setScreenKeepOn";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.26"},r[i.ENV_ENUM.android]={vs:"5.1.26"},r)),t.setKeepScreenOn$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setNavigationIcon$=void 0;var i=n(0),a=n(1),d="biz.navigation.setIcon",s=a.genDefaultParamsDealFn({watch:!0,showIcon:!0,iconIndex:1});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:s},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.setNavigationIcon$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setNavigationLeft$=void 0;var i=n(0),a=n(1),d="biz.navigation.setLeft",s=a.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:s},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.setNavigationLeft$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setNavigationTitle$=void 0;var i=n(0),a="biz.navigation.setTitle";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.setNavigationTitle$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setScreenBrightness$=void 0;var i=n(0),a="device.screen.setScreenBrightness";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.setScreenBrightness$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{name:e.key,value:e.data})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setStorage$=void 0;var i=n(0),a="util.domainStorage.setItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.0"},r)),t.setStorage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.share$=void 0;var i=n(0),a="biz.util.share";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.share$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI(d,{title:e.title,cancelButton:e.cancelButtonText,otherButtons:e.items,success:e.success,fail:e.fail,complete:e.complete})}var r,i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.showActionSheet$=void 0;var a=n(0),d="device.notification.actionSheet";a.ddSdk.setAPI(d,(r={},r[a.ENV_ENUM.ios]={vs:"7.0.10",resultDeal:function(e){return i(i({},e),{index:e.buttonIndex})}},r[a.ENV_ENUM.android]={vs:"7.0.10",resultDeal:function(e){return i(i({},e),{index:e.buttonIndex})}},r[a.ENV_ENUM.pc]={vs:"7.0.10",resultDeal:function(e){return i(i({},e),{index:e.buttonIndex})}},r)),t.showActionSheet$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showAuthGuide$=void 0;var i=n(0),a="biz.util.showAuthGuide";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.showAuthGuide$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showCallMenu$=void 0;var i=n(0),a="biz.telephone.showCallMenu";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.showCallMenu$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{text:e.content,showIcon:!0,success:e.success,fail:e.fail,complete:e.complete})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showLoading$=void 0;var i=n(0),a="device.notification.showPreloader";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.showLoading$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showModal$=void 0;var i=n(0),a="device.notification.extendModal";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.showModal$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showSharePanel$=void 0;var i=n(0),a="biz.util.showSharePanel";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.showSharePanel$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,{icon:e.type,duration:e.duration?e.duration/1e3:3,text:e.content,success:e.success,fail:e.fail,complete:e.complete})}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showToast$=void 0;var i=n(0),a="device.notification.toast";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.showToast$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.singleSelect$=void 0;var i=n(0),a="biz.util.chosen";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.singleSelect$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",i({_action:d},e))}var r,i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.startBeaconDiscovery$=void 0;var a=n(0),d="startBeaconDiscovery";a.ddSdk.setAPI("runtime.h5nuvabridge.exec",(r={},r[a.ENV_ENUM.ios]={vs:"4.6.38"},r[a.ENV_ENUM.android]={vs:"4.6.38"},r)),t.startBeaconDiscovery$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startLocating$=void 0;var i=n(0),a="device.geolocation.start";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.startLocating$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startRecord$=void 0;var i=n(0),a="device.audio.startRecord";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"7.0.30"},r)),t.startRecord$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopAudio$=void 0;var i=n(0),a="device.audio.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.stopAudio$=o,t.default=o},function(e,t,n){"use strict";function o(e){return a.ddSdk.invokeAPI("runtime.h5nuvabridge.exec",i({_action:d},e))}var r,i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.stopBeaconDiscovery$=void 0;var a=n(0),d="stopBeaconDiscovery";a.ddSdk.setAPI("runtime.h5nuvabridge.exec",(r={},r[a.ENV_ENUM.ios]={vs:"4.6.38"},r[a.ENV_ENUM.android]={vs:"4.6.38"},r)),t.stopBeaconDiscovery$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopLocating$=void 0;var i=n(0),a="device.geolocation.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.stopLocating$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopPullDownRefresh$=void 0;var i=n(0),a="ui.pullToRefresh.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.stopPullDownRefresh$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopRecord$=void 0;var i=n(0),a="device.audio.stopRecord";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"7.0.30"},r)),t.stopRecord$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.timePicker$=void 0;var i=n(0),a="biz.util.timepicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.timePicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.translateVoice$=void 0;var i=n(0),a="device.audio.translateVoice";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.translateVoice$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadAttachmentToDingTalk$=void 0;var i=n(0),a="biz.util.uploadAttachment";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.uploadAttachmentToDingTalk$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadFile$=void 0;var i=n(0),a="biz.util.uploadFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.uploadFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.vibrate$=void 0;var i=n(0),a="device.notification.vibrate";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.vibrate$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.watchShake$=void 0;var i=n(0),a=n(1),d="device.accelerometer.watchShake";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0",paramsDeal:function(e){return a.forceChangeParamsDealFn({sensitivity:3.2})(a.addWatchParamsDeal(e))}},r[i.ENV_ENUM.android]={vs:"6.0.0",paramsDeal:a.addWatchParamsDeal},r)),t.watchShake$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.writeNFC$=void 0;var i=n(0),a="device.nfc.nfcWrite";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.0"},r)),t.writeNFC$=o,t.default=o},function(e,t,n){"use strict";var o=n(0),r=n(415),i=Object.assign({},r,o.ddSdk.getExportSdk());e.exports=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.off=t.on=void 0;var o=["resume","pause","online","offline","backbutton","goBack","pullToRefresh","message","recycle","restore","drawer","tab","navHelpIcon","navRightButton","navMenu","navTitle","appLinkResponse","internalPageLinkResponse","networkEvent","hostTaskEvent","deviceOrientationChanged","autoCheckIn","deviceFound","hostCheckIn","screenshot","becomeActive","keepAlive","navTitleClick","sharePage","wxNotify","editNoteCommand","updateStyle","qrscanCommonNotify","__message__","dtChannelEvent","livePlayerEventPlay","livePlayerEventPause","livePlayerEventEnded","livePlayerEventError","navActions","attendEvents"],r=function(){return"undefined"==typeof WeakMap?void 0:new WeakMap}(),i=function(e,t){if(r){var n=r.get(t);return void 0===n?(n=function(e){var o=e.detail;if(o.namespace&&o.eventName){var r=o.namespace+"."+o.eventName;n&&-1!==n.__eventTypeList__.indexOf(r)&&t(o.data)}},n.__eventTypeList__=[e],r.set(t,n)):-1===n.__eventTypeList__.indexOf(e)&&n.__eventTypeList__.push(e),n}},a=function(e,t){if(r){var n=r.get(t);return n&&-1!==n.__eventTypeList__.indexOf(e)&&n.__eventTypeList__.splice(n.__eventTypeList__.indexOf(e),1),n&&n.__eventTypeList__.length<=1?n:void 0}};t.on=function(e,t){if(-1!==o.indexOf(e))document.addEventListener(e,t);else{var n=i(e,t);n?document.addEventListener("dtBizBridgeEvent",n):console.log("bind event : "+e+" need WeakMap support , current environment doesnot")}},t.off=function(e,t){if(-1!==o.indexOf(e))document.removeEventListener(e,t);else{var n=a(e,t);n&&document.removeEventListener("dtBizBridgeEvent",n)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){},r=function(e,t){return new Promise(function(n,r){var i=t.onSuccess||o,a=t.onFail||o;if(delete t.onSuccess,delete t.onFail,AlipayJSBridge){var d=e.split("."),s=d.pop()||"",u=d.join(".");AlipayJSBridge.call.apply(null,["webDdExec",{serviceName:u,actionName:s,args:t},function(e){var t={},o=e.content;if(o)try{t=JSON.parse(o)}catch(e){console.error("parse dt api result error",o,e)}e.success?(i.apply(null,[t]),n(t)):(a.apply(null,[t]),r(t))}])}else{var c=new Error("Fatal error, cannot find bridge ,current env is WebView in MiniApp");a(c),r(c)}})};t.default=r},function(e,t,n){"use strict";var o=this;Object.defineProperty(t,"__esModule",{value:!0}),t.off=t.on=void 0;var r=n(9);t.on=function(e,t){r.requireModule("globalEvent").addEventListener(e,function(e){var n={preventDefault:function(){throw new Error("does not support preventDefault")},detail:e};t.call(o,n)})},t.off=function(e,t){r.requireModule("globalEvent").removeEventListener(e,t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FRAMEWORK=t.PLATFORM=t.RUNTIME=void 0,t.RUNTIME={WEB:"Web",WEEX:"Weex",UNKNOWN:"Unknown"},t.PLATFORM={MAC:"Mac",WINDOWS:"Windows",IOS:"iOS",ANDROID:"Android",IPAD:"iPad",BROWSER:"Browser",UNKNOWN:"Unknown"},t.FRAMEWORK={VUE:"Vue",RAX:"Rax",UNKNOWN:"Unknown"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.platformConfig=void 0;var o=n(0),r=n(4),i=n(2),a=n(8),d=n(159),s=n(411),u=n(9),c=n(158),l=n(160),v=n(6);t.platformConfig={platform:r.ENV_ENUM.android,bridgeInit:function(){var e=r.getENV();return e.appType===i.APP_TYPE.MINI_APP?Promise.resolve(a.default):e.appType===i.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(d.default):e.appType===i.APP_TYPE.WEEX?u.androidWeexBridge():s.h5AndroidbridgeInit().then(function(){return s.default})},authMethod:"runtime.permission.requestJsApis",authParamsDeal:function(e){var t=Object.assign({},e);return e.jsApiList&&(t.jsApiList=e.jsApiList.map(function(e){return v[e]?v[e]:e})),t},event:{on:function(e,t){var n=r.getENV();switch(n.appType){case i.APP_TYPE.WEB:case i.APP_TYPE.WEBVIEW_IN_MINIAPP:c.on(e,t);break;case i.APP_TYPE.WEEX:l.on(e,t);break;default:throw new Error("Not support global event in the platfrom: "+n.appType)}},off:function(e,t){var n=r.getENV();switch(n.appType){case i.APP_TYPE.WEB:case i.APP_TYPE.WEBVIEW_IN_MINIAPP:c.off(e,t);break;case i.APP_TYPE.WEEX:l.off(e,t);break;default:throw new Error("Not support global event in the platfrom: "+n.appType)}}}},o.ddSdk.setPlatform(t.platformConfig)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.platformConfig=void 0;var o=n(0),r=n(4),i=n(2),a=n(8),d=n(159),s=n(412),u=n(9),c=n(158),l=n(6),v=n(160);t.platformConfig={platform:r.ENV_ENUM.ios,bridgeInit:function(){var e=r.getENV();return e.appType===i.APP_TYPE.MINI_APP?Promise.resolve(a.default):e.appType===i.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(d.default):e.appType===i.APP_TYPE.WEEX?u.iosWeexBridge():s.h5IosBridgeInit().then(function(){return s.default})},authMethod:"runtime.permission.requestJsApis",authParamsDeal:function(e){var t=Object.assign({},e);return e.jsApiList&&(t.jsApiList=e.jsApiList.map(function(e){return l[e]?l[e]:e})),t},event:{on:function(e,t){var n=r.getENV();switch(n.appType){case i.APP_TYPE.WEB:case i.APP_TYPE.WEBVIEW_IN_MINIAPP:c.on(e,t);break;case i.APP_TYPE.WEEX:v.on(e,t);break;default:throw new Error("Not support global event in the platfrom: "+n.appType)}},off:function(e,t){var n=r.getENV();switch(n.appType){case i.APP_TYPE.WEB:case i.APP_TYPE.WEBVIEW_IN_MINIAPP:c.off(e,t);break;case i.APP_TYPE.WEEX:v.off(e,t);break;default:throw new Error("Not support global event in the platfrom: "+n.appType)}}}},o.ddSdk.setPlatform(t.platformConfig)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isMobile=void 0;var o=n(4),r=o.getENV();t.isMobile=function(){return r.platform===o.ENV_ENUM.ios}()||function(){return r.platform===o.ENV_ENUM.android}()},function(e,t){function n(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function r(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function i(e){if(l===clearTimeout)return clearTimeout(e);if((l===o||!l)&&clearTimeout)return l=clearTimeout,clearTimeout(e);try{return l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}function a(){_&&f&&(_=!1,f.length?p=f.concat(p):P=-1,p.length&&d())}function d(){if(!_){var e=r(a);_=!0;for(var t=p.length;t;){for(f=p,p=[];++P<t;)f&&f[P].run();P=-1,t=p.length}f=null,_=!1,i(e)}}function s(e,t){this.fun=e,this.array=t}function u(){}var c,l,v=e.exports={};(function(){try{c="function"==typeof setTimeout?setTimeout:n}catch(e){c=n}try{l="function"==typeof clearTimeout?clearTimeout:o}catch(e){l=o}})();var f,p=[],_=!1,P=-1;v.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];p.push(new s(e,t)),1!==p.length||_||r(d)},s.prototype.run=function(){this.fun.apply(null,this.array)},v.title="browser",v.browser=!0,v.env={},v.argv=[],v.version="",v.versions={},v.on=u,v.addListener=u,v.once=u,v.off=u,v.removeListener=u,v.removeAllListeners=u,v.emit=u,v.prependListener=u,v.prependOnceListener=u,v.listeners=function(e){return[]},v.binding=function(e){throw new Error("process.binding is not supported")},v.cwd=function(){return"/"},v.chdir=function(e){throw new Error("process.chdir is not supported")},v.umask=function(){return 0}},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.beaconPicker$=void 0;var i=n(0),a="biz.ATMBle.beaconPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.0.7"},r[i.ENV_ENUM.android]={vs:"5.0.7"},r)),t.beaconPicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.detectFace$=void 0;var i=n(0),a="biz.ATMBle.detectFace";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.18"},r[i.ENV_ENUM.android]={vs:"5.1.18"},r)),t.detectFace$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.detectFaceFullScreen$=void 0;var i=n(0),a="biz.ATMBle.detectFaceFullScreen";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.18"},r[i.ENV_ENUM.android]={vs:"5.1.18"},r)),t.detectFaceFullScreen$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.exclusiveLiveCheck$=void 0;var i=n(0),a="biz.ATMBle.exclusiveLiveCheck";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.40"},r[i.ENV_ENUM.android]={vs:"6.5.40"},r)),t.exclusiveLiveCheck$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.faceManager$=void 0;var i=n(0),a="biz.ATMBle.faceManager";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.0.7"},r[i.ENV_ENUM.android]={vs:"5.0.7"},r)),t.faceManager$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.punchModePicker$=void 0;var i=n(0),a="biz.ATMBle.punchModePicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.0.7"},r[i.ENV_ENUM.android]={vs:"5.0.7"},r)),t.punchModePicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.bindAlipay$=void 0;var i=n(0),a="biz.alipay.bindAlipay";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.15"},r[i.ENV_ENUM.android]={vs:"6.3.15"},r)),t.bindAlipay$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openAuth$=void 0;var i=n(0),a="biz.alipay.openAuth";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.8"},r[i.ENV_ENUM.android]={vs:"5.1.8"},r)),t.openAuth$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.pay$=void 0;var i=n(0),a="biz.alipay.pay";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.pay$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getLBSWua$=void 0;var i=n(0),a="biz.attend.getLBSWua";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.35"},r[i.ENV_ENUM.android]={vs:"7.0.35"},r)),t.getLBSWua$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openAccountPwdLoginPage$=void 0;var i=n(0),a="biz.auth.openAccountPwdLoginPage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.0"},r[i.ENV_ENUM.android]={vs:"6.3.0"},r)),t.openAccountPwdLoginPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestAuthInfo$=void 0;var i=n(0),a="biz.auth.requestAuthInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.19"},r[i.ENV_ENUM.android]={vs:"5.1.19"},r)),t.requestAuthInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseDateTime$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseDateTime";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseDateTime$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseHalfDay$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseHalfDay";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseHalfDay$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseInterval$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseInterval";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseInterval$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseOneDay$=void 0;var i=n(0),a=n(1),d="biz.calendar.chooseOneDay";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},r)),t.chooseOneDay$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseConversationByCorpId$=void 0;var i=n(0),a=n(1),d="biz.chat.chooseConversationByCorpId",s=a.genDefaultParamsDealFn({max:50});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:s},r[i.ENV_ENUM.pc]={vs:"4.7.11",paramsDeal:s},r)),t.chooseConversationByCorpId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.collectSticker$=void 0;var i=n(0),a="biz.chat.collectSticker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.25"},r[i.ENV_ENUM.android]={vs:"4.6.25"},r)),t.collectSticker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createSceneGroup$=void 0;var i=n(0),a="biz.chat.createSceneGroup";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.7.17"},r[i.ENV_ENUM.android]={vs:"4.7.17"},r[i.ENV_ENUM.pc]={vs:"4.7.17"},r)),t.createSceneGroup$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getRealmCid$=void 0;var i=n(0),a="biz.chat.getRealmCid";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.7.12"},r[i.ENV_ENUM.android]={vs:"4.7.12"},r[i.ENV_ENUM.pc]={vs:"4.7.12"},r)),t.getRealmCid$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.locationChatMessage$=void 0;var i=n(0),a="biz.chat.locationChatMessage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.7.6"},r[i.ENV_ENUM.android]={vs:"2.7.6"},r)),t.locationChatMessage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openSingleChat$=void 0;var i=n(0),a="biz.chat.openSingleChat";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.4.10"},r[i.ENV_ENUM.android]={vs:"3.4.10"},r)),t.openSingleChat$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.pickConversation$=void 0;var i=n(0),a="biz.chat.pickConversation";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.2"},r[i.ENV_ENUM.android]={vs:"2.4.2"},r[i.ENV_ENUM.pc]={vs:"4.7.9"},r)),t.pickConversation$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.sendEmotion$=void 0;var i=n(0),a="biz.chat.sendEmotion";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.12"},r[i.ENV_ENUM.android]={vs:"4.6.12"},r)),t.sendEmotion$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.toConversation$=void 0;var i=n(0),a="biz.chat.toConversation";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.toConversation$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.toConversationByOpenConversationId$=void 0;var i=n(0),a="biz.chat.toConversationByOpenConversationId";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.30"},r[i.ENV_ENUM.android]={vs:"5.1.30"},r[i.ENV_ENUM.pc]={vs:"5.1.33"},r)),t.toConversationByOpenConversationId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setData$=void 0;var i=n(0),a="biz.clipboardData.setData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.7.0"},r[i.ENV_ENUM.android]={vs:"2.7.0"},r[i.ENV_ENUM.pc]={vs:"4.6.1"},r)),t.setData$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createCloudCall$=void 0;var i=n(0),a="biz.conference.createCloudCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.9"},r)),t.createCloudCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getCloudCallInfo$=void 0;var i=n(0),a="biz.conference.getCloudCallInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.9"},r)),t.getCloudCallInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getCloudCallList$=void 0;var i=n(0),a="biz.conference.getCloudCallList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.0"},r[i.ENV_ENUM.android]={vs:"6.0.0"},r[i.ENV_ENUM.pc]={vs:"6.0.9"},r)),t.getCloudCallList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.videoConfCall$=void 0;var i=n(0),a="biz.conference.videoConfCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.0.8"},r[i.ENV_ENUM.android]={vs:"5.0.8"},r[i.ENV_ENUM.pc]={vs:"5.1.28"},r)),t.videoConfCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.choose$=void 0;var i=n(0),a=n(1),d="biz.contact.choose",s=a.genDefaultParamsDealFn({multiple:!0,startWithDepartmentId:0,users:[]});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.choose$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseMobileContacts$=void 0;var i=n(0),a="biz.contact.chooseMobileContacts";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.1"},r[i.ENV_ENUM.android]={vs:"3.1"},r)),t.chooseMobileContacts$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.complexPicker$=void 0;var i=n(0),a="biz.contact.complexPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.9.0"},r[i.ENV_ENUM.android]={vs:"2.9.0"},r[i.ENV_ENUM.pc]={vs:"4.3.5"},r)),t.complexPicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createGroup$=void 0;var i=n(0),a="biz.contact.createGroup";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r[i.ENV_ENUM.pc]={vs:"4.6.1"},r)),t.createGroup$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.departmentsPicker$=void 0;var i=n(0),a="biz.contact.departmentsPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"4.2.5"},r[i.ENV_ENUM.ios]={vs:"3.0"},r[i.ENV_ENUM.android]={vs:"3.0"},r)),t.departmentsPicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.externalComplexPicker$=void 0;var i=n(0),a="biz.contact.externalComplexPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"3.0"},r[i.ENV_ENUM.android]={vs:"3.0"},r)),t.externalComplexPicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.externalEditForm$=void 0;var i=n(0),a="biz.contact.externalEditForm";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.0"},r[i.ENV_ENUM.android]={vs:"3.0"},r)),t.externalEditForm$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.rolesPicker$=void 0;var i=n(0),a="biz.contact.rolesPicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.16"},r[i.ENV_ENUM.android]={vs:"6.3.16"},r)),t.rolesPicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setRule$=void 0;var i=n(0),a="biz.contact.setRule";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.15"},r[i.ENV_ENUM.android]={vs:"2.15"},r)),t.setRule$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseSpaceDir$=void 0;var i=n(0),a="biz.cspace.chooseSpaceDir";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.6"},r[i.ENV_ENUM.android]={vs:"3.5.6"},r[i.ENV_ENUM.pc]={vs:"5.1.27"},r)),t.chooseSpaceDir$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.delete$=void 0;var i=n(0),a="biz.cspace.delete";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.21"},r[i.ENV_ENUM.android]={vs:"4.5.21"},r[i.ENV_ENUM.pc]={vs:"4.5.21"},r)),t.delete$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.preview$=void 0;var i=n(0),a="biz.cspace.preview";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.7.0"},r[i.ENV_ENUM.android]={vs:"2.7.0"},r)),t.preview$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.previewDentryImages$=void 0;var i=n(0),a="biz.cspace.previewDentryImages";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.30"},r[i.ENV_ENUM.android]={vs:"6.3.30"},r)),t.previewDentryImages$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.saveFile$=void 0;var i=n(0),a="biz.cspace.saveFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.7.6"},r[i.ENV_ENUM.android]={vs:"2.7.6"},r)),t.saveFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.choose$=void 0;var i=n(0),a=n(1),d="biz.customContact.choose",s=a.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.5.2",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.5.2",paramsDeal:s},r)),t.choose$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.multipleChoose$=void 0;var i=n(0),a=n(1),d="biz.customContact.multipleChoose",s=a.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.multipleChoose$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.rsa$=void 0;var r=n(0),i="biz.data.rsa";r.ddSdk.setAPI(i,{}),t.rsa$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.create$=void 0;var i=n(0),a="biz.ding.create";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.1",resultDeal:function(e){return""===e?e={dingCreateResult:!1}:"object"==typeof e&&(e.dingCreateResult=!!e.dingCreateResult),e}},r[i.ENV_ENUM.android]={vs:"3.5.1"},r[i.ENV_ENUM.pc]={vs:"4.5.9"},r)),t.create$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.post$=void 0;var i=n(0),a="biz.ding.post";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.post$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.finishMiniCourseByRecordId$=void 0;var i=n(0),a="biz.edu.finishMiniCourseByRecordId";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.15"},r[i.ENV_ENUM.android]={vs:"6.0.15"},r)),t.finishMiniCourseByRecordId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getMiniCourseDraftList$=void 0;var i=n(0),a="biz.edu.getMiniCourseDraftList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.15"},r[i.ENV_ENUM.android]={vs:"6.0.15"},r)),t.getMiniCourseDraftList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.joinClassroom$=void 0;var i=n(0),a="biz.edu.joinClassroom";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.15"},r[i.ENV_ENUM.android]={vs:"6.0.15"},r[i.ENV_ENUM.pc]={vs:"6.0.15"},r)),t.joinClassroom$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.makeMiniCourse$=void 0;var i=n(0),a="biz.edu.makeMiniCourse";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.15"},r[i.ENV_ENUM.android]={vs:"6.0.15"},r)),t.makeMiniCourse$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.newMsgNotificationStatus$=void 0;var i=n(0),a="biz.edu.newMsgNotificationStatus";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.20"},r[i.ENV_ENUM.android]={vs:"6.3.20"},r)),t.newMsgNotificationStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startAuth$=void 0;var i=n(0),a="biz.edu.startAuth";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.20"},r[i.ENV_ENUM.android]={vs:"6.3.20"},r)),t.startAuth$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.tokenFaceImg$=void 0;var i=n(0),a="biz.edu.tokenFaceImg";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.20"},r[i.ENV_ENUM.android]={vs:"6.3.20"},r)),t.tokenFaceImg$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.notifyWeex$=void 0;var i=n(0),a="biz.event.notifyWeex";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.0"},r)),t.notifyWeex$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.downloadFile$=void 0;var i=n(0),a="biz.file.downloadFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.15"},r[i.ENV_ENUM.android]={vs:"7.0.15"},r[i.ENV_ENUM.pc]={vs:"7.0.15"},r)),t.downloadFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.fetchData$=void 0;var i=n(0),a="biz.intent.fetchData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.7.6"},r[i.ENV_ENUM.android]={vs:"2.7.6"},r)),t.fetchData$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.bind$=void 0;var i=n(0),a="biz.iot.bind";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.34"},r[i.ENV_ENUM.android]={vs:"4.6.34"},r)),t.bind$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.bindMeetingRoom$=void 0;var i=n(0),a="biz.iot.bindMeetingRoom";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.34"},r[i.ENV_ENUM.android]={vs:"4.6.34"},r)),t.bindMeetingRoom$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getDeviceProperties$=void 0;var i=n(0),a="biz.iot.getDeviceProperties";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.42"},r[i.ENV_ENUM.android]={vs:"4.6.42"},r)),t.getDeviceProperties$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.invokeThingService$=void 0;var i=n(0),a="biz.iot.invokeThingService";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.42"},r[i.ENV_ENUM.android]={vs:"4.6.42"},r)),t.invokeThingService$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.queryMeetingRoomList$=void 0;var i=n(0),a="biz.iot.queryMeetingRoomList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.34"},r[i.ENV_ENUM.android]={vs:"4.6.34"},r)),t.queryMeetingRoomList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setDeviceProperties$=void 0;var i=n(0),a="biz.iot.setDeviceProperties";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.42"},r[i.ENV_ENUM.android]={vs:"4.6.42"},r)),t.setDeviceProperties$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.unbind$=void 0;var i=n(0),a="biz.iot.unbind";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.34"},r[i.ENV_ENUM.android]={vs:"4.6.34"},r)),t.unbind$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startClassRoom$=void 0;var i=n(0),a="biz.live.startClassRoom";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"5.1.19"},r)),t.startClassRoom$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startUnifiedLive$=void 0;var i=n(0),a="biz.live.startUnifiedLive";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"5.1.18"},r)),t.startUnifiedLive$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.locate$=void 0;var i=n(0),a="biz.map.locate";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.locate$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.search$=void 0;var i=n(0),a=n(1),d="biz.map.search",s=a.genDefaultParamsDealFn({scope:500});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.search$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.view$=void 0;var i=n(0),a="biz.map.view";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.view$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.compressVideo$=void 0;var i=n(0),a="biz.media.compressVideo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.37"},r[i.ENV_ENUM.android]={vs:"4.6.37"},r)),t.compressVideo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openApp$=void 0;var i=n(0),a="biz.microApp.openApp";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.6"},r[i.ENV_ENUM.android]={vs:"4.5.6"},r)),t.openApp$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.close$=void 0;var i=n(0),a="biz.navigation.close";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r[i.ENV_ENUM.pc]={vs:"4.3.5"},r)),t.close$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.goBack$=void 0;var i=n(0),a="biz.navigation.goBack";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.goBack$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.hideBar$=void 0;var i=n(0),a="biz.navigation.hideBar";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.6"},r[i.ENV_ENUM.android]={vs:"3.5.6"},r)),t.hideBar$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.navigateBackPage$=void 0;var i=n(0),a="biz.navigation.navigateBackPage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.31"},r[i.ENV_ENUM.android]={vs:"6.5.31"},r)),t.navigateBackPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.navigateToMiniProgram$=void 0;var i=n(0),a="biz.navigation.navigateToMiniProgram";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.31"},r[i.ENV_ENUM.android]={vs:"5.1.31"},r)),t.navigateToMiniProgram$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.navigateToPage$=void 0;var i=n(0),a="biz.navigation.navigateToPage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.31"},r[i.ENV_ENUM.android]={vs:"6.5.31"},r)),t.navigateToPage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.quit$=void 0;var i=n(0),a="biz.navigation.quit";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.quit$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.replace$=void 0;var i=n(0),a="biz.navigation.replace";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.4.6"},r[i.ENV_ENUM.android]={vs:"3.4.6"},r)),t.replace$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setIcon$=void 0;var i=n(0),a=n(1),d="biz.navigation.setIcon",s=a.genDefaultParamsDealFn({watch:!0,showIcon:!0,iconIndex:1});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.setIcon$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setLeft$=void 0;var i=n(0),a=n(1),d="biz.navigation.setLeft",s=a.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.setLeft$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setMenu$=void 0;var i=n(0),a=n(1),d="biz.navigation.setMenu";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:a.addWatchParamsDeal},r[i.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:a.addWatchParamsDeal},r)),t.setMenu$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setRight$=void 0;var i=n(0),a=n(1),d="biz.navigation.setRight",s=a.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.setRight$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setTitle$=void 0;var i=n(0),a="biz.navigation.setTitle";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.setTitle$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.componentPunchFromPartner$=void 0;var i=n(0),a="biz.pbp.componentPunchFromPartner";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.10"},r[i.ENV_ENUM.android]={vs:"5.1.10"},r)),t.componentPunchFromPartner$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startMatchRuleFromPartner$=void 0;var i=n(0),a="biz.pbp.startMatchRuleFromPartner";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.10"},r[i.ENV_ENUM.android]={vs:"5.1.10"},r)),t.startMatchRuleFromPartner$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopMatchRuleFromPartner$=void 0;var i=n(0),a="biz.pbp.stopMatchRuleFromPartner";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.10"},r[i.ENV_ENUM.android]={vs:"5.1.10"},r)),t.stopMatchRuleFromPartner$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.add$=void 0;var r=n(0),i="biz.phoneContact.add";r.ddSdk.setAPI(i,{}),t.add$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getRealtimeTracingStatus$=void 0;var i=n(0),a="biz.realm.getRealtimeTracingStatus";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.13"},r)),t.getRealtimeTracingStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getUserExclusiveInfo$=void 0;var i=n(0),a="biz.realm.getUserExclusiveInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.14"},r[i.ENV_ENUM.android]={vs:"6.0.14"},r[i.ENV_ENUM.pc]={vs:"6.0.17"},r)),t.getUserExclusiveInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startRealtimeTracing$=void 0;var i=n(0),a="biz.realm.startRealtimeTracing";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.13"},r)),t.startRealtimeTracing$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopRealtimeTracing$=void 0;var i=n(0),a="biz.realm.stopRealtimeTracing";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.13"},r)),t.stopRealtimeTracing$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.subscribe$=void 0;var i=n(0),a="biz.realm.subscribe";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.7.18"},r[i.ENV_ENUM.android]={vs:"4.7.18"},r)),t.subscribe$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.unsubscribe$=void 0;var i=n(0),a="biz.realm.unsubscribe";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.7.18"},r[i.ENV_ENUM.android]={vs:"4.7.18"},r)),t.unsubscribe$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getInfo$=void 0;var i=n(0),a="biz.resource.getInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.10"},r[i.ENV_ENUM.android]={vs:"6.5.10"},r)),t.getInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.reportDebugMessage$=void 0;var i=n(0),a="biz.resource.reportDebugMessage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.20"},r[i.ENV_ENUM.android]={vs:"6.5.20"},r)),t.reportDebugMessage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.addShortCut$=void 0;var i=n(0),a="biz.shortCut.addShortCut";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.7.32"},r)),t.addShortCut$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getHealthAuthorizationStatus$=void 0;var i=n(0),a="biz.sports.getHealthAuthorizationStatus";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.11"},r)),t.getHealthAuthorizationStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getHealthData$=void 0;var i=n(0),a="biz.sports.getHealthData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r)),t.getHealthData$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getHealthDeviceData$=void 0;var i=n(0),a="biz.sports.getHealthDeviceData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r)),t.getHealthDeviceData$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestHealthAuthorization$=void 0;var i=n(0),a="biz.sports.requestHealthAuthorization";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r)),t.requestHealthAuthorization$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.closeUnpayOrder$=void 0;var i=n(0),a=n(1),d="biz.store.closeUnpayOrder";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},r)),t.closeUnpayOrder$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.createOrder$=void 0;var i=n(0),a=n(1),d="biz.store.createOrder";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},r)),t.createOrder$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getPayUrl$=void 0;var i=n(0),a=n(1),d="biz.store.getPayUrl";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},r)),t.getPayUrl$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.inquiry$=void 0;var i=n(0),a=n(1),d="biz.store.inquiry";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},r[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},r)),t.inquiry$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isTab$=void 0;var i=n(0),a="biz.tabwindow.isTab";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.5.10"},r)),t.isTab$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.call$=void 0;var i=n(0),a="biz.telephone.call";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.call$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkBizCall$=void 0;var i=n(0),a="biz.telephone.checkBizCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"4.0.0"},r[i.ENV_ENUM.ios]={vs:"3.5.6"},r[i.ENV_ENUM.android]={vs:"3.5.6"},r)),t.checkBizCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.quickCallList$=void 0;var i=n(0),a="biz.telephone.quickCallList";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.5.6"},r[i.ENV_ENUM.ios]={vs:"3.5.6"},r[i.ENV_ENUM.android]={vs:"3.5.6"},r)),t.quickCallList$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showCallMenu$=void 0;var i=n(0),a="biz.telephone.showCallMenu";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.showCallMenu$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkPassword$=void 0;var i=n(0),a="biz.user.checkPassword";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.8"},r[i.ENV_ENUM.android]={vs:"4.5.8"},r)),t.checkPassword$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.get$=void 0;var i=n(0),a="biz.user.get";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.get$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.callComponent$=void 0;var i=n(0),a="biz.util.callComponent";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.35"},r[i.ENV_ENUM.android]={vs:"6.3.35"},r[i.ENV_ENUM.pc]={vs:"6.3.35"},r)),t.callComponent$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkAuth$=void 0;var i=n(0),a="biz.util.checkAuth";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.checkAuth$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseImage$=void 0;var i=n(0),a="biz.util.chooseImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.1"},r[i.ENV_ENUM.android]={vs:"5.1.1"},r)),t.chooseImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chooseRegion$=void 0;var i=n(0),a="biz.util.chooseRegion";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.chooseRegion$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.chosen$=void 0;var i=n(0),a="biz.util.chosen";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.chosen$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.clearWebStoreCache$=void 0;var i=n(0),a="biz.util.clearWebStoreCache";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.22"},r)),t.clearWebStoreCache$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.closePreviewImage$=void 0;var i=n(0),a="biz.util.closePreviewImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.19"},r[i.ENV_ENUM.android]={vs:"6.0.17"},r)),t.closePreviewImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.compressImage$=void 0;var i=n(0),a="biz.util.compressImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.1"},r[i.ENV_ENUM.android]={vs:"5.1.1"},r)),t.compressImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.datepicker$=void 0;var i=n(0),a="biz.util.datepicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.datepicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.datetimepicker$=void 0;var i=n(0),a="biz.util.datetimepicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.datetimepicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.decrypt$=void 0;var i=n(0),a="biz.util.decrypt";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.9.1"},r[i.ENV_ENUM.android]={vs:"2.9.1"},r)),t.decrypt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.downloadFile$=void 0;var i=n(0),a="biz.util.downloadFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.downloadFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.encrypt$=void 0;var i=n(0),a="biz.util.encrypt";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.9.1"},r[i.ENV_ENUM.android]={vs:"2.9.1"},r)),t.encrypt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getPerfInfo$=void 0;var i=n(0),a="biz.util.getPerfInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.14"},r[i.ENV_ENUM.android]={vs:"5.1.14"},r)),t.getPerfInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.invokeWorkbench$=void 0;var i=n(0),a="biz.util.invokeWorkbench";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.8"},r)),t.invokeWorkbench$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isEnableGPUAcceleration$=void 0;var i=n(0),a="biz.util.isEnableGPUAcceleration";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.22"},r)),t.isEnableGPUAcceleration$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isLocalFileExist$=void 0;var i=n(0),a="biz.util.isLocalFileExist";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.isLocalFileExist$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.multiSelect$=void 0;var i=n(0),a="biz.util.multiSelect";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.0.0"},r[i.ENV_ENUM.android]={vs:"3.0.0"},r)),t.multiSelect$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.open$=void 0;var i=n(0),a="biz.util.open";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.7.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.open$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.openBrowser$=void 0;var r=n(0),i="biz.util.openBrowser";r.ddSdk.setAPI(i,{}),t.openBrowser$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openDocument$=void 0;var i=n(0),a="biz.util.openDocument";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r[i.ENV_ENUM.pc]={vs:"7.0.10"},r)),t.openDocument$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openLink$=void 0;var i=n(0),a=n(1),d="biz.util.openLink",s=a.genDefaultParamsDealFn({credible:!0,showMenuBar:!0});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.7.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.openLink$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openLocalFile$=void 0;var i=n(0),a="biz.util.openLocalFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.openLocalFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openModal$=void 0;var i=n(0),a="biz.util.openModal";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.openModal$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openSlidePanel$=void 0;var i=n(0),a="biz.util.openSlidePanel";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r)),t.openSlidePanel$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.presentWindow$=void 0;var i=n(0),a="biz.util.presentWindow";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.presentWindow$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.previewImage$=void 0;var i=n(0),a="biz.util.previewImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"2.7.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.previewImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.previewVideo$=void 0;var i=n(0),a="biz.util.previewVideo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.7"},r[i.ENV_ENUM.android]={vs:"4.3.7"},r[i.ENV_ENUM.pc]={vs:"4.6.33"},r)),t.previewVideo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.saveImage$=void 0;var i=n(0),a="biz.util.saveImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.1"},r[i.ENV_ENUM.android]={vs:"4.1"},r)),t.saveImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.saveImageToPhotosAlbum$=void 0;var i=n(0),a="biz.util.saveImageToPhotosAlbum";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.saveImageToPhotosAlbum$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.scan$=void 0;var i=n(0),a=n(1),d="biz.util.scan",s=a.genDefaultParamsDealFn({type:"qrCode"});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.scan$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.scanCard$=void 0;var i=n(0),a="biz.util.scanCard";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.scanCard$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setGPUAcceleration$=void 0;var i=n(0),a="biz.util.setGPUAcceleration";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"6.0.22"},r)),t.setGPUAcceleration$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setScreenBrightnessAndKeepOn$=void 0;var i=n(0),a="biz.util.setScreenBrightnessAndKeepOn";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.37"},r[i.ENV_ENUM.android]={vs:"4.3.3"},r)),t.setScreenBrightnessAndKeepOn$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setScreenKeepOn$=void 0;var i=n(0),a="biz.util.setScreenKeepOn";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.26"},r[i.ENV_ENUM.android]={vs:"5.1.26"},r)),t.setScreenKeepOn$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.share$=void 0;var i=n(0),a=n(1),d="biz.util.share",s=a.genDefaultParamsDealFn({title:"",buttonName:"确定"});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.pc]={vs:"4.6.37",paramsDeal:s},r)),t.share$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.shareImage$=void 0;var i=n(0),a="biz.util.shareImage";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.1"},r[i.ENV_ENUM.android]={vs:"4.1"},r)),t.shareImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showAuthGuide$=void 0;var i=n(0),a="biz.util.showAuthGuide";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.showAuthGuide$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showSharePanel$=void 0;var i=n(0),a="biz.util.showSharePanel";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.10"},r[i.ENV_ENUM.android]={vs:"7.0.10"},r)),t.showSharePanel$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startDocSign$=void 0;var i=n(0),a="biz.util.startDocSign";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.6.33"},r)),t.startDocSign$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.systemShare$=void 0;var i=n(0),a="biz.util.systemShare";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.11"},r[i.ENV_ENUM.android]={vs:"4.5.11"},r)),t.systemShare$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.timepicker$=void 0;var i=n(0),a="biz.util.timepicker";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.timepicker$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadAttachment$=void 0;var i=n(0),a="biz.util.uploadAttachment";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.7.0"},r[i.ENV_ENUM.android]={vs:"2.7.0"},r)),t.uploadAttachment$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadFile$=void 0;var i=n(0),a="biz.util.uploadFile";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.28"},r[i.ENV_ENUM.android]={vs:"6.5.27"},r)),t.uploadFile$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadImage$=void 0;var i=n(0),a=n(1),d="biz.util.uploadImage",s=a.genDefaultParamsDealFn({multiple:!1});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.uploadImage$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadImageFromCamera$=void 0;var i=n(0),a="biz.util.uploadImageFromCamera";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.uploadImageFromCamera$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ut$=void 0;var i=n(0),a="biz.util.ut",d=function(e){var t=Object.assign({},e),n=t.value,o=[];if(n&&"object"==typeof n){for(var r in n)void 0!==n[r]&&o.push(r+"="+n[r]);n=o.join(",")}return t.value=n||"",t};i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.5.0",paramsDeal:d},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(e){var t=Object.assign({},e),n=t.value;return n&&"object"==typeof n&&(n=JSON.stringify(n)),t.value=n,t}},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},r)),t.ut$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openBindIDCard$=void 0;var i=n(0),a="biz.verify.openBindIDCard";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.21"},r[i.ENV_ENUM.android]={vs:"4.5.21"},r)),t.openBindIDCard$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startAuth$=void 0;var i=n(0),a="biz.verify.startAuth";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.5.21"},r[i.ENV_ENUM.android]={vs:"4.5.21"},r)),t.startAuth$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.makeCall$=void 0;var i=n(0),a="biz.voice.makeCall";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.40"},r[i.ENV_ENUM.android]={vs:"7.0.40"},r[i.ENV_ENUM.pc]={vs:"7.0.40"},r)),t.makeCall$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getWatermarkInfo$=void 0;var i=n(0),a="biz.watermarkCamera.getWatermarkInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.25"},r[i.ENV_ENUM.android]={vs:"6.5.25"},r)),t.getWatermarkInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setWatermarkInfo$=void 0;var i=n(0),a="biz.watermarkCamera.setWatermarkInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.25"},r[i.ENV_ENUM.android]={vs:"6.5.25"},r)),t.setWatermarkInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestAuthCode$=void 0;var i=n(0),a="channel.permission.requestAuthCode";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.0.0"},r[i.ENV_ENUM.android]={vs:"3.0.0"},r)),t.requestAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.clearShake$=void 0;var i=n(0),a="device.accelerometer.clearShake";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.clearShake$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.watchShake$=void 0;var i=n(0),a=n(1),d="device.accelerometer.watchShake";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(e){return a.forceChangeParamsDealFn({sensitivity:3.2})(a.addWatchParamsDeal(e))}},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},r)),t.watchShake$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.download$=void 0;var i=n(0),a="device.audio.download";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.download$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onPlayEnd$=void 0;var i=n(0),a="device.audio.onPlayEnd";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.onPlayEnd$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.onRecordEnd$=void 0;var i=n(0),a="device.audio.onRecordEnd";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.onRecordEnd$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.pause$=void 0;var i=n(0),a="device.audio.pause";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.pause$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.play$=void 0;var i=n(0),a="device.audio.play";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.play$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.resume$=void 0;var i=n(0),a="device.audio.resume";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.resume$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.startRecord$=void 0;var i=n(0),a="device.audio.startRecord";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r[i.ENV_ENUM.pc]={vs:"7.0.30"},r)),t.startRecord$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stop$=void 0;var i=n(0),a="device.audio.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.stop$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stopRecord$=void 0;var i=n(0),a="device.audio.stopRecord";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r[i.ENV_ENUM.pc]={vs:"7.0.30"},r)),t.stopRecord$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.translateVoice$=void 0;var i=n(0),a="device.audio.translateVoice";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.8.0"},r[i.ENV_ENUM.android]={vs:"2.8.0"},r)),t.translateVoice$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.getBatteryInfo$=void 0;var r=n(0),i="device.base.getBatteryInfo";r.ddSdk.setAPI(i,{}),t.getBatteryInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getInterface$=void 0;var i=n(0),a="device.base.getInterface";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.getInterface$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getPhoneInfo$=void 0;var i=n(0),a="device.base.getPhoneInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.5.0"},r[i.ENV_ENUM.android]={vs:"3.5.0"},r)),t.getPhoneInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getScanWifiListAsync$=void 0;var i=n(0),a="device.base.getScanWifiListAsync";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.41"},r[i.ENV_ENUM.android]={vs:"3.3.0"},r)),t.getScanWifiListAsync$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getUUID$=void 0;var i=n(0),a="device.base.getUUID";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r[i.ENV_ENUM.pc]={vs:"4.7.6"},r)),t.getUUID$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getWifiStatus$=void 0;var i=n(0),a="device.base.getWifiStatus";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.11.0"},r[i.ENV_ENUM.android]={vs:"2.11.0"},r)),t.getWifiStatus$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.openSystemSetting$=void 0;var i=n(0),a="device.base.openSystemSetting";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"6.0.27"},r[i.ENV_ENUM.ios]={vs:"6.3.15"},r)),t.openSystemSetting$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getNetworkType$=void 0;var i=n(0),a="device.connection.getNetworkType";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.getNetworkType$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkPermission$=void 0;var i=n(0),a="device.geolocation.checkPermission";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.5.0"},r)),t.checkPermission$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.get$=void 0;var i=n(0),a="device.geolocation.get";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.get$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.start$=void 0;var i=n(0),a="device.geolocation.start";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.4.7"},r[i.ENV_ENUM.android]={vs:"3.4.7"},r)),t.start$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.status$=void 0;var i=n(0),a="device.geolocation.status";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.4.8"},r[i.ENV_ENUM.android]={vs:"3.4.8"},r)),t.status$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stop$=void 0;var i=n(0),a="device.geolocation.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"3.4.7"},r[i.ENV_ENUM.android]={vs:"3.4.7"},r)),t.stop$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.checkInstalledApps$=void 0;var i=n(0),a="device.launcher.checkInstalledApps";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.checkInstalledApps$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.launchApp$=void 0;var i=n(0),a="device.launcher.launchApp";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.launchApp$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.nfcRead$=void 0;var i=n(0),a="device.nfc.nfcRead";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.11.0"},r[i.ENV_ENUM.android]={vs:"2.11.0"},r)),t.nfcRead$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.nfcStop$=void 0;var i=n(0),a="device.nfc.nfcStop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.3.9"},r[i.ENV_ENUM.android]={vs:"4.3.9"},r)),t.nfcStop$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.nfcWrite$=void 0;var i=n(0),a="device.nfc.nfcWrite";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.11.0"},r[i.ENV_ENUM.android]={vs:"2.11.0"},r)),t.nfcWrite$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.actionSheet$=void 0;var i=n(0),a="device.notification.actionSheet";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.actionSheet$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.alert$=void 0;var i=n(0),a=n(1),d="device.notification.alert",s=a.genDefaultParamsDealFn({title:"",buttonName:"确定"});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.alert$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.confirm$=void 0;var i=n(0),a=n(1),d="device.notification.confirm",s=a.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.confirm$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.extendModal$=void 0;var i=n(0),a="device.notification.extendModal";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.5.0"},r[i.ENV_ENUM.android]={vs:"2.5.0"},r)),t.extendModal$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.hidePreloader$=void 0;var i=n(0),a="device.notification.hidePreloader";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.hidePreloader$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.modal$=void 0;var i=n(0),a="device.notification.modal";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"4.2.5"},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.modal$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.prompt$=void 0;var i=n(0),a=n(1),d="device.notification.prompt",s=a.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.7.0"},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.prompt$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.showPreloader$=void 0;var i=n(0),a=n(1),d="device.notification.showPreloader",s=a.genDefaultParamsDealFn({text:"加载中...",showIcon:!0});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.showPreloader$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.toast$=void 0;var i=n(0),a=n(1),d="device.notification.toast",s=a.genDefaultParamsDealFn({text:"toast",duration:3,delay:0});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.pc]={vs:"2.5.0",paramsDeal:function(e){return e.icon&&!e.type&&("success"===e.icon?e.type="success":"error"===e.icon&&(e.type="error")),e}},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.toast$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.vibrate$=void 0;var i=n(0),a=n(1),d="device.notification.vibrate",s=a.genDefaultParamsDealFn({duration:300});i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:s},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},r)),t.vibrate$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.getScreenBrightness$=void 0;var r=n(0),i="device.screen.getScreenBrightness";r.ddSdk.setAPI(i,{}),t.getScreenBrightness$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.insetAdjust$=void 0;var i=n(0),a="device.screen.insetAdjust";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"4.6.18"},r)),t.insetAdjust$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.isScreenReaderEnabled$=void 0;var r=n(0),i="device.screen.isScreenReaderEnabled";r.ddSdk.setAPI(i,{}),t.isScreenReaderEnabled$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.resetView$=void 0;var i=n(0),a="device.screen.resetView";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.0.0"},r[i.ENV_ENUM.ios]={vs:"4.0.0"},r)),t.resetView$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.rotateView$=void 0;var i=n(0),a="device.screen.rotateView";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.0.0"},r[i.ENV_ENUM.ios]={vs:"4.0.0"},r)),t.rotateView$=o,t.default=o},function(e,t,n){"use strict";function o(e){return r.ddSdk.invokeAPI(i,e)}Object.defineProperty(t,"__esModule",{value:!0}),t.setScreenBrightness$=void 0;var r=n(0),i="device.screen.setScreenBrightness";r.ddSdk.setAPI(i,{}),t.setScreenBrightness$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.keepAlive$=void 0;var i=n(0),a="media.voiceRecorder.keepAlive";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.12"},r[i.ENV_ENUM.android]={vs:"5.1.12"},r[i.ENV_ENUM.pc]={vs:"5.1.12"},r)),t.keepAlive$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.pause$=void 0;var i=n(0),a="media.voiceRecorder.pause";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.12"},r[i.ENV_ENUM.android]={vs:"5.1.12"},r[i.ENV_ENUM.pc]={vs:"5.1.12"},r)),t.pause$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.resume$=void 0;var i=n(0),a="media.voiceRecorder.resume";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.12"},r[i.ENV_ENUM.android]={vs:"5.1.12"},r[i.ENV_ENUM.pc]={vs:"5.1.12"},r)),t.resume$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.start$=void 0;var i=n(0),a="media.voiceRecorder.start";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.12"},r[i.ENV_ENUM.android]={vs:"5.1.12"},r[i.ENV_ENUM.pc]={vs:"5.1.12"},r)),t.start$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stop$=void 0;var i=n(0),a="media.voiceRecorder.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"5.1.12"},r[i.ENV_ENUM.android]={vs:"5.1.12"},r[i.ENV_ENUM.pc]={vs:"5.1.12"},r)),t.stop$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.loginGovNet$=void 0;var i=n(0),a="net.bjGovApn.loginGovNet";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.android]={vs:"4.5.16"},r)),t.loginGovNet$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.exec$=void 0;var i=n(0),a="runtime.h5nuvabridge.exec";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"7.0.0"},r[i.ENV_ENUM.android]={vs:"7.0.0"},r)),t.exec$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.fetch$=void 0;var i=n(0),a="runtime.message.fetch";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.fetch$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.post$=void 0;var i=n(0),a="runtime.message.post";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.post$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getLoadTime$=void 0;var i=n(0),a="runtime.monitor.getLoadTime";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.0.10"},r[i.ENV_ENUM.android]={vs:"6.0.10"},r)),t.getLoadTime$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestAuthCode$=void 0;var i=n(0),a="runtime.permission.requestAuthCode",d=function(e){return Object.assign(e,{url:location.href.split("#")[0]})};i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.0.0",paramsDeal:d},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.requestAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.requestOperateAuthCode$=void 0;var i=n(0),a="runtime.permission.requestOperateAuthCode",d=function(e){return Object.assign(e,{url:location.href.split("#")[0]})};i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.pc]={vs:"3.3.0",paramsDeal:d},r[i.ENV_ENUM.ios]={vs:"3.3.0"},r[i.ENV_ENUM.android]={vs:"3.3.0"},r)),t.requestOperateAuthCode$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.plain$=void 0;var i=n(0),a="ui.input.plain";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.plain$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.addToFloat$=void 0;var i=n(0),a="ui.multitask.addToFloat";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.0"},r[i.ENV_ENUM.android]={vs:"6.5.0"},r)),t.addToFloat$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.removeFromFloat$=void 0;var i=n(0),a="ui.multitask.removeFromFloat";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.0"},r[i.ENV_ENUM.android]={vs:"6.5.0"},r)),t.removeFromFloat$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.close$=void 0;var i=n(0),a="ui.nav.close";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.close$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getCurrentId$=void 0;var i=n(0),a="ui.nav.getCurrentId";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.getCurrentId$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.go$=void 0;var i=n(0),a="ui.nav.go";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.go$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.preload$=void 0;var i=n(0),a="ui.nav.preload";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.preload$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.recycle$=void 0;var i=n(0),a="ui.nav.recycle";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.6.0"},r[i.ENV_ENUM.android]={vs:"2.6.0"},r)),t.recycle$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setColors$=void 0;var i=n(0),a="ui.progressBar.setColors";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.setColors$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.disable$=void 0;var i=n(0),a="ui.pullToRefresh.disable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.disable$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(d,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.enable$=void 0;var i=n(0),a=n(1),d="ui.pullToRefresh.enable";i.ddSdk.setAPI(d,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},r[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},r)),t.enable$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.stop$=void 0;var i=n(0),a="ui.pullToRefresh.stop";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.stop$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.disable$=void 0;var i=n(0),a="ui.webViewBounce.disable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.disable$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.enable$=void 0;var i=n(0),a="ui.webViewBounce.enable";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.4.0"},r[i.ENV_ENUM.android]={vs:"2.4.0"},r)),t.enable$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getItem$=void 0;var i=n(0),a="util.domainStorage.getItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.9.0"},r[i.ENV_ENUM.android]={vs:"2.9.0"},r[i.ENV_ENUM.pc]={vs:"4.6.29"},r)),t.getItem$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getStorageInfo$=void 0;var i=n(0),a="util.domainStorage.getStorageInfo";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.5.30"},r[i.ENV_ENUM.android]={vs:"6.5.30"},r)),t.getStorageInfo$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.removeItem$=void 0;var i=n(0),a="util.domainStorage.removeItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.9.0"},r[i.ENV_ENUM.android]={vs:"2.9.0"},r[i.ENV_ENUM.pc]={vs:"4.6.29"},r)),t.removeItem$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.setItem$=void 0;var i=n(0),a="util.domainStorage.setItem";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"2.9.0"},r[i.ENV_ENUM.android]={vs:"2.9.0"},r[i.ENV_ENUM.pc]={vs:"4.6.9"},r)),t.setItem$=o,t.default=o},function(e,t,n){"use strict";function o(e){return i.ddSdk.invokeAPI(a,e)}var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getData$=void 0;var i=n(0),a="util.openTemporary.getData";i.ddSdk.setAPI(a,(r={},r[i.ENV_ENUM.ios]={vs:"6.3.20"},r[i.ENV_ENUM.android]={vs:"6.3.20"},r[i.ENV_ENUM.pc]={vs:"6.3.30"},r)),t.getData$=o,t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.h5AndroidbridgeInit=void 0;var o;t.h5AndroidbridgeInit=function(){return o||(o=new Promise(function(e,t){var n=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e({})}catch(e){t(e)}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?n():(document.addEventListener("runtimeready",function(){n()},!1),document.addEventListener("runtimefailed",function(e){var n=e&&e.detail||{errorCode:"2",errorMessage:"unknown nuvajs bootstrap error"};t(n)},!1))})),o};var r=function(e,n){return o||(o=t.h5AndroidbridgeInit()),o.then(function(){return new Promise(function(t,o){var r=e.split("."),i=r.pop()||"",a=r.join("."),d=function(e){"function"==typeof n.success?n.success(e):"function"==typeof n.onSuccess&&n.onSuccess(e),t(e)},s=function(e){"function"==typeof n.fail?n.fail(e):"function"==typeof n.onFail&&n.onFail(e),o(e)};"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid(d,s,a,i,n)})})};t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.h5IosBridgeInit=void 0;var o;t.h5IosBridgeInit=function(){return o||(o=new Promise(function(e,t){if("undefined"!=typeof WebViewJavascriptBridge){try{WebViewJavascriptBridge.init(function(e,t){})}catch(e){return t()}return e({})}document.addEventListener("WebViewJavascriptBridgeReady",function(){if("undefined"==typeof WebViewJavascriptBridge)return t();try{WebViewJavascriptBridge.init(function(e,t){})}catch(e){return t()}return e({})},!1)})),o};var r=function(e,n){return o||(o=t.h5IosBridgeInit()),o.then(function(){var t=Object.assign({},n);return new Promise(function(n,o){if(!0===t.watch){var r=t.onSuccess;delete t.onSuccess,"function"==typeof t.success&&(r=t.success,delete t.success),"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(e,function(e,t){"function"==typeof r&&r.call(null,e),t&&t({errorCode:"0",errorMessage:"success"})})}void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler(e,Object.assign({},t),function(e){var r=e||{};"0"===r.errorCode?("function"==typeof t.success?t.success.call(null,r.result):"function"==typeof t.onSuccess&&t.onSuccess.call(null,r.result),n(r.result)):("-1"===r.errorCode?"function"==typeof t.cancel?t.cancel.call(null,r,r.errorCode):"function"==typeof t.onCancel&&t.onCancel.call(null,r,r.errorCode):"function"==typeof t.fail?t.fail.call(null,r,r.errorCode):"function"==typeof t.onFail&&t.onFail.call(null,r,r.errorCode),o(r))})})})};t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.h5PcBridgeInit=void 0,t.h5PcBridgeInit=function(){return Promise.resolve(n(7))};var o=function(e,t){return new Promise(function(o,r){return n(7).invokeAPI(e,t).result.then(function(e){return"function"==typeof t.success?t.success.call(null,e):"function"==typeof t.onSuccess&&t.onSuccess.call(null,e),o(e)},function(e){return"function"==typeof t.fail?t.fail.call(null,e):"function"==typeof t.onFail&&t.onFail.call(null,e),r(e)})})};t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.off=t.on=void 0,t.on=function(e,t){n(7).addEventListener(e,t)},t.off=function(e,t){n(7).removeEventListener(e,t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=t.language=t.compareVersion=t.other=t.pc=t.android=t.ios=void 0;var o=n(4),r=o.getENV();t.ios=r.platform===o.ENV_ENUM.ios,t.android=r.platform===o.ENV_ENUM.android,t.pc=r.platform===o.ENV_ENUM.pc,t.other=r.platform===o.ENV_ENUM.notInDingTalk,t.compareVersion=function(e,t,n){function o(e){return parseInt(e,10)||0}if("string"!=typeof e||"string"!=typeof t)return!1;for(var r,i,a=e.split("-")[0].split(".").map(o),d=t.split("-")[0].split(".").map(o);r===i&&d.length>0;)r=a.shift(),i=d.shift();return n?(i||0)>=(r||0):(i||0)>(r||0)},t.language=r.language,t.version=r.version},function(e,t,n){"use strict";function o(e,t,n){var o="Web"===n.platform,i="iOS"===n.platform,a="android"===n.platform,d=a||i,s=function(){return o?window.navigator.userAgent.toLowerCase():""}(),u=function(){var e={};if(o){var t=window.name;try{var n=JSON.parse(t);e.containerId=n.containerId,e.version=n.hostVersion,e.language=n.language||"*"}catch(e){}}return e}(),c=function(){return d?"DingTalk"===n.appName||"com.alibaba.android.rimet"===n.appName:s.indexOf("dingtalk")>-1||!!u.containerId}(),l=function(){if(o){if(u.version)return u.version;var e=s.match(/aliapp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===e&&(e=s.match(/dingtalk\/([a-zA-Z0-9.-]+)/));return e&&e[1]||"Unknown"}return n.appVersion}(),v=!!u.containerId,f=/iphone|ipod|ios/.test(s),p=/ipad/.test(s),_=s.indexOf("android")>-1,P=s.indexOf("mac")>-1&&v,E=s.indexOf("win")>-1&&v,N=!P&&!E&&v,b=v,g="";return g=c?f||i?r.PLATFORM.IOS:_||a?r.PLATFORM.ANDROID:p?r.PLATFORM.IPAD:P?r.PLATFORM.MAC:E?r.PLATFORM.WINDOWS:N?r.PLATFORM.BROWSER:r.PLATFORM.UNKNOWN:r.PLATFORM.UNKNOWN,{isDingTalk:c,isWebiOS:f,isWebAndroid:_,isWeexiOS:i,isWeexAndroid:a,isDingTalkPCMac:P,isDingTalkPCWeb:N,isDingTalkPCWindows:E,isDingTalkPC:b,runtime:e,framework:t,platform:g,version:l,isWeex:d}}Object.defineProperty(t,"__esModule",{value:!0});var r=n(161);t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(418),r=n(416),i=n(161),a=o.default().split("."),d=a[0],s=a[1],u=function(){var e={};switch(s){case i.FRAMEWORK.VUE:var t=weex.config,n=t.env;e.platform=n.platform,i.RUNTIME.WEEX===d&&(e.appVersion=n.appVersion,e.appName=n.appName);break;case i.FRAMEWORK.RAX:i.RUNTIME.WEEX===d&&(e.platform=navigator.platform,e.appName=navigator.appName,e.appVersion=navigator.appVersion);break;case i.FRAMEWORK.UNKNOWN:i.RUNTIME.WEB===d&&(e.platform=i.RUNTIME.WEB),i.RUNTIME.UNKNOWN===d&&(e.platform=i.RUNTIME.UNKNOWN)}return e}(),c=r.default(d,s,u);t.default=c},function(e,t,n){"use strict";function o(e,t){for(var n=e.length,o=0,r=!0;o<n;o++)try{if(!(e[o]in t)){r=!1;break}}catch(e){r=!1;break}return r}function r(){return i&&a?o(c,weex)?"Web.Vue":"Web.Unknown":!i&&a?o(c,weex)?"Weex.Vue":"Weex.Unknown":i&&d&&!a?o(s,window)?"Weex.Rax":"Weex.Unknown":i&&o(u,window)?"Web.Unknown":"Unknown.Unknown"}Object.defineProperty(t,"__esModule",{value:!0});var i="undefined"!=typeof window,a="undefined"!=typeof weex,d="undefined"!=typeof callNative,s=["__weex_config__","__weex_options__","__weex_require__"],u=["localStorage","location","navigator","XMLHttpRequest"],c=["config","requireModule","document"];t.default=r},function(e,t,n){"function"!=typeof Promise&&n(441)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(419),n(421),n(422)},function(e,t){"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,t){"use strict";if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),o=1;o<arguments.length;o++){var r=arguments[o];if(null!=r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])}return n},writable:!0,configurable:!0})},function(e,t){Object.keys||(Object.keys=function(e){if(e!==Object(e))throw new TypeError("Object.keys called on a non-object");var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.push(t);return n})},function(e,t,n){"use strict";function o(e){return r(this,void 0,void 0,function(){var t,n,o,r;return i(this,function(i){return t=e.invokeName,n=e.method,o=e.callParams,r=e.JSBridge,r?[2,r(t||n,o)]:[2,this.bridgeInitFn().then(function(e){return e(t||n,o)})]})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.bridge=void 0,t.bridge=o},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){var n,o,r,d,s,s;return i(this,function(i){return!1===this.devConfig.isAuthApi&&(e.isAuthApi=!1),n=e.isAuthApi,o=e.method,r=this.invokeAPIConfigMapByMethod[o],r||!n?(d=void 0,r&&(d=r[this.env.platform]),e.apiConfig=d,d||!n?[2,t()]:(s=a.formatLog(a.diagnosticMessageMap.call_api_support_platform_error,o,this.env.platform),[2,Promise.reject({errorCode:a.diagnosticMessageMap.call_api_support_platform_error.code,errorMessage:s})])):(s=a.formatLog(a.diagnosticMessageMap.call_api_config_platform_error,this.env.platform),[2,Promise.reject({errorCode:a.diagnosticMessageMap.call_api_config_platform_error.code,errorMessage:s})])})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.checkConfig=void 0;var a=n(10);t.checkConfig=o},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){var n,o,d,s,u,c,l,v,f,p=this;return i(this,function(_){switch(_.label){case 0:return n=e.method,o=e.params,d=e.apiConfig,s=this.devConfig.forceEnableDealApiFnMap&&this.devConfig.forceEnableDealApiFnMap[n]&&!0===this.devConfig.forceEnableDealApiFnMap[n](o),u=!s&&(!0===this.devConfig.isDisableDeal||this.devConfig.disbaleDealApiWhiteList&&-1!==this.devConfig.disbaleDealApiWhiteList.indexOf(n)),c={},!u&&d&&d.paramsDeal&&a.isFunction(d.paramsDeal)?[4,d.paramsDeal(o)]:[3,2];case 1:return c=_.sent(),[3,3];case 2:c=Object.assign({},o),_.label=3;case 3:return l=function(e){return r(p,void 0,void 0,function(){return i(this,function(t){return!u&&d&&d.resultDeal&&a.isFunction(d.resultDeal)?[2,d.resultDeal(e)]:[2,e]})})},a.isFunction(c.onSuccess)&&(v=c.onSuccess,c.onSuccess=function(e){return r(p,void 0,void 0,function(){var t;return i(this,function(n){switch(n.label){case 0:return t=v,[4,l(e)];case 1:return t.apply(void 0,[n.sent()]),[2]}})})}),a.isFunction(c.success)&&(f=c.success,c.success=function(e){return r(p,void 0,void 0,function(){var t;return i(this,function(n){switch(n.label){case 0:return t=f,[4,l(e)];case 1:return t.apply(void 0,[n.sent()]),[2]}})})}),Object.assign(e,{callParams:c,invokeName:null===d||void 0===d?void 0:d.invokeName}),[2,t().then(l)]}})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.dealParamsAndResult=void 0;var a=n(11);t.dealParamsAndResult=o},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){var n,o,r,d,s,u,c,l,v;return i(this,function(i){switch(i.label){case 0:if(n=e.method,o=e.params,r=+new Date,d=r+"_"+Math.floor(1e3*Math.random()),this.devConfig.onBeforeInvokeAPI)try{this.devConfig.onBeforeInvokeAPI({invokeId:d,method:n,params:o,startTime:r})}catch(e){a.formatLog(a.diagnosticMessageMap.call_api_on_before_error,e.toString())}c=!0,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,t()];case 2:return s=i.sent(),[3,4];case 3:return l=i.sent(),u=l,c=!1,[3,4];case 4:if(v=c?s:u,this.devConfig.onAfterInvokeAPI)try{this.devConfig.onAfterInvokeAPI({invokeId:d,method:n,params:o,payload:v,startTime:r,duration:+new Date-r,isSuccess:c})}catch(e){a.formatLog(a.diagnosticMessageMap.call_api_on_after_error,e.toString())}return[2,c?Promise.resolve(v):Promise.reject(v)]}})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.hookBeforeAndAfter=void 0;var a=n(10);t.hookBeforeAndAfter=o},function(e,t,n){"use strict";var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||t.hasOwnProperty(n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.ApiHandler=void 0;var i=function(){function e(){var e=this;this.middlewares=[],this.use=function(t){e.middlewares.push(t)},this.start=function(t){var n=e.middlewares.slice().reverse(),o=function(e){return e<n.length?function(){return n[e](t,o(e+1))}:function(){}};return o(0)()}}return e}();t.ApiHandler=i,r(n(423),t),r(n(429),t),r(n(425),t),r(n(424),t),r(n(428),t),r(n(426),t),r(n(430),t)},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){return i(this,function(n){return[2,this.bridgeInitFn().then(function(n){return e.JSBridge=n,t()})]})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.initBridge=void 0,t.initBridge=o},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){var n,o,r,s,u,c,l,v,f,p,_;return i(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,t()];case 1:return[2,i.sent()];case 2:return n=i.sent(),o=e.method,r=e.isAuthApi,s=e.apiConfig,u=this.hadConfig&&void 0===this.isReady&&-1!==this.configJsApiList.indexOf(o),c="object"==typeof n&&"string"==typeof n.errorCode&&n.errorCode===d.ERROR_CODE.no_permission,l="object"==typeof n&&"string"==typeof n.errorCode&&n.errorCode===d.ERROR_CODE.cancel,v=a.getTargetApiConfigVS(s,this.env),f=v&&this.env.version&&d.compareVersion(this.env.version,v),p=(this.env.platform===d.ENV_ENUM.ios||this.env.platform===d.ENV_ENUM.android)&&u&&c,_=this.env.platform===d.ENV_ENUM.pc&&u&&(f&&!l&&r||c),p||_?[2,this.config$.then(function(){return t()})]:[2,Promise.reject(n)];case 3:return[2]}})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.retry=void 0;var a=n(2),d=n(11);t.retry=o},function(e,t,n){"use strict";function o(e,t){return r(this,void 0,void 0,function(){var n,o,r,d,s,u,c,l,v;return i(this,function(i){switch(i.label){case 0:n=e.method,o=e.params,s=!0,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,t()];case 2:return r=i.sent(),[3,4];case 3:return u=i.sent(),d=u,s=!1,[3,4];case 4:return c=s?r:d,l=s?a.LogLevel.INFO:a.LogLevel.WARNING,v=s?"success":"fail",[2,s?Promise.resolve(c):Promise.reject(c)]}})})}var r=this&&this.__awaiter||function(e,t,n,o){function r(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,i){function a(e){try{s(o.next(e))}catch(e){i(e)}}function d(e){try{s(o.throw(e))}catch(e){i(e)}}function s(e){e.done?n(e.value):r(e.value).then(a,d)}s((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){function n(e){return function(t){return o([e,t])}}function o(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){s.label=n[1];break}if(6===n[0]&&s.label<a[1]){s.label=a[1],a=n;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(n);break}a[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var r,i,a,d,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return d={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d};Object.defineProperty(t,"__esModule",{value:!0}),t.simpleLogger=void 0;var a=n(2);t.simpleLogger=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(432),n(162),n(163)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=n(4),i=n(413),a=n(8),d=n(2),s=n(414),u=n(6);o.ddSdk.setPlatform({platform:r.ENV_ENUM.pc,bridgeInit:function(){switch(r.getENV().appType){case d.APP_TYPE.MINI_APP:return Promise.resolve(a.default);default:return i.h5PcBridgeInit().then(function(){return i.default})}},authMethod:"config",authParamsDeal:function(e){var t=Object.assign({},e);return e.jsApiList&&(t.jsApiList=e.jsApiList.map(function(e){return u[e]?u[e]:e})),t.url=window.location.href.split("#")[0],t},event:{on:function(e,t){if(r.getENV().appType===d.APP_TYPE.WEB)return s.on(e,t)},off:function(e,t){if(r.getENV().appType===d.APP_TYPE.WEB)return s.off(e,t)}}})},function(e,t,n){"use strict";function o(e){var t;return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(t=null===e||void 0===e?void 0:e.context)||void 0===t?void 0:t.corpId)+"#/add-members?params="+encodeURIComponent(JSON.stringify(e)),target:"float",title:"提示",wnId:"addMembers",panelHeight:"percent83"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.addMembers=void 0,n(3);var r=n(5);t.addMembers=o},function(e,t,n){"use strict";function o(e){var t=Object.assign({},e,{isBatchApi:!0});return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/"+(i.isMobile?"mob":"index")+".html?scene=addCoolAppToGroup&params="+encodeURIComponent(JSON.stringify(t)),target:i.isMobile?"":"float",title:"选择会话添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.batchInstallCoolApp=void 0,n(3);var r=n(5),i=n(164);t.batchInstallCoolApp=o},function(e,t,n){"use strict";function o(e){var t;return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(t=null===e||void 0===e?void 0:e.context)||void 0===t?void 0:t.corpId)+"#/create-group?params="+encodeURIComponent(JSON.stringify(e)),target:"float",title:"提示",wnId:"createGroup",panelHeight:"percent83"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.createGroup=void 0,n(3);var r=n(5);t.createGroup=o},function(e,t,n){"use strict";var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||t.hasOwnProperty(n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(n(437),t),r(n(438),t),r(n(435),t),r(n(433),t),r(n(439),t),r(n(434),t)},function(e,t,n){"use strict";function o(e){return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/"+(i.isMobile?"mob":"index")+".html?scene=addCoolAppToGroup&params="+encodeURIComponent(JSON.stringify(e)),target:i.isMobile?"":"float",title:"选择群添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.installCoolAppToGroup=void 0,n(3);var r=n(5),i=n(164);t.installCoolAppToGroup=o},function(e,t,n){"use strict";function o(e){var t,n=JSON.stringify(e).length;return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(t=null===e||void 0===e?void 0:e.context)||void 0===t?void 0:t.corpId)+"#/send-message?params="+encodeURIComponent(JSON.stringify({body:e,bodyLengthList:[n]})),target:"float",title:"提示",wnId:"sendMessageToGroup",panelHeight:"percent83"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.sendMessageToGroup=void 0,n(3);var r=n(5);t.sendMessageToGroup=o},function(e,t,n){"use strict";function o(e){var t,n=JSON.stringify(e).length;return r._invoke("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId="+encodeURIComponent(null===(t=null===e||void 0===e?void 0:e.context)||void 0===t?void 0:t.corpId)+"#/send-message-to-single-chat?params="+encodeURIComponent(JSON.stringify({body:e,bodyLengthList:[n]})),target:"float",title:"提示",wnId:"sendMessageToSingleChat",panelHeight:"percent83"}})}Object.defineProperty(t,"__esModule",{value:!0}),t.sendMessageToSingleChat=void 0,n(3);var r=n(5);t.sendMessageToSingleChat=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.coolAppSdk=void 0;var o=n(436);t.coolAppSdk=o},function(e,t,n){(function(e,t){(function(e,t){t()})(0,function(){"use strict";function n(){}function o(e,t){return function(){e.apply(t,arguments)}}function r(e){if(!(this instanceof r))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],c(e,this)}function i(e,t){for(;3===e._state;)e=e._value;if(0===e._state)return void e._deferreds.push(t);e._handled=!0,r._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null===n)return void(1===e._state?a:d)(t.promise,e._value);var o;try{o=n(e._value)}catch(e){return void d(t.promise,e)}a(t.promise,o)})}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof r)return e._state=3,e._value=t,void s(e);if("function"==typeof n)return void c(o(n,t),e)}e._state=1,e._value=t,s(e)}catch(t){d(e,t)}}function d(e,t){e._state=2,e._value=t,s(e)}function s(e){2===e._state&&0===e._deferreds.length&&r._immediateFn(function(){e._handled||r._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)i(e,e._deferreds[t]);e._deferreds=null}function u(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function c(e,t){var n=!1;try{e(function(e){n||(n=!0,a(t,e))},function(e){n||(n=!0,d(t,e))})}catch(e){if(n)return;n=!0,d(t,e)}}var l=setTimeout;r.prototype.catch=function(e){return this.then(null,e)},r.prototype.then=function(e,t){var o=new this.constructor(n);return i(this,new u(e,t,o)),o},r.prototype.finally=function(e){var t=this.constructor;return this.then(function(n){return t.resolve(e()).then(function(){return n})},function(n){return t.resolve(e()).then(function(){return t.reject(n)})})},r.all=function(e){return new r(function(t,n){function o(e,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var d=a.then;if("function"==typeof d)return void d.call(a,function(t){o(e,t)},n)}r[e]=a,0==--i&&t(r)}catch(e){n(e)}}if(!e||void 0===e.length)throw new TypeError("Promise.all accepts an array");var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);for(var i=r.length,a=0;a<r.length;a++)o(a,r[a])})},r.resolve=function(e){return e&&"object"==typeof e&&e.constructor===r?e:new r(function(t){t(e)})},r.reject=function(e){return new r(function(t,n){n(e)})},r.race=function(e){return new r(function(t,n){for(var o=0,r=e.length;o<r;o++)e[o].then(t,n)})},r._immediateFn="function"==typeof e&&function(t){e(t)}||function(e){l(e,0)},r._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};var v=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==t)return t;throw new Error("unable to locate global object")}();v.Promise||(v.Promise=r)})}).call(t,n(443).setImmediate,n(12))},function(e,t,n){(function(e,t){(function(e,n){"use strict";function o(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var o={callback:e,args:t};return u[s]=o,d(s),s++}function r(e){delete u[e]}function i(e){var t=e.callback,o=e.args;switch(o.length){case 0:t();break;case 1:t(o[0]);break;case 2:t(o[0],o[1]);break;case 3:t(o[0],o[1],o[2]);break;default:t.apply(n,o)}}function a(e){if(c)setTimeout(a,0,e);else{var t=u[e];if(t){c=!0;try{i(t)}finally{r(e),c=!1}}}}if(!e.setImmediate){var d,s=1,u={},c=!1,l=e.document,v=Object.getPrototypeOf&&Object.getPrototypeOf(e);v=v&&v.setTimeout?v:e,"[object process]"==={}.toString.call(e.process)?function(){d=function(e){t.nextTick(function(){a(e)})}}():!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?function(){var e=new MessageChannel;e.port1.onmessage=function(e){a(e.data)},d=function(t){e.port2.postMessage(t)}}():l&&"onreadystatechange"in l.createElement("script")?function(){var e=l.documentElement;d=function(t){var n=l.createElement("script");n.onreadystatechange=function(){a(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}():function(){d=function(e){setTimeout(a,0,e)}}():function(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&a(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),d=function(n){e.postMessage(t+n,"*")}}(),v.setImmediate=o,v.clearImmediate=r}})("undefined"==typeof self?void 0===e?this:e:self)}).call(t,n(12),n(165))},function(e,t,n){(function(e){function o(e,t){this._id=e,this._clearFn=t}var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;t.setTimeout=function(){return new o(i.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(442),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(t,n(12))},,,,,,,,,function(e,t,n){"use strict";var o=n(3),r=n(1053),i=n(440),a=Object.assign(o,r.apiObj,{plugin:i});e.exports=a},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.apiObj=void 0;var o=n(166),r=n(167),i=n(168),a=n(169),d=n(170),s=n(171),u=n(172),c=n(173),l=n(174),v=n(175),f=n(176),p=n(177),_=n(178),P=n(179),E=n(180),N=n(181),b=n(182),g=n(183),h=n(184),k=n(185),M=n(186),m=n(187),I=n(188),y=n(189),$=n(190),S=n(191),A=n(192),U=n(193),V=n(194),O=n(195),j=n(196),C=n(197),w=n(198),D=n(199),T=n(200),z=n(201),B=n(202),F=n(203),R=n(204),L=n(205),x=n(206),W=n(207),q=n(208),H=n(209),G=n(210),J=n(211),Y=n(212),K=n(213),X=n(214),Z=n(215),Q=n(216),ee=n(217),te=n(218),ne=n(219),oe=n(220),re=n(221),ie=n(222),ae=n(223),de=n(224),se=n(225),ue=n(226),ce=n(227),le=n(228),ve=n(229),fe=n(230),pe=n(231),_e=n(232),Pe=n(233),Ee=n(234),Ne=n(235),be=n(236),ge=n(237),he=n(238),ke=n(239),Me=n(240),me=n(241),Ie=n(242),ye=n(243),$e=n(244),Se=n(245),Ae=n(246),Ue=n(247),Ve=n(248),Oe=n(249),je=n(250),Ce=n(251),we=n(252),De=n(253),Te=n(254),ze=n(255),Be=n(256),Fe=n(257),Re=n(258),Le=n(259),xe=n(260),We=n(261),qe=n(262),He=n(263),Ge=n(264),Je=n(265),Ye=n(266),Ke=n(267),Xe=n(268),Ze=n(269),Qe=n(270),et=n(271),tt=n(272),nt=n(273),ot=n(274),rt=n(275),it=n(276),at=n(277),dt=n(278),st=n(279),ut=n(280),ct=n(281),lt=n(282),vt=n(283),ft=n(284),pt=n(285),_t=n(286),Pt=n(287),Et=n(288),Nt=n(289),bt=n(290),gt=n(291),ht=n(292),kt=n(293),Mt=n(294),mt=n(295),It=n(296),yt=n(297),$t=n(298),St=n(299),At=n(300),Ut=n(301),Vt=n(302),Ot=n(303),jt=n(304),Ct=n(305),wt=n(306),Dt=n(307),Tt=n(308),zt=n(309),Bt=n(310),Ft=n(311),Rt=n(312),Lt=n(313),xt=n(314),Wt=n(315),qt=n(316),Ht=n(317),Gt=n(318),Jt=n(319),Yt=n(320),Kt=n(321),Xt=n(322),Zt=n(323),Qt=n(324),en=n(325),tn=n(326),nn=n(327),on=n(328),rn=n(329),an=n(330),dn=n(331),sn=n(332),un=n(333),cn=n(334),ln=n(335),vn=n(336),fn=n(337),pn=n(338),_n=n(339),Pn=n(340),En=n(341),Nn=n(342),bn=n(343),gn=n(344),hn=n(345),kn=n(346),Mn=n(347),mn=n(348),In=n(349),yn=n(350),$n=n(351),Sn=n(352),An=n(353),Un=n(354),Vn=n(355),On=n(356),jn=n(357),Cn=n(358),wn=n(359),Dn=n(360),Tn=n(361),zn=n(362),Bn=n(363),Fn=n(364),Rn=n(365),Ln=n(366),xn=n(367),Wn=n(368),qn=n(369),Hn=n(370),Gn=n(371),Jn=n(372),Yn=n(373),Kn=n(374),Xn=n(375),Zn=n(376),Qn=n(377),eo=n(378),to=n(379),no=n(380),oo=n(381),ro=n(382),io=n(383),ao=n(384),so=n(385),uo=n(386),co=n(387),lo=n(388),vo=n(389),fo=n(390),po=n(391),_o=n(392),Po=n(393),Eo=n(394),No=n(395),bo=n(396),go=n(397),ho=n(398),ko=n(399),Mo=n(400),mo=n(401),Io=n(402),yo=n(403),$o=n(404),So=n(405),Ao=n(13),Uo=n(13);Object.defineProperty(t,"ExternalChannelPublish",{enumerable:!0,get:function(){return Uo.ExternalChannelPublish$}});var Vo=n(14),Oo=n(14);Object.defineProperty(t,"addPhoneContact",{enumerable:!0,get:function(){return Oo.addPhoneContact$}});var jo=n(15),Co=n(15);Object.defineProperty(t,"alert",{enumerable:!0,get:function(){return Co.alert$}});var wo=n(16),Do=n(16);Object.defineProperty(t,"callUsers",{enumerable:!0,get:function(){return Do.callUsers$}});var To=n(17),zo=n(17);Object.defineProperty(t,"checkAuth",{enumerable:!0,get:function(){return zo.checkAuth$}});var Bo=n(18),Fo=n(18);Object.defineProperty(t,"checkBizCall",{enumerable:!0,get:function(){return Fo.checkBizCall$}});var Ro=n(19),Lo=n(19);Object.defineProperty(t,"chooseChat",{enumerable:!0,get:function(){return Lo.chooseChat$}});var xo=n(20),Wo=n(20);Object.defineProperty(t,"chooseConversation",{enumerable:!0,get:function(){return Wo.chooseConversation$}});var qo=n(21),Ho=n(21);Object.defineProperty(t,"chooseDateRangeInCalendar",{enumerable:!0,get:function(){return Ho.chooseDateRangeInCalendar$}});var Go=n(22),Jo=n(22);Object.defineProperty(t,"chooseDateTime",{enumerable:!0,get:function(){return Jo.chooseDateTime$}});var Yo=n(23),Ko=n(23);Object.defineProperty(t,"chooseDepartments",{enumerable:!0,get:function(){return Ko.chooseDepartments$}});var Xo=n(24),Zo=n(24);Object.defineProperty(t,"chooseDingTalkDir",{enumerable:!0,get:function(){return Zo.chooseDingTalkDir$}});var Qo=n(25),er=n(25);Object.defineProperty(t,"chooseDistrict",{enumerable:!0,get:function(){return er.chooseDistrict$}});var tr=n(26),nr=n(26);Object.defineProperty(t,"chooseExternalUsers",{enumerable:!0,get:function(){return nr.chooseExternalUsers$}});var or=n(27),rr=n(27);Object.defineProperty(t,"chooseFile",{enumerable:!0,get:function(){return rr.chooseFile$}});var ir=n(28),ar=n(28);Object.defineProperty(t,"chooseHalfDayInCalendar",{enumerable:!0,get:function(){return ar.chooseHalfDayInCalendar$}});var dr=n(29),sr=n(29);Object.defineProperty(t,"chooseImage",{enumerable:!0,get:function(){return sr.chooseImage$}});var ur=n(30),cr=n(30);Object.defineProperty(t,"chooseOneDayInCalendar",{enumerable:!0,get:function(){return cr.chooseOneDayInCalendar$}});var lr=n(31),vr=n(31);Object.defineProperty(t,"choosePhonebook",{enumerable:!0,get:function(){return vr.choosePhonebook$}});var fr=n(32),pr=n(32);Object.defineProperty(t,"chooseStaffForPC",{enumerable:!0,get:function(){return pr.chooseStaffForPC$}});var _r=n(33),Pr=n(33);Object.defineProperty(t,"chooseUserFromList",{enumerable:!0,get:function(){return Pr.chooseUserFromList$}});var Er=n(34),Nr=n(34);Object.defineProperty(t,"clearShake",{enumerable:!0,get:function(){return Nr.clearShake$}});var br=n(35),gr=n(35);Object.defineProperty(t,"closePage",{enumerable:!0,get:function(){return gr.closePage$}});var hr=n(36),kr=n(36);Object.defineProperty(t,"complexChoose",{enumerable:!0,get:function(){return kr.complexChoose$}});var Mr=n(37),mr=n(37);Object.defineProperty(t,"compressImage",{enumerable:!0,get:function(){return mr.compressImage$}});var Ir=n(38),yr=n(38);Object.defineProperty(t,"confirm",{enumerable:!0,get:function(){return yr.confirm$}});var $r=n(39),Sr=n(39);Object.defineProperty(t,"createDing",{enumerable:!0,get:function(){return Sr.createDing$}});var Ar=n(40),Ur=n(40);Object.defineProperty(t,"createDingForPC",{enumerable:!0,get:function(){return Ur.createDingForPC$}});var Vr=n(41),Or=n(41);Object.defineProperty(t,"createGroupChat",{enumerable:!0,get:function(){return Or.createGroupChat$}});var jr=n(42),Cr=n(42);Object.defineProperty(t,"createLiveClassRoom",{enumerable:!0,get:function(){return Cr.createLiveClassRoom$}});var wr=n(43),Dr=n(43);Object.defineProperty(t,"customChooseUsers",{enumerable:!0,get:function(){return Dr.customChooseUsers$}});var Tr=n(44),zr=n(44);Object.defineProperty(t,"datePicker",{enumerable:!0,get:function(){return zr.datePicker$}});var Br=n(45),Fr=n(45);Object.defineProperty(t,"dateRangePicker",{enumerable:!0,get:function(){return Fr.dateRangePicker$}});var Rr=n(46),Lr=n(46);Object.defineProperty(t,"decrypt",{enumerable:!0,get:function(){return Lr.decrypt$}});var xr=n(47),Wr=n(47);Object.defineProperty(t,"disablePullDownRefresh",{enumerable:!0,get:function(){return Wr.disablePullDownRefresh$}});var qr=n(48),Hr=n(48);Object.defineProperty(t,"disableWebViewBounce",{enumerable:!0,get:function(){return Hr.disableWebViewBounce$}});var Gr=n(49),Jr=n(49);Object.defineProperty(t,"downloadAudio",{enumerable:!0,get:function(){return Jr.downloadAudio$}});var Yr=n(50),Kr=n(50);Object.defineProperty(t,"downloadFile",{enumerable:!0,get:function(){return Kr.downloadFile$}});var Xr=n(51),Zr=n(51);Object.defineProperty(t,"editExternalUser",{enumerable:!0,get:function(){return Zr.editExternalUser$}});var Qr=n(52),ei=n(52);Object.defineProperty(t,"enablePullDownRefresh",{enumerable:!0,get:function(){return ei.enablePullDownRefresh$}});var ti=n(53),ni=n(53);Object.defineProperty(t,"enableWebViewBounce",{enumerable:!0,get:function(){return ni.enableWebViewBounce$}});var oi=n(54),ri=n(54);Object.defineProperty(t,"encrypt",{enumerable:!0,get:function(){return ri.encrypt$}});var ii=n(55),ai=n(55);Object.defineProperty(t,"exclusiveLiveCheck",{enumerable:!0,get:function(){return ai.exclusiveLiveCheck$}});var di=n(56),si=n(56);Object.defineProperty(t,"generateImageFromCode",{enumerable:!0,get:function(){return si.generateImageFromCode$}});var ui=n(57),ci=n(57);Object.defineProperty(t,"getAuthCode",{enumerable:!0,get:function(){return ci.getAuthCode$}});var li=n(58),vi=n(58);Object.defineProperty(t,"getAuthCodeV2",{enumerable:!0,get:function(){return vi.getAuthCodeV2$}});var fi=n(59),pi=n(59);Object.defineProperty(t,"getBatteryInfo",{enumerable:!0,get:function(){return pi.getBatteryInfo$}});var _i=n(60),Pi=n(60);Object.defineProperty(t,"getBeacons",{enumerable:!0,get:function(){return Pi.getBeacons$}});var Ei=n(61),Ni=n(61);Object.defineProperty(t,"getCloudCallInfo",{enumerable:!0,get:function(){return Ni.getCloudCallInfo$}});var bi=n(62),gi=n(62);Object.defineProperty(t,"getCloudCallList",{enumerable:!0,get:function(){return gi.getCloudCallList$}});var hi=n(63),ki=n(63);Object.defineProperty(t,"getDeviceUUID",{enumerable:!0,get:function(){return ki.getDeviceUUID$}});var Mi=n(64),mi=n(64);Object.defineProperty(t,"getLocatingStatus",{enumerable:!0,get:function(){return mi.getLocatingStatus$}});var Ii=n(65),yi=n(65);Object.defineProperty(t,"getLocation",{enumerable:!0,get:function(){return yi.getLocation$}});var $i=n(66),Si=n(66);Object.defineProperty(t,"getNetworkType",{enumerable:!0,get:function(){return Si.getNetworkType$}});var Ai=n(67),Ui=n(67);Object.defineProperty(t,"getOperateAuthCode",{enumerable:!0,get:function(){return Ui.getOperateAuthCode$}});var Vi=n(68),Oi=n(68);Object.defineProperty(t,"getScreenBrightness",{enumerable:!0,get:function(){return Oi.getScreenBrightness$}});var ji=n(69),Ci=n(69);Object.defineProperty(t,"getStorage",{enumerable:!0,get:function(){return Ci.getStorage$}});var wi=n(70),Di=n(70);Object.defineProperty(t,"getSystemInfo",{enumerable:!0,get:function(){return Di.getSystemInfo$}});var Ti=n(71),zi=n(71);Object.defineProperty(t,"getSystemSettings",{enumerable:!0,get:function(){return zi.getSystemSettings$}});var Bi=n(72),Fi=n(72);Object.defineProperty(t,"getUserExclusiveInfo",{enumerable:!0,get:function(){return Fi.getUserExclusiveInfo$}});var Ri=n(73),Li=n(73);Object.defineProperty(t,"getWifiHotspotStatus",{enumerable:!0,get:function(){return Li.getWifiHotspotStatus$}});var xi=n(74),Wi=n(74);Object.defineProperty(t,"getWifiStatus",{enumerable:!0,get:function(){return Wi.getWifiStatus$}});var qi=n(75),Hi=n(75);Object.defineProperty(t,"goBackPage",{enumerable:!0,get:function(){return Hi.goBackPage$}});var Gi=n(76),Ji=n(76);Object.defineProperty(t,"hideLoading",{enumerable:!0,get:function(){return Ji.hideLoading$}});var Yi=n(77),Ki=n(77);Object.defineProperty(t,"hideToast",{enumerable:!0,get:function(){return Ki.hideToast$}});var Xi=n(78),Zi=n(78);Object.defineProperty(t,"isInTabWindow",{enumerable:!0,get:function(){return Zi.isInTabWindow$}});var Qi=n(79),ea=n(79);Object.defineProperty(t,"isLocalFileExist",{enumerable:!0,get:function(){return ea.isLocalFileExist$}});var ta=n(80),na=n(80);Object.defineProperty(t,"isScreenReaderEnabled",{enumerable:!0,get:function(){return na.isScreenReaderEnabled$}});var oa=n(81),ra=n(81);Object.defineProperty(t,"locateInMap",{enumerable:!0,get:function(){return ra.locateInMap$}});var ia=n(82),aa=n(82);Object.defineProperty(t,"makeCloudCall",{enumerable:!0,get:function(){return aa.makeCloudCall$}});var da=n(83),sa=n(83);Object.defineProperty(t,"makeVideoConfCall",{enumerable:!0,get:function(){return sa.makeVideoConfCall$}});var ua=n(84),ca=n(84);Object.defineProperty(t,"multiSelect",{enumerable:!0,get:function(){return ca.multiSelect$}});var la=n(85),va=n(85);Object.defineProperty(t,"navigateBackPage",{enumerable:!0,get:function(){return va.navigateBackPage$}});var fa=n(86),pa=n(86);Object.defineProperty(t,"navigateToPage",{enumerable:!0,get:function(){return pa.navigateToPage$}});var _a=n(87),Pa=n(87);Object.defineProperty(t,"nfcReadCardNumber",{enumerable:!0,get:function(){return Pa.nfcReadCardNumber$}});var Ea=n(88),Na=n(88);Object.defineProperty(t,"onBeaconServiceChange",{enumerable:!0,get:function(){return Na.onBeaconServiceChange$}});var ba=n(89),ga=n(89);Object.defineProperty(t,"onBeaconUpdate",{enumerable:!0,get:function(){return ga.onBeaconUpdate$}});var ha=n(90),ka=n(90);Object.defineProperty(t,"onPlayAudioEnd",{enumerable:!0,get:function(){return ka.onPlayAudioEnd$}});var Ma=n(91),ma=n(91);Object.defineProperty(t,"onRecordEnd",{enumerable:!0,get:function(){return ma.onRecordEnd$}});var Ia=n(92),ya=n(92);Object.defineProperty(t,"openChatByChatId",{enumerable:!0,get:function(){return ya.openChatByChatId$}});var $a=n(93),Sa=n(93);Object.defineProperty(t,"openChatByConversationId",{enumerable:!0,get:function(){return Sa.openChatByConversationId$}});var Aa=n(94),Ua=n(94);Object.defineProperty(t,"openChatByUserId",{enumerable:!0,get:function(){return Ua.openChatByUserId$}});var Va=n(95),Oa=n(95);Object.defineProperty(t,"openDocument",{enumerable:!0,get:function(){return Oa.openDocument$}});var ja=n(96),Ca=n(96);Object.defineProperty(t,"openLink",{enumerable:!0,get:function(){return Ca.openLink$}});var wa=n(97),Da=n(97);Object.defineProperty(t,"openLocalFile",{enumerable:!0,get:function(){return Da.openLocalFile$}});var Ta=n(98),za=n(98);Object.defineProperty(t,"openLocation",{enumerable:!0,get:function(){return za.openLocation$}});var Ba=n(99),Fa=n(99);Object.defineProperty(t,"openMicroApp",{enumerable:!0,get:function(){return Fa.openMicroApp$}});var Ra=n(100),La=n(100);Object.defineProperty(t,"openPageInMicroApp",{enumerable:!0,get:function(){return La.openPageInMicroApp$}});var xa=n(101),Wa=n(101);Object.defineProperty(t,"openPageInModalForPC",{enumerable:!0,get:function(){return Wa.openPageInModalForPC$}});var qa=n(102),Ha=n(102);Object.defineProperty(t,"openPageInSlidePanelForPC",{enumerable:!0,get:function(){return Ha.openPageInSlidePanelForPC$}});var Ga=n(103),Ja=n(103);Object.defineProperty(t,"openPageInWorkBenchForPC",{enumerable:!0,get:function(){return Ja.openPageInWorkBenchForPC$}});var Ya=n(104),Ka=n(104);Object.defineProperty(t,"pauseAduio",{enumerable:!0,get:function(){return Ka.pauseAduio$}});var Xa=n(105),Za=n(105);Object.defineProperty(t,"playAduio",{enumerable:!0,get:function(){return Za.playAduio$}});var Qa=n(106),ed=n(106);Object.defineProperty(t,"previewFileInDingTalk",{enumerable:!0,get:function(){return ed.previewFileInDingTalk$}});var td=n(107),nd=n(107);Object.defineProperty(t,"previewImage",{enumerable:!0,get:function(){return nd.previewImage$}});var od=n(108),rd=n(108);Object.defineProperty(t,"previewImagesInDingTalkBatch",{enumerable:!0,get:function(){return rd.previewImagesInDingTalkBatch$}});var id=n(109),ad=n(109);Object.defineProperty(t,"prompt",{enumerable:!0,get:function(){return ad.prompt$}});var dd=n(110),sd=n(110);Object.defineProperty(t,"quickCallList",{enumerable:!0,get:function(){return sd.quickCallList$}});var ud=n(111),cd=n(111);Object.defineProperty(t,"quitPage",{enumerable:!0,get:function(){return cd.quitPage$}});var ld=n(112),vd=n(112);Object.defineProperty(t,"readNFC",{enumerable:!0,get:function(){return vd.readNFC$}});var fd=n(113),pd=n(113);Object.defineProperty(t,"removeStorage",{enumerable:!0,get:function(){return pd.removeStorage$}});var _d=n(114),Pd=n(114);Object.defineProperty(t,"replacePage",{enumerable:!0,get:function(){return Pd.replacePage$}});var Ed=n(115),Nd=n(115);Object.defineProperty(t,"requestAuthCode",{enumerable:!0,get:function(){return Nd.requestAuthCode$}});var bd=n(116),gd=n(116);Object.defineProperty(t,"requestMoneySubmmitOrder",{enumerable:!0,get:function(){return gd.requestMoneySubmmitOrder$}});var hd=n(117),kd=n(117);Object.defineProperty(t,"resetScreenView",{enumerable:!0,get:function(){return kd.resetScreenView$}});var Md=n(118),md=n(118);Object.defineProperty(t,"resumeAudio",{enumerable:!0,get:function(){return md.resumeAudio$}});var Id=n(119),yd=n(119);Object.defineProperty(t,"rotateScreenView",{enumerable:!0,get:function(){return yd.rotateScreenView$}});var $d=n(120),Sd=n(120);Object.defineProperty(t,"rsa",{enumerable:!0,get:function(){return Sd.rsa$}});var Ad=n(121),Ud=n(121);Object.defineProperty(t,"saveFileToDingTalk",{enumerable:!0,get:function(){return Ud.saveFileToDingTalk$}});var Vd=n(122),Od=n(122);Object.defineProperty(t,"saveVideoToPhotosAlbum",{enumerable:!0,get:function(){return Od.saveVideoToPhotosAlbum$}});var jd=n(123),Cd=n(123);Object.defineProperty(t,"scan",{enumerable:!0,get:function(){return Cd.scan$}});var wd=n(124),Dd=n(124);Object.defineProperty(t,"scanCard",{enumerable:!0,get:function(){return Dd.scanCard$}});var Td=n(125),zd=n(125);Object.defineProperty(t,"searchMap",{enumerable:!0,get:function(){return zd.searchMap$}});var Bd=n(126),Fd=n(126);Object.defineProperty(t,"setClipboard",{enumerable:!0,get:function(){return Fd.setClipboard$}});var Rd=n(127),Ld=n(127);Object.defineProperty(t,"setKeepScreenOn",{enumerable:!0,get:function(){return Ld.setKeepScreenOn$}});var xd=n(128),Wd=n(128);Object.defineProperty(t,"setNavigationIcon",{enumerable:!0,get:function(){return Wd.setNavigationIcon$}});var qd=n(129),Hd=n(129);Object.defineProperty(t,"setNavigationLeft",{enumerable:!0,get:function(){return Hd.setNavigationLeft$}});var Gd=n(130),Jd=n(130);Object.defineProperty(t,"setNavigationTitle",{enumerable:!0,get:function(){return Jd.setNavigationTitle$}});var Yd=n(131),Kd=n(131);Object.defineProperty(t,"setScreenBrightness",{enumerable:!0,get:function(){return Kd.setScreenBrightness$}});var Xd=n(132),Zd=n(132);Object.defineProperty(t,"setStorage",{enumerable:!0,get:function(){return Zd.setStorage$}});var Qd=n(133),es=n(133);Object.defineProperty(t,"share",{enumerable:!0,get:function(){return es.share$}});var ts=n(134),ns=n(134);Object.defineProperty(t,"showActionSheet",{enumerable:!0,get:function(){return ns.showActionSheet$}});var os=n(135),rs=n(135);Object.defineProperty(t,"showAuthGuide",{enumerable:!0,get:function(){return rs.showAuthGuide$}});var is=n(136),as=n(136);Object.defineProperty(t,"showCallMenu",{enumerable:!0,get:function(){return as.showCallMenu$}});var ds=n(137),ss=n(137);Object.defineProperty(t,"showLoading",{enumerable:!0,get:function(){return ss.showLoading$}});var us=n(138),cs=n(138);Object.defineProperty(t,"showModal",{enumerable:!0,get:function(){return cs.showModal$}});var ls=n(139),vs=n(139);Object.defineProperty(t,"showSharePanel",{enumerable:!0,get:function(){return vs.showSharePanel$}});var fs=n(140),ps=n(140);Object.defineProperty(t,"showToast",{enumerable:!0,get:function(){return ps.showToast$}});var _s=n(141),Ps=n(141);Object.defineProperty(t,"singleSelect",{enumerable:!0,get:function(){return Ps.singleSelect$}});var Es=n(142),Ns=n(142);Object.defineProperty(t,"startBeaconDiscovery",{enumerable:!0,get:function(){return Ns.startBeaconDiscovery$}});var bs=n(143),gs=n(143);Object.defineProperty(t,"startLocating",{enumerable:!0,get:function(){return gs.startLocating$}});var hs=n(144),ks=n(144);Object.defineProperty(t,"startRecord",{enumerable:!0,get:function(){return ks.startRecord$}});var Ms=n(145),ms=n(145);Object.defineProperty(t,"stopAudio",{enumerable:!0,get:function(){return ms.stopAudio$}});var Is=n(146),ys=n(146);Object.defineProperty(t,"stopBeaconDiscovery",{enumerable:!0,get:function(){return ys.stopBeaconDiscovery$}});var $s=n(147),Ss=n(147);Object.defineProperty(t,"stopLocating",{enumerable:!0,get:function(){return Ss.stopLocating$}});var As=n(148),Us=n(148);Object.defineProperty(t,"stopPullDownRefresh",{enumerable:!0,get:function(){return Us.stopPullDownRefresh$}});var Vs=n(149),Os=n(149);Object.defineProperty(t,"stopRecord",{enumerable:!0,get:function(){return Os.stopRecord$}});var js=n(150),Cs=n(150);Object.defineProperty(t,"timePicker",{enumerable:!0,get:function(){return Cs.timePicker$}});var ws=n(151),Ds=n(151);Object.defineProperty(t,"translateVoice",{enumerable:!0,get:function(){return Ds.translateVoice$}});var Ts=n(152),zs=n(152);Object.defineProperty(t,"uploadAttachmentToDingTalk",{enumerable:!0,get:function(){return zs.uploadAttachmentToDingTalk$}});var Bs=n(153),Fs=n(153);Object.defineProperty(t,"uploadFile",{enumerable:!0,get:function(){return Fs.uploadFile$}});var Rs=n(154),Ls=n(154);Object.defineProperty(t,"vibrate",{enumerable:!0,get:function(){return Ls.vibrate$}});var xs=n(155),Ws=n(155);Object.defineProperty(t,"watchShake",{enumerable:!0,get:function(){return Ws.watchShake$}});var qs=n(156),Hs=n(156);Object.defineProperty(t,"writeNFC",{enumerable:!0,get:function(){return Hs.writeNFC$}});var Gs=n(406),Js=n(407),Ys=n(408),Ks=n(409),Xs=n(410);t.apiObj={biz:{ATMBle:{beaconPicker:o.beaconPicker$,detectFace:r.detectFace$,detectFaceFullScreen:i.detectFaceFullScreen$,exclusiveLiveCheck:a.exclusiveLiveCheck$,faceManager:d.faceManager$,punchModePicker:s.punchModePicker$},alipay:{bindAlipay:u.bindAlipay$,openAuth:c.openAuth$,pay:l.pay$},attend:{getLBSWua:v.getLBSWua$},auth:{openAccountPwdLoginPage:f.openAccountPwdLoginPage$,requestAuthInfo:p.requestAuthInfo$},calendar:{chooseDateTime:_.chooseDateTime$,chooseHalfDay:P.chooseHalfDay$,chooseInterval:E.chooseInterval$,chooseOneDay:N.chooseOneDay$},chat:{chooseConversationByCorpId:b.chooseConversationByCorpId$,collectSticker:g.collectSticker$,createSceneGroup:h.createSceneGroup$,getRealmCid:k.getRealmCid$,locationChatMessage:M.locationChatMessage$,openSingleChat:m.openSingleChat$,pickConversation:I.pickConversation$,sendEmotion:y.sendEmotion$,toConversation:$.toConversation$,toConversationByOpenConversationId:S.toConversationByOpenConversationId$},clipboardData:{setData:A.setData$},conference:{createCloudCall:U.createCloudCall$,getCloudCallInfo:V.getCloudCallInfo$,getCloudCallList:O.getCloudCallList$,videoConfCall:j.videoConfCall$},contact:{choose:C.choose$,chooseMobileContacts:w.chooseMobileContacts$,complexPicker:D.complexPicker$,createGroup:T.createGroup$,departmentsPicker:z.departmentsPicker$,externalComplexPicker:B.externalComplexPicker$,externalEditForm:F.externalEditForm$,rolesPicker:R.rolesPicker$,setRule:L.setRule$},cspace:{chooseSpaceDir:x.chooseSpaceDir$,delete:W.delete$,preview:q.preview$,previewDentryImages:H.previewDentryImages$,saveFile:G.saveFile$},customContact:{choose:J.choose$,multipleChoose:Y.multipleChoose$},data:{rsa:K.rsa$},ding:{create:X.create$,post:Z.post$},edu:{finishMiniCourseByRecordId:Q.finishMiniCourseByRecordId$,getMiniCourseDraftList:ee.getMiniCourseDraftList$,joinClassroom:te.joinClassroom$,makeMiniCourse:ne.makeMiniCourse$,newMsgNotificationStatus:oe.newMsgNotificationStatus$,startAuth:re.startAuth$,tokenFaceImg:ie.tokenFaceImg$},event:{notifyWeex:ae.notifyWeex$},file:{downloadFile:de.downloadFile$},intent:{fetchData:se.fetchData$},iot:{bind:ue.bind$,bindMeetingRoom:ce.bindMeetingRoom$,getDeviceProperties:le.getDeviceProperties$,invokeThingService:ve.invokeThingService$,queryMeetingRoomList:fe.queryMeetingRoomList$,setDeviceProperties:pe.setDeviceProperties$,unbind:_e.unbind$},live:{startClassRoom:Pe.startClassRoom$,startUnifiedLive:Ee.startUnifiedLive$},map:{locate:Ne.locate$,search:be.search$,view:ge.view$},media:{compressVideo:he.compressVideo$},microApp:{openApp:ke.openApp$},navigation:{close:Me.close$,goBack:me.goBack$,hideBar:Ie.hideBar$,navigateBackPage:ye.navigateBackPage$,navigateToMiniProgram:$e.navigateToMiniProgram$,navigateToPage:Se.navigateToPage$,quit:Ae.quit$,replace:Ue.replace$,setIcon:Ve.setIcon$,setLeft:Oe.setLeft$,setMenu:je.setMenu$,setRight:Ce.setRight$,setTitle:we.setTitle$},pbp:{componentPunchFromPartner:De.componentPunchFromPartner$,startMatchRuleFromPartner:Te.startMatchRuleFromPartner$,stopMatchRuleFromPartner:ze.stopMatchRuleFromPartner$},phoneContact:{add:Be.add$},realm:{getRealtimeTracingStatus:Fe.getRealtimeTracingStatus$,getUserExclusiveInfo:Re.getUserExclusiveInfo$,startRealtimeTracing:Le.startRealtimeTracing$,stopRealtimeTracing:xe.stopRealtimeTracing$,subscribe:We.subscribe$,unsubscribe:qe.unsubscribe$},resource:{getInfo:He.getInfo$,reportDebugMessage:Ge.reportDebugMessage$},shortCut:{addShortCut:Je.addShortCut$},sports:{getHealthAuthorizationStatus:Ye.getHealthAuthorizationStatus$,getHealthData:Ke.getHealthData$,getHealthDeviceData:Xe.getHealthDeviceData$,requestHealthAuthorization:Ze.requestHealthAuthorization$},store:{closeUnpayOrder:Qe.closeUnpayOrder$,createOrder:et.createOrder$,getPayUrl:tt.getPayUrl$,inquiry:nt.inquiry$},tabwindow:{isTab:ot.isTab$},telephone:{call:rt.call$,checkBizCall:it.checkBizCall$,quickCallList:at.quickCallList$,showCallMenu:dt.showCallMenu$},user:{checkPassword:st.checkPassword$,get:ut.get$},util:{callComponent:ct.callComponent$,checkAuth:lt.checkAuth$,chooseImage:vt.chooseImage$,chooseRegion:ft.chooseRegion$,chosen:pt.chosen$,clearWebStoreCache:_t.clearWebStoreCache$,closePreviewImage:Pt.closePreviewImage$,compressImage:Et.compressImage$,datepicker:Nt.datepicker$,datetimepicker:bt.datetimepicker$,decrypt:gt.decrypt$,downloadFile:ht.downloadFile$,encrypt:kt.encrypt$,getPerfInfo:Mt.getPerfInfo$,invokeWorkbench:mt.invokeWorkbench$,isEnableGPUAcceleration:It.isEnableGPUAcceleration$,isLocalFileExist:yt.isLocalFileExist$,multiSelect:$t.multiSelect$,open:St.open$,openBrowser:At.openBrowser$,openDocument:Ut.openDocument$,openLink:Vt.openLink$,openLocalFile:Ot.openLocalFile$,openModal:jt.openModal$,openSlidePanel:Ct.openSlidePanel$,presentWindow:wt.presentWindow$,previewImage:Dt.previewImage$,previewVideo:Tt.previewVideo$,saveImage:zt.saveImage$,saveImageToPhotosAlbum:Bt.saveImageToPhotosAlbum$,scan:Ft.scan$,scanCard:Rt.scanCard$,setGPUAcceleration:Lt.setGPUAcceleration$,setScreenBrightnessAndKeepOn:xt.setScreenBrightnessAndKeepOn$,setScreenKeepOn:Wt.setScreenKeepOn$,share:qt.share$,shareImage:Ht.shareImage$,showAuthGuide:Gt.showAuthGuide$,showSharePanel:Jt.showSharePanel$,startDocSign:Yt.startDocSign$,systemShare:Kt.systemShare$,timepicker:Xt.timepicker$,uploadAttachment:Zt.uploadAttachment$,uploadFile:Qt.uploadFile$,uploadImage:en.uploadImage$,uploadImageFromCamera:tn.uploadImageFromCamera$,ut:nn.ut$},verify:{openBindIDCard:on.openBindIDCard$,startAuth:rn.startAuth$},voice:{makeCall:an.makeCall$},watermarkCamera:{getWatermarkInfo:dn.getWatermarkInfo$,setWatermarkInfo:sn.setWatermarkInfo$}},channel:{permission:{requestAuthCode:un.requestAuthCode$}},device:{accelerometer:{clearShake:cn.clearShake$,watchShake:ln.watchShake$},audio:{download:vn.download$,onPlayEnd:fn.onPlayEnd$,onRecordEnd:pn.onRecordEnd$,pause:_n.pause$,play:Pn.play$,resume:En.resume$,startRecord:Nn.startRecord$,stop:bn.stop$,stopRecord:gn.stopRecord$,translateVoice:hn.translateVoice$},base:{getBatteryInfo:kn.getBatteryInfo$,getInterface:Mn.getInterface$,getPhoneInfo:mn.getPhoneInfo$,getScanWifiListAsync:In.getScanWifiListAsync$,getUUID:yn.getUUID$,getWifiStatus:$n.getWifiStatus$,openSystemSetting:Sn.openSystemSetting$},connection:{getNetworkType:An.getNetworkType$},geolocation:{checkPermission:Un.checkPermission$,get:Vn.get$,start:On.start$,status:jn.status$,stop:Cn.stop$},launcher:{checkInstalledApps:wn.checkInstalledApps$,launchApp:Dn.launchApp$},nfc:{nfcRead:Tn.nfcRead$,nfcStop:zn.nfcStop$,nfcWrite:Bn.nfcWrite$},notification:{actionSheet:Fn.actionSheet$,alert:Rn.alert$,confirm:Ln.confirm$,extendModal:xn.extendModal$,hidePreloader:Wn.hidePreloader$,modal:qn.modal$,prompt:Hn.prompt$,showPreloader:Gn.showPreloader$,toast:Jn.toast$,vibrate:Yn.vibrate$},screen:{getScreenBrightness:Kn.getScreenBrightness$,insetAdjust:Xn.insetAdjust$,isScreenReaderEnabled:Zn.isScreenReaderEnabled$,resetView:Qn.resetView$,rotateView:eo.rotateView$,setScreenBrightness:to.setScreenBrightness$}},media:{voiceRecorder:{keepAlive:no.keepAlive$,pause:oo.pause$,resume:ro.resume$,start:io.start$,stop:ao.stop$}},net:{bjGovApn:{loginGovNet:so.loginGovNet$}},runtime:{h5nuvabridge:{exec:uo.exec$},message:{fetch:co.fetch$,post:lo.post$},monitor:{getLoadTime:vo.getLoadTime$},permission:{requestAuthCode:fo.requestAuthCode$,requestOperateAuthCode:po.requestOperateAuthCode$}},ui:{input:{plain:_o.plain$},multitask:{addToFloat:Po.addToFloat$,removeFromFloat:Eo.removeFromFloat$},nav:{close:No.close$,getCurrentId:bo.getCurrentId$,go:go.go$,preload:ho.preload$,recycle:ko.recycle$},progressBar:{setColors:Mo.setColors$},pullToRefresh:{disable:mo.disable$,enable:Io.enable$,stop:yo.stop$},webViewBounce:{disable:$o.disable$,enable:So.enable$}},ExternalChannelPublish:Ao.ExternalChannelPublish$,addPhoneContact:Vo.addPhoneContact$,alert:jo.alert$,callUsers:wo.callUsers$,checkAuth:To.checkAuth$,checkBizCall:Bo.checkBizCall$,chooseChat:Ro.chooseChat$,chooseConversation:xo.chooseConversation$,chooseDateRangeInCalendar:qo.chooseDateRangeInCalendar$,chooseDateTime:Go.chooseDateTime$,chooseDepartments:Yo.chooseDepartments$,chooseDingTalkDir:Xo.chooseDingTalkDir$,chooseDistrict:Qo.chooseDistrict$,chooseExternalUsers:tr.chooseExternalUsers$,chooseFile:or.chooseFile$,chooseHalfDayInCalendar:ir.chooseHalfDayInCalendar$,chooseImage:dr.chooseImage$,chooseOneDayInCalendar:ur.chooseOneDayInCalendar$,choosePhonebook:lr.choosePhonebook$,chooseStaffForPC:fr.chooseStaffForPC$,chooseUserFromList:_r.chooseUserFromList$,clearShake:Er.clearShake$,closePage:br.closePage$,complexChoose:hr.complexChoose$,compressImage:Mr.compressImage$,confirm:Ir.confirm$,createDing:$r.createDing$,createDingForPC:Ar.createDingForPC$,createGroupChat:Vr.createGroupChat$,createLiveClassRoom:jr.createLiveClassRoom$,customChooseUsers:wr.customChooseUsers$,datePicker:Tr.datePicker$,dateRangePicker:Br.dateRangePicker$,decrypt:Rr.decrypt$,disablePullDownRefresh:xr.disablePullDownRefresh$,disableWebViewBounce:qr.disableWebViewBounce$,downloadAudio:Gr.downloadAudio$,downloadFile:Yr.downloadFile$,editExternalUser:Xr.editExternalUser$,enablePullDownRefresh:Qr.enablePullDownRefresh$,enableWebViewBounce:ti.enableWebViewBounce$,encrypt:oi.encrypt$,exclusiveLiveCheck:ii.exclusiveLiveCheck$,generateImageFromCode:di.generateImageFromCode$,getAuthCode:ui.getAuthCode$,getAuthCodeV2:li.getAuthCodeV2$,getBatteryInfo:fi.getBatteryInfo$,getBeacons:_i.getBeacons$,getCloudCallInfo:Ei.getCloudCallInfo$,getCloudCallList:bi.getCloudCallList$,getDeviceUUID:hi.getDeviceUUID$,getLocatingStatus:Mi.getLocatingStatus$,getLocation:Ii.getLocation$,getNetworkType:$i.getNetworkType$,getOperateAuthCode:Ai.getOperateAuthCode$,getScreenBrightness:Vi.getScreenBrightness$,getStorage:ji.getStorage$,getSystemInfo:wi.getSystemInfo$,getSystemSettings:Ti.getSystemSettings$,getUserExclusiveInfo:Bi.getUserExclusiveInfo$,getWifiHotspotStatus:Ri.getWifiHotspotStatus$,getWifiStatus:xi.getWifiStatus$,goBackPage:qi.goBackPage$,hideLoading:Gi.hideLoading$,hideToast:Yi.hideToast$,isInTabWindow:Xi.isInTabWindow$,isLocalFileExist:Qi.isLocalFileExist$,isScreenReaderEnabled:ta.isScreenReaderEnabled$,locateInMap:oa.locateInMap$,makeCloudCall:ia.makeCloudCall$,makeVideoConfCall:da.makeVideoConfCall$,multiSelect:ua.multiSelect$,navigateBackPage:la.navigateBackPage$,navigateToPage:fa.navigateToPage$,nfcReadCardNumber:_a.nfcReadCardNumber$,onBeaconServiceChange:Ea.onBeaconServiceChange$,onBeaconUpdate:ba.onBeaconUpdate$,onPlayAudioEnd:ha.onPlayAudioEnd$,onRecordEnd:Ma.onRecordEnd$,openChatByChatId:Ia.openChatByChatId$,openChatByConversationId:$a.openChatByConversationId$,openChatByUserId:Aa.openChatByUserId$,openDocument:Va.openDocument$,openLink:ja.openLink$,openLocalFile:wa.openLocalFile$,openLocation:Ta.openLocation$,openMicroApp:Ba.openMicroApp$,openPageInMicroApp:Ra.openPageInMicroApp$,openPageInModalForPC:xa.openPageInModalForPC$,openPageInSlidePanelForPC:qa.openPageInSlidePanelForPC$,openPageInWorkBenchForPC:Ga.openPageInWorkBenchForPC$,pauseAduio:Ya.pauseAduio$,playAduio:Xa.playAduio$,previewFileInDingTalk:Qa.previewFileInDingTalk$,previewImage:td.previewImage$,previewImagesInDingTalkBatch:od.previewImagesInDingTalkBatch$,prompt:id.prompt$,quickCallList:dd.quickCallList$,quitPage:ud.quitPage$,readNFC:ld.readNFC$,removeStorage:fd.removeStorage$,replacePage:_d.replacePage$,requestAuthCode:Ed.requestAuthCode$,requestMoneySubmmitOrder:bd.requestMoneySubmmitOrder$,resetScreenView:hd.resetScreenView$,resumeAudio:Md.resumeAudio$,rotateScreenView:Id.rotateScreenView$,rsa:$d.rsa$,saveFileToDingTalk:Ad.saveFileToDingTalk$,saveVideoToPhotosAlbum:Vd.saveVideoToPhotosAlbum$,scan:jd.scan$,scanCard:wd.scanCard$,searchMap:Td.searchMap$,setClipboard:Bd.setClipboard$,setKeepScreenOn:Rd.setKeepScreenOn$,setNavigationIcon:xd.setNavigationIcon$,setNavigationLeft:qd.setNavigationLeft$,setNavigationTitle:Gd.setNavigationTitle$,setScreenBrightness:Yd.setScreenBrightness$,setStorage:Xd.setStorage$,share:Qd.share$,showActionSheet:ts.showActionSheet$,showAuthGuide:os.showAuthGuide$,showCallMenu:is.showCallMenu$,showLoading:ds.showLoading$,showModal:us.showModal$,showSharePanel:ls.showSharePanel$,showToast:fs.showToast$,singleSelect:_s.singleSelect$,startBeaconDiscovery:Es.startBeaconDiscovery$,startLocating:bs.startLocating$,startRecord:hs.startRecord$,stopAudio:Ms.stopAudio$,stopBeaconDiscovery:Is.stopBeaconDiscovery$,stopLocating:$s.stopLocating$,stopPullDownRefresh:As.stopPullDownRefresh$,stopRecord:Vs.stopRecord$,timePicker:js.timePicker$,translateVoice:ws.translateVoice$,uploadAttachmentToDingTalk:Ts.uploadAttachmentToDingTalk$,uploadFile:Bs.uploadFile$,vibrate:Rs.vibrate$,watchShake:xs.watchShake$,writeNFC:qs.writeNFC$,util:{domainStorage:{getItem:Gs.getItem$,getStorageInfo:Js.getStorageInfo$,removeItem:Ys.removeItem$,setItem:Ks.setItem$},openTemporary:{getData:Xs.getData$}}}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){e.exports=n(452)}])});