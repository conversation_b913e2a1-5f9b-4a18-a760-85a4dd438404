/*var commonUrl = "https://my.mycc.edu.cn";
var commonUrlTwo = "https://my.mycc.edu.cn/sopplus";*/
var commonUrlTwo = 'https://my.mycc.edu.cn/sopplus';
var commonUrl = commonUrlTwo.slice(0,-8);
localStorage.setItem('domainUrl',commonUrl);
localStorage.setItem('portalDomainUrl',commonUrlTwo);

var _p = 'YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__';
var _p2 = 'YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m';

var index_urls = {
    // getUserInfo:"/sopplus/_web/portal/api/user/loginInfo.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
    getUserInfo:"/sopplus/_web/customized/loginInfo.jsp?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
    // 首页 新闻资讯
    getPortalArticleList16:"/mnews/mobile/getPortalArticleList16.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__&forceHttps=1",
    // 首页 一网
    getYW:"/sopplus/mobile/loadServiceApps.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
    // 首页 热门分类
    getPortalIndexAppList:"/sopplus/mobile/getPortalIndexAppList.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
    // 首页 我的收藏
    getMyFavoriteAppsForMobile:"/sopplus/mobile/getMyFavoriteAppsForMobile.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
    // 应用 功能
    getPortalAppsListByOS:"/sopplus/mobile/getPortalAppsListByOS.rst?_p=YXA9MSZtPU4m",
    // 收藏 取消收藏
    cancelApp: "/sopplus/mobile/favoritesPortalApp.rst?_p="+_p,
	  //最近使用
	  getRecentlyUsedApps: "/sopplus/mobile/getRecentlyUsedApps.rst?_p="+_p ,

    favoritesPortalApp:'/sopplus/mobile/favoritesPortalApp.rst?_p='+_p,
    // 应用 部门
    loadServiceAppsByOrg:"/sopplus/mobile/loadServiceAppsByOrg.rst?_p="+_p2,
    // 日程、课表数据
    getAllCalendarLists:"/calendar/mgr/api/calendarList.rst?_p=YXM9MiZ0PTYmZD0xMTMmcD0xJmY9MjImbT1OJg__&queryType=2",
    // 首页 我的待办
    getTodoTask:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/todotask.rst?_p="+_p2+"+&isCount=true",
    // 首页 我的邮箱
    getMyEmail:"/sopplus/_web/customized/getWYUnRead.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 我的邮箱跳转
    emailLink:"/sopplus/_web/customized/lookUserEmail.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 我的OA
    getMyOA:"/sopplus/_web/customized/getOATodo.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 我的云盘
    getMyCloud:"/sopplus/_web/customized/getCloudDisk.jsp?_p="+_p2,
    // 首页 我的一卡通
    getMyCard:"/sopplus/_web/customized/getCard.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 待办列表
    getTodo:"/sopplus/_web/customized/getTodo.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 日程安排
    getRcap:"/calendar/mgr/api/calendarList.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m&queryType=2&categoryIds=0,-2",
    getRcapNew:"/calendar/mgr/api/calendarDataList.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m&queryType=2&categoryIds=0,-2",
    // 首页 我的OA
    getMyOA:"/sopplus/_web/customized/getOATodo.jsp?isCount=true&title=&end=true&_p=YXM9MSZwPTEmbT1OJg__",
    // 首页 网站导航
    getNavList:"/sopplus/mobile/loadServiceApps.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m",
    // 一卡通
    getCard:"/sopplus/_web/customized/getCard.jsp?_p=YXM9MSZwPTEmbT1OJg__",
    //获取首页头部应用
    getTopAppsUrl:'/sopplus/_web/customized/getPortalAppListByPCAppType.jsp?_p='+_p,
    //获取服务大厅数据
    getFwdtUrl:'/sopplus/_web/customized/getPortalCategory.jsp?_p='+_p,
    //获取我的收藏
    // getMyFavoriteAppsUrl:'/sopplus/mobile/getMyFavoriteApps.rst?_p='+_p,
    getMyFavoriteAppsUrl:'/sopplus/mobile/getMyFavoriteAppsForMobile.rst?_p='+_p,
    //统计应用访问信息
    openModule: '/sopplus/mobile/openModule.do?_p=' + _p + '&timeStamp=' + new Date().getTime(),
    //日程分类接口
    getCalendarCategoryUrl:'/calendar/mgr/api/category/list.rst?_p='+_p,
    //日程接口
    getCalendarUrl: '/calendar/mgr/api/ids/list.rst?_p=YXQ9MSZwPTEmbT1OJg__', 
    //课表接口
    getCalendarListUrl:'/calendar/mgr/api/mycc/calendarList.rst',
    //日程-判断人员是否有课表数据
    isHaveLesson:'/calendar/mgr/api/mycc/isHaveLesson.rst',
    //删除日程
    getDelCalendarUrl:'/calendar/mgr/api/calendar.rst?_p=YXQ9MSZwPTEmbT1OJg__',//删除某个日程接口,
    //搜索页-获取热词接口
    getHotWords:'/sopplus/_web/_plugs/elsearch/api/searchItem/rate/hotWords.rst',
    //获取热词接口-新
    getHotWordsNew:'/sopplus/_web/customized/rateHotWords.jsp?_p='+_p,
    //搜索咨询页-搜索
    getSearchUrl:'/sopplus/_web/_plugs/elsearch/api/custom/search.rst',
    //应用收藏页
    //获取对应分类下应用
    getAllServiceAppsUrl:'/sopplus/_web/customized/loadAllServiceApps.jsp?_p='+_p,
    //获取部门下分类
    getOrgListUrl:'/sopplus/_web/customized/getOrgList.jsp?_p='+_p,
    //获取部门分类下应用
    getServiceAppsByOrgUrl:'/sopplus/_web/customized/loadServiceAppsByOrg.jsp?_p='+_p,
    //我的收藏排序
    saveMyFavoriteAppsSortUrl:'/sopplus/mobile/saveMyFavoriteAppsSort.rst?_p='+_p,
    //首页资讯栏目
    getPortalServiceSpecialTopicUrl:'/sopplus/mobile/getPortalServiceSpecialTopic.rst?_p='+_p,
    //获取人员身份-学生-还是教师
    getUserLbUrl:'/sopplus/_web/customized/getUserLb.jsp?_p='+_p,
    //是否有邮箱
    hasMailUrl:'/sopplus/_web/customized/stuEmail/getUser.jsp?_p='+_p,
    //创建注册邮箱
    createEmailUrl:'/sopplus/_web/customized/stuEmail/createEmail.jsp?_p='+_p,
    //获取学生未读邮箱数量
    getStuUnReadCountUrl:'/sopplus/_web/customized/stuEmail/getUnReadCount.jsp?_p='+_p,
    //学生邮箱单点登录地址
    lookUserEmailUrl:'/sopplus/_web/customized/stuEmail/lookUserEmail.jsp?_p='+_p,
    //获取教师未读邮箱数量
    getUnReadCountUrl:'/sopplus/_web/customized/email/getUnReadCount.jsp?_p='+_p,
    //教师邮箱登录地址
    getMailUrl:'https://mail.mycc.edu.cn/',
    //获取订阅栏目
    querySubsUrl:'/sopplus/mobile/querySubs.mo',
    //待办
    getToDoUrl:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/todotask.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__"+"&isCount=true",
    //已办
    getDoneUrl:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/donetask.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__"+"&isCount=true",
    //流程追踪
    getTraceUrl:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/processTrack.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__"+"&isCount=true",
    //办结
    getFinishedUrl:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/processDone.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__"+"&isCount=true",
    //获取事务分类
    getTaskCategoryUrl:"/sopplus/_web/_apps/taskcenter/query/api/v3/taskCategory.rst",
    //消息中心接口
    fetchReceiveBoxJsonpUrl: '/ucp/_web/onlinenews/receiveBox/api/fetchReceiveBoxJsonp.rst?_p=YXM9MSZwPTEmbT1OJg__',
    fetchReceiveBoxDetailJsonpUrl: '/ucp/_web/onlinenews/receiveBox/api/findMessageDetailJsonp.rst?_p=YXM9MiZ0PTUmZD0xMzYmcD0xJmY9NTAmbT1OJg__',
    checkidentity: '/sopplus/mobile/wechatAuth?ret=',   
    //获取应用详情
    getServiceAppDetailUrl:'/sopplus/mobile/getServiceAppDetail.do?_p='+_p,
    //获取头像
    getUserZp:'/sopplus/_web/customized/getUserZp.jsp?_p='+_p
}

//打开目标页面
function DDOpen(tabAppUrl) {
    if(tabAppUrl){
        dd.biz.util.openLink({
            url: tabAppUrl,
            onSuccess: function (result) {
              //msg(result);
            },
            onFail: function (err) {
              //msg(err);
              console.log(err);
            },
		});
    }
}

// 获取浏览器的版本
function myBrowser(){
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1;
    if (isOpera) {
        return "Opera";
    }else
    if (userAgent.indexOf("Firefox") > -1) {
        return "Firefox";
    }else
    if (userAgent.indexOf("Chrome") > -1){
      return "Chrome";
    }else
    if (userAgent.indexOf("Safari") > -1) {
        return "Safari";
    }else
    if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
        return "IE";
    }else{
      return "Other";
    };
}

//签名认证 
function checkIdentity(paramUrl) {
    var url = '/sopplus/mobile/wechatAuth?ret=' + encodeURIComponent(paramUrl);
    var params = {
        isReturn: 'false'
    };
    var returnUrl = '';
    $.ajax({
        url: url,
        type: "get",
        data: params,
        dataType: 'json',
        async: false,
        timeout: 10000,
        success: function (data) {
            var u = data.data;
            returnUrl = u;
        },
        error: function (err) {
            $.poptips("接口暂时无法调用，请稍候访问！")
        }
    });
    return returnUrl;
}

//.svg 图片转 svg
function imgToSvg(svgUrl){
  return new Promise((resolve, reject) => {
    $.ajax({
        url: svgUrl,
        dataType: 'text',
        type: 'get',
        crossDomain: true,
        success: function (res) {
          var reg = /<title[^>]*>(.|\n)*<\/title>/gi;
          var newRes = res.replace(reg, '');
          resolve(newRes)
        },
        error:function(err){
          reject('')
        }
    })
  }).catch((err) => {
    console.log(err);
  })
}

//公共跳转方法
function commonToUrl(url, type='_self'){
  if(url){
    //判断是否钉钉环境
    var userAgent = navigator.userAgent; // 获取User Agent信息
    if (userAgent.indexOf('DingTalk') > -1) {
      // return true; // User Agent包含"DingTalk"关键词，则表明当前环境为钉钉
      //钉钉环境-钉钉方式跳转
      DDOpen(url);
    } else {
      // return false; // 其他情况返回false
      window.open(url, type);
    }
  }
}