$(function (){
    //获取我的收藏应用
    getMyFavourite();
    //获取应用分类
    getAppCategory();
    
})
function getMyFavourite(){
    $.ajax({
        url: index_urls.getMyFavoriteAppsUrl,
        data: {
          targetType: 4
        },
        crossDomain: true,
        dataType: "jsonp",
        success:async function (res) {
          var data = res.data?res.data:[];
          var appHtml = '<div class="appGrid sortable" id="myApps"> ';
          var appDetailHtml = await returnAppStr(data,true);
          appHtml +=appDetailHtml;
          appHtml +='</div>';
          if(data.length === 0){
            // appHtml = '<div class="nofavourite">暂无数据</div>';
            appHtml =`
            <div class="nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nofavourite">暂无数据</div>
                </div>
            `;
          }
          $('.main .addModule .myApps').html(appHtml);
          if(data.length > 0){
            //拖拽排序
            setSortable();
          }
        },
        error(err){
            $('.main .addModule .nofavourite').show();
        }
      });
}

//获取应用分类
function getAppCategory(){
    $.ajax({
                url: index_urls.getFwdtUrl,
                dataType: 'jsonp',
                data: {
                    clientType: 4,
                    parentCategoryId: 38,
                    showType: 1
                },
                success: function (res) {
                    var categoryList = res.data?res.data:[];
                    var categoryStr = '';
                    for(var i=0;i<categoryList.length;i++){
                        var isActive ='';
                        if(i===0){
                            isActive='active';
                            loadAllServiceApps(categoryList[i].id);
                        }
                        categoryStr +=`
                        <div class="item ${isActive}" data-categoryid=${categoryList[i].id}>${categoryList[i].typeName}</div>
                        `;
                    }
                    $('.main .addModule .allApps .categoryBox .tabBox').html(categoryStr);
                }
            })

      
}
//加载对应分类下的应用
function loadAllServiceApps(parentCategoryId){
    $.ajax({
        url: index_urls.getAllServiceAppsUrl,
        data: {
            clientType: 4,
            parentCategoryId: parentCategoryId
        },
        crossDomain: true,
        dataType: "jsonp",
        success:async function (res) {
            var data = res.data?res.data:{};
            var appList = (data.appList)?data.appList:[];
            var appListStr='<div class="appGrid">';
            var appListDetailHtml = await returnAppStr(appList);
            appListStr += appListDetailHtml;
            appListStr +='</div>';
            if(appList.length < 1 ){
                // appListStr ='<div class="nodata">暂无数据</div>';
                appListStr =`
                <div class="nodataBox">
                <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
                <div class="nodata">暂无数据</div>
                </div>
                `;
            }
            $('.main .addModule .allApps .appList').html(appListStr);
        },
        error(err){
            var appHtml =`
            <div class="nodataBox">
            <div class="noDataImg"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-designer-yzefkw e1de0imv0" focusable="false" aria-hidden="true" viewBox="0 0 64 41"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse cx="32" cy="33" rx="32" ry="7" style="fill: rgb(245, 245, 245);"></ellipse><g fill-rule="nonzero" style="stroke: rgb(217, 217, 217);"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" style="fill: rgb(250, 250, 250);"></path></g></g></svg></div>
            <div class="nodata">暂无数据</div>
            </div>
            `;
            $('.main .addModule .allApps .appList').html(appHtml);
        }
      });
}

//添加收藏
$(document).on("click", " .main .appGrid .appItem .imgBox .btnImg.addBtn", function (e){
    e.stopPropagation();
    var this_id = $(this).parents('.appItem').data('id');
    //添加收藏
     $.ajax({
                url: index_urls.favoritesPortalApp,
                dataType : "jsonp",
                // type:"POST",
                type:"GET",
                data:{
                    appVersionId:this_id,
                	act:"add"
                },
                success: function (res) {
                    //隐藏的模块
                    var this_hide_module = $('.main .hideModule').data('type');
                    if(this_hide_module === 'addMod'){
                      //当前是搜素模块
                      //重新搜索
                      var keyword = $('.main .searchModule .top input').val();
                      checkVal(keyword);
                    }
                    //哪个模块都要重新请求收藏列表和全部列表
                    getMyFavourite();
                    //重新加载选中分类下应用
                    var this_category = $('.main .addModule .allApps .categoryBox .tabBox .item.active').data('cateogryid');
                    loadAllServiceApps(this_category);
                    //父级页面刷新一下我的收藏
                    window.parent.postMessage('reloadMyFavourite', '*');
                }
            })
  })

  //取消收藏
  $(document).on("click", " .main .appGrid .appItem .imgBox .btnImg.cancelBtn", function (e){
    e.stopPropagation();
    var this_id = $(this).parents('.appItem').data('id');
    var dia = $.dialog({
      content:"您确认取消收藏此应用吗?",
      title:"提示",
      width:60
  }).on("dialog:confirm",function(e, dialog){
     //取消收藏
     $.ajax({
      url: index_urls.favoritesPortalApp,
      dataType : "jsonp",
      // type:"POST",
      type:"GET",
      data:{
          appVersionId:this_id,
        act:"cancel"
      },
      success: function (res) {
        //隐藏的模块
        var this_hide_module = $('.main .hideModule').data('type');
        if(this_hide_module === 'addMod'){
          //当前是搜素模块
          //重新搜索
          var keyword = $('.main .searchModule .top input').val();
          checkVal(keyword);
        }
        //模块
         getMyFavourite();
         //重新加载选中分类下应用
         var this_category = $('.main .addModule .allApps .categoryBox .tabBox .item.active').data('cateogryid');
         loadAllServiceApps(this_category);
        //父级页面刷新一下我的收藏
        window.parent.postMessage('reloadMyFavourite', '*');
      }
  })
   
  })
   
  })


//拖拽排序
function setSortable(){
        var el = document.getElementById('myApps');
        var sortable = new Sortable(el, {
            animation: 150, // 动画持续时间，单位毫秒
            chosenClass: 'sort-chosen',
            ghostClass: 'sort-ghost',
            animation: 400,
            delay: 150,
            isDropAnimation: false, //DragDrop时是否对DragEl启用滑动效果
            ghostScale: 1.2,//DragGhostEl 缩放效果
            onEnd: function (evt, b) {
                // 排序结束时的回调函数
                var item = evt.item; // 被拖拽的元素
                // var from = evt.from; // 拖拽开始的列表
                // var to = evt.to; // 拖拽结束的列表
                // var oldIndex = evt.oldIndex; // 原始索引
                var newIndex = evt.newIndex; // 新的索引
                //移动的应用id
                var this_activeId =item.dataset && item.dataset.id;
                if(newIndex<1){
                  $.poptips('不支持移动到该位置')
                }else{
                  //新位置的前一个应用id
                  var pre_id = $('.main #myApps .appItem').eq(newIndex-1).attr('data-id');
                  setSortableApps(this_activeId,pre_id)
                }
                
            }
        });
        //重新提交接口
        function setSortableApps(target,bourn) {
            $.ajax({
                url: index_urls.saveMyFavoriteAppsSortUrl,
                dataType: 'jsonp',
                data: {
                  target:target,
                  bourn:bourn,
                  targetOs:"dingtalk"
                },
                success: function (res) {
                    console.log(res)
                    if(res.data ==='success'){
                      //成功
                      $.poptips('操作成功');
                      //父级页面刷新一下我的收藏
                      window.parent.postMessage('reloadMyFavourite', '*');
                    }else{
                      $.poptips('请稍后重试');
                    }
                    
                },
              error:function(err){
                $.poptips('请稍后重试');
              }
            })
        }
}

//点击搜索图标
$(document).on("click", " .main .addModule .btnBox .searchBtn", function (e){
    e.stopPropagation();
    $('.main .addModule').addClass('hideModule');
    //搜索模块打开
    $('.main .searchModule').removeClass('hideModule');
  })

  //点击返回箭头图标
$(document).on("click", " .main .searchModule .backModule", function (e){
    e.stopPropagation();
    //搜索模块关闭
    $('.main .searchModule').addClass('hideModule');
    //收藏应用模块打开
    $('.main .addModule').removeClass('hideModule');
  })

//切换分类
$(document).on("click", " .main .addModule .allApps .categoryBox .tabBox .item", function (e){
    e.stopPropagation();
   $(this).addClass('active').siblings().removeClass('active');
   var this_category = $(this).data('categoryid');
   loadAllServiceApps(this_category);
  })

//搜索模块查询按钮
$(document).on("click", " .main .searchModule .top .searchText", function (e){
    e.stopPropagation();
   var keyword = $('.main .searchModule .top input').val();
   checkVal(keyword);
  })


//关闭按钮
$(document).on("click", " .main .addModule .btnBox .closeBtn", function (e){
    e.stopPropagation();
    //关闭应用收藏弹窗
    window.parent.addAppDia.iframe[0].close();
  })

  //检查搜索文字
  function checkVal(keyWord){
    keyWord = $.trim(keyWord);
    if(keyWord){
      let re = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;//只能输入汉字、英文字母或数字
      if(re.test(keyWord)){
        //正常搜索
        searchModInfo(keyWord);
      }else{
        //提示信息
        $.poptips('请输入汉字、英文字母或数字！');
      }
    }else{
      //提示信息
     $("#portal_header .inputTips").html('请输入您要查找的内容').show();
     $.poptips('请输入您要查找的内容');
    }
  }

  //搜索模块搜索
  function searchModInfo(appName){
    $.ajax({
      url: index_urls.getAllServiceAppsUrl,
      dataType: 'jsonp',
      data: {
          clientType: 4,
          parentCategoryId:38,
          appName:appName
      },
    success: function (res) {
        var data = res.data?res.data:{};
        var appList = data.appList?data.appList:[];
        renderSearchApps(appList);
    },
    error:function(err){
      renderSearchApps([]);
    }
    })
  }

  //渲染搜索的数据
  async function renderSearchApps(appList){
    var appListStr='<div class="searchInfo appGrid">';
    var appListDetailStr= await returnAppStr(appList);
    appListStr +=appListDetailStr;
    appListStr +='</div>';
    if(appList.length ===0){
      appListStr ='<div class="nodata">暂无数据，换个搜索词试试</div>';
    }
    $('.main .searchModule .searchModContent').html(appListStr);
  }

  //返回应用标签字符串
  async function returnAppStr(appList,isFavorite=false){
    var appListStr='';
    for(var i=0;i<appList.length;i++){
      if(isFavorite === true){
        var this_btnImg = "./images/cancelBtn.png";
        var this_btnImgClass="cancelBtn";
      }else{
        var this_isfavorite = appList[i].isfavorite;
        var this_btnImg = (this_isfavorite==false)?"./images/addBtn.png":"./images/cancelBtn.png";
        var this_btnImgClass= (this_isfavorite==false)?"addBtn":"cancelBtn";
      }
    
       //判断 svg 格式
       var item = appList[i];
       var this_iconUrl = item.iconUrl;
       var this_isSvg = false;  
       if(this_iconUrl.slice(-4) === '.svg'){
         var this_svgDetail = await imgToSvg(this_iconUrl);  
         this_isSvg = true;
       }
       if((this_isSvg === true) && this_svgDetail){
        appListStr +=`
        <div class="appItem" data-id="${appList[i].id}" data-enableservice="${appList[i].enableService}" data-isallow="${appList[i].isAllow}" 
        data-reason="${appList[i].reason}" data-appurl="${appList[i].entranceUrl}" data-appname="${appList[i].appName}" >
        <div class="imgBox">
        <div class="svgImg">${this_svgDetail}</div>
            <img class="btnImg ${this_btnImgClass}" src="${this_btnImg}" alt="">
        </div>
        <div class="name">${appList[i].name}</div>
        </div>
        `;

       }else{
        appListStr +=`
        <div class="appItem" data-id="${appList[i].id}" data-enableservice="${appList[i].enableService}" data-isallow="${appList[i].isAllow}" 
        data-reason="${appList[i].reason}" data-appurl="${appList[i].entranceUrl}" data-appname="${appList[i].appName}" >
        <div class="imgBox">
            <img src="${appList[i].iconUrl}" alt="">
            <img class="btnImg ${this_btnImgClass}" src="${this_btnImg}" alt="">
        </div>
        <div class="name">${appList[i].name}</div>
        </div>
        `;
       }
     
    }
    return appListStr;
  }


  $("body").on("keyup", ".main .searchModule .top input", function (e) {
    //筛选框赋值
    if (e.keyCode == 13) {
      //enter键为13
      var keyword = $('.main .searchModule .top input').val();
      checkVal(keyword);
    }
  });