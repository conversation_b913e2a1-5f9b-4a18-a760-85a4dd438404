"use strict";

var _index_urls;

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

/*var commonUrl = "https://my.mycc.edu.cn";
var commonUrlTwo = "https://my.mycc.edu.cn/sopplus";*/
var commonUrlTwo = 'https://my.mycc.edu.cn/sopplus';
var commonUrl = commonUrlTwo.slice(0, -8);
localStorage.setItem('domainUrl', commonUrl);
localStorage.setItem('portalDomainUrl', commonUrlTwo);
var _p = 'YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__';
var _p2 = 'YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m';
var index_urls = (_index_urls = {
  // getUserInfo:"/sopplus/_web/portal/api/user/loginInfo.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
  getUserInfo: "/sopplus/_web/customized/loginInfo.jsp?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
  // 首页 新闻资讯
  getPortalArticleList16: "/mnews/mobile/getPortalArticleList16.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__&forceHttps=1",
  // 首页 一网
  getYW: "/sopplus/mobile/loadServiceApps.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__",
  // 首页 热门分类
  getPortalIndexAppList: "/sopplus/mobile/getPortalIndexAppList.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
  // 首页 我的收藏
  getMyFavoriteAppsForMobile: "/sopplus/mobile/getMyFavoriteAppsForMobile.rst?_p=YXM9MiZ0PTUmZD0xMzQmcD0xJmY9NDgmbT1OJg__",
  // 应用 功能
  getPortalAppsListByOS: "/sopplus/mobile/getPortalAppsListByOS.rst?_p=YXA9MSZtPU4m",
  // 收藏 取消收藏
  cancelApp: "/sopplus/mobile/favoritesPortalApp.rst?_p=" + _p,
  //最近使用
  getRecentlyUsedApps: "/sopplus/mobile/getRecentlyUsedApps.rst?_p=" + _p,
  favoritesPortalApp: '/sopplus/mobile/favoritesPortalApp.rst?_p=' + _p,
  // 应用 部门
  loadServiceAppsByOrg: "/sopplus/mobile/loadServiceAppsByOrg.rst?_p=" + _p2,
  // 日程、课表数据
  getAllCalendarLists: "/calendar/mgr/api/calendarList.rst?_p=YXM9MiZ0PTYmZD0xMTMmcD0xJmY9MjImbT1OJg__&queryType=2",
  // 首页 我的待办
  getTodoTask: "/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/todotask.rst?_p=" + _p2 + "+&isCount=true",
  // 首页 我的邮箱
  getMyEmail: "/sopplus/_web/customized/getWYUnRead.jsp?_p=YXM9MSZwPTEmbT1OJg__",
  // 首页 我的邮箱跳转
  emailLink: "/sopplus/_web/customized/lookUserEmail.jsp?_p=YXM9MSZwPTEmbT1OJg__",
  // 首页 我的OA
  getMyOA: "/sopplus/_web/customized/getOATodo.jsp?_p=YXM9MSZwPTEmbT1OJg__",
  // 首页 我的云盘
  getMyCloud: "/sopplus/_web/customized/getCloudDisk.jsp?_p=" + _p2,
  // 首页 我的一卡通
  getMyCard: "/sopplus/_web/customized/getCard.jsp?_p=YXM9MSZwPTEmbT1OJg__",
  // 首页 待办列表
  getTodo: "/sopplus/_web/customized/getTodo.jsp?_p=YXM9MSZwPTEmbT1OJg__",
  // 首页 日程安排
  getRcap: "/calendar/mgr/api/calendarList.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m&queryType=2&categoryIds=0,-2",
  getRcapNew: "/calendar/mgr/api/calendarDataList.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m&queryType=2&categoryIds=0,-2"
}, _defineProperty(_index_urls, "getMyOA", "/sopplus/_web/customized/getOATodo.jsp?isCount=true&title=&end=true&_p=YXM9MSZwPTEmbT1OJg__"), _defineProperty(_index_urls, "getNavList", "/sopplus/mobile/loadServiceApps.rst?_p=YXM9MiZ0PTUmZD05NyZwPTEmZj0yMiZtPU4m"), _defineProperty(_index_urls, "getCard", "/sopplus/_web/customized/getCard.jsp?_p=YXM9MSZwPTEmbT1OJg__"), _defineProperty(_index_urls, "getTopAppsUrl", '/sopplus/_web/customized/getPortalAppListByPCAppType.jsp?_p=' + _p), _defineProperty(_index_urls, "getFwdtUrl", '/sopplus/_web/customized/getPortalCategory.jsp?_p=' + _p), _defineProperty(_index_urls, "getMyFavoriteAppsUrl", '/sopplus/mobile/getMyFavoriteAppsForMobile.rst?_p=' + _p), _defineProperty(_index_urls, "openModule", '/sopplus/mobile/openModule.do?_p=' + _p + '&timeStamp=' + new Date().getTime()), _defineProperty(_index_urls, "getCalendarCategoryUrl", '/calendar/mgr/api/category/list.rst?_p=' + _p), _defineProperty(_index_urls, "getCalendarUrl", '/calendar/mgr/api/ids/list.rst?_p=YXQ9MSZwPTEmbT1OJg__'), _defineProperty(_index_urls, "getCalendarListUrl", '/calendar/mgr/api/mycc/calendarList.rst'), _defineProperty(_index_urls, "isHaveLesson", '/calendar/mgr/api/mycc/isHaveLesson.rst'), _defineProperty(_index_urls, "getDelCalendarUrl", '/calendar/mgr/api/calendar.rst?_p=YXQ9MSZwPTEmbT1OJg__'), _defineProperty(_index_urls, "getHotWords", '/sopplus/_web/_plugs/elsearch/api/searchItem/rate/hotWords.rst'), _defineProperty(_index_urls, "getHotWordsNew", '/sopplus/_web/customized/rateHotWords.jsp?_p=' + _p), _defineProperty(_index_urls, "getSearchUrl", '/sopplus/_web/_plugs/elsearch/api/custom/search.rst'), _defineProperty(_index_urls, "getAllServiceAppsUrl", '/sopplus/_web/customized/loadAllServiceApps.jsp?_p=' + _p), _defineProperty(_index_urls, "getOrgListUrl", '/sopplus/_web/customized/getOrgList.jsp?_p=' + _p), _defineProperty(_index_urls, "getServiceAppsByOrgUrl", '/sopplus/_web/customized/loadServiceAppsByOrg.jsp?_p=' + _p), _defineProperty(_index_urls, "saveMyFavoriteAppsSortUrl", '/sopplus/mobile/saveMyFavoriteAppsSort.rst?_p=' + _p), _defineProperty(_index_urls, "getPortalServiceSpecialTopicUrl", '/sopplus/mobile/getPortalServiceSpecialTopic.rst?_p=' + _p), _defineProperty(_index_urls, "getUserLbUrl", '/sopplus/_web/customized/getUserLb.jsp?_p=' + _p), _defineProperty(_index_urls, "hasMailUrl", '/sopplus/_web/customized/stuEmail/getUser.jsp?_p=' + _p), _defineProperty(_index_urls, "createEmailUrl", '/sopplus/_web/customized/stuEmail/createEmail.jsp?_p=' + _p), _defineProperty(_index_urls, "getStuUnReadCountUrl", '/sopplus/_web/customized/stuEmail/getUnReadCount.jsp?_p=' + _p), _defineProperty(_index_urls, "lookUserEmailUrl", '/sopplus/_web/customized/stuEmail/lookUserEmail.jsp?_p=' + _p), _defineProperty(_index_urls, "getUnReadCountUrl", '/sopplus/_web/customized/email/getUnReadCount.jsp?_p=' + _p), _defineProperty(_index_urls, "getMailUrl", 'https://mail.mycc.edu.cn/'), _defineProperty(_index_urls, "querySubsUrl", '/sopplus/mobile/querySubs.mo'), _defineProperty(_index_urls, "getToDoUrl", "/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/todotask.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" + "&isCount=true"), _defineProperty(_index_urls, "getDoneUrl", "/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/donetask.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" + "&isCount=true"), _defineProperty(_index_urls, "getTraceUrl", "/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/processTrack.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" + "&isCount=true"), _defineProperty(_index_urls, "getFinishedUrl", "/sopplus/_web/_apps/taskcenter/query/api/v3/taskcenter/processDone.rst?_p=YXM9MiZ0PTUmZD0xMzMmcD0xJmY9NDQmbT1OJg__" + "&isCount=true"), _defineProperty(_index_urls, "getTaskCategoryUrl", "/sopplus/_web/_apps/taskcenter/query/api/v3/taskCategory.rst"), _defineProperty(_index_urls, "fetchReceiveBoxJsonpUrl", '/ucp/_web/onlinenews/receiveBox/api/fetchReceiveBoxJsonp.rst?_p=YXM9MSZwPTEmbT1OJg__'), _defineProperty(_index_urls, "fetchReceiveBoxDetailJsonpUrl", '/ucp/_web/onlinenews/receiveBox/api/findMessageDetailJsonp.rst?_p=YXM9MiZ0PTUmZD0xMzYmcD0xJmY9NTAmbT1OJg__'), _defineProperty(_index_urls, "checkidentity", '/sopplus/mobile/wechatAuth?ret='), _defineProperty(_index_urls, "getServiceAppDetailUrl", '/sopplus/mobile/getServiceAppDetail.do?_p=' + _p), _defineProperty(_index_urls, "getUserZp", '/sopplus/_web/customized/getUserZp.jsp?_p=' + _p), _index_urls); //打开目标页面

function DDOpen(tabAppUrl) {
  if (tabAppUrl) {
    dd.biz.util.openLink({
      url: tabAppUrl,
      onSuccess: function onSuccess(result) {//msg(result);
      },
      onFail: function onFail(err) {
        //msg(err);
        console.log(err);
      }
    });
  }
} // 获取浏览器的版本


function myBrowser() {
  var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串

  var isOpera = userAgent.indexOf("Opera") > -1;

  if (isOpera) {
    return "Opera";
  } else if (userAgent.indexOf("Firefox") > -1) {
    return "Firefox";
  } else if (userAgent.indexOf("Chrome") > -1) {
    return "Chrome";
  } else if (userAgent.indexOf("Safari") > -1) {
    return "Safari";
  } else if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
    return "IE";
  } else {
    return "Other";
  }

  ;
} //签名认证 


function checkIdentity(paramUrl) {
  var url = '/sopplus/mobile/wechatAuth?ret=' + encodeURIComponent(paramUrl);
  var params = {
    isReturn: 'false'
  };
  var returnUrl = '';
  $.ajax({
    url: url,
    type: "get",
    data: params,
    dataType: 'json',
    async: false,
    timeout: 10000,
    success: function success(data) {
      var u = data.data;
      returnUrl = u;
    },
    error: function error(err) {
      $.poptips("接口暂时无法调用，请稍候访问！");
    }
  });
  return returnUrl;
} //.svg 图片转 svg


function imgToSvg(svgUrl) {
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: svgUrl,
      dataType: 'text',
      type: 'get',
      crossDomain: true,
      success: function success(res) {
        var reg = /<title[^>]*>(.|\n)*<\/title>/gi;
        var newRes = res.replace(reg, '');
        resolve(newRes);
      },
      error: function error(err) {
        reject('');
      }
    });
  })["catch"](function (err) {
    console.log(err);
  });
} //公共跳转方法


function commonToUrl(url) {
  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '_self';

  if (url) {
    //判断是否钉钉环境
    var userAgent = navigator.userAgent; // 获取User Agent信息

    if (userAgent.indexOf('DingTalk') > -1) {
      // return true; // User Agent包含"DingTalk"关键词，则表明当前环境为钉钉
      //钉钉环境-钉钉方式跳转
      DDOpen(url);
    } else {
      // return false; // 其他情况返回false
      window.open(url, type);
    }
  }
}