var Zepto=function(){function t(t){return null==t?String(t):q[J.call(t)]||"object"}function e(e){return"function"==t(e)}function n(t){return null!=t&&t==t.window}function i(t){return null!=t&&t.nodeType==t.DOCUMENT_NODE}function a(e){return"object"==t(e)}function o(t){return a(t)&&!n(t)&&Object.getPrototypeOf(t)==Object.prototype}function r(e){var i=t(e),a="length"in e&&e.length;return"function"!==i&&!n(e)&&(!(1!==e.nodeType||!a)||("array"===i||0===a||"number"==typeof a&&a>0&&a-1 in e))}function s(t){return A.call(t,function(t){return null!=t})}function l(t){return t.length>0?C.fn.concat.apply([],t):t}function c(t){return t.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function u(t){return t in Y?Y[t]:Y[t]=new RegExp("(^|\\s)"+t+"(\\s|$)")}function d(t,e){return"number"!=typeof e||j[c(t)]?e:e+"px"}function h(t){var e,n;return O[t]||(e=$.createElement(t),$.body.appendChild(e),n=getComputedStyle(e,"").getPropertyValue("display"),e.parentNode.removeChild(e),"none"==n&&(n="block"),O[t]=n),O[t]}function f(t){return"children"in t?P.call(t.children):C.map(t.childNodes,function(t){if(1==t.nodeType)return t})}function p(t,e){var n,i=t?t.length:0;for(n=0;n<i;n++)this[n]=t[n];this.length=i,this.selector=e||""}function m(t,e,n){for(_ in e)n&&(o(e[_])||K(e[_]))?(o(e[_])&&!o(t[_])&&(t[_]={}),K(e[_])&&!K(t[_])&&(t[_]=[]),m(t[_],e[_],n)):e[_]!==T&&(t[_]=e[_])}function v(t,e){return null==e?C(t):C(t).filter(e)}function b(t,n,i,a){return e(n)?n.call(t,i,a):n}function g(t,e,n){null==n?t.removeAttribute(e):t.setAttribute(e,n)}function y(t,e){var n=t.className||"",i=n&&n.baseVal!==T;return e===T?i?n.baseVal:n:void(i?n.baseVal=e:t.className=e)}function w(t){try{return t?"true"==t||"false"!=t&&("null"==t?null:+t+""==t?+t:/^[\[\{]/.test(t)?C.parseJSON(t):t):t}catch(e){return t}}function x(t,e){e(t);for(var n=0,i=t.childNodes.length;n<i;n++)x(t.childNodes[n],e)}var T,_,C,D,E,k,S=[],M=S.concat,A=S.filter,P=S.slice,$=window.document,O={},Y={},j={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},R=/^\s*<(\w+|!)[^>]*>/,N=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,L=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,I=/^(?:body|html)$/i,F=/([A-Z])/g,V=["val","css","html","text","data","width","height","offset"],B=["after","prepend","before","append"],W=$.createElement("table"),U=$.createElement("tr"),H={tr:$.createElement("tbody"),tbody:W,thead:W,tfoot:W,td:U,th:U,"*":$.createElement("div")},X=/complete|loaded|interactive/,z=/^[\w-]*$/,q={},J=q.toString,Z={},Q=$.createElement("div"),G={tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},K=Array.isArray||function(t){return t instanceof Array};return Z.matches=function(t,e){if(!e||!t||1!==t.nodeType)return!1;var n=t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.matchesSelector;if(n)return n.call(t,e);var i,a=t.parentNode,o=!a;return o&&(a=Q).appendChild(t),i=~Z.qsa(a,e).indexOf(t),o&&Q.removeChild(t),i},E=function(t){return t.replace(/-+(.)?/g,function(t,e){return e?e.toUpperCase():""})},k=function(t){return A.call(t,function(e,n){return t.indexOf(e)==n})},Z.fragment=function(t,e,n){var i,a,r;return N.test(t)&&(i=C($.createElement(RegExp.$1))),i||(t.replace&&(t=t.replace(L,"<$1></$2>")),e===T&&(e=R.test(t)&&RegExp.$1),e in H||(e="*"),r=H[e],r.innerHTML=""+t,i=C.each(P.call(r.childNodes),function(){r.removeChild(this)})),o(n)&&(a=C(i),C.each(n,function(t,e){V.indexOf(t)>-1?a[t](e):a.attr(t,e)})),i},Z.Z=function(t,e){return new p(t,e)},Z.isZ=function(t){return t instanceof Z.Z},Z.init=function(t,n){var i;if(!t)return Z.Z();if("string"==typeof t)if(t=t.trim(),"<"==t[0]&&R.test(t))i=Z.fragment(t,RegExp.$1,n),t=null;else{if(n!==T)return C(n).find(t);i=Z.qsa($,t)}else{if(e(t))return C($).ready(t);if(Z.isZ(t))return t;if(K(t))i=s(t);else if(a(t))i=[t],t=null;else if(R.test(t))i=Z.fragment(t.trim(),RegExp.$1,n),t=null;else{if(n!==T)return C(n).find(t);i=Z.qsa($,t)}}return Z.Z(i,t)},C=function(t,e){return Z.init(t,e)},C.extend=function(t){var e,n=P.call(arguments,1);return"boolean"==typeof t&&(e=t,t=n.shift()),n.forEach(function(n){m(t,n,e)}),t},Z.qsa=function(t,e){var n,i="#"==e[0],a=!i&&"."==e[0],o=i||a?e.slice(1):e,r=z.test(o);return t.getElementById&&r&&i?(n=t.getElementById(o))?[n]:[]:1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType?[]:P.call(r&&!i&&t.getElementsByClassName?a?t.getElementsByClassName(o):t.getElementsByTagName(e):t.querySelectorAll(e))},C.contains=$.documentElement.contains?function(t,e){return t!==e&&t.contains(e)}:function(t,e){for(;e&&(e=e.parentNode);)if(e===t)return!0;return!1},C.type=t,C.isFunction=e,C.isWindow=n,C.isArray=K,C.isPlainObject=o,C.isEmptyObject=function(t){var e;for(e in t)return!1;return!0},C.inArray=function(t,e,n){return S.indexOf.call(e,t,n)},C.camelCase=E,C.trim=function(t){return null==t?"":String.prototype.trim.call(t)},C.uuid=0,C.support={},C.expr={},C.noop=function(){},C.map=function(t,e){var n,i,a,o=[];if(r(t))for(i=0;i<t.length;i++)n=e(t[i],i),null!=n&&o.push(n);else for(a in t)n=e(t[a],a),null!=n&&o.push(n);return l(o)},C.each=function(t,e){var n,i;if(r(t)){for(n=0;n<t.length;n++)if(e.call(t[n],n,t[n])===!1)return t}else for(i in t)if(e.call(t[i],i,t[i])===!1)return t;return t},C.grep=function(t,e){return A.call(t,e)},window.JSON&&(C.parseJSON=JSON.parse),C.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){q["[object "+e+"]"]=e.toLowerCase()}),C.fn={constructor:Z.Z,length:0,forEach:S.forEach,reduce:S.reduce,push:S.push,sort:S.sort,splice:S.splice,indexOf:S.indexOf,concat:function(){var t,e,n=[];for(t=0;t<arguments.length;t++)e=arguments[t],n[t]=Z.isZ(e)?e.toArray():e;return M.apply(Z.isZ(this)?this.toArray():this,n)},map:function(t){return C(C.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return C(P.apply(this,arguments))},ready:function(t){return X.test($.readyState)&&$.body?t(C):$.addEventListener("DOMContentLoaded",function(){t(C)},!1),this},get:function(t){return t===T?P.call(this):this[t>=0?t:t+this.length]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){null!=this.parentNode&&this.parentNode.removeChild(this)})},each:function(t){return S.every.call(this,function(e,n){return t.call(e,n,e)!==!1}),this},filter:function(t){return e(t)?this.not(this.not(t)):C(A.call(this,function(e){return Z.matches(e,t)}))},add:function(t,e){return C(k(this.concat(C(t,e))))},is:function(t){return this.length>0&&Z.matches(this[0],t)},not:function(t){var n=[];if(e(t)&&t.call!==T)this.each(function(e){t.call(this,e)||n.push(this)});else{var i="string"==typeof t?this.filter(t):r(t)&&e(t.item)?P.call(t):C(t);this.forEach(function(t){i.indexOf(t)<0&&n.push(t)})}return C(n)},has:function(t){return this.filter(function(){return a(t)?C.contains(this,t):C(this).find(t).size()})},eq:function(t){return t===-1?this.slice(t):this.slice(t,+t+1)},first:function(){var t=this[0];return t&&!a(t)?t:C(t)},last:function(){var t=this[this.length-1];return t&&!a(t)?t:C(t)},find:function(t){var e,n=this;return e=t?"object"==typeof t?C(t).filter(function(){var t=this;return S.some.call(n,function(e){return C.contains(e,t)})}):1==this.length?C(Z.qsa(this[0],t)):this.map(function(){return Z.qsa(this,t)}):C()},closest:function(t,e){var n=this[0],a=!1;for("object"==typeof t&&(a=C(t));n&&!(a?a.indexOf(n)>=0:Z.matches(n,t));)n=n!==e&&!i(n)&&n.parentNode;return C(n)},parents:function(t){for(var e=[],n=this;n.length>0;)n=C.map(n,function(t){if((t=t.parentNode)&&!i(t)&&e.indexOf(t)<0)return e.push(t),t});return v(e,t)},parent:function(t){return v(k(this.pluck("parentNode")),t)},children:function(t){return v(this.map(function(){return f(this)}),t)},contents:function(){return this.map(function(){return this.contentDocument||P.call(this.childNodes)})},siblings:function(t){return v(this.map(function(t,e){return A.call(f(e.parentNode),function(t){return t!==e})}),t)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(t){return C.map(this,function(e){return e[t]})},show:function(){return this.each(function(){"none"==this.style.display&&(this.style.display=""),"none"==getComputedStyle(this,"").getPropertyValue("display")&&(this.style.display=h(this.nodeName))})},replaceWith:function(t){return this.before(t).remove()},wrap:function(t){var n=e(t);if(this[0]&&!n)var i=C(t).get(0),a=i.parentNode||this.length>1;return this.each(function(e){C(this).wrapAll(n?t.call(this,e):a?i.cloneNode(!0):i)})},wrapAll:function(t){if(this[0]){C(this[0]).before(t=C(t));for(var e;(e=t.children()).length;)t=e.first();C(t).append(this)}return this},wrapInner:function(t){var n=e(t);return this.each(function(e){var i=C(this),a=i.contents(),o=n?t.call(this,e):t;a.length?a.wrapAll(o):i.append(o)})},unwrap:function(){return this.parent().each(function(){C(this).replaceWith(C(this).children())}),this},clone:function(){return this.map(function(){return this.cloneNode(!0)})},hide:function(){return this.css("display","none")},toggle:function(t){return this.each(function(){var e=C(this);(t===T?"none"==e.css("display"):t)?e.show():e.hide()})},prev:function(t){return C(this.pluck("previousElementSibling")).filter(t||"*")},next:function(t){return C(this.pluck("nextElementSibling")).filter(t||"*")},html:function(t){return 0 in arguments?this.each(function(e){var n=this.innerHTML;C(this).empty().append(b(this,t,e,n))}):0 in this?this[0].innerHTML:null},text:function(t){return 0 in arguments?this.each(function(e){var n=b(this,t,e,this.textContent);this.textContent=null==n?"":""+n}):0 in this?this[0].textContent:null},attr:function(t,e){var n;return"string"!=typeof t||1 in arguments?this.each(function(n){if(1===this.nodeType)if(a(t))for(_ in t)g(this,_,t[_]);else g(this,t,b(this,e,n,this.getAttribute(t)))}):this.length&&1===this[0].nodeType?!(n=this[0].getAttribute(t))&&t in this[0]?this[0][t]:n:T},removeAttr:function(t){return this.each(function(){1===this.nodeType&&t.split(" ").forEach(function(t){g(this,t)},this)})},prop:function(t,e){return t=G[t]||t,1 in arguments?this.each(function(n){this[t]=b(this,e,n,this[t])}):this[0]&&this[0][t]},data:function(t,e){var n="data-"+t.replace(F,"-$1").toLowerCase(),i=1 in arguments?this.attr(n,e):this.attr(n);return null!==i?w(i):T},val:function(t){return 0 in arguments?this.each(function(e){this.value=b(this,t,e,this.value)}):this[0]&&(this[0].multiple?C(this[0]).find("option").filter(function(){return this.selected}).pluck("value"):this[0].value)},offset:function(t){if(t)return this.each(function(e){var n=C(this),i=b(this,t,e,n.offset()),a=n.offsetParent().offset(),o={top:i.top-a.top,left:i.left-a.left};"static"==n.css("position")&&(o.position="relative"),n.css(o)});if(!this.length)return null;if(!C.contains($.documentElement,this[0]))return{top:0,left:0};var e=this[0].getBoundingClientRect();return{left:e.left+window.pageXOffset,top:e.top+window.pageYOffset,width:Math.round(e.width),height:Math.round(e.height)}},css:function(e,n){if(arguments.length<2){var i,a=this[0];if(!a)return;if(i=getComputedStyle(a,""),"string"==typeof e)return a.style[E(e)]||i.getPropertyValue(e);if(K(e)){var o={};return C.each(e,function(t,e){o[e]=a.style[E(e)]||i.getPropertyValue(e)}),o}}var r="";if("string"==t(e))n||0===n?r=c(e)+":"+d(e,n):this.each(function(){this.style.removeProperty(c(e))});else for(_ in e)e[_]||0===e[_]?r+=c(_)+":"+d(_,e[_])+";":this.each(function(){this.style.removeProperty(c(_))});return this.each(function(){this.style.cssText+=";"+r})},index:function(t){return t?this.indexOf(C(t)[0]):this.parent().children().indexOf(this[0])},hasClass:function(t){return!!t&&S.some.call(this,function(t){return this.test(y(t))},u(t))},addClass:function(t){return t?this.each(function(e){if("className"in this){D=[];var n=y(this),i=b(this,t,e,n);i.split(/\s+/g).forEach(function(t){C(this).hasClass(t)||D.push(t)},this),D.length&&y(this,n+(n?" ":"")+D.join(" "))}}):this},removeClass:function(t){return this.each(function(e){if("className"in this){if(t===T)return y(this,"");D=y(this),b(this,t,e,D).split(/\s+/g).forEach(function(t){D=D.replace(u(t)," ")}),y(this,D.trim())}})},toggleClass:function(t,e){return t?this.each(function(n){var i=C(this),a=b(this,t,n,y(this));a.split(/\s+/g).forEach(function(t){(e===T?!i.hasClass(t):e)?i.addClass(t):i.removeClass(t)})}):this},scrollTop:function(t){if(this.length){var e="scrollTop"in this[0];return t===T?e?this[0].scrollTop:this[0].pageYOffset:this.each(e?function(){this.scrollTop=t}:function(){this.scrollTo(this.scrollX,t)})}},scrollLeft:function(t){if(this.length){var e="scrollLeft"in this[0];return t===T?e?this[0].scrollLeft:this[0].pageXOffset:this.each(e?function(){this.scrollLeft=t}:function(){this.scrollTo(t,this.scrollY)})}},position:function(){if(this.length){var t=this[0],e=this.offsetParent(),n=this.offset(),i=I.test(e[0].nodeName)?{top:0,left:0}:e.offset();return n.top-=parseFloat(C(t).css("margin-top"))||0,n.left-=parseFloat(C(t).css("margin-left"))||0,i.top+=parseFloat(C(e[0]).css("border-top-width"))||0,i.left+=parseFloat(C(e[0]).css("border-left-width"))||0,{top:n.top-i.top,left:n.left-i.left}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||$.body;t&&!I.test(t.nodeName)&&"static"==C(t).css("position");)t=t.offsetParent;return t})}},C.fn.detach=C.fn.remove,["width","height"].forEach(function(t){var e=t.replace(/./,function(t){return t[0].toUpperCase()});C.fn[t]=function(a){var o,r=this[0];return a===T?n(r)?r["inner"+e]:i(r)?r.documentElement["scroll"+e]:(o=this.offset())&&o[t]:this.each(function(e){r=C(this),r.css(t,b(this,a,e,r[t]()))})}}),B.forEach(function(e,n){var i=n%2;C.fn[e]=function(){var e,a,o=C.map(arguments,function(n){return e=t(n),"object"==e||"array"==e||null==n?n:Z.fragment(n)}),r=this.length>1;return o.length<1?this:this.each(function(t,e){a=i?e:e.parentNode,e=0==n?e.nextSibling:1==n?e.firstChild:2==n?e:null;var s=C.contains($.documentElement,a);o.forEach(function(t){if(r)t=t.cloneNode(!0);else if(!a)return C(t).remove();a.insertBefore(t,e),s&&x(t,function(t){null==t.nodeName||"SCRIPT"!==t.nodeName.toUpperCase()||t.type&&"text/javascript"!==t.type||t.src||window.eval.call(window,t.innerHTML)})})})},C.fn[i?e+"To":"insert"+(n?"Before":"After")]=function(t){return C(t)[e](this),this}}),Z.Z.prototype=p.prototype=C.fn,Z.uniq=k,Z.deserializeValue=w,C.zepto=Z,C}();window.Zepto=Zepto,void 0===window.$&&(window.$=Zepto),function(t){function e(t){return t._zid||(t._zid=h++)}function n(t,n,o,r){if(n=i(n),n.ns)var s=a(n.ns);return(v[e(t)]||[]).filter(function(t){return t&&(!n.e||t.e==n.e)&&(!n.ns||s.test(t.ns))&&(!o||e(t.fn)===e(o))&&(!r||t.sel==r)})}function i(t){var e=(""+t).split(".");return{e:e[0],ns:e.slice(1).sort().join(" ")}}function a(t){return new RegExp("(?:^| )"+t.replace(" "," .* ?")+"(?: |$)")}function o(t,e){return t.del&&!g&&t.e in y||!!e}function r(t){return w[t]||g&&y[t]||t}function s(n,a,s,l,u,h,f){var p=e(n),m=v[p]||(v[p]=[]);a.split(/\s/).forEach(function(e){if("ready"==e)return t(document).ready(s);var a=i(e);a.fn=s,a.sel=u,a.e in w&&(s=function(e){var n=e.relatedTarget;if(!n||n!==this&&!t.contains(this,n))return a.fn.apply(this,arguments)}),a.del=h;var p=h||s;a.proxy=function(t){if(t=c(t),!t.isImmediatePropagationStopped()){t.data=l;var e=p.apply(n,t._args==d?[t]:[t].concat(t._args));return e===!1&&(t.preventDefault(),t.stopPropagation()),e}},a.i=m.length,m.push(a),"addEventListener"in n&&n.addEventListener(r(a.e),a.proxy,o(a,f))})}function l(t,i,a,s,l){var c=e(t);(i||"").split(/\s/).forEach(function(e){n(t,e,a,s).forEach(function(e){delete v[c][e.i],"removeEventListener"in t&&t.removeEventListener(r(e.e),e.proxy,o(e,l))})})}function c(e,n){return!n&&e.isDefaultPrevented||(n||(n=e),t.each(C,function(t,i){var a=n[t];e[t]=function(){return this[i]=x,a&&a.apply(n,arguments)},e[i]=T}),(n.defaultPrevented!==d?n.defaultPrevented:"returnValue"in n?n.returnValue===!1:n.getPreventDefault&&n.getPreventDefault())&&(e.isDefaultPrevented=x)),e}function u(t){var e,n={originalEvent:t};for(e in t)_.test(e)||t[e]===d||(n[e]=t[e]);return c(n,t)}var d,h=1,f=Array.prototype.slice,p=t.isFunction,m=function(t){return"string"==typeof t},v={},b={},g="onfocusin"in window,y={focus:"focusin",blur:"focusout"},w={mouseenter:"mouseover",mouseleave:"mouseout"};b.click=b.mousedown=b.mouseup=b.mousemove="MouseEvents",t.event={add:s,remove:l},t.proxy=function(n,i){var a=2 in arguments&&f.call(arguments,2);if(p(n)){var o=function(){return n.apply(i,a?a.concat(f.call(arguments)):arguments)};return o._zid=e(n),o}if(m(i))return a?(a.unshift(n[i],n),t.proxy.apply(null,a)):t.proxy(n[i],n);throw new TypeError("expected function")},t.fn.bind=function(t,e,n){return this.on(t,e,n)},t.fn.unbind=function(t,e){return this.off(t,e)},t.fn.one=function(t,e,n,i){return this.on(t,e,n,i,1)};var x=function(){return!0},T=function(){return!1},_=/^([A-Z]|returnValue$|layer[XY]$)/,C={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};t.fn.delegate=function(t,e,n){return this.on(e,t,n)},t.fn.undelegate=function(t,e,n){return this.off(e,t,n)},t.fn.live=function(e,n){return t(document.body).delegate(this.selector,e,n),this},t.fn.die=function(e,n){return t(document.body).undelegate(this.selector,e,n),this},t.fn.on=function(e,n,i,a,o){var r,c,h=this;return e&&!m(e)?(t.each(e,function(t,e){h.on(t,n,i,e,o)}),h):(m(n)||p(a)||a===!1||(a=i,i=n,n=d),a!==d&&i!==!1||(a=i,i=d),a===!1&&(a=T),h.each(function(d,h){o&&(r=function(t){return l(h,t.type,a),a.apply(this,arguments)}),n&&(c=function(e){var i,o=t(e.target).closest(n,h).get(0);if(o&&o!==h)return i=t.extend(u(e),{currentTarget:o,liveFired:h}),(r||a).apply(o,[i].concat(f.call(arguments,1)))}),s(h,e,a,i,n,c||r)}))},t.fn.off=function(e,n,i){var a=this;return e&&!m(e)?(t.each(e,function(t,e){a.off(t,n,e)}),a):(m(n)||p(i)||i===!1||(i=n,n=d),i===!1&&(i=T),a.each(function(){l(this,e,i,n)}))},t.fn.trigger=function(e,n){return e=m(e)||t.isPlainObject(e)?t.Event(e):c(e),e._args=n,this.each(function(){e.type in y&&"function"==typeof this[e.type]?this[e.type]():"dispatchEvent"in this?this.dispatchEvent(e):t(this).triggerHandler(e,n)})},t.fn.triggerHandler=function(e,i){var a,o;return this.each(function(r,s){a=u(m(e)?t.Event(e):e),a._args=i,a.target=s,t.each(n(s,e.type||e),function(t,e){if(o=e.proxy(a),a.isImmediatePropagationStopped())return!1})}),o},"focusin focusout focus blur load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select keydown keypress keyup error".split(" ").forEach(function(e){t.fn[e]=function(t){return 0 in arguments?this.bind(e,t):this.trigger(e)}}),t.Event=function(t,e){m(t)||(e=t,t=e.type);var n=document.createEvent(b[t]||"Events"),i=!0;if(e)for(var a in e)"bubbles"==a?i=!!e[a]:n[a]=e[a];return n.initEvent(t,i,!0),c(n)}}(Zepto),function(t){function e(e,n,i){var a=t.Event(n);return t(e).trigger(a,i),!a.isDefaultPrevented()}function n(t,n,i,a){if(t.global)return e(n||g,i,a)}function i(e){e.global&&0===t.active++&&n(e,null,"ajaxStart")}function a(e){e.global&&!--t.active&&n(e,null,"ajaxStop")}function o(t,e){var i=e.context;return e.beforeSend.call(i,t,e)!==!1&&n(e,i,"ajaxBeforeSend",[t,e])!==!1&&void n(e,i,"ajaxSend",[t,e])}function r(t,e,i,a){var o=i.context,r="success";i.success.call(o,t,r,e),a&&a.resolveWith(o,[t,r,e]),n(i,o,"ajaxSuccess",[e,i,t]),l(r,e,i)}function s(t,e,i,a,o){var r=a.context;a.error.call(r,i,e,t),o&&o.rejectWith(r,[i,e,t]),n(a,r,"ajaxError",[i,a,t||e]),l(e,i,a)}function l(t,e,i){var o=i.context;i.complete.call(o,e,t),n(i,o,"ajaxComplete",[e,i]),a(i)}function c(){}function u(t){return t&&(t=t.split(";",2)[0]),t&&(t==_?"html":t==T?"json":w.test(t)?"script":x.test(t)&&"xml")||"text"}function d(t,e){return""==e?t:(t+"&"+e).replace(/[&?]{1,2}/,"?")}function h(e){e.processData&&e.data&&"string"!=t.type(e.data)&&(e.data=t.param(e.data,e.traditional)),!e.data||e.type&&"GET"!==e.type||(e.url=d(e.url,e.data),e.data=void 0)}function f(e,n,i,a){return t.isFunction(n)&&(a=i,i=n,n=void 0),t.isFunction(i)||(a=i,i=void 0),{url:e,data:n,success:i,dataType:a}}function p(e,n,i,a){var o,r=t.isArray(n),s=t.isPlainObject(n);t.each(n,function(n,l){o=t.type(l),a&&(n=i?a:a+"["+(s||"object"==o||"array"==o?n:"")+"]"),!a&&r?e.add(l.name,l.value):"array"==o||!i&&"object"==o?p(e,l,i,n):e.add(n,l)})}var m,v,b=0,g=window.document,y=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,w=/^(?:text|application)\/javascript/i,x=/^(?:text|application)\/xml/i,T="application/json, text/javascript",_="text/html",C=/^\s*$/,D=g.createElement("a");D.href=window.location.href,t.active=0,t.ajaxJSONP=function(e,n){if(!("type"in e))return t.ajax(e);var i,a,l=e.jsonpCallback,c=(t.isFunction(l)?l():l)||"jsonp"+ ++b,u=g.createElement("script"),d=window[c],h=function(e){t(u).triggerHandler("error",e||"abort")},f={abort:h};return n&&n.promise(f),t(u).on("load error",function(o,l){clearTimeout(a),t(u).off().remove(),"error"!=o.type&&i?r(i[0],f,e,n):s(null,l||"error",f,e,n),window[c]=d,i&&t.isFunction(d)&&d(i[0]),d=i=void 0}),o(f,e)===!1?(h("abort"),f):(window[c]=function(){i=arguments},u.src=e.url.replace(/\?(.+)=\?/,"?$1="+c),g.head.appendChild(u),e.timeout>0&&(a=setTimeout(function(){h("timeout")},e.timeout)),f)},t.ajaxSettings={type:"GET",beforeSend:c,success:c,error:c,complete:c,context:null,global:!0,xhr:function(){return new window.XMLHttpRequest},accepts:{script:"text/javascript, application/javascript, application/x-javascript",json:T,xml:"application/xml, text/xml",html:_,text:"text/plain"},crossDomain:!1,timeout:0,processData:!0,cache:!0},t.ajax=function(e){var n,a,l=t.extend({},e||{}),f=t.Deferred&&t.Deferred();for(m in t.ajaxSettings)void 0===l[m]&&(l[m]=t.ajaxSettings[m]);i(l),l.crossDomain||(n=g.createElement("a"),n.href=l.url,n.href=n.href,l.crossDomain=D.protocol+"//"+D.host!=n.protocol+"//"+n.host),l.url||(l.url=window.location.toString()),(a=l.url.indexOf("#"))>-1&&(l.url=l.url.slice(0,a)),l.type=l.type.toUpperCase(),h(l);var p=l.dataType,b=/\?.+=\?/.test(l.url);if(b&&(p="jsonp"),l.cache!==!1&&(e&&e.cache===!0||"script"!=p&&"jsonp"!=p)||(l.url=d(l.url,"_="+Date.now())),"jsonp"==p)return b||(l.url=d(l.url,l.jsonp?l.jsonp+"=?":l.jsonp===!1?"":"callback=?")),t.ajaxJSONP(l,f);var y,w=l.accepts[p],x={},T=function(t,e){x[t.toLowerCase()]=[t,e]},_=/^([\w-]+:)\/\//.test(l.url)?RegExp.$1:window.location.protocol,E=l.xhr(),k=E.setRequestHeader;if(f&&f.promise(E),l.crossDomain||T("X-Requested-With","XMLHttpRequest"),T("Accept",w||"*/*"),(w=l.mimeType||w)&&(w.indexOf(",")>-1&&(w=w.split(",",2)[0]),E.overrideMimeType&&E.overrideMimeType(w)),(l.contentType||l.contentType!==!1&&l.data&&"GET"!==l.type)&&T("Content-Type",l.contentType||"application/x-www-form-urlencoded; charset=UTF-8"),l.headers)for(v in l.headers)T(v,l.headers[v]);if(E.setRequestHeader=T,E.onreadystatechange=function(){if(4==E.readyState){E.onreadystatechange=c,clearTimeout(y);var e,n=!1;if(E.status>=200&&E.status<300||304==E.status||0==E.status&&"file:"==_){p=p||u(l.mimeType||E.getResponseHeader("content-type")),e=E.responseText;try{"script"==p?(0,eval)(e):"xml"==p?e=E.responseXML:"json"==p&&(e=C.test(e)?null:t.parseJSON(e))}catch(t){n=t}n?s(n,"parsererror",E,l,f):r(e,E,l,f)}else s(E.statusText||null,E.status?"error":"abort",E,l,f)}},o(E,l)===!1)return E.abort(),s(null,"abort",E,l,f),E;if(l.xhrFields)for(v in l.xhrFields)E[v]=l.xhrFields[v];var S=!("async"in l)||l.async;E.open(l.type,l.url,S,l.username,l.password);for(v in x)k.apply(E,x[v]);return l.timeout>0&&(y=setTimeout(function(){E.onreadystatechange=c,E.abort(),s(null,"timeout",E,l,f)},l.timeout)),E.send(l.data?l.data:null),E},t.get=function(){return t.ajax(f.apply(null,arguments))},t.post=function(){var e=f.apply(null,arguments);return e.type="POST",t.ajax(e)},t.getJSON=function(){var e=f.apply(null,arguments);return e.dataType="json",t.ajax(e)},t.fn.load=function(e,n,i){if(!this.length)return this;var a,o=this,r=e.split(/\s/),s=f(e,n,i),l=s.success;return r.length>1&&(s.url=r[0],a=r[1]),s.success=function(e){o.html(a?t("<div>").html(e.replace(y,"")).find(a):e),l&&l.apply(o,arguments)},t.ajax(s),this};var E=encodeURIComponent;t.param=function(e,n){var i=[];return i.add=function(e,n){t.isFunction(n)&&(n=n()),null==n&&(n=""),this.push(E(e)+"="+E(n))},p(i,e,n),i.join("&").replace(/%20/g,"+")}}(Zepto),function(t){t.fn.serializeArray=function(){var e,n,i=[],a=function(t){return t.forEach?t.forEach(a):void i.push({name:e,value:t})};return this[0]&&t.each(this[0].elements,function(i,o){n=o.type,e=o.name,e&&"fieldset"!=o.nodeName.toLowerCase()&&!o.disabled&&"submit"!=n&&"reset"!=n&&"button"!=n&&"file"!=n&&("radio"!=n&&"checkbox"!=n||o.checked)&&a(t(o).val())}),i},t.fn.serialize=function(){var t=[];return this.serializeArray().forEach(function(e){t.push(encodeURIComponent(e.name)+"="+encodeURIComponent(e.value))}),t.join("&")},t.fn.submit=function(e){if(0 in arguments)this.bind("submit",e);else if(this.length){var n=t.Event("submit");this.eq(0).trigger(n),n.isDefaultPrevented()||this.get(0).submit()}return this}}(Zepto),function(){try{getComputedStyle(void 0)}catch(e){var t=getComputedStyle;window.getComputedStyle=function(e){try{return t(e)}catch(t){return null}}}}(),function(t){function e(t,e){var n=this.os={},i=this.browser={},a=t.match(/Web[kK]it[\/]{0,1}([\d.]+)/),o=t.match(/(Android);?[\s\/]+([\d.]+)?/),r=!!t.match(/\(Macintosh\; Intel /),s=t.match(/(iPad).*OS\s([\d_]+)/),l=t.match(/(iPod)(.*OS\s([\d_]+))?/),c=!s&&t.match(/(iPhone\sOS)\s([\d_]+)/),u=t.match(/(webOS|hpwOS)[\s\/]([\d.]+)/),d=/Win\d{2}|Windows/.test(e),h=t.match(/Windows Phone ([\d.]+)/),f=u&&t.match(/TouchPad/),p=t.match(/Kindle\/([\d.]+)/),m=t.match(/Silk\/([\d._]+)/),v=t.match(/(BlackBerry).*Version\/([\d.]+)/),b=t.match(/(BB10).*Version\/([\d.]+)/),g=t.match(/(RIM\sTablet\sOS)\s([\d.]+)/),y=t.match(/PlayBook/),w=t.match(/Chrome\/([\d.]+)/)||t.match(/CriOS\/([\d.]+)/),x=t.match(/Firefox\/([\d.]+)/),T=t.match(/\((?:Mobile|Tablet); rv:([\d.]+)\).*Firefox\/[\d.]+/),_=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/[\d](?=[^\?]+).*rv:([0-9.].)/),C=!w&&t.match(/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/),D=C||t.match(/Version\/([\d.]+)([^S](Safari)|[^M]*(Mobile)[^S]*(Safari))/);(i.webkit=!!a)&&(i.version=a[1]),o&&(n.android=!0,n.version=o[2]),c&&!l&&(n.ios=n.iphone=!0,n.version=c[2].replace(/_/g,".")),s&&(n.ios=n.ipad=!0,n.version=s[2].replace(/_/g,".")),l&&(n.ios=n.ipod=!0,n.version=l[3]?l[3].replace(/_/g,"."):null),h&&(n.wp=!0,n.version=h[1]),u&&(n.webos=!0,n.version=u[2]),f&&(n.touchpad=!0),v&&(n.blackberry=!0,n.version=v[2]),b&&(n.bb10=!0,n.version=b[2]),g&&(n.rimtabletos=!0,n.version=g[2]),y&&(i.playbook=!0),p&&(n.kindle=!0,n.version=p[1]),m&&(i.silk=!0,i.version=m[1]),!m&&n.android&&t.match(/Kindle Fire/)&&(i.silk=!0),w&&(i.chrome=!0,i.version=w[1]),x&&(i.firefox=!0,i.version=x[1]),T&&(n.firefoxos=!0,n.version=T[1]),_&&(i.ie=!0,i.version=_[1]),D&&(r||n.ios||d)&&(i.safari=!0,n.ios||(i.version=D[1])),C&&(i.webview=!0),n.tablet=!!(s||y||o&&!t.match(/Mobile/)||x&&t.match(/Tablet/)||_&&!t.match(/Phone/)&&t.match(/Touch/)),n.phone=!(n.tablet||n.ipod||!(o||c||u||v||b||w&&t.match(/Android/)||w&&t.match(/CriOS\/([\d.]+)/)||x&&t.match(/Mobile/)||_&&t.match(/Touch/)))}e.call(t,navigator.userAgent,navigator.platform),t.__detect=e}(Zepto),function(t,e){function n(t){return t.replace(/([a-z])([A-Z])/,"$1-$2").toLowerCase()}function i(t){return a?a+t:t.toLowerCase()}var a,o,r,s,l,c,u,d,h,f,p="",m={Webkit:"webkit",Moz:"",O:"o"},v=document.createElement("div"),b=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i,g={};t.each(m,function(t,n){if(v.style[t+"TransitionProperty"]!==e)return p="-"+t.toLowerCase()+"-",a=n,!1}),o=p+"transform",g[r=p+"transition-property"]=g[s=p+"transition-duration"]=g[c=p+"transition-delay"]=g[l=p+"transition-timing-function"]=g[u=p+"animation-name"]=g[d=p+"animation-duration"]=g[f=p+"animation-delay"]=g[h=p+"animation-timing-function"]="",t.fx={off:a===e&&v.style.transitionProperty===e,speeds:{_default:400,fast:200,slow:600},cssPrefix:p,transitionEnd:i("TransitionEnd"),animationEnd:i("AnimationEnd")},t.fn.animate=function(n,i,a,o,r){return t.isFunction(i)&&(o=i,a=e,i=e),t.isFunction(a)&&(o=a,a=e),t.isPlainObject(i)&&(a=i.easing,o=i.complete,r=i.delay,i=i.duration),i&&(i=("number"==typeof i?i:t.fx.speeds[i]||t.fx.speeds._default)/1e3),r&&(r=parseFloat(r)/1e3),this.anim(n,i,a,o,r)},t.fn.anim=function(i,a,p,m,v){var y,w,x,T={},_="",C=this,D=t.fx.transitionEnd,E=!1;if(a===e&&(a=t.fx.speeds._default/1e3),v===e&&(v=0),t.fx.off&&(a=0),"string"==typeof i)T[u]=i,T[d]=a+"s",T[f]=v+"s",T[h]=p||"linear",D=t.fx.animationEnd;else{w=[];for(y in i)b.test(y)?_+=y+"("+i[y]+") ":(T[y]=i[y],w.push(n(y)));_&&(T[o]=_,w.push(o)),a>0&&"object"==typeof i&&(T[r]=w.join(", "),T[s]=a+"s",T[c]=v+"s",T[l]=p||"linear")}return x=function(e){if("undefined"!=typeof e){if(e.target!==e.currentTarget)return;t(e.target).unbind(D,x)}else t(this).unbind(D,x);E=!0,t(this).css(g),m&&m.call(this)},a>0&&(this.bind(D,x),setTimeout(function(){E||x.call(C)},1e3*(a+v)+25)),this.size()&&this.get(0).clientLeft,this.css(T),a<=0&&setTimeout(function(){C.each(function(){x.call(this)})},0),this},v=null}(Zepto),function(t,e){function n(n,i,a,o,r){"function"!=typeof i||r||(r=i,i=e);var s={opacity:a};return o&&(s.scale=o,n.css(t.fx.cssPrefix+"transform-origin","0 0")),n.animate(s,i,null,r)}function i(e,i,a,o){return n(e,i,0,a,function(){r.call(t(this)),o&&o.call(this)})}var a=window.document,o=(a.documentElement,t.fn.show),r=t.fn.hide,s=t.fn.toggle;t.fn.show=function(t,i){return o.call(this),t===e?t=0:this.css("opacity",0),n(this,t,1,"1,1",i)},t.fn.hide=function(t,n){return t===e?r.call(this):i(this,t,"0,0",n)},t.fn.toggle=function(n,i){return n===e||"boolean"==typeof n?s.call(this,n):this.each(function(){var e=t(this);e["none"==e.css("display")?"show":"hide"](n,i)})},t.fn.fadeTo=function(t,e,i){return n(this,t,e,null,i)},t.fn.fadeIn=function(t,e){var n=this.css("opacity");return n>0?this.css("opacity",0):n=1,o.call(this).fadeTo(t,n,e)},t.fn.fadeOut=function(t,e){return i(this,t,null,e)},t.fn.fadeToggle=function(e,n){return this.each(function(){var i=t(this);i[0==i.css("opacity")||"none"==i.css("display")?"fadeIn":"fadeOut"](e,n)})}}(Zepto),function(t){function e(e,i){var l=e[s],c=l&&a[l];if(void 0===i)return c||n(e);if(c){if(i in c)return c[i];var u=r(i);if(u in c)return c[u]}return o.call(t(e),i)}function n(e,n,o){var l=e[s]||(e[s]=++t.uuid),c=a[l]||(a[l]=i(e));return void 0!==n&&(c[r(n)]=o),c}function i(e){var n={};return t.each(e.attributes||l,function(e,i){0==i.name.indexOf("data-")&&(n[r(i.name.replace("data-",""))]=t.zepto.deserializeValue(i.value))}),n}var a={},o=t.fn.data,r=t.camelCase,s=t.expando="Zepto"+ +new Date,l=[];t.fn.data=function(i,a){return void 0===a?t.isPlainObject(i)?this.each(function(e,a){t.each(i,function(t,e){n(a,t,e)})}):0 in this?e(this[0],i):void 0:this.each(function(){n(this,i,a)})},t.fn.removeData=function(e){return"string"==typeof e&&(e=e.split(/\s+/)),this.each(function(){var n=this[s],i=n&&a[n];i&&t.each(e||i,function(t){delete i[e?r(this):t]})})},["remove","empty"].forEach(function(e){var n=t.fn[e];t.fn[e]=function(){var t=this.find("*");return"remove"===e&&(t=t.add(this)),t.removeData(),n.call(this)}})}(Zepto),function(t){function e(e){return e=t(e),!(!e.width()&&!e.height())&&"none"!==e.css("display")}function n(t,e){t=t.replace(/=#\]/g,'="#"]');var n,i,a=s.exec(t);if(a&&a[2]in r&&(n=r[a[2]],i=a[3],t=a[1],i)){var o=Number(i);i=isNaN(o)?i.replace(/^["']|["']$/g,""):o}return e(t,n,i)}var i=t.zepto,a=i.qsa,o=i.matches,r=t.expr[":"]={visible:function(){if(e(this))return this},hidden:function(){if(!e(this))return this},selected:function(){if(this.selected)return this;
},checked:function(){if(this.checked)return this},parent:function(){return this.parentNode},first:function(t){if(0===t)return this},last:function(t,e){if(t===e.length-1)return this},eq:function(t,e,n){if(t===n)return this},contains:function(e,n,i){if(t(this).text().indexOf(i)>-1)return this},has:function(t,e,n){if(i.qsa(this,n).length)return this}},s=new RegExp("(.*):(\\w+)(?:\\(([^)]+)\\))?$\\s*"),l=/^\s*>/,c="Zepto"+ +new Date;i.qsa=function(e,o){return n(o,function(n,r,s){try{var u;!n&&r?n="*":l.test(n)&&(u=t(e).addClass(c),n="."+c+" "+n);var d=a(e,n)}catch(t){throw console.error("error performing selector: %o",o),t}finally{u&&u.removeClass(c)}return r?i.uniq(t.map(d,function(t,e){return r.call(t,e,d,s)})):d})},i.matches=function(t,e){return n(e,function(e,n,i){return(!e||o(t,e))&&(!n||n.call(t,null,i)===t)})}}(Zepto),function(t){t.fn.end=function(){return this.prevObject||t()},t.fn.andSelf=function(){return this.add(this.prevObject||t())},"filter,add,not,eq,first,last,find,closest,parents,parent,children,siblings".split(",").forEach(function(e){var n=t.fn[e];t.fn[e]=function(){var t=n.apply(this,arguments);return t.prevObject=this,t}})}(Zepto),function(t){["width","height"].forEach(function(e){t.fn[e]=function(n){var i,a=document.body,o=document.documentElement,r=e.replace(/./,function(t){return t[0].toUpperCase()});return void 0===n?this[0]==window?o["client"+r]:this[0]==document?Math.max(a["scroll"+r],a["offset"+r],o["client"+r],o["scroll"+r],o["offset"+r]):(i=this.offset())&&i[e]:this.each(function(i){t(this).css(e,n)})}}),["width","height"].forEach(function(e){var n=e.replace(/./,function(t){return t[0].toUpperCase()});t.fn["outer"+n]=function(t){var i=this;if(i){var a=i[0]["offset"+n],o={width:["left","right"],height:["top","bottom"]};return o[e].forEach(function(e){t&&(a+=parseInt(i.css("margin-"+e),10))}),a}return null}}),["width","height"].forEach(function(e){var n=e.replace(/./,function(t){return t[0].toUpperCase()});t.fn["inner"+n]=function(){var t=this;if(t[0]["inner"+n])return t[0]["inner"+n];var i=t[0]["offset"+n],a={width:["left","right"],height:["top","bottom"]};return a[e].forEach(function(e){i-=parseInt(t.css("border-"+e+"-width"),10)}),i}}),["Left","Top"].forEach(function(e,n){function i(t){return t&&"object"==typeof t&&"setInterval"in t}function a(t){return i(t)?t:9===t.nodeType&&(t.defaultView||t.parentWindow)}var o="scroll"+e;t.fn[o]=function(e){var i,r;return void 0===e?(i=this[0])?(r=a(i),r?"pageXOffset"in r?r[n?"pageYOffset":"pageXOffset"]:r.document.documentElement[o]||r.document.body[o]:i[o]):null:void this.each(function(){if(r=a(this)){var i=n?t(r).scrollLeft():e,s=n?e:t(r).scrollTop();r.scrollTo(i,s)}else this[o]=e})}}),t.fn.prevUntil=function(e){for(var n=this,i=[];n.length&&!t(n).filter(e).length;)i.push(n[0]),n=n.prev();return t(i)},t.fn.nextUntil=function(e){for(var n=this,i=[];n.length&&!n.filter(e).length;)i.push(n[0]),n=n.next();return t(i)},t._extend=t.extend,t.extend=function(){return arguments[0]=arguments[0]||{},t._extend.apply(this,arguments)}}(Zepto);var JQUERY=window.jQuery||window.Zepto;if(!JQUERY)throw new Error("jQuery or Zepto is needed!");window.console&&console.log(JQUERY.fn.jquery?"jQuery-"+jQuery.fn.jquery:"Zepto"),Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};/(y+)/i.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(var n in e)new RegExp("("+n+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[n]:("00"+e[n]).substr((""+e[n]).length)));return t},function($){"use strict";var arm=function(t,e){return new arm.pt.init(t,e)},version="1.1",index=0,_console={log:$.noop,error:$.noop,info:$.noop,warn:$.noop,debug:$.noop,group:$.noop,groupEnd:$.noop},config={base:"./",charset:"utf-8",debug:!1};arm.pt=arm.prototype={arm:version,constructor:arm};var ArmClass=arm.pt.init=function(t,e){return this.index=++index,this.$=$(t,e),this};ArmClass.prototype=arm.pt;var reg=arm.R={trim:/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,http:/^http(s)?:\/\//i,_http:/^(http(s)?:\/\/|\/|file:\/\/)/i};$.extend(!0,arm,{data:{},config:function(t,e){if("string"===$.type(t)||"number"===$.type(t)){if("undefined"===$.type(e))return config[t];config[t]=e}return $.isPlainObject(t)&&(t.modules!==config.modules&&arm.use(t.modules),$.extend(!0,config,t)),config},console:function(t){return window.console&&(config.debug||t)?window.console:_console},path:function(){var t=document.scripts,e=t[t.length-1],n=e.src;if(!e.getAttribute("merge"))return n.substring(0,n.lastIndexOf("/")+1)}(),srcUrl:function(){var t=document.scripts,e=t[t.length-1],n=e.src;if(!e.getAttribute("merge"))return n}(),refer:window.location.href.substring(0,window.location.href.lastIndexOf("/")+1),isMobile:$.os&&($.os.tablet||$.os.phone)||/Android|Windows Phone|webOS|iPhone|iPod|iPad|BlackBerry/i.test(navigator.userAgent),rAF:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)},support:{touch:window.Modernizr&&Modernizr.touch===!0||function(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)}(),transition:function(){var t=document.createElement("arm"),e={transition:"transitionend",WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend"};for(var n in e)if(void 0!==t.style[n])return{end:e[n]};return!1}()},is_lessIE:function(t){if(/Microsoft Internet Explorer/i.test(navigator.appName)){var e=navigator.appVersion.match(/msie(\s+)?(\d)/i);if(e&&Number(e[2])<t)return!0}return!1},prevent:function(t){return!!t&&void(t.preventDefault?t.preventDefault():t.returnValue=!1)},JSONstring:function(t){return window.JSON?JSON.stringify(t):"JSON is not support!"},objectJoin:function(t,e){var n=e||"";if("string"==typeof t)return t;if($.isArray(t))return t.join(n);if($.isPlainObject(t)){var i=[];return $.each(t,function(t,e){i.push(e)}),i.join(n)}return String(t)},inLen:function(t,e){return e>t-1&&(e=t-1),e<0&&(e=0),e},inObject:function(t,e){var n=-1;return $.each(t,function(t,i){if(e(i,t))return n=t,!1}),n},inArrayObj:function(t,e,n){var i=-1,a=$.isArray(n)?n.slice(0):[];if(!a.length)return i;for(var o=0;o<a.length;o++)if(a[o][t]===e)return o;return i},inObjKey:function(t,e,n){var i=!1;if(!t||"object"!=typeof e)return i;var a;for(a in e)if(e[a]===t&&("undefined"==typeof n||n===a)){i=!0;break}return i},sortAarry:function(t,e,n){var i=t.length;if(!t.length)return-1;var a=n;if("uper"===n&&(a=e+1),"downer"===n&&(a=e-1),"top"===n&&(a=i-1),"bottom"===n&&(a=0),a=arm.inLen(i,a),a===e)return a;var o=t.splice(arm.inLen(i,e),1);return t.splice(a,0,o[0]),a},getImgNaturalSize:function(t,e){var n,i;if(t.naturalWidth)n=t.naturalWidth,i=t.naturalHeight,e(n,i);else{var a=new Image;a.src=t.src,a.onload=function(){e(a.width,a.height)}}return[n,i]},getUrlParam:function(t,e,n){var i=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),n=n||"boolean"==typeof e&&e,e=A.R.http.test(e)?e:window.location.href,a=e.substr(e.indexOf(n?"#":"?")+1).match(i);return null!=a?unescape(a[2]):null},updateUrlParam:function(url,name,value){var r=url;if(null!=r&&"undefined"!=r&&""!=r){value=encodeURIComponent(value);var reg=new RegExp("(^|)"+name+"=([^&]*)(|$)"),tmp=name+"="+value;r=null!=url.match(reg)?url.replace(eval(reg),tmp):url.match("[?]")?url+"&"+tmp:url+"?"+tmp}return r},toHump:function(t,e){var n=$.trim(t).split(/\s+/),i="";return $.each(n,function(t,e){i+=arm.utils.firstUpper(e)}),e||(i=arm.utils.firstLower(i)),i},utils:function(){function t(t){if(t in n)return t;for(var e,i=["webkit","Moz","ms","O"],a=0,o=i.length;a<o;a++)if(e=i[a]+t.charAt(0).toUpperCase()+t.substr(1),e in n)return e;return!1}var e={},n=document.createElement("div").style;e.getTime=Date.now||function(){return(new Date).getTime()},e.generateGUID=function(t){var e=t+"-"||"arm-";do e+=Math.random().toString(36).substring(2,7);while(document.getElementById(e));return e},e.firstUpper=function(t){return t.replace(/^\w|\s\w/g,function(t){return t.toUpperCase()})},e.firstLower=function(t){return t.replace(/^\w|\s\w/g,function(t){return t.toLowerCase()})},e.getJSUrl=function(){for(var t=document.scripts,e=t.length-1;e>=0;e--)if(t[e].src&&!t[e].getAttribute("merge"))return t[e].src;return location.href},e.getClassFn=function(t){var e=new RegExp(t,"ig");return function(t,n){var i=n.match(e);return i?i.join(" "):""}},e.error=function(t,e,n){return n=(n?n+" ":"")+"Error：",arm.console().error(n+(t||"")+"\n"+(e||"")),t},e.extend=function(t,e){for(var n in e)t[n]=e[n]},e.addEvent=function(t,e,n,i){t.addEventListener(e,n,!!i)},e.removeEvent=function(t,e,n,i){t.removeEventListener(e,n,!!i)},e.prefixPointerEvent=function(t){return window.MSPointerEvent?"MSPointer"+t.charAt(9).toUpperCase()+t.substr(10):t},e.momentum=function(t,e,n,i,a,o){var r,s,l=t-e,c=Math.abs(l)/n;return o=void 0===o?6e-4:o,r=t+c*c/(2*o)*(l<0?-1:1),s=c/o,r<i?(r=a?i-a/2.5*(c/8):i,l=Math.abs(r-t),s=l/c):r>0&&(r=a?a/2.5*(c/8):0,l=Math.abs(t)+r,s=l/c),{destination:Math.round(r),duration:s}};var i=t("transform");return e.prefixStyle=t,e.extend(e,{hasTransform:i!==!1,hasPerspective:t("perspective")in n,hasTouch:"ontouchstart"in window,hasPointer:window.PointerEvent||window.MSPointerEvent,hasTransition:t("transition")in n}),e.isBadAndroid=/Android /.test(window.navigator.appVersion)&&!/Chrome\/\d/.test(window.navigator.appVersion),e.extend(e.style={},{transform:i,transitionTimingFunction:t("transitionTimingFunction"),transitionDuration:t("transitionDuration"),transitionDelay:t("transitionDelay"),transformOrigin:t("transformOrigin")}),e.hasClass=function(t,e){var n=new RegExp("(^|\\s)"+e+"(\\s|$)");return n.test(t.className)},e.addClass=function(t,n){if(!e.hasClass(t,n)){var i=t.className.split(" ");i.push(n),t.className=i.join(" ")}},e.removeClass=function(t,n){if(e.hasClass(t,n)){var i=new RegExp("(^|\\s)"+n+"(\\s|$)","g");t.className=t.className.replace(i," ")}},e.offset=function(t){for(var e=-t.offsetLeft,n=-t.offsetTop;t=t.offsetParent;)e-=t.offsetLeft,n-=t.offsetTop;return{left:e,top:n}},e.preventDefaultException=function(t,e){for(var n in e)if(e[n].test(t[n]))return!0;return!1},e.extend(e.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),e.extend(e.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(t){return Math.sqrt(1- --t*t)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(t){var e=4;return(t-=1)*t*((e+1)*t+e)+1}},bounce:{style:"",fn:function(t){return(t/=1)<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}},elastic:{style:"",fn:function(t){var e=.22,n=.4;return 0===t?0:1==t?1:n*Math.pow(2,-10*t)*Math.sin((t-e/4)*(2*Math.PI)/e)+1}}}),e.tap=function(t,e){var n=document.createEvent("Event");n.initEvent(e,!0,!0),n.pageX=t.pageX,n.pageY=t.pageY,t.target.dispatchEvent(n)},e.dispatchEvent=function(t,e,n){var i=document.createEvent("MouseEvents");i.initMouseEvent(t,!0,!0,e.view,1,n.screenX,n.screenY,n.clientX,n.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,0,null),i._constructed=!0,n.dispatchEvent(i)},e.click=function(t){var n=t.target;/(SELECT|INPUT|TEXTAREA)/i.test(n.tagName)||e.dispatchEvent("click",t,n)},e.realPath=function(t,e){var t=t||"";for(t=t.replace(/^\.\//,"");t.match(/^\.\.\//);)t=t.replace(/\.\.\//,""),e=e.replace(/[^\/]+\/$/,"");return e+t},e.realBase=function(){var t=arm.config("base");return t="string"==typeof t?t:arm.path,arm.R._http.test(t)?t:e.realPath(t,arm.path)},e.moduleUrl=function(t){var n=e.realBase(),i=e.realPath(t,n);return i},e.moduleType=function(t){var e=t.substring(t.lastIndexOf("/")+1),n=e.substr(e.lastIndexOf(".")),i=/^\.css(\W)?/.test(n),a=!/[\?|&]/.test(t)&&!/^\.(js|css)(\W)?/.test(n);return{name:e.replace(/[^a-zA-Z0-9\-]/g,"-"),isCSS:i,isJS:!i,noCSSJS:a}},e}()});var _arm=window.arm,_A=window.A;window.arm=window.A=arm,arm.noConflict=function(t){return window.A=_A,t&&_arm===arm&&(window.arm=_arm),arm._isConflict=!0,arm};var varArm=arm.getUrlParam("var",arm.srcUrl);return varArm&&/^[a-zA-Z|_]+/.test(varArm)&&!window[varArm]&&(arm.pt._var=varArm,window[varArm]=arm),"function"==typeof define&&define("arm",function(){return arm}),arm}(JQUERY),function(t,e,n){e.fn.emulateTransitionEnd=function(t){var i=!1,a=this;e(this).one(n.support.transition.end,function(){i=!0});var o=function(){i||e(a).trigger(n.support.transition.end),a.transitionEndTimmer=void 0};return this.transitionEndTimmer=setTimeout(o,t),this},e.extend(!0,n,{touchEvents:function(){var e=arm.support.touch?["touchstart","touchmove","touchend","touchcancel"]:["mousedown","mousemove","mouseup","mouseout"],n={ie10:t.navigator.msPointerEnabled,ie11:t.navigator.pointerEnabled};return n.ie10&&(e=["MSPointerDown","MSPointerMove","MSPointerUp","MSPointerCancel"]),n.ie11&&(e=["pointerdown","pointermove","pointerup","pointercancel"]),{touchStart:e[0],touchMove:e[1],touchEnd:e[2],touchCancel:e[3]}}(),inAarryObj:n.inArrayObj})}(window,JQUERY,window.arm),!function(t,e,n){"use strict";function i(t,n,i,a){t.handlers=t.handlers||{};var o=a.toString(),r=o.match(/[^\n]*\.preventDefault\(.*?\).*/);return e.each(n,function(e,n){t.handlers[n]=t.handlers[n]||{},t.handlers[n][i]=t.handlers[n][i]||[],t.handlers[n][i].push(a),t.preventDefault=t.preventDefault||{},t.defaultPrevented=t.defaultPrevented||{},!r||/((\/\/|\/\*).*preventDefault)|(preventDefault.*(\*\/))/.test(r[0])||t.defaultPrevented[n]||(t.preventDefault[n]=t.defaultPrevented[n]=!0)}),t}function a(t,n){var i=this;e.each(t,function(t,e){e.call(i,n)})}function o(t,e){var e=e.toUpperCase();return h.touch?t.touches[0]["page"+e]:t["page"+e]||t["client"+e]}function r(n){var i=this,a=i.touch,r=a.lastT;i.cancelAll(),/mouse/.test(f.touchStart)&&0!==n.button||(a={},a.target=n.target,h.touch&&!n.touches&&(n.touches=n.originalEvent.touches,n.touches.length&&(a.target=("tagName"in n.touches[0].target?n.touches[0].target:n.touches[0].target.parentNode)||n.target)),i.touchTimeout&&clearTimeout(i.touchTimeout),a.startX=a.curX=o(n,"x"),a.startY=a.curY=o(n,"y"),a.startT=+new Date,a.moveX=a.moveY=a.angle=0,a.scrollT=e(t).scrollTop(),a.scrollL=e(t).scrollLeft(),a.deltaT=a.startT-(r||a.startT),a.lastT=a.startT,a.target&&!a.active&&(a.active=!0,i.touch=a,i.isDefaultPrevent("dragStart")&&n.preventDefault(),i._trigger("dragStart",n),i.holdTouchTimeout=setTimeout(function(){i.holdTouch(n)},i.config.holdTouchDelay)))}function s(t){var e=this,n=e.touch;if(n.active&&(h.touch&&!t.touches&&(t.touches=t.originalEvent.touches),!/mouse/.test(f.touchStart)||0===t.button)){e.cancelholdTouch(),n.curX=o(t,"x"),n.curY=o(t,"y"),n.moveX=n.curX-n.startX,n.moveY=n.curY-n.startY,n.angle=Math.atan2(-n.moveY,n.moveX),e.touch=n,e._trigger("drag",t);var i=Math.PI/6,a=Math.abs(n.angle);n.angleX=a<i||a>5*i,n.angleY=a>2*i&&a<4*i,n.angleY&&!n.dragX&&(n.dragY=!0),n.angleX&&!n.dragY&&(n.dragX=!0),e.isDefaultPrevent("drag")&&t.preventDefault(),e.isDefaultPrevent("swipeLeft|swipeRight")&&n.angleX&&t.preventDefault(),e.isDefaultPrevent("swipeUp|swipeDown")&&n.angleY&&t.preventDefault()}}function l(t){var n=this,i=n.touch,a=n.config;/mouse/.test(f.touchStart)&&0!==t.button||i.active&&(n.cancelholdTouch(),i.endT=+new Date,i.touchT=i.endT-i.startT,i.isTap=i.touchT<a.tapTime&&d(i.moveX)<a.tapDistance&&d(i.moveY)<a.tapDistance,i.isSwipe=(d(i.moveX)>a.swipeDistance||d(i.moveY)>a.swipeDistance)&&i.touchT<a.swipeTime,i.isDoubleTap=i.deltaT>0&&i.deltaT<a.doubleTapInterval,i.active=!1,n.touch=i,setTimeout(function(){n._trigger("dragEnd",t)},5),i.isSwipe&&(n.swipeTimeout=setTimeout(function(){n._trigger("swipe swipe"+n.swipeDirection(),t)},3)),i.lastT&&i.isTap&&!/cancel/i.test(t.type)&&(n.isDefaultPrevent("tap")&&(t.preventDefault(),e.isFunction(n.onclick)&&n.onclick.apply(n.el[0],[t])),n.tapTimeout=setTimeout(function(){n._trigger("tap",t),i.isDoubleTap?n._trigger("doubleTap",t):n._trigger("singleTap",t)},3)))}function c(t,n,a){if("string"!=typeof t||!t)return this;var o=e.trim(t).split(/\s+/);if(!o.length)return this;if("string"!=typeof n)"function"==typeof n&&(a=n),n="self";else if(!n.length)return this;return"function"!=typeof a?this:this.each(function(r,s){var l=e(s).data("arm.touch");l||(e(s).data("arm.touch",l=new m(s)),l.onclick=s.onclick,s.onclick=null),i(l,o,n,a);for(var c in l.preventDefault)if(/tap/gi.test(t)&&!l.isClickPrevented)return e(s).on("click",function(t){t.preventDefault()}),void(l.isClickPrevented=!0)})}function u(t,n,i){if("string"!=typeof t||!t)return this.handlers={};var a=e.trim(t).split(/\s+/);if(!a.length)return this.handlers={};"string"!=typeof n&&("function"==typeof n&&(i=n),n="self");var o=this;e.each(a,function(t,a){if(o.handlers[a]){if(o.handlers[a][n]){var t=e.inArray(i,o.handlers[a][n]);return t!==-1?o.handlers[a][n].splice(t,1):o.handlers[a][n]=[]}return o.handlers[a]={}}})}var d=(Math.PI,Math.abs),h=n.support,f=n.touchEvents,p=["dragStart","drag","dragEnd","swipe","swipeLeft","swipeRight","swipeUp","swipeDown","tap","singleTap","doubleTap","holdTouch"];n.config({touch:{tapTime:350,tapDistance:15,holdTouchDelay:600,doubleTapInterval:400,swipeTime:300,swipeDistance:20,scrollCancel:30,preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|SELECT)$/}}});var m=function(t){var i=this;i.el=e(t),i.touch={},i.config=n.config("touch"),i.touchTimeout=i.tapTimeout=i.swipeTimeout=i.holdTouchTimeout=null,i.init()};m.prototype.init=function(){var t=this;t._touchStart=e.proxy(r,t),t._touchMove=e.proxy(s,t),t._touchEnd=e.proxy(l,t),t.onEvents(),t.scrollCancel()},m.prototype.swipeDirection=function(){var t=this.touch;return d(t.moveX)>=d(t.moveY)?t.moveX>0?"Right":"Left":t.moveY>0?"Down":"Up"},m.prototype.holdTouch=function(t){var e=this;e.holdTouchTimeout=null,e.touch.lastT&&(e.touch.isHoldTouch=!0,e._trigger("holdTouch"))},m.prototype.cancelholdTouch=function(){var t=this;t.holdTouchTimeout&&clearTimeout(t.holdTouchTimeout),t.holdTouchTimeout=null},m.prototype.isDefaultPrevent=function(t){var i=this,a=i.touch;if(!i.config.preventDefault||!e.isPlainObject(i.preventDefault)||e.isEmptyObject(i.preventDefault)||n.utils.preventDefaultException(a.target,i.config.preventDefaultException))return!1;if("undefined"===e.type(t))return!0;var o=new RegExp(t,"gi");for(var r in i.preventDefault)if(o.test(r))return!0;return!1},m.prototype._trigger=function(t,n){var i=this,o=i.touch,r=e.trim(t).split(/\s+/);e.each(r,function(t,r){var s=e.Event(r,{touch:o,originalEvent:n});i.handlers[r]&&e.each(i.handlers[r],function(t,n){if("self"!==t){var r=e(o.target).closest(t);r.length&&a.call(r[0],n,s)}else a.call(i.el[0],n,s)})})},m.prototype.cancelAll=function(){var t=this;t.touchTimeout&&clearTimeout(t.touchTimeout),t.tapTimeout&&clearTimeout(t.tapTimeout),t.swipeTimeout&&clearTimeout(t.swipeTimeout),t.holdTouchTimeout&&clearTimeout(t.holdTouchTimeout),t.touchTimeout=t.tapTimeout=t.swipeTimeout=t.holdTouchTimeout=null,t.touch={}},m.prototype.scrollCancel=function(){var n=this;e(t).on("scroll",function(){var i=d(e(t).scrollTop()-n.touch.scrollT),a=d(e(t).scrollLeft()-n.touch.scrollL);(i>n.config.scrollCancel||a>n.config.scrollCancel)&&n.cancelAll()})},m.prototype.onEvents=function(){var t=this;t.el.on(f.touchStart,t._touchStart),e(document).on(f.touchEnd,t._touchEnd).on(f.touchMove,t._touchMove),h.touch&&e(document).on(f.touchCancel,t._touchEnd)},n.pt.onTouch=e.fn.touch=function(t,e,n){return c.call(this.$||this,t,e,n)},n.pt.offTouch=e.fn.offTouch=function(t,n,i){var a=this.$||this;return a.each(function(a,o){var r=e(o).data("arm.touch");r&&u.call(r,t,n,i)})},n.pt.touch=e.fn.doTouch=function(t){var n=this.$||this;return n.each(function(n,i){var a=e(i).data("arm.touch");a&&a._trigger(t)})},e.each(p,function(t,n){e.fn[n]=function(t,e){return c.call(this,n,t,e)}}),arm.touch=m}(window,JQUERY,window.arm),!function(t,e){var n={bg:"rgba(0,0,0,0.25)",maskFix:!0},i=function(e,i,a){this.element=e,this.masklayer=i,this.maskliving=[],this.args=t.extend(!0,{},n,a),this.creat()};i.prototype={creat:function(){var t=this.args;this.reset(t),this.masklayer.appendTo(this.element)},remove:function(n){var i=t.inArray(n,this.maskliving);if(i!==-1){if(this.maskliving.splice(i,1),this.maskliving.length>0)return this.masklayer.css("z-index",this.maskliving[this.maskliving.length-1]);this.masklayer.remove(),this.element.removeClass("ui-mask ui-mask-fix"),this.element.data("hasmask",!1),this.element.off(e.touchEvents.touchMove,e.prevent)}},reset:function(e){this.args=t.extend(!0,{},n,e||{}),this.masklayer.css({background:this.args.bg,zIndex:this.args.zIndex}),this.maskliving.push(this.args.zIndex),this.args.maskFix?this.element.addClass("ui-mask-fix"):this.element.removeClass("ui-mask-fix")}},e.mask=function(e,n){if(e){var a=t(n||"body").addClass("ui-mask").eq(0),o=a.data("maskliving")||[];o.push(1),a.data("maskliving",o);var r=arm.utils.generateGUID("ui-mask"),s=a.children("ui-mask-layer");s.length>0?s.attr("id",r):(s=t('<div class="ui-mask-layer" id="'+r+'" />'),/body/i.test(a[0].tagName)&&s.addClass("ui-mask-body"));var l=a.data("hasmask");return l?l.reset(e):(l=new i(a,s,e),a.data("hasmask",l)),l}}}(JQUERY,window.arm),!function(t,e){"use strict";var n={open:"<%",close:"%>"},i={exp:function(t){return new RegExp(t,"g")},query:function(t,e,i){var o=["#([\\s\\S])+?","([^{#}])*?"][t||0];return a((e||"")+n.open+o+n.close+(i||""))},escape:function(t){return String(t||"").replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;")},error:function(t,n){e.utils.error(t,n,"Tpl")}},a=i.exp,o=function(t){this.tpl=t};o.pt=o.prototype,o.pt.parse=function(t,e){var o=this,r=t,s=a("^"+n.open+"#",""),l=a(n.close+"$","");t=t.replace(/[\r\t\n]/g," ").replace(a(n.open+"#"),n.open+"# ").replace(a(n.close+"}"),"} "+n.close).replace(/\\/g,"\\\\").replace(/(?="|')/g,"\\").replace(i.query(),function(t){return t=t.replace(s,"").replace(l,""),'";'+t.replace(/\\/g,"")+'; view+="'}).replace(i.query(1),function(t){var e='"+(';return t.replace(/\s/g,"")===n.open+n.close?"":(t=t.replace(a(n.open+"|"+n.close),""),/^=/.test(t)&&(t=t.replace(/^=/,""),e='"+_escape_('),e+t.replace(/\\/g,"")+')+"')}),t='"use strict";var view = "'+t+'";return view;';try{return o.cache=t=new Function("d, _escape_",t),t(e,i.escape)}catch(t){return delete o.cache,i.error(t,r)}},o.pt.render=function(t,e){var n,a=this;return t?(n=a.cache?a.cache(t,i.escape):a.parse(a.tpl,t),e?void e(n):n):i.error("no data")};var r=function(t){return"string"!=typeof t?i.error("Template not found"):new o(t)};r.config=function(t){t=t||{};for(var e in t)n[e]=t[e]},e.tpl=t.tpl=r}(JQUERY,window.arm),!function(t,e){"use strict";function n(t){return new s(t)}var i=document,a="getElementById",o="getElementsByTagName",r=0,s=function(t){var e=this,n=e.config=t||{};n.item=r++,e.render(!0)};s.on=function(t,e,n){return t.attachEvent?t.attachEvent("on"+e,function(){n.call(t,window.even)}):t.addEventListener(e,n,!1),s},s.prototype.type=function(){var t=this.config;if("object"==typeof t.cont)return void 0===t.cont.length?2:3},s.prototype.view=function(){var t=this,e=t.config,n=[],i={};e.pages=0|e.pages,e.curr=0|e.curr||1,e.groups="groups"in e?0|e.groups:3,e.first="first"in e?e.first:1,e.last="last"in e?e.last:e.pages,e.prev="prev"in e?e.prev:"&lt;",e.next="next"in e?e.next:"&gt;",e.groups>e.pages&&(e.groups=e.pages),i.index=Math.ceil((e.curr+(e.groups>1&&e.groups!==e.pages?1:0))/(0===e.groups?1:e.groups)),e.curr>1&&e.prev&&n.push('<a href="javascript:;" class="ui-paging-prev" data-arm-paging="'+(e.curr-1)+'">'+e.prev+"</a>"),i.index>1&&e.first&&0!==e.groups&&n.push('<a href="javascript:;" class="ui-paging-first" data-arm-paging="1"  title="首页">'+e.first+"</a><span>…</span>"),i.poor=Math.floor((e.groups-1)/2),i.start=i.index>1?e.curr-i.poor:1,i.end=i.index>1?function(){var t=e.curr+(e.groups-i.poor-1);return t>e.pages?e.pages:t}():e.groups,i.end-i.start<e.groups-1&&(i.start=i.end-e.groups+1);for(;i.start<=i.end;i.start++)i.start===e.curr?n.push('<span class="ui-paging-curr" '+(/^#/.test(e.skin)?'style="background-color:'+e.skin+'"':"")+">"+i.start+"</span>"):n.push('<a href="javascript:;" data-arm-paging="'+i.start+'">'+i.start+"</a>");return e.pages>e.groups&&i.end<e.pages&&e.last&&0!==e.groups&&n.push('<span>…</span><a href="javascript:;" class="ui-paging-last" title="尾页"  data-arm-paging="'+e.pages+'">'+e.last+"</a>"),i.flow=!e.prev&&0===e.groups,(e.curr!==e.pages&&e.next||i.flow)&&n.push(function(){return i.flow&&e.curr===e.pages?'<span class="ui-paging-nomore" title="已没有更多">'+e.next+"</span>":'<a href="javascript:;" class="ui-paging-next" data-arm-paging="'+(e.curr+1)+'">'+e.next+"</a>"}()),'<div class="ui-paging ui-paging-skin-'+(e.skin?function(t){return/^#/.test(t)?"molv":t}(e.skin):"default")+'" id="ui-paging-'+t.config.item+'">'+n.join("")+function(){return e.skip?'<span class="ui-paging-total"><label>到第</label><input type="number" min="1" onkeyup="this.value=this.value.replace(/\\D/, \'\');" class="ui-paging-skip"><label>页</label><button type="button" class="ui-paging-btn">确定</button></span>':""}()+"</div>"},s.prototype.jump=function(t){for(var e=this,n=e.config,i=t.children,a=t[o]("button")[0],r=t[o]("input")[0],l=0,c=i.length;l<c;l++)"a"===i[l].nodeName.toLowerCase()&&s.on(i[l],"click",function(){var t=0|this.getAttribute("data-arm-paging");n.curr=t,e.render()});a&&s.on(a,"click",function(){var t=0|r.value.replace(/\s|\D/g,"");t&&t<=n.pages&&(n.curr=t,e.render())})},s.prototype.render=function(t){var e=this,n=e.config,o=e.type(),r=e.view();2===o?n.cont.innerHTML=r:3===o?n.cont.html(r):i[a](n.cont).innerHTML=r,n.jump&&n.jump(n,t),e.jump(i[a]("ui-paging-"+n.item)),n.hash&&!t&&(location.hash="!"+n.hash+"="+n.curr)},e.paging=t.paging=n}(JQUERY,window.arm),!function(t,e){function n(t,n,i,a){if(!this.length)return this;if(i)return this.length>1&&e.console().warn("插件",t,"不支持多元素实例化，默认实例化第1个元素！"),new n(this[0],a);var o=[];return this.each(function(t,e){o[t]=new n(e,a)}),this[t]=o,this}e.register=function(i,a,o){t[i]=e[i]=function(e,r){var s=t(e);return s.selector!==e&&(s=t(document),r=r||e),n.call(s,i,a,o,r)},t.fn[i]=e.pt[i]=function(t){return n.call(this.$||this,i,a,o,t)}}}(JQUERY,window.arm),!function(t,e,n){function i(n){if(n&&n.length){var i=e.config("alias");t.isPlainObject(i)&&t.each(i,function(t,e){n.replace(t,e)}),n=e.R._http.test(n)?n:m.moduleUrl(n);var a=m.moduleType(n);return a.noCSSJS&&(n+=".js"),n}}function o(e){var n=e||[];"string"==typeof e&&(n=[e]);var a=[];return t.each(n,function(t,e){a.push(i(e))}),a}function r(t){if(h)return h;if(p.currentScript)return p.currentScript.src;if(f&&"interactive"===f.readyState)return f.src;var e;try{a.b.c()}catch(t){e=t.stack,!e&&window.opera&&(e=(String(t).match(/of linked script \S+/g)||[]).join(" "))}if(e)return e=e.split(/[@ ]/g).pop(),e="("===e[0]?e.slice(1,-1):e.replace(/\s/,""),e.replace(/(:\d+)?:\d+$/i,"");for(var n,i=(t?p:v).getElementsByTagName("script"),o=i.length;n=i[--o];)if("interactive"===n.readyState)return n.hasAttribute?n.src:n.getAttribute("src",4)}function s(n,i,a,o){var r=[];if(i=(i||0)+1,!n.length)return a(i,r);var l=0,a=t.isFunction(a)?a:t.noop,o=o||!1;if(t.each(n,function(n,i){var a=e.modules[i];return!(a.status<w.READY&&t.inArray(o,a.deps)===-1)&&void l++}),l===n.length){for(var c=0;c<l;c++)r.push(e.modules[n[c]].exports);return a(i,r)}setTimeout(function(){s(n,i,a,o)},y)}function l(t){if(t.status>w.SAVED)return c();t.status=w.LOADING,t.requestTime=m.getTime();var n=document.createElement(t.type.isCSS?"link":"script");return n.charset=e.config("charset")||"utf-8",t.type.isCSS?(n.type="text/css",n.rel="stylesheet"):(n.type="text/javascript",n.async=!0,h=t.uri),u(n,t),n[t.type.isCSS?"href":"src"]=t.uri,v.appendChild(n),h=null,c()}function c(){var t=b.shift();if(!t||!e.modules[t])return g=!1;var n=e.modules[t];g=!0,l(n)}function u(t,n){var i=function(i){t.onload=t.onerror=t.onreadystatechange=null,e.config("debug")||n.type.isCSS||v.removeChild(t),t=null,n.loadTime=m.getTime(),i&&(n.status=w.ERROR),e.console()[i?"error":"log"]("加载模块->",n.uri,i?"失败":"成功",",耗时",(n.loadTime-n.requestTime)/1e3,"秒"),n.polling()};"onload"in t?(t.onload=function(){i()},t.onerror=function(){i(!0)}):t.onreadystatechange=function(){/loaded|complete/.test(t.readyState)?i():i(!0)}}function d(t){this.uri=t,this.deps=[],this.times=0,this.exports=void 0,this.type=m.moduleType(t),this.status=w.SAVED,e.modules[t]=this}var h,f,p=document,m=e.utils,v=p.head||p.getElementsByTagName("head")[0]||p.documentElement,b=[],g=!1,y=10,w={SAVED:1,LOADING:2,PENDING:3,READY:4,ERROR:5};d.prototype.compile=function(){function n(n,i){if(t.isFunction(i))return e.use(n,i);var a=[];return t.each(o(n),function(t,n){var i=e.module[n];a.push(i?i.exports:void 0)}),1===a.length?a[0]:a}var i=this,a=i.factory;t.isFunction(a)&&(a=a(n,i)),"undefined"==typeof i.exports&&(i.exports=a),i.status=w.READY,e.console().log("编译模块->",i.uri,",巡检",i.times,"次")},d.prototype.polling=function(){var t=this;s(t.deps,0,function(e){t.times=e,t.compile()},t.uri)},d.define=function(n,a,s){var l=arguments.length;1===l?(s=n,n=void 0):2===l&&(s=a,t.isArray(n)?(a=n,n=void 0):a=void 0),a=o(a);var c=i(n)||r(),u=arm.modules[c];if(!u){var h=!0;u=new d(c),e.modules[c]=u}a.length&&(u.status=w.PENDING,e.console().info("模块",u.uri,"依赖于",a),u.deps=a,e.use(a)),u.factory=s,h&&u.polling()},d.use=function(n,i){var a=o(n);if(!a.length)return n;for(var r=[],l=0;l<a.length;l++){var u=a[l],h=e.modules[u];h?e.console().log("模块-> "+u+" 已加载！"):(h=new d(u),b.push(u)),h.type.isCSS||r.push(u)}s(r,0,function(e,n){t.isFunction(i)&&i.apply(this,n)}),g||c()},t.extend(!0,e,{root:function(){return e.path.replace(/js\//,"")}(),modules:{},use:d.use,define:d.define})}(JQUERY,window.arm,window),!function(t,e){"use strict";var n=(e.utils,{start:0,param:"tab",handle:".ui-tab-nav",content:".ui-tab-content",callback:function(t,n,i){e.console().log("fromIndex:"+n+", curIndex:"+i)}}),i=function(e,i){this.el=t(e),this.options=t.extend(!0,{},n,i||{}),this.handles=t(this.options.handle,this.el),this.contents=t(this.options.content,this.el),this.contentItems=this.contents.children(),this.len=this.contentItems.length,this.cur=this.options.start,this._init()};i.prototype={_init:function(){var n=this,i=e.getUrlParam(this.options.param,location.href);this.handles.each(function(){var e=t(this).children();e.tap(function(e){var i=t(this).index();n.tab(i)})}),null!==i&&(n.options.start=Number(i)),n._tab(n.options.start)},tab:function(t){t!==this.cur&&this._tab(t)},next:function(){var t=this.cur+1;t>this.len-1&&(t=this.len-1),this._tab(t)},prev:function(){var t=this.cur-1;t<0&&(t=0),this._tab(t)},_tab:function(e){var n=this;this.handles.each(function(){var n=t(this).children();n.removeClass("current").eq(e).addClass("current")}),this.contentItems.eq(n.cur).removeClass("active"),this.contentItems.eq(e).addClass("active"),this.options.callback.call(n,n.options.start,n.cur,e),this.cur=e}},e.register("tab",i)}(JQUERY,window.arm),!function(t,e){"use strict";var n=(e.utils,{activeClass:"active",handle:".ui-panel-hd",content:".ui-panel-bd",duration:200,callback:function(t,n){e.console().log("index:",t,"; active:",n)}}),i=function(e,i){this.el=t(e),this.options=t.extend(!0,{},n,i||{}),this.handles=this.el.children(this.options.handle),this.contents=this.el.children(this.options.content),this.items=this.contents.children(),this._init()};i.prototype={_init:function(){var t=this;t.handles.tap(function(e){e.preventDefault(),t._toggle()})},_toggle:function(){var t=this;t.el.toggleClass(t.options.activeClass),t.active=t.el.hasClass(t.options.activeClass),t.toggle()},toggle:function(){var t=this;t.options.callback.call(t,t.options.index,t.active),
t.contents.toggle(t.active)}},e.register("drawer",i)}(JQUERY,window.arm),!function(t,e){var n={},i={iPortal:!1};n.config=function(n,a){return i=t.extend(!0,i,n),e.config(n,a)},n.run=function(t){console.debug("APP 运行成功"),t()},n.init=function(t){return!i.iPortal||i.debug?n.run(t):(window.iPortal={sudyInitCallback:function(){var t=!1;return function(e){if(t)return t;t=!0,window.iPortal.context=e;var n=document.createElement("script"),i="?_vt="+Number(new Date),a=/\?/.test(e.libPath)?e.libPath.replace(/\?/,i+"&"):e.libPath+i;return n.type="text/javascript",n.src=a,n.charset="utf-8",document.getElementsByTagName("head")[0].appendChild(n),t}}()},void(iPortal.isReady?n.run(t):iPortal.onReady=function(){n.run(t)}))},window.app=e.app=n}(JQUERY,window.arm),function(t,e){var n=[43856,19416,19168,42352,21717,53856,55632,25940,22191,39632,21970,19168,42422,42192,53840,53845,46415,54944,44450,38320,18807,18815,42160,46261,27216,27968,43860,11119,38256,21234,18800,25958,54432,59984,27285,23263,11104,34531,37615,51415,51551,54432,55462,46431,22176,42420,9695,37584,53938,43344,46423,27808,46416,21333,19887,42416,17779,21183,43432,59728,27296,44710,43856,19296,43748,42352,21088,62051,55632,23383,22176,38608,19925,19152,42192,54484,53840,54616,46400,46752,38310,38335,18864,43380,42160,45690,27216,27968,44870,43872,38256,19189,18800,25776,29859,59984,27480,23232,43872,38613,37600,51552,55636,54432,55888,30034,22176,43959,9680,37584,51893,43344,46240,47780,44368,21977,19360,42416,20854,21183,43312,31060,27296,44368,23378,19296,42726,42208,53856,60005,54576,23200,30371,38608,19195,19152,42192,53430,53855,54560,56645,46496,22224,21938,18864,42359,42160,43600,45653,27951,44448,19299,37759,18936,18800,25776,26790,59999,27424,42692,43759,37600,53987,51552,54615,54432,55888,23893,22176,42704,21972,21200,43448,43344,46240,46758,44368,21920,43940,42416,21168,45683,26928,29495,27296,44368,19285,19311,42352,21732,53856,59752,54560,55968,27302,22239,19168,43476,42192,53584,62034,54560],i=["9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","9778397bd19801ec9210c965cc920e","97b6b97bd19801ec95f8c965cc920f","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd197c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bcf97c3598082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd19801ec9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bd07f1487f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b97bd197c36c9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b70c9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","977837f0e37f149b0723b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0723b06bd","7f07e7f0e37f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f595b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e37f14998083b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14898082b0723b02d5","7f07e7f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66aa89801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e26665b66a449801e9808297c35","665f67f0e37f1489801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722"],a=["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],o=["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],r=["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],s=["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],l=["初","十","廿","三十"],c=["","一","二","三","四","五","六","七","八","九"],u=["正","二","三","四","五","六","七","八","九","十","十一","腊"],d={yearDataCache:{},getDate:function(t){for(var e,n,i,a,o,r=Math.ceil((t-new Date(1899,1,10))/864e5),s=1899;s<2100&&r>0;s++)e=this.getYearDays(s),r-=e;for(r<0&&(r+=e,s--),i=s,n=this.getLeapMonth(i)||!1,s=1;s<=12;s++){if(e=this.getMonthDays(i,s),n===!0&&(n=!1,s--,e=this.getLeapDays(i),r<e&&(a=!0)),n===s&&(n=!0),r<e){o=30===e;break}r-=e}return{lunarYear:i,lunarMonth:s,lunarDay:r+1,isLeap:a,isBigMonth:o}},getYearDays:function(t){var e,i=this.yearDataCache;if(i[t])return i[t];var a=348,o=n[t-1899];for(e=32768;e>8;e>>=1)a+=e&o?1:0;return a+=this.getLeapDays(t),i[t]=a,a},getLeapDays:function(t){return this.getLeapMonth(t)?n[t-1899+1]&!0?30:29:0},getLeapMonth:function(t){var e=15&n[t-1899];return 15==e?0:e},getMonthDays:function(t,e){return n[t-1899]&65536>>e?30:29}},h=function(t,e){for(var n,a=i[t-1900],o=[],r=0;r<30;r+=5)n=(+("0x"+a.substr(r,5))).toString(),o.push(n.substr(0,1)),o.push(n.substr(1,2)),o.push(n.substr(3,1)),o.push(n.substr(4,2));return new Date(t,parseInt(e/2,10),o[e])},f={calculate:function(t){return o[t%10]+r[t%12]},getGzYear:function(t,e,n){return this.calculate(e-1900+36-(n===e?0:1))},getGzMonth:function(t,e,n){var i=h(e,2*t.getMonth());return this.calculate(12*(e-1900)+n+12-(t<i?1:0))},getGzDay:function(t){return this.calculate(Math.ceil(t/864e5+25567+10))}},p={b0101:"b,春节 ",b0115:"b,元宵节",b0202:"b,龙头节",b0505:"b,端午节",b0707:"b,七夕节",b0715:"b,中元节",b0815:"b,中秋节",b0909:"b,重阳节",b1001:"b,寒衣节",b1015:"b,下元节",b1208:"b,腊八节",b1223:"b,小年",i0202:"i,湿地日,1996",i0308:"i,妇女节,1975",i0315:"i,消费者权益日,1983",i0401:"i,愚人节,1564",i0422:"i,地球日,1990",i0501:"i,劳动节,1889",i0512:"i,护士节,1912",i0518:"i,博物馆日,1977",i0605:"i,环境日,1972",i0623:"i,奥林匹克日,1948",i1020:"i,骨质疏松日,1998",i1117:"i,学生日,1942",i1201:"i,艾滋病日,1988",h0101:"h,元旦",h0312:"h,植树节,1979",h0504:"h,五四青年节,1939",h0601:"h,儿童节,1950",h0701:"h,建党节,1941",h0801:"h,建军节,1933",h0903:"h,抗战胜利日,1945",h0930:"h,烈士纪念日,1949",h0910:"h,教师节,1985",h1001:"h,国庆节,1949",h1204:"h,宪法日,1982",h1213:"h,国家公祭日,1937",c1224:"c,平安夜",c1225:"c,圣诞节",c0214:"c,情人节",w0520:"a,母亲节,1913",w0630:"a,父亲节",w1144:"a,感恩节"},m=function(t){return t<10?"0"+t:t},v=function(t,e,n){var i,a=t.getFullYear(),o=t.getMonth()+1,r=t.getDate(),s=t.getDay(),l=Math.ceil(r/7),c="w"+m(o)+l+s,u="b"+m(e.lunarMonth)+m(e.lunarDay),d="i"+m(o)+m(r),h="h"+m(o)+m(r),f="c"+m(o)+m(r),v=[];12===e.lunarMonth&&e.lunarDay===(e.isBigMonth?30:29)&&v.push("t,除夕"),n&&"清明"===n&&v.push("t,清明节"),v.push(p[u],p[h],p[c],p[f],p[d]);for(var b=0,g=[];b<v.length;b++)if(v[b]){if(i=v[b].split(","),i[2]&&a<Number(i[2]))continue;g.push({type:i[0],desc:i[1],value:i[1]})}return g.sort(function(t,e){return t&&e?t.type.charCodeAt(0)-e.type.charCodeAt(0):t?-1:1}),g},b=function(t){var e,n=t.getFullYear(),i=t.getMonth()+1,o=t.getDate(),r=2*(i-1),p=h(n,r),m="";o!=p.getDate()?(e=h(n,r+1),o==e.getDate()&&(m=a[r+1])):m=a[r];var b=d.getDate(t);return{animal:s[(b.lunarYear-4)%12],gzDate:f.getGzDay(t),gzMonth:f.getGzMonth(t,n,i),gzYear:f.getGzYear(t,n,b.lunarYear),lunarYear:b.lunarYear,lunarMonth:b.lunarMonth,lunarDate:b.lunarDay,lMonth:(b.isLeap?"闰":"")+u[b.lunarMonth-1],lDate:b.lunarDay%10==0?["初十","二十","三十"][b.lunarDay/10-1]:l[parseInt(b.lunarDay/10,10)]+c[parseInt(b.lunarDay%10,10)],term:m,festival:function(){return v(t,b,m)}(),isBigMonth:b.isBigMonth,oDate:t,cnDay:"日一二三四五六七".charAt(t.getDay())}};e.utils.lunar=function(t){var t=new Date(t),e=t.getFullYear();return!(e<1900||e>2100)&&b(t)}}(JQUERY,window.arm),!function(t,e){function n(t,e,n){if(/string|object/i.test(typeof t))var i=new Date(t);else{var i=new Date;e="undefined"==typeof e?t:e}return e?i.format(n||c.dateFormat):i}function i(t,e){var i=n(t,!1),a={Y:i.getFullYear(),M:i.getMonth(),D:i.getDate(),H:i.getHours(),m:i.getMinutes(),s:i.getSeconds(),S:i.getMilliseconds(),t:i.getTime(),w:i.getDay()};return a.days=new Date(a.Y,a.M+1,0).getDate(),a[0]=i,a.date=i.format(e||c.dateFormat),a.day=i.format(c.dateFormat),a.month=i.format(c.monthFormat),a}function a(e,n){var a=i(e),o=n.length,r=t.inArray(new Date(a.Y,0,1).getDay(),n),s=Number(0===r?new Date(a.Y,0,1):new Date(a.Y,0,o-r+1)),l=Number(new Date(a.Y,a.M,a.D)),c=Math.floor((l-s)/86400/o/1e3)+1;return l<0?1:0===r?c:c+1}function o(t,e,n){var a=i(t),o=a.Y,r=a.M,s=a.D,e=e||"day",n="undefined"==typeof n?1:n;if(0===n)return a[0];"day"===e&&(s-=n),"week"===e&&(s-=7),s<1&&(e="month"),"month"===e&&(r--,r<0&&(e="year",r=11)),"year"===e&&o--;var l=new Date(o,r+1,0).getDate();return s>l&&(s=l),s<1&&(s+=l),new Date(o,r,s)}function r(t,e,n){var a=i(t),o=a.Y,r=a.M,s=a.D,e=e||"month",n="undefined"==typeof n?1:n;if(0===n)return a[0];"day"===e&&(s+=n),"week"===e&&(s+=7),s>a.days&&(e="month",s-=a.days),"month"===e&&(r++,r>11&&(e="year",r=0)),"year"===e&&o++;var l=new Date(o,r+1,0).getDate();return s>l&&(s=l),s<1&&(s+=l),new Date(o,r,s)}function s(e,n){var i=this;i.$element=t(e),i.options=t.extend(!0,{},l,n),t.isFunction(i.options.render)?i.$container=i.$element.children():(i.$element.html('<div class="'+c.calendarClass+'" />'),i.$container=t(".arm-calendar",i.$element)),i.$container.addClass(c.calendarClass+"-"+i.options.calendarTheme),i.boxDistance=0,i._init()}var l={daysIndexOfWeek:[0,1,2,3,4,5,6],daysLabelOfWeek:["日","一","二","三","四","五","六"],calendarTheme:"default",tpl:{build:'<%# if(d.toolbar){ %><div class="calendar-toolbar"><div class="calendar-title" data-calendar-title></div><%# if(d.modeBtn){ %><div class="ui-btn-multi calendar-tool calendar-view-switch"><a data-calendar-view="month" class="ui-btn"><%d.string.month%></a><a data-calendar-view="week" class="ui-btn"><%d.string.week%></a><a data-calendar-view="day" class="ui-btn"><%d.string.day%></a></div><%# } %><%# if(d.todayBtn){ %><a data-calendar-control="today" class="calendar-tool calendar-today"><%d.string.today%></a><%#}%><%# if(d.navigation){ %><a data-calendar-control="prev" class="calendar-tool calendar-prev"><%d.navigation.prev%></a><a data-calendar-control="next" class="calendar-tool calendar-next"><%d.navigation.next%></a><%#}%></div><%# } %><%# if(d.calendar){ %><div class="calendar-wrap"><ul class="ui-tiled calendar-week-bar ui-border-b"><%# for (var i = 0; i < d.daysIndexOfWeek.length; i++) { %><li class="calendar-week-label week-label-<%i%> week-<%d.daysIndexOfWeek[i]%>-label"><% d.daysLabelOfWeek[d.daysIndexOfWeek[i]] %></li><%# }%></ul><div class="calendar-days-box ui-border-r"></div></div><%# } %>',grid:'<div class="calendar-days calendar-<%d.role%>-view"><%# for (var i = 0; i < d.rows; i++) { %><div class="ui-row-flex ui-border-b calendar-row row-<%i%>" data-weeknumber="<%i+d.weekIndex%>"><%# for (var j = 0; j < d.daysIndexOfWeek.length; j++) { var grid = i*d.daysIndexOfWeek.length+j; %><div class="ui-col ui-border-l ui-center calendar-grid grid-<%grid%> grid-week-<%d.daysIndexOfWeek[j]%><%# if(d.dates[grid].siblings){ %> grid-siblings grid-siblings-<%d.dates[grid].siblings%><%# } %><%# if(d.dates[grid].date===d.today){ %> grid-today<%# } %><%# if(d.dates[grid].date===d.curday){ %> grid-curday<%# } %><%# if(d.hidePolish&&d.dates[grid].siblings){ %> grid-hidden"<%# }else{ %>" data-date="<%d.dates[grid].date%>"<%# }%>><div class="grid"><div class="day-number"><%d.dates[grid].D%></div></div></div><%# } %></div><%# } %></div>'},selector:{wrap:".calendar-wrap",toolbar:".calendar-toolbar",box:".calendar-days-box",grid:".calendar-grid",date:"[data-date]",today:".calendar-today",days:".calendar-days",gridBox:".grid",dayNum:".day-number"},classNames:{curday:"grid-curday",mode:"view-mode",lunar:"grid-lunar",festival:"grid-festival"},string:{today:"今",week:"周",month:"月",day:"日",index:"第",weekdays:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},format:{day:"yyyy年M月d日",month:"yyyy年M月",lunar:"农历M月"},sixRows:1,rows:5,toolbar:!0,viewMode:"month",modeBtn:1,todayBtn:1,navigation:{prev:'<i class="ui-icon-prev"></i>',next:'<i class="ui-icon-next"></i>'},swipe:1,weekNumber:1,slide:400,transitionType:"ease",hidePolish:0,lunar:1,festival:1,holiday:0,curday:new Date},c={dateFormat:"yyyy/MM/dd",monthFormat:"yyyy/MM",calendarClass:"arm-calendar",holidayTip:{1:"休",2:"班"},viewModes:["month","week","day"]};t.extend(!0,e.utils,{clipDate:i,getDate:n,getPrevDate:o,getNextDate:r}),s.prototype._init=function(){var t=this,e=t.options;t.bindEvents(),t.build(),t.setCurday(e.curday)},s.prototype.bindEvents=function(){var e=this,n=e.options;e.$element.on("clndr:build",function(i){t.isFunction(n.onbuild)&&n.onbuild.call(e,i)}).on("clndr:render",function(i){t.isFunction(n.onrender)&&n.onrender.call(e,i)}).on("clndr:change",function(i){e.view(),t.isFunction(n.onchange)&&n.onchange.call(e,i)}),e.$container.on("clndr:viewmode",function(){e.build(),e.render(),e.view()}).on("click","[data-calendar-view]",function(){var n=t(this).data("calendar-view");e.changeMode(n)}).touch("tap","[data-calendar-control]",function(n){var i=t(this).data("calendar-control");e[i]&&e[i]()}).touch("swipeLeft",n.selector.wrap,function(t){t.preventDefault(),n.swipe&&t.touch.angleX&&e.next()}).touch("swipeRight",n.selector.wrap,function(t){t.preventDefault(),n.swipe&&t.touch.angleX&&e.prev()}).touch("tap",n.selector.date,function(){e.setCurday(t(this).data("date"))}).touch("drag",n.selector.wrap,function(t){n.slide&&t.touch.dragX&&e.translateBox(t.touch.moveX)}).touch("dragEnd",n.selector.wrap,function(t){if(e.boxDistance&&!e.transitioning){if(e.boxDistance>e.containerWidth/2)return e.slideBox(-1);if(e.boxDistance<-e.containerWidth/2)return e.slideBox(1);e.slideBox(0)}})},s.prototype.build=function(){var n=this,i=n.options,a={toolbar:i.toolbar,calendar:"day"!==i.viewMode,daysIndexOfWeek:i.daysIndexOfWeek,daysLabelOfWeek:i.daysLabelOfWeek,string:i.string,modeBtn:i.modeBtn,todayBtn:i.todayBtn,navigation:i.navigation};if(t.isFunction(i.build))return i.build.call(n,a);var o=e.tpl(i.tpl.build).render(a);n.$container.html(o),n.containerWidth=n.$container.width(),n.$element.trigger("clndr:build",[n])},s.prototype.getRenderDates=function(e,s){var l=this,c=l.options,e=e||l.curday,u=i(e),d=i(o(e,c.viewMode)),h=i(r(e,c.viewMode)),f=c.daysIndexOfWeek.length,p=a(e,c.daysIndexOfWeek),m=[];if("day"===c.viewMode)m.push(u);else if("week"===c.viewMode)for(var v=1,b=t.inArray(u.w,c.daysIndexOfWeek),g=o(e,"day",b),y=0;y<f;y++){var w=i(r(g,"day",y));m.push(w)}else{var x=new Date(u.Y,u.M,1),T=t.inArray(x.getDay(),c.daysIndexOfWeek),_=t.inArray(new Date(u.Y,u.M,u.days).getDay(),c.daysIndexOfWeek),C=Math.ceil((u.days+T-f)/f)+1,v=c.sixRows?6:c.rows?c.rows:C,D=(v-C+1)*f-1-_,p=a(x,c.daysIndexOfWeek);if(T>0)for(var y=T-1;y>=0;y--){var w=t.extend(!0,i(new Date(d.Y,d.M,d.days-y)),{siblings:c.viewMode});m.push(w)}for(var y=0;y<u.days;y++){var w=i(new Date(u.Y,u.M,y+1));m.push(w)}if(D>0)for(var y=0;y<D;y++){var w=t.extend(!0,i(new Date(h.Y,h.M,y+1)),{siblings:c.viewMode});m.push(w)}}var E={dates:m,string:c.string,daysIndexOfWeek:c.daysIndexOfWeek,rows:v,weekIndex:p,weekNumber:c.weekNumber,today:n(!0),curday:e,curDate:u,prevDate:d,nextDate:h,role:s||"cur",hidePolish:c.hidePolish&&"month"===c.viewMode};return E},s.prototype.render=function(){var n=this,i=n.options,a=n.getRenderDates(),o=n.getRenderDates(a.prevDate.date,"prev"),r=n.getRenderDates(a.nextDate.date,"next");n.renderDates=a.dates,n.prevDates=o.dates,n.nextDates=r.dates,n.$render=t('<div class="arm-calendar-days">'),n.$render.html(e.tpl(i.tpl.grid).render(a)),t(i.selector.box,n.$container).html(n.$render),n.$element.trigger("clndr:render",[n]),i.slide&&n.$render.prepend(e.tpl(i.tpl.grid).render(o)).append(e.tpl(i.tpl.grid).render(r)),n.addIns()},s.prototype.addIns=function(){var n=this,i=n.options;t("[data-date]",n.$container).each(function(){var n=t(this).data("date");if(i.lunar||i.festival){var a=e.utils.lunar(n);if(a){t(this).addClass(i.classNames.lunar);var o=a.term?' day-festival">'+a.term:'">'+a.lDate;i.festival&&a.festival&&a.festival.length&&(t(this).addClass(i.classNames.festival),o=' day-festival">'+a.festival[0].value),t(i.selector.gridBox,this).append('<div class="day-lunar'+o+"</div>")}}})},s.prototype.view=function(){var e=this,i=e.options,o=e.curDate,r={day:o[0].format(i.format.day)+" "+i.string.weekdays[o.w],month:o[0].format(i.format.month)+(i.lunar&&o.lunar?" "+i.format.lunar.replace(/[m]+/i,o.lunar.lMonth):""),week:o[0].format(i.format.month)+" "+i.string.index+a(e.curday,i.daysIndexOfWeek)+i.string.week};t("."+i.classNames.mode,e.$container).removeClass(i.classNames.mode),t('[data-calendar-view="'+i.viewMode+'"]',e.$container).addClass(i.classNames.mode),t("[data-calendar-title]",e.$container).html(r[i.viewMode]),t("."+i.classNames.curday,e.$container).removeClass(i.classNames.curday),t('[data-date="'+e.curday+'"]',e.$container).addClass(i.classNames.curday),t(i.selector.today,e.$container).toggle(n(!0)!==e.curday)},s.prototype.changeMode=function(e){var n=this,i=n.options;i.viewMode!==e&&t.inArray(e,c.viewModes)!==-1&&(i.viewMode=e,n.$container.trigger("clndr:viewmode"))},s.prototype.setCurday=function(t){var a=this,o=a.options,r=n(t,!0);if(r!==a.curday)if(a.curday=r,a.curDate=i(r),a.curDate.lunar=e.utils.lunar(r),a.$element.trigger("clndr:change",[a]),a.renderDates){var s=e.inObject(a.renderDates,function(t,e){return t.date===r});(s===-1||"month"===o.viewMode&&a.renderDates[s].siblings)&&a.render()}else a.render();return r},s.prototype.translateBox=function(t){var n=this;n.options;n.boxDistance=t,n.$render[0].style[e.utils.prefixStyle("transform")]="translateX("+t+"px)"},s.prototype.slideBox=function(n){var i=this,a=i.options;if(!i.transitioning){i.transitioning=!0;var s=t(a.selector.box,i.$container).width(),l=-s*n;i.$render.removeAttr("style"),i.$render[0].style[e.utils.prefixStyle("transition")]="transform "+a.slide+"ms "+a.transitionType,i.translateBox(l),i.$container.one(e.support.transition.end,function(){i.$render.removeAttr("style"),i.transitioning=!1,i.boxDistance=0,n<0&&i.setCurday(o(i.curday,a.viewMode)),n>0&&i.setCurday(r(i.curday,a.viewMode))}).emulateTransitionEnd(a.slide)}},s.prototype.prev=function(){var t=this,e=t.options;return e.slide?t.slideBox(-1):void t.setCurday(o(t.curday,e.viewMode))},s.prototype.next=function(){var t=this,e=t.options;return e.slide?t.slideBox(1):void t.setCurday(r(t.curday,e.viewMode))},s.prototype.today=function(){var t=this;t.options;t.setCurday(new Date)},e.register("calendar",s,!0)}(JQUERY,window.arm),function(t,e){function n(t){return o.api+o.paths[t]}function i(e){if(t.isPlainObject(e))return e.x+","+e.y}function a(e){if(/^(\s+)?\d+(\.\d+)?\D+\d+(\.\d+)?(\s+)?$/.test(e)){var n=t.trim(e).split(/\D+/);return{x:Number(n[0]),y:Number(n[1])}}}var o={api:"http://api.map.baidu.com/",v:"2.0",ak:e.config("BMapKey")||"YN79qK7VCGEOBTNzFEX0v5ej",paths:{map:"getscript",locationIp:"location/ip",staticimage:"staticimage",geoconv:"geoconv/v1/"}},r={coord:"118.783681,31.981661"},s=n("map")+"?v="+o.v+"&ak="+o.ak+"&t="+(new Date).getTime(),l={version:o.v,api:"baidu",pointToCoord:i,coordToPoint:a};l.geoconv=function(e,i,a){"string"==typeof e&&(e={coords:e}),e=t.extend(!0,{ak:o.ak,from:1,to:5},e),i=t.isFunction(i)?i:t.noop,a=t.isFunction(a)?a:t.noop,t.ajax({url:n("geoconv"),dataType:"jsonp",data:e,success:function(t){t.status?a(t):i(t)},error:function(t,e,n){a({status:e,error:n})}})},l.getCoord=function(e,n,o){if(t.isPlainObject(e))var r={coords:i(e),from:e.type};return n=t.isFunction(n)?n:t.noop,o=t.isFunction(o)?o:t.noop,r&&5===r.type?n(a(r.coords)):void l.geoconv(r,function(t){n(t.result[0])},o)},l.getLocationByIp=function(e,i,a){t.isFunction(e)&&(a=i,i=e,e="");var r={ak:o.ak,coor:"bd09ll",ip:e};i=t.isFunction(i)?i:t.noop,a=t.isFunction(a)?a:t.noop,t.ajax({url:n("locationIp"),dataType:"jsonp",data:r,success:function(t){t.status?a(t):i(t)},error:function(t,e,n){n({status:e,error:n})}})},l.getPosition=function(e,n,i){e=t.isFunction(e)?e:t.noop,n=t.isFunction(n)?n:t.noop,i=t.extend(!0,{enableHighAccuracy:!0,timeout:2e4},i),navigator.geolocation?navigator.geolocation.getCurrentPosition(function(t){t.content={point:{x:t.coords.longitude,y:t.coords.latitude,type:1}},e(t)},n,i):getLocationByIp(e,n)},l.staticimage=function(e,a){return e===!0&&(a=!0),e=t.extend(!0,{zoom:17,scale:2,width:400,height:300,copyright:1,center:r.coord},e),i(e.center)&&(e.center=i(e.center)),this.src=n("staticimage")+"?"+t.param(e),a?this.src:'<img width="'+e.width+'" height="'+e.height+'" src="'+this.src+'">'},l.staticimage.prototype.creatTo=function(e){var n=t('<img src="'+this.src+'">');return t(e).html(n),n},l.creatMap=function(n,a){if($element=t(n),!$element.length||!$element[0].nodeType)return e.console().warn("dom is needed to creat a map!");var o=$element[0];t(o).addClass("ui-map-holder"),"string"==typeof a&&(a={center:a});var l=t.extend(!0,{center:r.coord,mapOptions:{enableHighResolution:!0},navigationControl:!0,geolocationControl:!0,locationSuccess:t.noop,locationError:t.noop,onCreated:t.noop,mapTypeControl:!1,zoom:17},a),c={};e.use([s],function(){e.BMap=e.BMap||BMap,c.Map=new BMap.Map(o,l.mapOptions),c.Map.addEventListener("tilesloaded",l.onRender),c.Point=l.center,i(l.center)&&(c.Point=new BMap.Point(l.center.x,l.center.y)),c.Zoom=l.zoom,c.Map.centerAndZoom(c.Point,c.Zoom),l.navigationControl&&(c.navigationControl=new BMap.NavigationControl(t.extend(!0,{anchor:BMAP_ANCHOR_TOP_RIGHT,type:BMAP_NAVIGATION_CONTROL_SMALL},l.navigationControl)),c.Map.addControl(c.navigationControl)),l.geolocationControl&&(c.geolocationControl=new BMap.GeolocationControl(t.extend(!0,{},l.geolocationControl)),c.geolocationControl.addEventListener("locationSuccess",function(t){l.locationSuccess.call(c,t)}),c.geolocationControl.addEventListener("locationError",function(t){l.locationError.call(c,t)}),c.Map.addControl(c.geolocationControl)),l.mapTypeControl&&(c.mapTypeControl=new BMap.MapTypeControl(t.extend(!0,{anchor:BMAP_ANCHOR_TOP_LEFT,mapTypes:[BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP]},l.mapTypeControl)),c.Map.addControl(c.mapTypeControl)),l.onCreated.call(c)})},e.map=l}(JQUERY,window.arm),function(t,e){function n(t){var n;for(n in t)if(l[t[n]]!==e)return!0;return!1}function i(){var t,e=["Webkit","Moz","O","ms"];for(t in e)if(n([e[t]+"Transform"]))return"-"+e[t].toLowerCase()+"-";return""}function a(n,i,a){var o=n;return"object"==typeof i?n.each(function(){r[this.id]&&r[this.id].destroy(),new t.mobiscroll.classes[i.component||"Scroller"](this,i)}):("string"==typeof i&&n.each(function(){var t,n=r[this.id];if(n&&n[i]&&(t=n[i].apply(this,Array.prototype.slice.call(a,1)),t!==e))return o=t,!1}),o)}var o=+new Date,r={},s=t.extend,l=document.createElement("modernizr").style,c=n(["perspectiveProperty","WebkitPerspective","MozPerspective","OPerspective","msPerspective"]),u=n(["flex","msFlex","WebkitBoxDirection"]),d=i(),h=d.replace(/^\-/,"").replace(/\-$/,"").replace("moz","Moz");t.fn.mobiscroll=function(e){return s(this,t.mobiscroll.components),a(this,e,arguments)},t.mobiscroll=t.mobiscroll||{version:"2.15.1",util:{prefix:d,jsPrefix:h,has3d:c,hasFlex:u,testTouch:function(e,n){if("touchstart"==e.type)t(n).attr("data-touch","1");else if(t(n).attr("data-touch"))return t(n).removeAttr("data-touch"),!1;return!0},objectToArray:function(t){var e,n=[];for(e in t)n.push(t[e]);return n},arrayToObject:function(t){var e,n={};if(t)for(e=0;e<t.length;e++)n[t[e]]=t[e];return n},isNumeric:function(t){return t-parseFloat(t)>=0},isString:function(t){return"string"==typeof t},getCoord:function(t,e){var n=t.originalEvent||t;return n.changedTouches?n.changedTouches[0]["page"+e]:t["page"+e]},getPosition:function(n,i){var a,o,r=window.getComputedStyle?getComputedStyle(n[0]):n[0].style;return c?(t.each(["t","webkitT","MozT","OT","msT"],function(t,n){if(r[n+"ransform"]!==e)return a=r[n+"ransform"],!1}),a=a.split(")")[0].split(", "),o=i?a[13]||a[5]:a[12]||a[4]):o=i?r.top.replace("px",""):r.left.replace("px",""),o},constrain:function(t,e,n){return Math.max(e,Math.min(t,n))},vibrate:function(t){"vibrate"in navigator&&navigator.vibrate(t||50)}},tapped:!1,autoTheme:"ios",presets:{scroller:{},numpad:{},listview:{},menustrip:{}},themes:{frame:{},listview:{},menustrip:{}},i18n:{},instances:r,classes:{},components:{},defaults:{context:"body",mousewheel:!0,vibrate:!0},setDefaults:function(t){s(this.defaults,t)},presetShort:function(t,n,i){this.components[t]=function(o){return a(this,s(o,{component:n,preset:i===!1?e:t}),arguments)}}},t.mobiscroll.classes.Base=function(e,n){var i,a,l,c,u,d,h=t.mobiscroll,f=this;f.settings={},f._presetLoad=function(){},f._init=function(t){l=f.settings,s(n,t),f._hasDef&&(d=h.defaults),s(l,f._defaults,d,n),f._hasTheme&&(u=l.theme,"auto"!=u&&u||(u=h.autoTheme),"default"==u&&(u="mobiscroll"),n.theme=u,c=h.themes[f._class][u]),f._hasLang&&(i=h.i18n[l.lang]),f._hasTheme&&f.trigger("onThemeLoad",[i,n]),s(l,c,i,d,n),f._hasPreset&&(f._presetLoad(l),a=h.presets[f._class][l.preset],a&&(a=a.call(e,f),s(l,a,n)))},f._destroy=function(){f.trigger("onDestroy",[]),delete r[e.id],f=null},f.trigger=function(i,o){var r;return o.push(f),t.each([d,c,a,n],function(t,n){n&&n[i]&&(r=n[i].apply(e,o))}),r},f.option=function(t,e){var n={};"object"==typeof t?n=t:n[t]=e,f.init(n)},f.getInst=function(){return f},n=n||{},e.id||(e.id="mobiscroll"+ ++o),r[e.id]=f}}(JQUERY),function(t,e,n,i){var a,o,r=t.mobiscroll,s=r.instances,l=r.util,c=l.jsPrefix,u=l.has3d,d=l.getCoord,h=l.constrain,f=l.isString,p=/android [1-3]/i.test(navigator.userAgent),m=/(iphone|ipod|ipad).* os 8_/i.test(navigator.userAgent),v="webkitAnimationEnd animationend",b=function(){},g=function(t){t.preventDefault()};r.classes.Frame=function(l,m,y){function w(e){L&&L.removeClass("dwb-a"),L=t(this),L.hasClass("dwb-d")||L.hasClass("dwb-nhl")||L.addClass("dwb-a"),"mousedown"===e.type&&t(n).on("mouseup",x)}function x(e){L&&(L.removeClass("dwb-a"),L=null),"mouseup"===e.type&&t(n).off("mouseup",x)}function T(t){13==t.keyCode?G.select():27==t.keyCode&&G.cancel()}function _(t){t||Y.focus(),G.ariaMessage(z.ariaMessage)}function C(e){var n,l,c,u=z.focusOnClose;P.remove(),a&&!e&&setTimeout(function(){if(u===i||u===!0){o=!0,n=a[0],c=n.type,l=n.value;try{n.type="button"}catch(t){}a.focus(),n.type=c,n.value=l}else u&&(s[t(u).attr("id")]&&(r.tapped=!1),t(u).focus())},200),G._isVisible=!1,F("onHide",[])}function D(t){clearTimeout(et[t.type]),et[t.type]=setTimeout(function(){var e="scroll"==t.type;e&&!q||G.position(!e)},200)}function E(t){Y[0].contains(t.target)||Y.focus()}function k(e,i){r.tapped||(e&&e(),t(n.activeElement).is("input,textarea")&&t(n.activeElement).blur(),a=i,G.show()),setTimeout(function(){o=!1},300)}var S,M,A,P,$,O,Y,j,R,N,L,I,F,V,B,W,U,H,X,z,q,J,Z,Q,G=this,K=t(l),tt=[],et={};r.classes.Base.call(this,l,m,!0),G.position=function(e){var a,o,r,s,l,c,u,d,f,p,m,v,b,g,y,w,x=0,T=0,_={},C=Math.min(j[0].innerWidth||j.innerWidth(),O.width()),D=j[0].innerHeight||j.innerHeight();Z===C&&Q===D&&e||X||((G._isFullScreen||/top|bottom/.test(z.display))&&Y.width(C),F("onPosition",[P,C,D])!==!1&&B&&(y=j.scrollLeft(),w=j.scrollTop(),s=z.anchor===i?K:t(z.anchor),G._isLiquid&&"liquid"!==z.layout&&(C<400?P.addClass("dw-liq"):P.removeClass("dw-liq")),!G._isFullScreen&&/modal|bubble/.test(z.display)&&(R.width(""),t(".mbsc-w-p",P).each(function(){a=t(this).outerWidth(!0),x+=a,T=a>T?a:T;
}),a=x>C?T:x,R.width(a).css("white-space",x>C?"":"nowrap")),W=G._isFullScreen?C:Y.outerWidth(),U=G._isFullScreen?D:Y.outerHeight(!0),q=U<=D&&W<=C,G.scrollLock=q,"modal"==z.display?(o=Math.max(0,y+(C-W)/2),r=w+(D-U)/2):"bubble"==z.display?(g=!0,p=t(".dw-arrw-i",P),u=s.offset(),d=Math.abs(M.offset().top-u.top),f=Math.abs(M.offset().left-u.left),l=s.outerWidth(),c=s.outerHeight(),o=h(f-(Y.outerWidth(!0)-l)/2,y+3,y+C-W-3),r=d-U,r<w||d>w+D?(Y.removeClass("dw-bubble-top").addClass("dw-bubble-bottom"),r=d+c):Y.removeClass("dw-bubble-bottom").addClass("dw-bubble-top"),m=p.outerWidth(),v=h(f+l/2-(o+(W-m)/2),0,m),t(".dw-arr",P).css({left:v})):(o=y,"top"==z.display?r=w:"bottom"==z.display&&(r=w+D-U)),r=r<0?0:r,_.top=r,_.left=o,Y.css(_),O.height(0),b=Math.max(r+U,"body"==z.context?t(n).height():M[0].scrollHeight),O.css({height:b}),g&&(r+U>w+D||d>w+D)&&(X=!0,setTimeout(function(){X=!1},300),j.scrollTop(Math.min(r+U-D,b-D))),Z=C,Q=D))},G.attachShow=function(t,e){tt.push({readOnly:t.prop("readonly"),el:t}),"inline"!==z.display&&(J&&t.is("input")&&t.prop("readonly",!0).on("mousedown.dw",function(t){t.preventDefault()}),z.showOnFocus&&t.on("focus.dw",function(){o||k(e,t)}),z.showOnTap&&(t.on("keydown.dw",function(n){32!=n.keyCode&&13!=n.keyCode||(n.preventDefault(),n.stopPropagation(),k(e,t))}),G.tap(t,function(){k(e,t)})))},G.select=function(){B&&G.hide(!1,"set")===!1||(G._fillValue(),F("onSelect",[G._value]))},G.cancel=function(){B&&G.hide(!1,"cancel")===!1||F("onCancel",[G._value])},G.clear=function(){F("onClear",[P]),B&&!G.live&&G.hide(!1,"clear"),G.setVal(null,!0)},G.enable=function(){z.disabled=!1,G._isInput&&K.prop("disabled",!1)},G.disable=function(){z.disabled=!0,G._isInput&&K.prop("disabled",!0)},G.show=function(n,a){var o;z.disabled||G._isVisible||(I!==!1&&("top"==z.display&&(I="slidedown"),"bottom"==z.display&&(I="slideup")),G._readValue(),F("onBeforeShow",[]),o='<div lang="'+z.lang+'" class="mbsc-'+z.theme+(z.baseTheme?" mbsc-"+z.baseTheme:"")+" dw-"+z.display+" "+(z.cssClass||"")+(G._isLiquid?" dw-liq":"")+(p?" mbsc-old":"")+(V?"":" dw-nobtn")+'"><div class="dw-persp">'+(B?'<div class="dwo"></div>':"")+"<div"+(B?' role="dialog" tabindex="-1"':"")+' class="dw'+(z.rtl?" dw-rtl":" dw-ltr")+'">'+("bubble"===z.display?'<div class="dw-arrw"><div class="dw-arrw-i"><div class="dw-arr"></div></div></div>':"")+'<div class="dwwr"><div aria-live="assertive" class="dw-aria dw-hidden"></div>'+(z.headerText?'<div class="dwv">'+(f(z.headerText)?z.headerText:"")+"</div>":"")+'<div class="dwcc">',o+=G._generateContent(),o+="</div>",V&&(o+='<div class="dwbc">',t.each(N,function(t,e){e=f(e)?G.buttons[e]:e,"set"===e.handler&&(e.parentClass="dwb-s"),"cancel"===e.handler&&(e.parentClass="dwb-c"),e.handler=f(e.handler)?G.handlers[e.handler]:e.handler,o+="<div"+(z.btnWidth?' style="width:'+100/N.length+'%"':"")+' class="dwbw '+(e.parentClass||"")+'"><div tabindex="0" role="button" class="dwb'+t+" dwb-e "+(e.cssClass===i?z.btnClass:e.cssClass)+(e.icon?" mbsc-ic mbsc-ic-"+e.icon:"")+'">'+(e.text||"")+"</div></div>"}),o+="</div>"),o+="</div></div></div></div>",P=t(o),O=t(".dw-persp",P),$=t(".dwo",P),R=t(".dwwr",P),A=t(".dwv",P),Y=t(".dw",P),S=t(".dw-aria",P),G._markup=P,G._header=A,G._isVisible=!0,H="orientationchange resize",G._markupReady(P),F("onMarkupReady",[P]),B?(t(e).on("keydown",T),z.scrollLock&&P.on("touchmove mousewheel wheel",function(t){q&&t.preventDefault()}),"Moz"!==c&&t("input,select,button",M).each(function(){this.disabled||t(this).addClass("dwtd").prop("disabled",!0)}),H+=" scroll",r.activeInstance=G,P.appendTo(M),u&&I&&!n&&P.addClass("dw-in dw-trans").on(v,function(){P.off(v).removeClass("dw-in dw-trans").find(".dw").removeClass("dw-"+I),_(a)}).find(".dw").addClass("dw-"+I)):K.is("div")&&!G._hasContent?K.html(P):P.insertAfter(K),F("onMarkupInserted",[P]),G.position(),j.on(H,D).on("focusin",E),P.on("selectstart mousedown",g).on("click",".dwb-e",g).on("keydown",".dwb-e",function(e){32==e.keyCode&&(e.preventDefault(),e.stopPropagation(),t(this).click())}).on("keydown",function(e){if(32==e.keyCode)e.preventDefault();else if(9==e.keyCode){var n=P.find('[tabindex="0"]').filter(function(){return this.offsetWidth>0||this.offsetHeight>0}),i=n.index(t(":focus",P)),a=n.length-1,o=0;e.shiftKey&&(a=0,o=-1),i===a&&(n.eq(o).focus(),e.preventDefault())}}),t("input",P).on("selectstart mousedown",function(t){t.stopPropagation()}),setTimeout(function(){t.each(N,function(e,n){G.tap(t(".dwb"+e,P),function(t){n=f(n)?G.buttons[n]:n,n.handler.call(this,t,G)},!0)}),z.closeOnOverlay&&G.tap($,function(){G.cancel()}),B&&!I&&_(a),P.on("touchstart mousedown",".dwb-e",w).on("touchend",".dwb-e",x),G._attachEvents(P)},300),F("onShow",[P,G._tempValue]))},G.hide=function(n,i,a){return!(!G._isVisible||!a&&!G._isValid&&"set"==i||!a&&F("onClose",[G._tempValue,i])===!1)&&(P&&("Moz"!==c&&t(".dwtd",M).each(function(){t(this).prop("disabled",!1).removeClass("dwtd")}),u&&B&&I&&!n&&!P.hasClass("dw-trans")?P.addClass("dw-out dw-trans").find(".dw").addClass("dw-"+I).on(v,function(){C(n)}):C(n),j.off(H,D).off("focusin",E)),void(B&&(t(e).off("keydown",T),delete r.activeInstance)))},G.ariaMessage=function(t){S.html(""),setTimeout(function(){S.html(t)},100)},G.isVisible=function(){return G._isVisible},G.setVal=b,G._generateContent=b,G._attachEvents=b,G._readValue=b,G._fillValue=b,G._markupReady=b,G._processSettings=b,G._presetLoad=function(t){t.buttons=t.buttons||("inline"!==t.display?["set","cancel"]:[]),t.headerText=t.headerText===i?"inline"!==t.display&&"{value}":t.headerText},G.tap=function(t,e,n){var i,a,o;z.tap&&t.on("touchstart.dw",function(t){n&&t.preventDefault(),i=d(t,"X"),a=d(t,"Y"),o=!1}).on("touchmove.dw",function(t){(Math.abs(d(t,"X")-i)>20||Math.abs(d(t,"Y")-a)>20)&&(o=!0)}).on("touchend.dw",function(t){var n=this;o||(t.preventDefault(),e.call(n,t)),r.tapped=!0,setTimeout(function(){r.tapped=!1},500)}),t.on("click.dw",function(t){r.tapped||e.call(this,t),t.preventDefault()})},G.destroy=function(){G.hide(!0,!1,!0),t.each(tt,function(t,e){e.el.off(".dw").prop("readonly",e.readOnly)}),G._destroy()},G.init=function(n){G._init(n),G._isLiquid="liquid"===(z.layout||(/top|bottom/.test(z.display)?"liquid":"")),G._processSettings(),K.off(".dw"),I=!p&&z.animate,N=z.buttons||[],B="inline"!==z.display,J=z.showOnFocus||z.showOnTap,j=t("body"==z.context?e:z.context),M=t(z.context),G.context=j,G.live=!0,t.each(N,function(t,e){if("ok"==e||"set"==e||"set"==e.handler)return G.live=!1,!1}),G.buttons.set={text:z.setText,handler:"set"},G.buttons.cancel={text:G.live?z.closeText:z.cancelText,handler:"cancel"},G.buttons.clear={text:z.clearText,handler:"clear"},G._isInput=K.is("input"),V=N.length>0,G._isVisible&&G.hide(!0,!1,!0),F("onInit",[]),B?(G._readValue(),G._hasContent||G.attachShow(K)):G.show(),K.on("change.dw",function(){G._preventChange||G.setVal(K.val(),!0,!1),G._preventChange=!1})},G.buttons={},G.handlers={set:G.select,cancel:G.cancel,clear:G.clear},G._value=null,G._isValid=!0,G._isVisible=!1,z=G.settings,F=G.trigger,y||G.init(m)},r.classes.Frame.prototype._defaults={lang:"zh",setText:"Set",selectedText:"Selected",closeText:"Close",cancelText:"Cancel",clearText:"Clear",disabled:!1,closeOnOverlay:!0,showOnFocus:!1,showOnTap:!0,display:"bottom",scrollLock:!0,tap:!0,btnClass:"dwb",btnWidth:!0,focusOnClose:!m},r.themes.frame.mobiscroll={rows:2,showLabel:!1,headerText:!1,btnWidth:!1,selectedLineHeight:!0,selectedLineBorder:1,dateOrder:"MMddyy",weekDays:"min",checkIcon:"ion-ios7-checkmark-empty",btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5"},t(e).on("focus",function(){a&&(o=!0)}),t(n).on("mouseover mouseup mousedown click",function(t){if(r.tapped)return t.stopPropagation(),t.preventDefault(),!1})}(JQUERY,window,document),function(t,e,n,i){var a,o=t.mobiscroll,r=o.classes,s=o.util,l=s.jsPrefix,c=s.has3d,u=s.hasFlex,d=s.getCoord,h=s.constrain,f=s.testTouch;o.presetShort("scroller","Scroller",!1),r.Scroller=function(e,o,p){function m(e){!f(e,this)||a||H||L||C(this)||(e.preventDefault(),e.stopPropagation(),a=!0,I="clickpick"!=B.mode,K=t(".dw-ul",this),E(K),X=ot[tt]!==i,Z=X?S(K):rt[tt],z=d(e,"Y"),q=new Date,J=z,A(K,tt,Z,.001),I&&K.closest(".dwwl").addClass("dwa"),"mousedown"===e.type&&t(n).on("mousemove",v).on("mouseup",b))}function v(t){a&&I&&(t.preventDefault(),t.stopPropagation(),J=d(t,"Y"),(Math.abs(J-z)>3||X)&&(A(K,tt,h(Z+(z-J)/F,Q-1,G+1)),X=!0))}function b(e){if(a){var i,o,r=new Date-q,s=h(Math.round(Z+(z-J)/F),Q-1,G+1),l=s,u=K.offset().top;if(e.stopPropagation(),a=!1,"mouseup"===e.type&&t(n).off("mousemove",v).off("mouseup",b),c&&r<300?(i=(J-z)/r,o=i*i/B.speedUnit,J-z<0&&(o=-o)):o=J-z,X)l=h(Math.round(Z-o/F),Q,G),r=i?Math.max(.1,Math.abs((l-s)/i)*B.timeUnit):.1;else{var d=Math.floor((J-u)/F),f=t(t(".dw-li",K)[d]),p=f.hasClass("dw-v"),m=I;if(r=.1,U("onValueTap",[f])!==!1&&p?l=d:m=!0,m&&p&&(f.addClass("dw-hl"),setTimeout(function(){f.removeClass("dw-hl")},100)),!V&&(B.confirmOnTap===!0||B.confirmOnTap[tt])&&f.hasClass("dw-sel"))return void it.select()}I&&O(K,tt,l,0,r,!0)}}function g(e){L=t(this),f(e,this)&&_(e,L.closest(".dwwl"),L.hasClass("dwwbp")?Y:j),"mousedown"===e.type&&t(n).on("mouseup",y)}function y(e){L=null,H&&(clearInterval(nt),H=!1),"mouseup"===e.type&&t(n).off("mouseup",y)}function w(e){38==e.keyCode?_(e,t(this),j):40==e.keyCode&&_(e,t(this),Y)}function x(){H&&(clearInterval(nt),H=!1)}function T(e){if(!C(this)){e.preventDefault(),e=e.originalEvent||e;var n=e.deltaY||e.wheelDelta||e.detail,i=t(".dw-ul",this);E(i),A(i,tt,h(((n<0?-20:20)-st[tt])/F,Q-1,G+1)),clearTimeout(W),W=setTimeout(function(){O(i,tt,Math.round(rt[tt]),n>0?1:2,.1)},200)}}function _(t,e,n){if(t.stopPropagation(),t.preventDefault(),!H&&!C(e)&&!e.hasClass("dwa")){H=!0;var i=e.find(".dw-ul");E(i),clearInterval(nt),nt=setInterval(function(){n(i)},B.delay),n(i)}}function C(e){if(t.isArray(B.readonly)){var n=t(".dwwl",N).index(e);return B.readonly[n]}return B.readonly}function D(e){var n='<div class="dw-bf">',i=lt[e],a=1,o=i.labels||[],r=i.values||[],s=i.keys||r;return t.each(r,function(t,e){a%20===0&&(n+='</div><div class="dw-bf">'),n+='<div role="option" aria-selected="false" class="dw-li dw-v" data-val="'+s[t]+'"'+(o[t]?' aria-label="'+o[t]+'"':"")+' style="height:'+F+"px;line-height:"+F+'px;"><div class="dw-i"'+(et>1?' style="line-height:'+Math.round(F/et)+"px;font-size:"+Math.round(F/et*.8)+'px;"':"")+">"+e+"</div></div>",a++}),n+="</div>"}function E(e){V=e.closest(".dwwl").hasClass("dwwms"),Q=t(".dw-li",e).index(t(V?".dw-li":".dw-v",e).eq(0)),G=Math.max(Q,t(".dw-li",e).index(t(V?".dw-li":".dw-v",e).eq(-1))-(V?B.rows-("scroller"==B.mode?1:3):0)),tt=t(".dw-ul",N).index(e)}function k(t){var n=B.headerText;return n?"function"==typeof n?n.call(e,t):n.replace(/\{value\}/i,t):""}function S(t){return Math.round(-s.getPosition(t,!0)/F)}function M(t,e){clearTimeout(ot[e]),delete ot[e],t.closest(".dwwl").removeClass("dwa")}function A(t,e,n,i,a){var o=-n*F,r=t[0].style;o==st[e]&&ot[e]||(st[e]=o,c?(r[l+"Transition"]=s.prefix+"transform "+(i?i.toFixed(3):0)+"s ease-out",r[l+"Transform"]="translate3d(0,"+o+"px,0)"):r.top=o+"px",ot[e]&&M(t,e),i&&a&&(t.closest(".dwwl").addClass("dwa"),ot[e]=setTimeout(function(){M(t,e)},1e3*i)),rt[e]=n)}function P(e,n,i,a,o){var r,s=t('.dw-li[data-val="'+e+'"]',n),l=t(".dw-li",n),c=l.index(s),u=l.length;if(a)E(n);else if(!s.hasClass("dw-v")){for(var d=s,f=s,p=0,m=0;c-p>=0&&!d.hasClass("dw-v");)p++,d=l.eq(c-p);for(;c+m<u&&!f.hasClass("dw-v");)m++,f=l.eq(c+m);(m<p&&m&&2!==i||!p||c-p<0||1==i)&&f.hasClass("dw-v")?(s=f,c+=m):(s=d,c-=p)}return r=s.hasClass("dw-sel"),o&&(a||(t(".dw-sel",n).removeAttr("aria-selected"),s.attr("aria-selected","true")),t(".dw-sel",n).removeClass("dw-sel"),s.addClass("dw-sel")),{selected:r,v:a?h(c,Q,G):c,val:s.hasClass("dw-v")?s.attr("data-val"):null}}function $(e,n,a,o,r){U("validate",[N,n,e,o])!==!1&&(t(".dw-ul",N).each(function(a){var s=t(this),l=s.closest(".dwwl").hasClass("dwwms"),c=a==n||n===i,u=P(it._tempWheelArray[a],s,o,l,!0),d=u.selected;d&&!c||(it._tempWheelArray[a]=u.val,A(s,a,u.v,c?e:.1,!!c&&r))}),U("onValidated",[]),it._tempValue=B.formatValue(it._tempWheelArray,it),it.live&&(it._hasValue=a||it._hasValue,R(a,a,0,!0)),it._header.html(k(it._tempValue)),a&&U("onChange",[it._tempValue]))}function O(e,n,i,a,o,r){i=h(i,Q,G),it._tempWheelArray[n]=t(".dw-li",e).eq(i).attr("data-val"),A(e,n,i,o,r),setTimeout(function(){$(o,n,!0,a,r)},10)}function Y(t){var e=rt[tt]+1;O(t,tt,e>G?Q:e,1,.1)}function j(t){var e=rt[tt]-1;O(t,tt,e<Q?G:e,2,.1)}function R(t,e,n,i,a){it._isVisible&&!i&&$(n),it._tempValue=B.formatValue(it._tempWheelArray,it),a||(it._wheelArray=it._tempWheelArray.slice(0),it._value=it._hasValue?it._tempValue:null),t&&(U("onValueFill",[it._hasValue?it._tempValue:"",e]),it._isInput&&at.val(it._hasValue?it._tempValue:""),e&&(it._preventChange=!0,at.change()))}var N,L,I,F,V,B,W,U,H,X,z,q,J,Z,Q,G,K,tt,et,nt,it=this,at=t(e),ot={},rt={},st={},lt=[];r.Frame.call(this,e,o,!0),it.setVal=it._setVal=function(n,a,o,r,s){it._hasValue=null!==n&&n!==i,it._tempWheelArray=t.isArray(n)?n.slice(0):B.parseValue.call(e,n,it)||[],R(a,o===i?a:o,s,!1,r)},it.getVal=it._getVal=function(t){var e=it._hasValue||t?it[t?"_tempValue":"_value"]:null;return s.isNumeric(e)?+e:e},it.setArrayVal=it.setVal,it.getArrayVal=function(t){return t?it._tempWheelArray:it._wheelArray},it.setValue=function(t,e,n,i,a){it.setVal(t,e,a,i,n)},it.getValue=it.getArrayVal,it.changeWheel=function(e,n,a){if(N){var o=0,r=e.length;t.each(B.wheels,function(s,l){if(t.each(l,function(s,l){return t.inArray(o,e)>-1&&(lt[o]=l,t(".dw-ul",N).eq(o).html(D(o)),r--,!r)?(it.position(),$(n,i,a),!1):void o++}),!r)return!1})}},it.getValidCell=P,it.scroll=A,it._generateContent=function(){var e,n="",a=0;return t.each(B.wheels,function(o,r){n+='<div class="mbsc-w-p dwc'+("scroller"!=B.mode?" dwpm":" dwsc")+(B.showLabel?"":" dwhl")+'"><div class="dwwc"'+(B.maxWidth?"":' style="max-width:600px;"')+">"+(u?"":'<table class="dw-tbl" cellpadding="0" cellspacing="0"><tr>'),t.each(r,function(t,o){lt[a]=o,e=o.label!==i?o.label:t,n+="<"+(u?"div":"td")+' class="dwfl" style="'+(o.hide?"display:none;":"")+(B.fixedWidth?"width:"+(B.fixedWidth[a]||B.fixedWidth)+"px;":(B.minWidth?"min-width:"+(B.minWidth[a]||B.minWidth)+"px;":"min-width:"+B.width+"px;")+(B.maxWidth?"max-width:"+(B.maxWidth[a]||B.maxWidth)+"px;":""))+'"><div class="dwwl dwwl'+a+(o.multiple?" dwwms":"")+'">'+("scroller"!=B.mode?'<div class="dwb-e dwwb dwwbp '+(B.btnPlusClass||"")+'" style="height:'+F+"px;line-height:"+F+'px;"><span>+</span></div><div class="dwb-e dwwb dwwbm '+(B.btnMinusClass||"")+'" style="height:'+F+"px;line-height:"+F+'px;"><span>&ndash;</span></div>':"")+'<div class="dwl">'+e+'</div><div tabindex="0" aria-live="off" aria-label="'+e+'" role="listbox" class="dwww"><div class="dww" style="height:'+B.rows*F+'px;"><div class="dw-ul" style="margin-top:'+(o.multiple?"scroller"==B.mode?0:F:B.rows/2*F-F/2)+'px;">',n+=D(a)+'</div></div><div class="dwwo"></div></div><div class="dwwol"'+(B.selectedLineHeight?' style="height:'+F+"px;margin-top:-"+(F/2+(B.selectedLineBorder||0))+'px;"':"")+"></div></div>"+(u?"</div>":"</td>"),a++}),n+=(u?"":"</tr></table>")+"</div></div>"}),n},it._attachEvents=function(t){t.on("keydown",".dwwl",w).on("keyup",".dwwl",x).on("touchstart mousedown",".dwwl",m).on("touchmove",".dwwl",v).on("touchend",".dwwl",b).on("touchstart mousedown",".dwwb",g).on("touchend",".dwwb",y),B.mousewheel&&t.on("wheel mousewheel",".dwwl",T)},it._markupReady=function(t){N=t,$()},it._fillValue=function(){it._hasValue=!0,R(!0,!0,0,!0)},it._readValue=function(){var t=at.val()||"";""!==t&&(it._hasValue=!0),it._tempWheelArray=it._hasValue&&it._wheelArray?it._wheelArray.slice(0):B.parseValue.call(e,t,it)||[],R()},it._processSettings=function(){B=it.settings,U=it.trigger,F=B.height,et=B.multiline,it._isLiquid="liquid"===(B.layout||(/top|bottom/.test(B.display)&&1==B.wheels.length?"liquid":"")),B.formatResult&&(B.formatValue=B.formatResult),et>1&&(B.cssClass=(B.cssClass||"")+" dw-ml"),"scroller"!=B.mode&&(B.rows=Math.max(3,B.rows))},it._selectedValues={},p||it.init(o)},r.Scroller.prototype={_hasDef:!0,_hasTheme:!0,_hasLang:!0,_hasPreset:!0,_class:"scroller",_defaults:t.extend({},r.Frame.prototype._defaults,{minWidth:80,height:40,rows:2,multiline:1,delay:300,readonly:!1,showLabel:!0,confirmOnTap:!0,wheels:[],mode:"scroller",preset:"",speedUnit:.0012,timeUnit:.08,formatValue:function(t){return t.join(" ")},parseValue:function(e,n){var a,o,r=[],s=[],l=0;return null!==e&&e!==i&&(r=(e+"").split(" ")),t.each(n.settings.wheels,function(e,n){t.each(n,function(e,n){o=n.keys||n.values,a=o[0],t.each(o,function(t,e){if(r[l]==e)return a=e,!1}),s.push(a),l++})}),s}})},o.themes.scroller=o.themes.frame}(JQUERY,window,document),function(t,e){var n=t.mobiscroll;n.datetime={defaults:{shortYearCutoff:"+10",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["S","M","T","W","T","F","S"],amText:"am",pmText:"pm",getYear:function(t){return t.getFullYear()},getMonth:function(t){return t.getMonth()},getDay:function(t){return t.getDate()},getDate:function(t,e,n,i,a,o,r){return new Date(t,e,n,i||0,a||0,o||0,r||0)},getMaxDayOfMonth:function(t,e){return 32-new Date(t,e,32).getDate()},getWeekNumber:function(t){t=new Date(t),t.setHours(0,0,0),t.setDate(t.getDate()+4-(t.getDay()||7));var e=new Date(t.getFullYear(),0,1);return Math.ceil(((t-e)/864e5+1)/7)}},formatDate:function(e,i,a){if(!i)return null;var o,r,s=t.extend({},n.datetime.defaults,a),l=function(t){for(var n=0;o+1<e.length&&e.charAt(o+1)==t;)n++,o++;return n},c=function(t,e,n){var i=""+e;if(l(t))for(;i.length<n;)i="0"+i;return i},u=function(t,e,n,i){return l(t)?i[e]:n[e]},d="",h=!1;for(o=0;o<e.length;o++)if(h)"'"!=e.charAt(o)||l("'")?d+=e.charAt(o):h=!1;else switch(e.charAt(o)){case"d":d+=c("d",s.getDay(i),2);break;case"D":d+=u("D",i.getDay(),s.dayNamesShort,s.dayNames);break;case"o":d+=c("o",(i.getTime()-new Date(i.getFullYear(),0,0).getTime())/864e5,3);break;case"m":d+=c("m",s.getMonth(i)+1,2);break;case"M":d+=u("M",s.getMonth(i),s.monthNamesShort,s.monthNames);break;case"y":r=s.getYear(i),d+=l("y")?r:(r%100<10?"0":"")+r%100;break;case"h":var f=i.getHours();d+=c("h",f>12?f-12:0===f?12:f,2);break;case"H":d+=c("H",i.getHours(),2);break;case"i":d+=c("i",i.getMinutes(),2);break;case"s":d+=c("s",i.getSeconds(),2);break;case"a":d+=i.getHours()>11?s.pmText:s.amText;break;case"A":d+=i.getHours()>11?s.pmText.toUpperCase():s.amText.toUpperCase();break;case"'":l("'")?d+="'":h=!0;break;default:d+=e.charAt(o)}return d},parseDate:function(e,i,a){var o=t.extend({},n.datetime.defaults,a),r=o.defaultValue||new Date;if(!e||!i)return r;if(i.getTime)return i;i="object"==typeof i?i.toString():i+"";var s,l=o.shortYearCutoff,c=o.getYear(r),u=o.getMonth(r)+1,d=o.getDay(r),h=-1,f=r.getHours(),p=r.getMinutes(),m=0,v=-1,b=!1,g=function(t){var n=s+1<e.length&&e.charAt(s+1)==t;return n&&s++,n},y=function(t){g(t);var e="@"==t?14:"!"==t?20:"y"==t?4:"o"==t?3:2,n=new RegExp("^\\d{1,"+e+"}"),a=i.substr(T).match(n);return a?(T+=a[0].length,parseInt(a[0],10)):0},w=function(t,e,n){var a,o=g(t)?n:e;for(a=0;a<o.length;a++)if(i.substr(T,o[a].length).toLowerCase()==o[a].toLowerCase())return T+=o[a].length,a+1;return 0},x=function(){T++},T=0;for(s=0;s<e.length;s++)if(b)"'"!=e.charAt(s)||g("'")?x():b=!1;else switch(e.charAt(s)){case"d":d=y("d");break;case"D":w("D",o.dayNamesShort,o.dayNames);break;case"o":h=y("o");break;case"m":u=y("m");break;case"M":u=w("M",o.monthNamesShort,o.monthNames);break;case"y":c=y("y");break;case"H":f=y("H");break;case"h":f=y("h");break;case"i":p=y("i");break;case"s":m=y("s");break;case"a":v=w("a",[o.amText,o.pmText],[o.amText,o.pmText])-1;break;case"A":v=w("A",[o.amText,o.pmText],[o.amText,o.pmText])-1;break;case"'":g("'")?x():b=!0;break;default:x()}if(c<100&&(c+=(new Date).getFullYear()-(new Date).getFullYear()%100+(c<=("string"!=typeof l?l:(new Date).getFullYear()%100+parseInt(l,10))?0:-100)),h>-1)for(u=1,d=h;;){var _=32-new Date(c,u-1,32).getDate();if(d<=_)break;u++,d-=_}f=v==-1?f:v&&f<12?f+12:v||12!=f?f:0;var C=o.getDate(c,u-1,d,f,p,m);return o.getYear(C)!=c||o.getMonth(C)+1!=u||o.getDay(C)!=d?r:C}},n.formatDate=n.datetime.formatDate,n.parseDate=n.datetime.parseDate}(JQUERY),function(t,e){var n=t.mobiscroll,i=n.datetime,a=new Date,o={startYear:a.getFullYear()-100,endYear:a.getFullYear()+1,separator:" ",dateFormat:"mm/dd/yy",dateOrder:"mmddy",timeWheels:"hhiiA",timeFormat:"hh:ii A",dayText:"Day",monthText:"Month",yearText:"Year",hourText:"Hours",minuteText:"Minutes",ampmText:"&nbsp;",secText:"Seconds",nowText:"Now"},r=function(a){function r(t,n,i){return J[n]!==e?+t[J[n]]:Z[n]!==e?Z[n]:i!==e?i:Q[n](st)}function s(t,e,n,i,a){t.push({values:n,keys:e,label:i,hide:a})}function l(t,e,n,i){return Math.min(i,Math.floor(t/e)*e+n)}function c(t){return U.getYear(t)}function u(t){return U.getMonth(t)}function d(t){return U.getDay(t)}function h(t){var e=t.getHours();return e=ot&&e>=12?e-12:e,l(e,ct,mt,gt)}function f(t){return l(t.getMinutes(),ut,vt,yt)}function p(t){return l(t.getSeconds(),dt,bt,wt)}function m(t){return t.getMilliseconds()}function v(t){return at&&t.getHours()>11?1:0}function b(t){if(null===t)return t;var e=r(t,"y"),n=r(t,"m"),i=Math.min(r(t,"d",1),U.getMaxDayOfMonth(e,n)),a=r(t,"h",0);return U.getDate(e,n,i,r(t,"a",0)?a+12:a,r(t,"i",0),r(t,"s",0),r(t,"u",0))}function g(t,e,n){return Math.floor((n-e)/t)*t+e}function y(t,e){var n,i,a=!1,o=!1,r=0,s=0;if(ft=b(E(ft)),pt=b(E(pt)),w(t))return t;if(t<ft&&(t=ft),t>pt&&(t=pt),n=t,i=t,2!==e)for(a=w(n);!a&&n<pt;)n=new Date(n.getTime()+864e5),a=w(n),r++;if(1!==e)for(o=w(i);!o&&i>ft;)i=new Date(i.getTime()-864e5),o=w(i),s++;return 1===e&&a?n:2===e&&o?i:s<=r&&o?i:n}function w(t){return!(t<ft)&&(!(t>pt)&&(!!x(t,K)||!x(t,G)))}function x(t,e){var n,i,a;if(e)for(i=0;i<e.length;i++)if(n=e[i],a=n+"",!n.start)if(n.getTime){if(t.getFullYear()==n.getFullYear()&&t.getMonth()==n.getMonth()&&t.getDate()==n.getDate())return!0}else if(a.match(/w/i)){if(a=+a.replace("w",""),a==t.getDay())return!0}else if(a=a.split("/"),a[1]){if(a[0]-1==t.getMonth()&&a[1]==t.getDate())return!0}else if(a[0]==t.getDate())return!0;return!1}function T(t,e,n,i,a,o,r){var s,l,c;if(t)for(s=0;s<t.length;s++)if(l=t[s],c=l+"",!l.start)if(l.getTime)U.getYear(l)==e&&U.getMonth(l)==n&&(o[U.getDay(l)-1]=r);else if(c.match(/w/i))for(c=+c.replace("w",""),Y=c-i;Y<a;Y+=7)Y>=0&&(o[Y]=r);else c=c.split("/"),c[1]?c[0]-1==n&&(o[c[1]-1]=r):o[c[0]-1]=r}function _(n,i,a,o,r,s,c,u,d){var h,f,p,m,v,b,g,y,w,x,T,_,C,E,k,S,M,A,P={},$={h:ct,i:ut,s:dt,a:1},O=U.getDate(r,s,c),Y=["a","h","i","s"];n&&(t.each(n,function(t,e){e.start&&(e.apply=!1,h=e.d,f=h+"",p=f.split("/"),h&&(h.getTime&&r==U.getYear(h)&&s==U.getMonth(h)&&c==U.getDay(h)||!f.match(/w/i)&&(p[1]&&c==p[1]&&s==p[0]-1||!p[1]&&c==p[0])||f.match(/w/i)&&O.getDay()==+f.replace("w",""))&&(e.apply=!0,P[O]=!0))}),t.each(n,function(n,o){if(C=0,E=0,T=0,_=e,b=!0,g=!0,k=!1,o.start&&(o.apply||!o.d&&!P[O])){for(m=o.start.split(":"),v=o.end.split(":"),x=0;x<3;x++)m[x]===e&&(m[x]=0),v[x]===e&&(v[x]=59),m[x]=+m[x],v[x]=+v[x];for(m.unshift(m[0]>11?1:0),v.unshift(v[0]>11?1:0),ot&&(m[1]>=12&&(m[1]=m[1]-12),v[1]>=12&&(v[1]=v[1]-12)),x=0;x<i;x++)X[x]!==e&&(y=l(m[x],$[Y[x]],V[Y[x]],B[Y[x]]),w=l(v[x],$[Y[x]],V[Y[x]],B[Y[x]]),S=0,M=0,A=0,ot&&1==x&&(S=m[0]?12:0,M=v[0]?12:0,A=X[0]?12:0),b||(y=0),g||(w=B[Y[x]]),(b||g)&&y+S<X[x]+A&&X[x]+A<w+M&&(k=!0),X[x]!=y&&(b=!1),X[x]!=w&&(g=!1));if(!d)for(x=i+1;x<4;x++)m[x]>0&&(C=$[a]),v[x]<B[Y[x]]&&(E=$[a]);k||(y=l(m[i],$[a],V[a],B[a])+C,w=l(v[i],$[a],V[a],B[a])-E,b&&(T=D(u,y,B[a],0)),g&&(_=D(u,w,B[a],1))),(b||g||k)&&(d?t(".dw-li",u).slice(T,_).addClass("dw-v"):t(".dw-li",u).slice(T,_).removeClass("dw-v"))}}))}function C(e,n){return t(".dw-li",e).index(t('.dw-li[data-val="'+n+'"]',e))}function D(e,n,i,a){return n<0?0:n>i?t(".dw-li",e).length:C(e,n)+a}function E(n,i){var a=[];return null===n||n===e?n:(t.each(["y","m","d","a","h","i","s","u"],function(t,o){J[o]!==e&&(a[J[o]]=Q[o](n)),i&&(Z[o]=Q[o](n))}),a)}function k(t){var e,n,i,a=[];if(t){for(e=0;e<t.length;e++)if(n=t[e],n.start&&n.start.getTime)for(i=new Date(n.start);i<=n.end;)a.push(new Date(i.getFullYear(),i.getMonth(),i.getDate())),i.setDate(i.getDate()+1);else a.push(n);return a}return t}var S,M=t(this),A={};if(M.is("input")){switch(M.attr("type")){case"date":S="yy-mm-dd";break;case"datetime":S="yy-mm-ddTHH:ii:ssZ";break;case"datetime-local":S="yy-mm-ddTHH:ii:ss";break;case"month":S="yy-mm",A.dateOrder="mmyy";break;case"time":S="HH:ii:ss"}var P=M.attr("min"),$=M.attr("max");P&&(A.minDate=i.parseDate(S,P)),$&&(A.maxDate=i.parseDate(S,$))}var O,Y,j,R,N,L,I,F,V,B,W=t.extend({},a.settings),U=t.extend(a.settings,n.datetime.defaults,o,A,W),H=0,X=[],z=[],q=[],J={},Z={},Q={y:c,m:u,d:d,h:h,i:f,s:p,u:m,a:v},G=U.invalid,K=U.valid,tt=U.preset,et=U.dateOrder,nt=U.timeWheels,it=et.match(/D/),at=nt.match(/a/i),ot=nt.match(/h/),rt="datetime"==tt?U.dateFormat+U.separator+U.timeFormat:"time"==tt?U.timeFormat:U.dateFormat,st=new Date,lt=U.steps||{},ct=lt.hour||U.stepHour||1,ut=lt.minute||U.stepMinute||1,dt=lt.second||U.stepSecond||1,ht=lt.zeroBased,ft=U.minDate||new Date(U.startYear,0,1),pt=U.maxDate||new Date(U.endYear,11,31,23,59,59),mt=ht?0:ft.getHours()%ct,vt=ht?0:ft.getMinutes()%ut,bt=ht?0:ft.getSeconds()%dt,gt=g(ct,mt,ot?11:23),yt=g(ut,vt,59),wt=g(ut,vt,59);if(S=S||rt,tt.match(/date/i)){for(t.each(["y","m","d"],function(t,e){O=et.search(new RegExp(e,"i")),O>-1&&q.push({o:O,v:e})}),q.sort(function(t,e){return t.o>e.o?1:-1}),t.each(q,function(t,e){J[e.v]=t}),N=[],Y=0;Y<3;Y++)if(Y==J.y){for(H++,R=[],j=[],L=U.getYear(ft),I=U.getYear(pt),O=L;O<=I;O++)j.push(O),R.push((et.match(/yy/i)?O:(O+"").substr(2,2))+(U.yearSuffix||""));s(N,j,R,U.yearText,!1)}else if(Y==J.m){for(H++,R=[],j=[],O=0;O<12;O++){var xt=et.replace(/[dy]/gi,"").replace(/mm/,(O<9?"0"+(O+1):O+1)+(U.monthSuffix||"")).replace(/m/,O+1+(U.monthSuffix||""));j.push(O),R.push(xt.match(/MM/)?xt.replace(/MM/,'<span class="dw-mon">'+U.monthNames[O]+"</span>"):xt.replace(/M/,'<span class="dw-mon">'+U.monthNamesShort[O]+"</span>"))}s(N,j,R,U.monthText,!1)}else if(Y==J.d){for(H++,R=[],j=[],O=1;O<32;O++)j.push(O),R.push((et.match(/dd/i)&&O<10?"0"+O:O)+(U.daySuffix||""));s(N,j,R,U.dayText,U.monthSelect)}z.push(N)}if(tt.match(/time/i)){for(F=!0,q=[],t.each(["h","i","s","a"],function(t,e){t=nt.search(new RegExp(e,"i")),t>-1&&q.push({o:t,v:e})}),q.sort(function(t,e){return t.o>e.o?1:-1}),t.each(q,function(t,e){J[e.v]=H+t}),N=[],Y=H;Y<H+4;Y++)if(Y==J.h){for(H++,R=[],j=[],O=mt;O<(ot?12:24);O+=ct)j.push(O),R.push(ot&&0===O?12:nt.match(/hh/i)&&O<10?"0"+O:O);s(N,j,R,U.hourText)}else if(Y==J.i){for(H++,R=[],j=[],O=vt;O<60;O+=ut)j.push(O),R.push(nt.match(/ii/)&&O<10?"0"+O:O);s(N,j,R,U.minuteText)}else if(Y==J.s){for(H++,R=[],j=[],O=bt;O<60;O+=dt)j.push(O),R.push(nt.match(/ss/)&&O<10?"0"+O:O);s(N,j,R,U.secText)}else if(Y==J.a){H++;var Tt=nt.match(/A/);s(N,[0,1],Tt?[U.amText.toUpperCase(),U.pmText.toUpperCase()]:[U.amText,U.pmText],U.ampmText)}z.push(N)}return a.getVal=function(t){return a._hasValue||t?b(a.getArrayVal(t)):null},a.setDate=function(t,e,n,i,o){a.setArrayVal(E(t),e,o,i,n)},a.getDate=a.getVal,a.format=rt,a.order=J,a.handlers.now=function(){a.setDate(new Date,!1,.3,!0,!0)},a.buttons.now={text:U.nowText,handler:"now"},G=k(G),K=k(K),V={y:ft.getFullYear(),m:0,d:1,h:mt,i:vt,s:bt,a:0},B={y:pt.getFullYear(),m:11,d:31,h:gt,i:yt,s:wt,a:1},{wheels:z,headerText:!!U.headerText&&function(){return i.formatDate(rt,b(a.getArrayVal(!0)),U)},formatValue:function(t){return i.formatDate(S,b(t),U)},parseValue:function(t){return t||(Z={}),E(t?i.parseDate(S,t,U):U.defaultValue||new Date,!!t&&!!t.getTime)},validate:function(n,i,o,s){var l=y(b(a.getArrayVal(!0)),s),c=E(l),u=r(c,"y"),d=r(c,"m"),h=!0,f=!0;t.each(["y","m","d","a","h","i","s"],function(i,a){if(J[a]!==e){var o=V[a],s=B[a],l=31,p=r(c,a),m=t(".dw-ul",n).eq(J[a]);if("d"==a&&(l=U.getMaxDayOfMonth(u,d),s=l,it&&t(".dw-li",m).each(function(){var e=t(this),n=e.data("val"),i=U.getDate(u,d,n).getDay(),a=et.replace(/[my]/gi,"").replace(/dd/,(n<10?"0"+n:n)+(U.daySuffix||"")).replace(/d/,n+(U.daySuffix||""));t(".dw-i",e).html(a.match(/DD/)?a.replace(/DD/,'<span class="dw-day">'+U.dayNames[i]+"</span>"):a.replace(/D/,'<span class="dw-day">'+U.dayNamesShort[i]+"</span>"))})),h&&ft&&(o=Q[a](ft)),f&&pt&&(s=Q[a](pt)),"y"!=a){var v=C(m,o),b=C(m,s);t(".dw-li",m).removeClass("dw-v").slice(v,b+1).addClass("dw-v"),"d"==a&&t(".dw-li",m).removeClass("dw-h").slice(l).addClass("dw-h")}if(p<o&&(p=o),p>s&&(p=s),h&&(h=p==o),f&&(f=p==s),"d"==a){var g=U.getDate(u,d,1).getDay(),y={};T(G,u,d,g,l,y,1),T(K,u,d,g,l,y,0),t.each(y,function(e,n){n&&t(".dw-li",m).eq(e).removeClass("dw-v")})}}}),F&&t.each(["a","h","i","s"],function(i,o){var l=r(c,o),h=r(c,"d"),f=t(".dw-ul",n).eq(J[o]);J[o]!==e&&(_(G,i,o,c,u,d,h,f,0),_(K,i,o,c,u,d,h,f,1),X[i]=+a.getValidCell(l,f,s).val)}),a._tempWheelArray=c}}};t.each(["date","time","datetime"],function(t,e){n.presets.scroller[e]=r})}(JQUERY),function(t){t.each(["date","time","datetime"],function(e,n){t.mobiscroll.presetShort(n)})}(JQUERY),function(t){t.mobiscroll.i18n.zh=t.extend(t.mobiscroll.i18n.zh,{setText:"确定",cancelText:"取消",clearText:"清除",selectedText:"选择",dateFormat:"yy-mm-dd",dateOrder:"yymmdd",dayNames:["周日","周一","周二","周三","周四","周五","周六"],dayNamesShort:["日","一","二","三","四","五","六"],dayNamesMin:["日","一","二","三","四","五","六"],dayText:"日",hourText:"时",minuteText:"分",monthNames:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],monthNamesShort:["一","二","三","四","五","六","七","八","九","十","十一","十二"],monthText:"月",secText:"秒",timeFormat:"HH:ii:ss",timeWheels:"HHiiss",yearText:"年",nowText:"当前",pmText:"下午",amText:"上午",dateText:"日",timeText:"时间",calendarText:"日历",closeText:"关闭",fromText:"开始时间",toText:"结束时间",wholeText:"合计",fractionText:"分数",unitText:"单位",labels:["年","月","日","小时","分钟","秒",""],labelsShort:["年","月","日","点","分","秒",""],startText:"开始",stopText:"停止",resetText:"重置",lapText:"圈",hideText:"隐藏",backText:"背部",undoText:"复原"})}(JQUERY),function(t,e){var n=t.mobiscroll,i=n.util,a=i.isString,o={batch:40,inputClass:"",invalid:[],rtl:!1,showInput:!0,groupLabel:"Groups",checkIcon:"checkmark",dataText:"text",dataValue:"value",dataGroup:"group",dataDisabled:"disabled"};n.presetShort("select"),n.presets.scroller.select=function(n){function r(){var n,i,a,o,r,s=0,l=0,c={};at={},ot={},C=[],g=[],nt.length=0,J?t.each(R.data,function(t,s){o=s[R.dataText],r=s[R.dataValue],i=s[R.dataGroup],a={value:r,text:o,index:t},at[r]=a,C.push(a),Z&&(c[i]===e?(n={text:i,value:l,options:[],index:l},ot[l]=n,c[i]=l,g.push(n),l++):n=ot[c[i]],K&&(a.index=n.options.length),a.group=c[i],n.options.push(a)),s[R.dataDisabled]&&nt.push(r)}):Z?t("optgroup",F).each(function(e){ot[e]={text:this.label,value:e,options:[],index:e},g.push(ot[e]),t("option",this).each(function(t){a={value:this.value,text:this.text,index:K?t:s++,group:e},at[this.value]=a,C.push(a),ot[e].options.push(a),this.disabled&&nt.push(this.value)})}):t("option",F).each(function(t){a={value:this.value,text:this.text,index:t},at[this.value]=a,C.push(a),this.disabled&&nt.push(this.value)}),C.length&&(v=C[0].value),tt&&(C=[],s=0,t.each(ot,function(e,n){r="__group"+e,a={text:n.text,value:r,group:e,index:s++},at[r]=a,C.push(a),nt.push(a.value),t.each(n.options,function(t,e){e.index=s++,C.push(e)})}))}function s(t,n,i,a,o,r,s){var l,c,u=[],d=[],h=i[a]!==e?i[a].index:0,f=Math.max(0,h-N),p=Math.min(n.length-1,f+2*N);if(P[o]!==f||$[o]!==p){for(l=f;l<=p;l++)d.push(n[l].text),u.push(n[l].value);A[o]=!0,O[o]=f,Y[o]=p,c={multiple:r,values:d,keys:u,label:s},I?t[0][o]=c:t[o]=[c]}else A[o]=!1}function l(t){s(t,g,ot,b,x,!1,R.groupLabel)}function c(t){s(t,K?ot[b].options:C,at,E,D,V,H)}function u(){var t=[[]];return G&&l(t),
c(t),t}function d(n){V&&(n&&a(n)&&(n=n.split(",")),t.isArray(n)&&(n=n[0])),E=n!==e&&null!==n&&""!==n&&at[n]?n:v,G&&(b=at[E]?at[E].group:null,S=b)}function h(t,e){var i=t?n._tempWheelArray:n._hasValue?n._wheelArray:null;return i?R.group&&e?i:i[D]:null}function f(){var t,e,i=[],a=0;if(V){e=[];for(a in it)i.push(at[a]?at[a].text:""),e.push(a);t=i.join(", ")}else e=E,t=at[E]?at[E].text:"";n._tempValue=e,_.val(t),F.val(e)}function p(t){var e=t.attr("data-val"),n=t.hasClass("dw-msel");return V&&t.closest(".dwwl").hasClass("dwwms")?(t.hasClass("dw-v")&&(n?(t.removeClass(X).removeAttr("aria-selected"),delete it[e]):i.objectToArray(it).length<B&&(t.addClass(X).attr("aria-selected","true"),it[e]=e)),!1):void(t.hasClass("dw-w-gr")&&(w=t.attr("data-val")))}var m,v,b,g,y,w,x,T,_,C,D,E,k,S,M,A={},P={},$={},O={},Y={},j=t.extend({},n.settings),R=t.extend(n.settings,o,j),N=R.batch,L=R.layout||(/top|bottom/.test(R.display)?"liquid":""),I="liquid"==L,F=t(this),V=R.multiple||F.prop("multiple"),B=i.isNumeric(R.multiple)?R.multiple:1/0,W=this.id+"_dummy",U=t('label[for="'+this.id+'"]').attr("for",W),H=R.label!==e?R.label:U.length?U.text():F.attr("name"),X="dw-msel mbsc-ic mbsc-ic-"+R.checkIcon,z=R.readonly,q=R.data,J=!!q,Z=J?!!R.group:t("optgroup",F).length,Q=R.group,G=Z&&Q&&Q.groupWheel!==!1,K=Z&&Q&&G&&Q.clustered===!0,tt=Z&&(!Q||Q.header!==!1&&!K),et=F.val()||[],nt=[],it={},at={},ot={};if(R.invalid.length||(R.invalid=nt),G?(x=0,D=1):(x=-1,D=0),V)for(F.prop("multiple",!0),et&&a(et)&&(et=et.split(",")),T=0;T<et.length;T++)it[et[T]]=et[T];return r(),d(F.val()),t("#"+W).remove(),F.next().is("input.mbsc-control")?_=F.off(".mbsc-form").next().removeAttr("tabindex"):(_=t('<input type="text" id="'+W+'" class="mbsc-control mbsc-control-ev '+R.inputClass+'" readonly />'),R.showInput&&_.insertBefore(F)),n.attachShow(_.attr("placeholder",R.placeholder||"")),F.addClass("dw-hsel").attr("tabindex",-1).closest(".ui-field-contain").trigger("create"),f(),n.setVal=function(t,e,o,r,s){V&&(t&&a(t)&&(t=t.split(",")),it=i.arrayToObject(t),t=t?t[0]:null),n._setVal(t,e,o,r,s)},n.getVal=function(t,e){return V?i.objectToArray(it):h(t,e)},n.refresh=function(){r(),P={},$={},R.wheels=u(),P[x]=O[x],$[x]=Y[x],P[D]=O[D],$[D]=Y[D],m=!0,d(E),n._tempWheelArray=G?[b,E]:[E],n._isVisible&&n.changeWheel(G?[x,D]:[D],0,!0)},n.getValues=n.getVal,n.getValue=h,{width:50,layout:L,headerText:!1,anchor:_,confirmOnTap:!G||[!1,!0],formatValue:function(t){var e,n,i=[];if(V){for(e in it)i.push(at[e]?at[e].text:"");return i.join(", ")}return n=t[D],at[n]?at[n].text:""},parseValue:function(t){return d(t===e?F.val():t),G?[b,E]:[E]},onValueTap:p,onValueFill:f,onBeforeShow:function(){V&&R.counter&&(R.headerText=function(){var e=0;return t.each(it,function(){e++}),(e>1?R.selectedPluralText||R.selectedText:R.selectedText).replace(/{count}/,e)}),d(F.val()),G&&(n._tempWheelArray=[b,E]),n.refresh()},onMarkupReady:function(e){e.addClass("dw-select"),t(".dwwl"+x,e).on("mousedown touchstart",function(){clearTimeout(M)}),t(".dwwl"+D,e).on("mousedown touchstart",function(){y||clearTimeout(M)}),tt&&t(".dwwl"+D,e).addClass("dw-select-gr"),V&&(e.addClass("dwms"),t(".dwwl",e).on("keydown",function(e){32==e.keyCode&&(e.preventDefault(),e.stopPropagation(),p(t(".dw-sel",this)))}).eq(D).attr("aria-multiselectable","true"),k=t.extend({},it))},validate:function(i,a,o,r){var s,u,d=[],h=n.getArrayVal(!0),f=h[x],p=h[D],v=t(".dw-ul",i).eq(x),T=t(".dw-ul",i).eq(D);if(P[x]>1&&t(".dw-li",v).slice(0,2).removeClass("dw-v").addClass("dw-fv"),$[x]<g.length-2&&t(".dw-li",v).slice(-2).removeClass("dw-v").addClass("dw-fv"),P[D]>1&&t(".dw-li",T).slice(0,2).removeClass("dw-v").addClass("dw-fv"),$[D]<(K?ot[f].options:C).length-2&&t(".dw-li",T).slice(-2).removeClass("dw-v").addClass("dw-fv"),!m&&(E=p,G&&(b=at[E].group,a!==e&&a!==x||(b=+h[x],y=!1,b!==S?(E=ot[b].options[0].value,P[D]=null,$[D]=null,y=!0,R.readonly=[!1,!0]):R.readonly=z)),Z&&(/__group/.test(E)||w)&&(E=ot[at[w||E].group].options[0].value,p=E,w=!1),n._tempWheelArray=G?[f,p]:[p],G&&(l(R.wheels),A[x]&&d.push(x)),c(R.wheels),A[D]&&d.push(D),clearTimeout(M),M=setTimeout(function(){d.length&&(m=!0,y=!1,S=b,P[x]=O[x],$[x]=Y[x],P[D]=O[D],$[D]=Y[D],n._tempWheelArray=G?[f,E]:[E],n.changeWheel(d,0,a!==e)),G&&(a===D&&n.scroll(v,x,n.getValidCell(b,v,r,!1,!0).v,.1),n._tempWheelArray[x]=b),R.readonly=z},a===e?100:1e3*o),d.length))return!y;if(a===e&&V){u=it,s=0,t(".dwwl"+D+" .dw-li",i).removeClass(X).removeAttr("aria-selected");for(s in u)t(".dwwl"+D+' .dw-li[data-val="'+u[s]+'"]',i).addClass(X).attr("aria-selected","true")}tt&&t('.dw-li[data-val^="__group"]',i).addClass("dw-w-gr"),t.each(R.invalid,function(e,n){t('.dw-li[data-val="'+n+'"]',T).removeClass("dw-v dw-fv")}),m=!1},onValidated:function(){E=n._tempWheelArray[D]},onClear:function(e){it={},_.val(""),t(".dwwl"+D+" .dw-li",e).removeClass(X).removeAttr("aria-selected")},onCancel:function(){!n.live&&V&&(it=t.extend({},k))},onDestroy:function(){_.hasClass("mbsc-control")||_.remove(),F.removeClass("dw-hsel").removeAttr("tabindex")}}}}(JQUERY),!function(t,e){function n(e,n,i){return this.length?(t.each(this,function(t,a){a[e](n,i)}),this):this}function i(e,n){var i=t(e),o=this;return i.length?(t.each(i,function(e,i){var r=t(i).data(a);if(r){if(n===e||n===i.id)return o[0]=r,o.length=1,o.index=n,!1;o[e]=r,o.length=e+1}}),o):o}var a="arm.modal",o=["close","open","toggle"];e._modalBuild=t._modalBuild=function(n,i,r,s,l,c,u){var d=n,h=t.isPlainObject(r)?r:{},f=i||{},p="object"!=typeof d||!d.selector;/string|number/i.test(typeof r)&&(h={content:r},"string"!=typeof u&&(u=r));var m=u||f.initMethod||"open";h.initMethod=t.inArray(m,o)!==-1?m:"toggle";var s=s||"";h=t.extend(!0,{},f,h),h.parent=t(h.parent||"body")[0],p&&(d=t(e.tpl(s).render(h)),h.hooks&&"function"==typeof h.hooks._insertDom?h.hooks._insertDom.call(d,h):d.appendTo(h.parent));var v=[];return d.each(function(n){var i=t(this),o=i.data(a);o?o[h.initMethod]():(p||i.appendTo(h.parent),this.id||(this.id=e.utils.generateGUID("arm-modal")),i.data(a,o=new l(this,h,p,c))),v[n]=o}),d[c]=v,d},i.prototype.close=function(t,e){return n.call(this,"close",t,e)},i.prototype.open=function(t,e){return n.call(this,"open",t,e)},i.prototype.toggle=function(t,e){return n.call(this,"toggle",t,e)},e.getModal=function(t,e){return new i(t,e)},e.getModal.openIndex=19880815}(JQUERY,window.arm),!function(t,e){function n(n,a,o,r){this.option=t.extend(!0,{},i,a||{}),this.target=n,this._isFromTpl=o,this.type=r||"",this.modalId=this.target.id,this.transitionEnd=e.support.transition&&e.support.transition.end,this.active=this.transitioning=0,this._init()}var i={durationIn:200,durationOut:150,modalClassName:"ui-modal",modalClassTypes:[],activeClass:"show",animateInClass:"ani-zoomin",animateOutClass:"ani-zoomout",closeOnConfirm:!0,closeOnCancel:!0,closeOnButton:!0,clickClose:!1,stayTime:0,onTimeout:t.noop,btnsHandler:[],mask:{},closeOnMask:!1,scrollable:!1,cssFix:t.noop,beforeOpen:t.noop,afterClose:t.noop,hooks:{_init:t.noop,_insertDom:t.noop}},a=["init","action","cancel","confirm","open","opened","close","closed"];t.each(a,function(n,a){i[e.toHump("on "+a)]=i.hooks["_"+a]=t.noop}),n.prototype={_event:function(e){return t.inArray(e,a)!==-1?this.type+":"+e:this.type+":"+a.join(" "+this.type+":")},_init:function(){var n=this,i=n.option.modalClassName;t.each(n.option.modalClassTypes,function(t,e){className=" "+n.option.modalClassName+"-"+e,i+=className}),t(n.target).addClass(i),n.option.cssFix.call(n,n.option);var o=t(n.target).find("[data-modal-close]"),r=t(n.target).find('[data-role="button"]'),s=t(n.target).find("[data-modal-cancel]"),l=t(n.target).find("[data-modal-confirm]");t.each(a,function(i,a){t(n.target).on(n._event(a),function(t){n.option.hooks["_"+a].call(n,t),n.option[e.toHump("on "+a)].call(this,t,n)})}),r.each(function(e,i){t(i).tap(function(i){i.preventDefault(),i.index=e,t(n.target).trigger(t.Event(n._event("action"),{relatedTarget:this}),[n]),t.isFunction(n.option.btnsHandler)&&n.option.btnsHandler.call(this,i,n),t.isArray(n.option.btnsHandler)&&t.isFunction(n.option.btnsHandler[e])&&n.option.btnsHandler[e].call(this,i,n),n.option.closeOnButton&&!t(this).is("[data-modal-cancel],[data-modal-confirm]")&&n.close()})}),s.tap(function(e){e.preventDefault(),t(n.target).trigger(t.Event(n._event("cancel"),{relatedTarget:this}),[n]),n.option.closeOnCancel&&n.close()}),l.tap(function(e){e.preventDefault(),t(n.target).trigger(t.Event(n._event("confirm"),{relatedTarget:this}),[n]),n.option.closeOnConfirm&&n.close()}),o.tap(function(t){t.preventDefault(),n.close()}),n.option.clickClose&&t(n.target).tap(function(t){t.preventDefault(),n.close()}),t(n.target).trigger(t.Event(n._event("init")),[n]),n[n.option.initMethod]()},_complete:function(e){var n=this,i=n._event("open"===e?"opened":"closed");t(n.target).trigger(t.Event(i),[n]),n.transitioning=0,"close"===e?(t(n.target).removeClass(n.option.activeClass),n._isFromTpl&&t(n.target).remove(),n.mask&&(n.mask.remove(n.openid),n.mask=!1),n.option.afterClose.call(n)):n.option.stayTime>0&&!n.closeTimer&&(n.closeTimer=setTimeout(function(){n.close(),n.option.onTimeout.call(n)},this.option.stayTime))},toggle:function(t,e){var n=this;n.active?n.close(t,e):n.open(t,e)},open:function(n,i){var a=this;a.active||(a.openid=e.getModal.openIndex++,a.option.beforeOpen.call(a),a.active=!0,a.transitioning=1,a.target.style[e.utils.prefixStyle("animationDuration")]="",t(a.target).removeClass(e.utils.getClassFn("ani-[\\S]+")).css("z-index",e.getModal.openIndex++),a.option.mask&&!a.mask&&(a.mask=e.mask(t.extend(!0,{zIndex:a.openid},a.option.mask),a.option.parent)),t(a.target).trigger(t.Event(a._event("open")),[a]),a._isFromTpl&&a.option.parent&&!/body/i.test(a.option.parent.tagName)&&t(a.target).css("position","absolute"),a.option.closeOnMask&&a.mask&&(a.mask.masklayer.tap(function(t){a.close()}),t(a.target).tap(function(t){t.touch.target==a.target&&a.close()})),/ani-[\S]+in/gi.test(n)||("number"==typeof n&&"number"!=typeof i&&(i=n),n=a.option.animateInClass),"number"!=typeof i&&(i=a.option.durationIn),a.target.style[e.utils.prefixStyle("animationDuration")]=i+"ms",t(a.target).addClass(n+" "+a.option.activeClass),a.transitionEnd?t(a.target).one(a.transitionEnd,function(){a._complete("open")}).emulateTransitionEnd(i):a._complete("open"))},close:function(n,i){var a=this;a.active&&(a.active=!1,a.transitioning=1,a.target.style[e.utils.prefixStyle("animationDuration")]="",t(a.target).removeClass(e.utils.getClassFn("ani-[\\S]+")),t(a.target).trigger(t.Event(a._event("close")),[a]),/ani-[\S]+out/gi.test(n)||("number"==typeof n&&"number"!=typeof i&&(i=n),n=a.option.animateOutClass),"number"!=typeof i&&(i=a.option.durationOut),a.target.style[e.utils.prefixStyle("animationDuration")]=i+"ms",t(a.target).addClass(n),a.transitionEnd?t(a.target).one(a.transitionEnd,function(){a._complete("close")}).emulateTransitionEnd(i):a._complete("close"))}},e._modalRegister=function(i,a,o,r){e[i]=function(t,r){return e._modalBuild(this,a,t,o,n,i,r)},arm.pt._var||(t[i]=e[i]),r||(e.pt[i]=function(t,r){return e._modalBuild(this.$||this,a,t,o,n,i,r)},arm.pt._var||(t.fn[i]=e.pt[i]))};var o='<div class="ui-modal ui-page"><div class="ui-modal-cnt"><header class="ui-modal-hd ui-border-b"><h3 class="ui-modal-title"><%d.title%></h3><i class="ui-modal-close" data-modal-close></i></header><div class="ui-modal-bd"><div><%d.content%></div></div></div></div>',r={title:"新窗口",content:"",modalClassName:"ui-page"};e._modalRegister("modal",r,o)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-actionsheet"><div class="ui-actionsheet-cnt"><%# if(d.title){ %><h4><%d.title%></h4><%# } %><%# if(d.buttons&&d.buttons.length){ %><%# for (var i = 0; i < d.buttons.length; i++) { %><button class="ui-actionsheet-btn<%# if (i == d.btnSelect) { %> select<%#}%>" type="button" data-role="button"<%# if(d.buttons.length===1||i===d.buttons.length-1){%> data-modal-confirm<%#}%><%# if(d.buttons.length>1&&i===d.buttons.length-2){%> data-modal-cancel<%#}%>><%d.buttons[i]%></button><%# } %><%# } %><button type="button" data-modal-cancel>取消</button></div>',i={title:"您将要做什么操作？",buttons:['<span class="ui-txt-red">删除</span>'],btnSelect:0,modalClassName:"ui-actionsheet",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",durationIn:250,durationOut:150};e._modalRegister("actionsheet",i,n)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-modal"><div class="ui-modal-cnt"><%# if(d.buttons&&d.buttons.length){ %><div class="ui-modal-ft ui-btn-group ui-border-t"><%# for (var i = 0; i < d.buttons.length; i++) { %><button class="ui-actionsheet-btn<%# if (i == d.btnSelect) { %> select<%#}%>" type="button" data-role="button"<%# if(d.buttons.length===1||i===d.buttons.length-1){%> data-modal-confirm<%#}%><%# if(d.buttons.length>1&&i===d.buttons.length-2){%> data-modal-cancel<%#}%>><%d.buttons[i]%></button><%# } %></div><%# } %><div class="ui-modal-bd"><h4><%d.title%></h4><div><%d.content%></div></div></div></div>',i={modalClassName:"ui-alert",title:"提示",content:"Alert",buttons:["确定"],btnSelect:0};e._modalRegister("alert",i,n)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-modal"><div class="ui-modal-cnt"><header class="ui-modal-hd ui-border-b"><h3 class="ui-modal-title"><%d.title%></h3><i class="ui-modal-close" data-modal-close></i></header><%# if(d.buttons&&d.buttons.length){ %><div class="ui-modal-ft ui-border-t ui-btn-group"><%# for (var i = 0; i < d.buttons.length; i++) { %><button class="ui-actionsheet-btn<%# if (i == d.btnSelect) { %> select<%#}%>" type="button" data-role="button"<%# if(d.buttons.length===1||i===d.buttons.length-1){%> data-modal-confirm<%#}%><%# if(d.buttons.length>1&&i===d.buttons.length-2){%> data-modal-cancel<%#}%>><%d.buttons[i]%></button><%# } %></div><%# } %><div class="ui-modal-bd"><div><%d.content%></div></div></div></div>',i={modalClassName:"ui-dialog",title:"新会话",content:"Dialog is a type of Modal.",buttons:["取消","确定"],btnSelect:0};e._modalRegister("dialog",i,n)}(JQUERY,window.arm),!function(t,e){function n(t,e,n,i){e.hasClass("ani-loading")||(e.addClass("ani-loading"),t.one("load",function(t){e.removeClass("ani-loading")}),t[0].src=n)}var i='<div class="ui-modal"><div class="ui-modal-cnt"><header class="ui-modal-hd ui-border-b"><h3 class="ui-modal-title"><%d.title%></h3><i class="ui-modal-close" data-modal-close></i></header><div class="ui-modal-ft ui-btn-group ui-border-t"><button onclick="history.back()"><i class="ui-icon-prev"></i></button><button data-iframe-action="reload"><i class="ui-icon-refresh"></i></button><button onclick="history.forward()"><i class="ui-icon-next"></i></button></div><div class="ui-modal-bd"><iframe width="100%" height="100%" marginheight="0" marginwidth="0" frameborder="0" scrolling="yes"></iframe></div></div></div>',a={title:"新页面",modalClassName:"ui-iframe",animateInClass:"ani-fadeinB",animateOutClass:"ani-fadeoutB",durationIn:150,durationOut:100,hooks:{_init:function(){var e=this,i=t("iframe",e.target),a=t(".ui-icon-refresh",e.target);n(i,a,e.option.content),t("[data-iframe-action]",e.target).tap(function(){var e=t(this).attr("data-iframe-action"),o=i[0].src;"reload"===e&&n(i,a,o)})}}};e._modalRegister("iframe",a,i)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-loading-block"><div class="ui-loading-cnt"><%# if(d.icon){ %><i class="<%d.icon%>"></i><%# } %><%# if(d.content){ %><p><%d.content%></p><%# } %></div></div>',i={modalClassName:"ui-modal-loading",animateInClass:"ani-fadein",animateOutClass:"ani-fadeout",icon:"ui-loading-bright",inline:0,afterIcon:1,blank:0,clickCancel:!1,content:"",hooks:{_init:function(){var e=this.option,n=this;e.theme&&t(this.target).addClass("ui-loading-theme-"+e.theme),e.blank&&t(this.target).addClass("ui-loading-blank"),e.afterIcon||t("p",this.target).prependTo(t(".ui-loading-cnt",this.target)),e.inline&&t(this.target).addClass("ui-loading-inline"),e.clickCancel&&t(this.target).tap(function(){n.close(),t.isFunction(e.clickCancel)&&e.clickCancel.call(n)})}}};e._modalRegister("loading",i,n)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-poptips"><div class="ui-poptips-cnt"><i></i><%d.content%></div></div>',i={content:"提示",mask:!1,stayTime:2e3,durationIn:200,durationOut:400,modalClassName:"ui-poptips",animateInClass:"ani-fadein",animateOutClass:"ani-fadeout",clickClose:!0,tipIsColor:!1,tipIsBlock:!1,tipNoIcon:!0,tipLevel:"primary",tipPosition:"center",hooks:{_init:function(){var e="",n=this,i=n.option;i.tipIsColor&&(e+=i.modalClassName+"-color"),i.tipNoIcon&&(e+=" "+i.modalClassName+"-noicon"),i.tipLevel&&(e+=" "+i.modalClassName+"-"+i.tipLevel),i.tipPosition&&!i.tipIsBlock&&(e+=" "+i.modalClassName+"-"+i.tipPosition),i.tipIsBlock&&(e+=" "+i.modalClassName+"-block"),t(n.target).addClass(e)}}};e._modalRegister("poptips",i,n)}(JQUERY,window.arm),!function(t,e){var n='<div class="ui-tips"><div class="ui-tips-cnt"><i></i><span><%d.content%></span></div></div>',i={content:"请稍后...",modalClassName:"ui-tips",iconClass:"ui-icon-info",textClass:"",wrapper:".ui-container",insertType:"html",mask:!1,isBlock:!1,hooks:{_insertDom:function(e){e.isBlock&&this.addClass(e.modalClassName+"-block"),this.find("i").addClass(e.iconClass),this.find("span").addClass(e.textClass),t(e.wrapper)[e.insertType](this)}}};e._modalRegister("tips",i,n)}(JQUERY,window.arm),function(t,e){function n(t){t.preventDefault()}function i(){g||(t(document).on(e.touchEvents.touchMove,n),g=!0)}function a(){t(document).off(e.touchEvents.touchMove,n),g=!1}function o(t,e){y[t]=e}function r(e){return t.extend(!0,{},y)[e]}function s(e){var n;return n=t.isPlainObject(e)?t.extend(!0,{},e):t.isArray(n)?e.slice(0):e}var l=3,c={1:"pullDown","-1":"pullUp"},u=1.8,d=1.7,h=["before","ready","action","error","success","complete","empty"],f=300,p=3e5,m=e.utils.ease.circular,v={className:"ui-pullaction-wrap",height:"auto",pullDown:{html:{before:'<i class="ui-icon-more"></i> 下拉刷新...',ready:'<i class="ui-icon-more"></i> 释放刷新...',action:'<i class="ui-icon-change ui-icon-changing"></i> 正在刷新...',error:'<i class="ui-icon-warn"></i> 刷新失败...',success:'<i class="ui-icon-success"></i> 刷新成功...'},size:40},pullUp:{html:{before:'<i class="ui-icon-more"></i> 上拉更多...',ready:'<i class="ui-icon-more"></i> 释放加载...',action:'<i class="ui-icon-change ui-icon-changing"></i> 正在加载...',error:'<i class="ui-icon-warn"></i> 加载失败...',success:'<i class="ui-icon-success"></i> 加载成功...'},size:50},template:"",stepRatio:d,target:null,clickLoad:!1,loadBtn:{html:{before:'<i class="ui-icon-more"></i> 点击加载...',action:'<i class="ui-icon-change ui-icon-changing"></i> 正在加载...',error:'<i class="ui-icon-warn"></i> 点击重试...',success:'<i class="ui-icon-more"></i> 点击加载...',empty:'<i class="ui-icon-info"></i> 没有了...'},target:null},fnAjaxSettings:t.noop,fnDataFilter:t.noop,ajaxUrl:"",ajaxDataProp:"data",otherParams:{},toStringData:!1,startIndex:1,pageIndex:1,pageRows:10,accuracy:u,speed:f,ease:m,initRefresh:!1,autoRefresh:!1,interval:p,goTop:0,goTopHtml:'<i class="ui-icon-gototop"></i>',pullDownBefore:t.noop,pullUpBefore:t.noop,pullDownReady:t.noop,pullUpReady:t.noop,pullDownAction:t.noop,pullUpAction:t.noop,pullDownError:t.noop,pullUpError:t.noop,pullDownSuccess:t.noop,pullUpSuccess:t.noop,callback:t.noop},b=e.support.transition&&e.support.transition.end,g=!1,y={},w=function(n,i){this.$element=t(n).addClass("ui-pullaction-scroller"),this.options=t.extend(!0,{},v,i||{}),this.id=e.utils.generateGUID("pullaction"),this._init(),this.getData=function(){return r(this.id)}};w.prototype._init=function(){var e=this.options;this.$wrapper=t('<div class="ui-pullaction" id="'+this.id+'" />').addClass(e.className),this.$inner=t('<div class="ui-pullaction-inner" />'),this.$parent=this.$element.parent(),this.$pullDown=t('<div class="ui-pullaction-down" />'),this.$pullUp=t('<div class="ui-pullaction-up" />'),this.$loadBtn=t('<div class="ui-pullaction-loadbtn" />'),this.$goTop=t('<div class="ui-pullaction-gotop" />'),this.$element.wrap(this.$inner),this.$inner.wrap(this.$wrapper),this.$wrapper.append(this.$pullUp),this.$wrapper.prepend(this.$pullDown),this.$target=this.$element,t(e.target,this.$element).length&&(this.$target=t(e.target,this.$element)),e.clickLoad&&(t(e.loadBtn.target,this.$element).length&&(this.$loadBtn=t(e.loadBtn.target,this.$element)),this.$inner.append(this.$loadBtn.hide())),e.goTop&&e.goTop>0&&this.$wrapper.append(this.$goTop.html(e.goTopHtml)),this.ajaxData={},this.status={pulling:!1,translating:!1,distance:0,action:0,code:-1,ajax:!1,text:"init"},this._transition(0),this._transform(0),this._bindEvents(),this._setHeight(),e.initRefresh&&!e.autoRefresh&&this.action(1),this._autoRefresh()},w.prototype._bindEvents=function(){var n=this,a=this.options,o=a.goTop&&a.goTop>0?a.goTop:0;t(window).on("resize",function(t){n._setHeight()}),this.$target.on("change",function(t){n._setHeight()}),this.$inner.on("scroll",function(e){n.status.scrollTop=t(this).scrollTop(),console.log(o,n.status.scrollTop),n.$goTop.toggleClass("block",o&&n.status.scrollTop>o),n._cancelPull(e)}),this.$wrapper.touch("drag",function(t){n._pulling(t)}).on(e.touchEvents.touchEnd,function(t){n._pullEnd(t)}).on(e.touchEvents.touchCancel,function(t){n._pullEnd(t)}).on(e.touchEvents.touchStart,function(t){i(),n._pullStart(t)}),this.$loadBtn.on("click",function(t){t.preventDefault(),n._ajax(-1)}),this.$goTop.on("click",function(t){t.preventDefault(),n.$inner.scrollTop(0)})},w.prototype._autoRefresh=function(){var t=this,e=this.options,n=function(){return function(){if(e.autoRefresh)return t.action(1),setTimeout(function(){t.status.timer=n()},e.interval||p)}}();return this.status.timer=n()},w.prototype.stopAutoRefresh=function(){this.options.autoRefresh=!1},w.prototype.startAutoRefresh=function(){this.options.autoRefresh=!0,this._autoRefresh()},w.prototype._setHeight=function(t,e){var n=this.options;this.height=t||n.height,t&&"auto"!==t||(this.height=this.$parent.height()),this.scrollHeight=this.$element.height()-this.height,this.$wrapper.height(this.height),void 0!==e&&this.$inner.scrollTop(e)},w.prototype.action=function(t){var e=this.options;this.status.action=t,this.status.autoAction=!0;var n=this._getState(),i=e[n.name].size*n.action,a=(e.speed||f)*Math.abs(i)/25;this._pullCallback(n.name,2),this._translate(i,a)},w.prototype._pullStart=function(t){this.options;this.status.scrollTop=this.$inner.scrollTop(),this.status._distance=this.status.distance||0},w.prototype._pullable=function(e){var n=this.options,i=this.status.scrollTop,a=this.scrollHeight,o=e.touch.moveY,r=e.touch.curY;if(r<l||r+l>t(window).height()||i>l&&i+l<a)return!1;if(!this.status.action)if(i<l&&o>0)this.status.action=1;else{if(!(i+l>a&&o<0))return!1;this.status.action=-1}return!(o*this.status.action<0||this.status.action==-1&&n.clickLoad)&&(!this.status.ajax||!(this.status.action>0&&i>l||this.status.action<0&&i<a-l))},w.prototype._pulling=function(t){var e=this.options,n=(this.status.scrollTop,this.status._distance),i=(this.scrollHeight,t.touch.moveY);if(!this.status.autoAction){if(!this._pullable(t))return this._cancelPull(t);this.status.pulling=!0,this.$inner.css("overflow","hidden");var a=i/(e.accuracy||u)+n;this._translate(a)}},w.prototype._cancelPull=function(t){this.options;this._pullEnd(t,!0)},w.prototype._pullEnd=function(t,e){var n=this.options;if(this.$inner.css("overflow","auto"),a(),this.status.distance){this.status.pulling=!1;var i=this._getState(),o=0,r=0;i&&!e&&(i.code&&(o=n[i.name].size*i.action),r=n.speed||f),this._translate(o,r)}},w.prototype._transition=function(t){var n=this.options,i=n.ease||m||"linear";this.$pullUp[0].style[e.utils.prefixStyle("transition")]="transform",this.$pullDown[0].style[e.utils.prefixStyle("transition")]="transform",this.$inner[0].style[e.utils.prefixStyle("transition")]="transform",this.$inner[0].style[e.utils.style.transitionTimingFunction]=i,this.$pullDown[0].style[e.utils.style.transitionTimingFunction]=i,this.$pullUp[0].style[e.utils.style.transitionTimingFunction]=i,this.$inner[0].style[e.utils.style.transitionDuration]=t+"ms",this.$pullDown[0].style[e.utils.style.transitionDuration]=t+"ms",this.$pullUp[0].style[e.utils.style.transitionDuration]=t+"ms"},w.prototype._transform=function(t){this.options;this.$inner[0].style[e.utils.style.transform]="translateY("+t+"px)",this.$pullDown[0].style[e.utils.style.transform]="translateY("+t+"px)",this.$pullUp[0].style[e.utils.style.transform]="translateY("+t+"px)"},w.prototype._translate=function(t,e){var n=this,t=(this.options,t||0),e=e||0;this.status.distance=t,this.status.translating=!0,this._transition(e),b&&e?this.$inner.one(b,function(){n._translateEnd()}).emulateTransitionEnd(e):n._translateEnd(),this._transform(t)},w.prototype._translateEnd=function(){this.options;this.status.translating=!1,this._setState()},w.prototype._getState=function(){var t=this.options,e=this.status.distance,n=Math.abs(e),i=this.status.action;if(i){var a=c[i],o=t[a],r=o.size*t.stepRatio,s=0;return n>r&&(s=1),(n===o.size||this.status.ajax)&&(s=2),{name:a,delta:n,code:s,action:i}}},w.prototype._pullCallback=function(n,i){var a=this.options,o=a[n],r=h[i],s=n+e.utils.firstUpper(r),l=o.html[r];if(l&&this["$"+n].html(l),a.clickLoad){var c=a.loadBtn.html[r];c&&this.$loadBtn.html(c)}t.isFunction(a[s])&&a[s].call(this,s),this.status.code=i,5===i&&(this.status.code=-1)},w.prototype._setState=function(){var t=(this.options,this._getState());if(t&&(2!=t.code||this.status.ajax||this._ajax(t.action),this.status.code!==t.code))return this._pullCallback(t.name,t.code),t},w.prototype._ajax=function(e){var n=this,i=this.options,e=e||1;if(this.status&&this.status.ajax)return i.jqXHR;if(e==-1&&this.status.lastpage)return this._error(-1,this._getState());this.status.ajax=!0,this.ajaxData=this.ajaxData||{},"pageIndex"in this.ajaxData||(this.ajaxData.pageIndex=i.pageIndex),"pageRows"in this.ajaxData||(this.ajaxData.pageRows=i.pageRows),1==e&&(this.status.lastpage=!1,this.ajaxData.pageIndex=i.startIndex);var a=t.extend(!0,{},i.otherParams,this.ajaxData);if(t.isFunction(i.fnAjaxSettings))var r=i.fnAjaxSettings(a,i.ajaxUrl,e);var s=t.extend({url:i.ajaxUrl,type:"GET",dataType:"json",data:a},t.isPlainObject(r)?r:{},{success:function(a,r,s){n.status.timeStamp=Number(new Date),o(n.id,t.extend(!0,{},a)),t.isFunction(i.fnDataFilter)&&(a=i.fnDataFilter(a,r,s)||a),n._ajaxSuccess(a,r,s,e)},error:function(t,i,a){n._ajaxError(t,i,a,e)},complete:function(t,i){n._ajaxComplete(t,i,e)}});return t.extend(!0,s.data,i.otherParams),i.toStringData&&window.JSON&&(s.data=JSON.stringify(s.data)),i.jqXHR=t.ajax(s)},w.prototype._ajaxSuccess=function(t,e,n,i){this.options;this.status.text=e,this.status.ajaxCode=n.status,this._pullCallback(c[i],4),this._callback(t,i)},w.prototype._ajaxError=function(t,e,n,i){this.options;this.status.text=e,this.status.ajaxCode=t.status,this._pullCallback(c[i],3)},w.prototype._ajaxComplete=function(t,e,n){this.options;this.status.text=e,this.status.ajaxCode=t.status,this.status.ajax=!1,this._pullCallback(c[n],5),this.status.action=0,this.status.autoAction=!1,this._cancelPull()},w.prototype._error=function(e,n){var i=this.options;this.status.action=0,this._cancelPull(),e==-1&&this._pullCallback(c[-1],6),t.isFunction(i.fail)&&i.fail.apply(this,arguments)},w.prototype._callback=function(e,n){var i=this.options,a=e.length;i.clickLoad&&this.$loadBtn.show(),i.ajaxDataProp&&"object"==typeof e&&e[i.ajaxDataProp]&&(a=e[i.ajaxDataProp].length),a>=this.ajaxData.pageRows?this.ajaxData.pageIndex++:this.status.lastpage=!0,t.isFunction(i.done)&&i.done.call(this,s(e),n),this._render(s(e),this.$target,n)},w.prototype._render=function(n,i,a){var o=this.options,r=a==-1?this.scrollHeight+this.height/3:0;return t.isFunction(o.onRender)?o.onRender(n,i,a):(e.tpl(o.template).render(n,function(t){i["1"==a?"html":"append"](t)}),void this._setHeight(null,r))},e.register("pullaction",w,!0)}(JQUERY,window.arm),function(t,e,n,i){function a(t,n){this.wrapper="string"==typeof t?e.querySelector(t):t,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0};for(var i in n)this.options[i]=n[i];this.translateZ=this.options.HWCompositing&&s.hasPerspective?" translateZ(0)":"",this.options.useTransition=s.hasTransition&&this.options.useTransition,this.options.useTransform=s.hasTransform&&this.options.useTransform,this.options.eventPassthrough=this.options.eventPassthrough===!0?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY="vertical"!=this.options.eventPassthrough&&this.options.scrollY,this.options.scrollX="horizontal"!=this.options.eventPassthrough&&this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing="string"==typeof this.options.bounceEasing?s.ease[this.options.bounceEasing]||s.ease.circular:this.options.bounceEasing,this.options.resizePolling=void 0===this.options.resizePolling?60:this.options.resizePolling,this.options.tap===!0&&(this.options.tap="tap"),this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()}function o(t){var e=this,t=t||{};if("function"==typeof this){if(e=$(t.wrapper),e.length<1)return;e=e[0]}if(e.children)return new a(e,t)}var r=i.rAF,s=i.utils;a.prototype={_init:function(){this._initEvents()},destroy:function(){this._initEvents(!0),this._execEvent("destroy")},_transitionEnd:function(t){t.target==this.scroller&&this.isInTransition&&!this.pullrefreshEnd&&(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))},_start:function(t){if((1==s.eventType[t.type]||0===t.button)&&this.enabled&&(!this.initiated||s.eventType[t.type]===this.initiated)){this.options.preventDefault&&!s.isBadAndroid&&!s.preventDefaultException(t.target,this.options.preventDefaultException);var e,i=t.touches?t.touches[0]:t;this.initiated=s.eventType[t.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this._transitionTime(),this.startTime=s.getTime(),this.options.useTransition&&this.isInTransition?(this.isInTransition=!1,e=this.getComputedPosition(),this._translate(n.round(e.x),n.round(e.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=i.pageX,this.pointY=i.pageY,this._execEvent("beforeScrollStart"),$(this.wrapper).trigger($.Event("pullrefresh:start"))}},_move:function(t){if(this.enabled&&s.eventType[t.type]===this.initiated){this.options.preventDefault&&t.preventDefault();var e,i,a,o,r=t.touches?t.touches[0]:t,l=r.pageX-this.pointX,c=r.pageY-this.pointY,u=s.getTime();if(this.pointX=r.pageX,this.pointY=r.pageY,this.distX+=l,this.distY+=c,a=n.abs(this.distX),o=n.abs(this.distY),!(u-this.endTime>300&&a<10&&o<10)){if(this.directionLocked||this.options.freeScroll||(a>o+this.options.directionLockThreshold?this.directionLocked="h":o>=a+this.options.directionLockThreshold?this.directionLocked="v":this.directionLocked="n"),"h"==this.directionLocked){if("vertical"==this.options.eventPassthrough)t.preventDefault();else if("horizontal"==this.options.eventPassthrough)return void(this.initiated=!1);c=0}else if("v"==this.directionLocked){if("horizontal"==this.options.eventPassthrough)t.preventDefault();else if("vertical"==this.options.eventPassthrough)return void(this.initiated=!1);l=0}l=this.hasHorizontalScroll?l:0,c=this.hasVerticalScroll?c:0,e=this.x+l,i=this.y+c,(e>0||e<this.maxScrollX)&&(e=this.options.bounce?this.x+l/3:e>0?0:this.maxScrollX),
(i>0||i<this.maxScrollY)&&(i=this.options.bounce?this.y+c/3:i>0?0:this.maxScrollY),this.directionX=l>0?-1:l<0?1:0,this.directionY=c>0?-1:c<0?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(e,i),$(this.wrapper).trigger($.Event("pullrefresh:move")),u-this.startTime>300&&(this.startTime=u,this.startX=this.x,this.startY=this.y)}}},_end:function(t){if(this.enabled&&s.eventType[t.type]===this.initiated){this.options.preventDefault&&!s.preventDefaultException(t.target,this.options.preventDefaultException);var e,i,a=(t.changedTouches?t.changedTouches[0]:t,s.getTime()-this.startTime),o=n.round(this.x),r=n.round(this.y),l=n.abs(o-this.startX),c=n.abs(r-this.startY),u=0,d="";if(this.isInTransition=0,this.initiated=0,this.endTime=s.getTime(),$(this.wrapper).trigger($.Event("pullrefresh:end")),!this.pullrefreshEnd&&!this.resetPosition(this.options.bounceTime))return this.scrollTo(o,r),this.moved?this._events.flick&&a<200&&l<100&&c<100?void this._execEvent("flick"):(this.options.momentum&&a<300&&(e=this.hasHorizontalScroll?s.momentum(this.x,this.startX,a,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:o,duration:0},i=this.hasVerticalScroll?s.momentum(this.y,this.startY,a,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:r,duration:0},o=e.destination,r=i.destination,u=n.max(e.duration,i.duration),this.isInTransition=1),o!=this.x||r!=this.y?((o>0||o<this.maxScrollX||r>0||r<this.maxScrollY)&&(d=s.ease.quadratic),void this.scrollTo(o,r,u,d)):void this._execEvent("scrollEnd")):(this.options.tap&&s.tap(t,this.options.tap),this.options.click&&s.click(t),void this._execEvent("scrollCancel"))}},_resize:function(){var t=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t.refresh()},this.options.resizePolling)},resetPosition:function(t){var e=this.x,n=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?e=0:this.x<this.maxScrollX&&(e=this.maxScrollX),!this.hasVerticalScroll||this.y>0?n=0:this.y<this.maxScrollY&&(n=this.maxScrollY),(e!=this.x||n!=this.y)&&(this.scrollTo(e,n,t,this.options.bounceEasing),!0)},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){this.wrapper.offsetHeight;this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight,this.scrollerWidth=this.scroller.offsetWidth,this.scrollerHeight=this.scroller.offsetHeight,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,this.wrapperOffset=s.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()},on:function(t,e){this._events[t]||(this._events[t]=[]),this._events[t].push(e)},off:function(t,e){if(this._events[t]){var n=this._events[t].indexOf(e);n>-1&&this._events[t].splice(n,1)}},_execEvent:function(t){if(this._events[t]){var e=0,n=this._events[t].length;if(n)for(;e<n;e++)this._events[t][e].apply(this,[].slice.call(arguments,1))}},scrollBy:function(t,e,n,i){t=this.x+t,e=this.y+e,n=n||0,this.scrollTo(t,e,n,i)},scrollTo:function(t,e,n,i){i=i||s.ease.circular,this.isInTransition=this.options.useTransition&&n>0,!n||this.options.useTransition&&i.style?(this._transitionTimingFunction(i.style),this._transitionTime(n),this._translate(t,e)):this._animate(t,e,n,i.fn)},scrollToElement:function(t,e,i,a,o){if(t=t.nodeType?t:this.scroller.querySelector(t)){var r=s.offset(t);r.left-=this.wrapperOffset.left,r.top-=this.wrapperOffset.top,i===!0&&(i=n.round(t.offsetWidth/2-this.wrapper.offsetWidth/2)),a===!0&&(a=n.round(t.offsetHeight/2-this.wrapper.offsetHeight/2)),r.left-=i||0,r.top-=a||0,r.left=r.left>0?0:r.left<this.maxScrollX?this.maxScrollX:r.left,r.top=r.top>0?0:r.top<this.maxScrollY?this.maxScrollY:r.top,e=void 0===e||null===e||"auto"===e?n.max(n.abs(this.x-r.left),n.abs(this.y-r.top)):e,this.scrollTo(r.left,r.top,e,o)}},_transitionTime:function(t){t=t||0,this.scrollerStyle[s.style.transitionDuration]=t+"ms",!t&&s.isBadAndroid&&(this.scrollerStyle[s.style.transitionDuration]="0.001s")},_transitionTimingFunction:function(t){this.scrollerStyle[s.style.transitionTimingFunction]=t},_translate:function(t,e){this.options.useTransform?this.scrollerStyle[s.style.transform]="translate("+t+"px,"+e+"px)"+this.translateZ:(t=n.round(t),e=n.round(e),this.scrollerStyle.left=t+"px",this.scrollerStyle.top=e+"px"),this.x=t,this.y=e,"function"==typeof this.options.scrolling&&this.options.scrolling.call(this)},_initEvents:function(e){var n=e?s.removeEvent:s.addEvent,i=this.options.bindToWrapper?this.wrapper:t;n(t,"orientationchange",this),n(t,"resize",this),this.options.click&&n(this.wrapper,"click",this,!0),this.options.disableMouse||(n(this.wrapper,"mousedown",this),n(i,"mousemove",this),n(i,"mousecancel",this),n(i,"mouseup",this)),s.hasPointer&&!this.options.disablePointer&&(n(this.wrapper,s.prefixPointerEvent("pointerdown"),this),n(i,s.prefixPointerEvent("pointermove"),this),n(i,s.prefixPointerEvent("pointercancel"),this),n(i,s.prefixPointerEvent("pointerup"),this)),s.hasTouch&&!this.options.disableTouch&&(n(this.wrapper,"touchstart",this),n(i,"touchmove",this),n(i,"touchcancel",this),n(i,"touchend",this)),n(this.scroller,"transitionend",this),n(this.scroller,"webkitTransitionEnd",this),n(this.scroller,"oTransitionEnd",this),n(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var e,n,i=t.getComputedStyle(this.scroller,null);return this.options.useTransform?(i=i[s.style.transform].split(")")[0].split(", "),e=+(i[12]||i[4]),n=+(i[13]||i[5])):(e=+i.left.replace(/[^-\d.]/g,""),n=+i.top.replace(/[^-\d.]/g,"")),{x:e,y:n}},_animate:function(t,e,n,i){function a(){var h,f,p,m=s.getTime();return m>=d?(o.isAnimating=!1,o._translate(t,e),void(o.resetPosition(o.options.bounceTime)||o._execEvent("scrollEnd"))):(m=(m-u)/n,p=i(m),h=(t-l)*p+l,f=(e-c)*p+c,o._translate(h,f),void(o.isAnimating&&r(a)))}var o=this,l=this.x,c=this.y,u=s.getTime(),d=u+n;this.isAnimating=!0,a()},handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(t);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(t);break;case"keydown":this._key(t);break;case"click":t._constructed||(t.preventDefault(),t.stopPropagation())}}},i.scroller=$.fn.scroller=$.scroller=o,i.pt.scroller=function(t){return o.call(this.$[0],t)}}(window,document,Math,window.arm),!function(t,e){var n={onRefreshBefore:function(){},onRefreshReady:function(){},onRefresh:function(){},onRefreshDone:function(){},onLoadmoreBefore:function(){},onLoadmoreReady:function(){},onLoadmore:function(){},onLoadmoreDone:function(){},onComplete:function(){}},i='<div class="ui-pullrefresh-tip pullrefresh"><i class="ui-pullrefresh-down"></i><span></span></div>',a='<div class="ui-pullrefresh-tip pullloadmore"><i class="ui-pullrefresh-up"></i><span></span></div>',o=function(e,i){this.el=e,this.opt=t.extend(!0,{},n,i||{}),this._bindEvents()};o.prototype={_bindEvents:function(){var n=this;n.$refresh=t(".ui-pullrefresh-refresh",n.el),n.$refresh.length<1&&(n.$refresh=t(i),n.$refresh.isFromTpl=!0),n.$loadmore=t(".ui-pullrefresh-loadmore",n.el),n.$loadmore.length<1&&(n.$loadmore=t(a),n.$loadmore.isFromTpl=!0),n.inner=t(n.el).children()[0],t(n.el).addClass("ui-pullrefresh"),t(n.inner).addClass("ui-pullrefresh-container"),t(n.el).on("pullrefresh:start",function(t){n._pullstart(t)}).on("pullrefresh:move",function(t){n._pullmove(t)}).on("pullrefresh:end",function(t){n._pullend(t)}).on("pullrefresh:transitionEnd",function(t){n._pullTransEnd(t)}),n.$refresh.prependTo(n.inner),n.$loadmore.appendTo(n.inner),n.refreshHeight=n.$refresh.height(),n.loadmoreHeight=n.$loadmore.height(),n.opt.refreshRange=n.opt.refreshRange?n.opt.refreshRange:n.refreshHeight+25,n.opt.loadRange=n.opt.loadRange?n.opt.loadRange:n.loadmoreHeight,n._scroll=new e(n.el).scroller(n.opt)},_bindOne:function(){var e=this;t(e.el).off(".pullrefresh"),e.isfreshing||t(e.el).one("refresh:before.pullrefresh",function(t){e.opt.onRefreshBefore.call(e,t)}).one("refresh:ready.pullrefresh",function(t){e.opt.onRefreshReady.call(e,t)}).one("refresh:action.pullrefresh",function(t){e.opt.onRefresh.call(e,t)}),e.isloading||t(e.el).one("loadmore:before.pullrefresh",function(t){e.opt.onLoadmoreBefore.call(e,t)}).one("loadmore:ready.pullrefresh",function(t){e.opt.onLoadmoreReady.call(e,t)}).one("loadmore:action.pullrefresh",function(t){e.opt.onLoadmore.call(e,t)}),t(e.el).one("loadmore:done.pullrefresh",function(t){e.isloading=!1,e.opt.onLoadmoreDone.call(e,t),e.opt.onComplete.call(e,"loadmore")}).one("refresh:done.pullrefresh",function(t){e.isfreshing=!1,e.opt.onRefreshDone.call(e,t),e.opt.onComplete.call(e,"refresh")})},_pullstart:function(){var t=this;scroll=t._scroll,scroll.pullrefreshEnd=!1,t.isfreshing=!!t.isfreshing,t.isloading=!!t.isloading,t._bindOne()},_pullmove:function(t){var e=this;scroll=this._scroll;var n=0;n=scroll.y>0?scroll.y:n,n=scroll.y<scroll.maxScrollY?scroll.y-scroll.maxScrollY:n,this.distance=n,e.isfreshing||(0<n&&n<e.opt.refreshRange&&e._trigger("refresh:before"),n>=e.opt.refreshRange&&e._trigger("refresh:ready")),e.isloading||(0>n&&n>-e.opt.loadRange&&e._trigger("loadmore:before"),n<=-e.opt.loadRange&&e._trigger("loadmore:ready"))},_pullTransEnd:function(){var t=this,e=this._scroll;e.pullrefreshEnd=!0,e.scrollTo(0,t.$refresh.height(),500,e.options.bounceEasing)},_pullend:function(t){var e=this,n=this._scroll;"refresh:ready"!==e.state||n.pullrefreshEnd||(e.isfreshing=!0,e._trigger("refresh:action")),"loadmore:ready"!==e.state||n.pullrefreshEnd||(e.isloading=!0,e._trigger("loadmore:action")),e.isfreshing&&!e.isloading&&this.distance>e.$refresh.height()&&e._pullTransEnd()},_update:function(t){var e=this._scroll;e.refresh(),"refresh"===t&&e.scrollTo(0,0,500,e.options.bounceEasing)},refreshed:function(){var t=this;t._update("refresh"),t._trigger("refresh:done")},loadedmore:function(){var t=this;t._update("loadmore"),t._trigger("loadmore:done")},_stateStyle:function(e){var n=this;n.state=e,e=e.split(":");var i=n["$"+e[0]],a="refresh"===e[0],o=("loadmore"===e[0],e[1]),r={before:"ui-pullrefresh-"+(a?"down":"up"),ready:"ui-pullrefresh-"+(a?"refresh":"loading")};r.action=r.ready+" ani-loading",r.done=r.before,t("i",i).removeClass().addClass(r[o])},_trigger:function(e){var n=this;this._scroll;n._stateStyle(e),t(n.el).trigger(e)}},e.register("pullrefresh",o,!0)}(JQUERY,window.arm),function(t,e,n){"use strict";function i(t,e,n){e===t.opts.ul&&(e[0].style[p.prefixStyle("transition")]="all "+n/1e3+"s "+t.opts.transitionType)}function a(t,n,i){if(n===t.opts.ul){var a=t.opts.axisX?i+"px,0":"0,"+i+"px";n[0].style[p.prefixStyle("transform")]="translate("+a+")"}else{var a=t.opts.axisX?{top:0,left:i}:{top:i,left:0};e(n[0]).css(a)}}function o(t,n){var i=t.opts.ul.children(),a=i.eq(n).find("[data-src]");a&&a.each(function(t){var n=e(this);n.is("img")?(n.attr("src",n.data("src")),n.removeAttr("data-src")):(n.css({"background-image":"url("+n.data("src")+")"}),n.removeAttr("data-src"))})}function r(t){v.touch&&!t.touches&&(t.touches=t.originalEvent.touches)}function s(t,e){e.active=!0,e.isScrolling=void 0,e._moveDistance=e._moveDistanceIE=0,e._startX=v.touch?t.touches[0].pageX:t.pageX||t.clientX,e._startY=v.touch?t.touches[0].pageY:t.pageY||t.clientY}function l(t,e){return!!e.active&&(e.opts.autoSwipe&&d(e),e.allowSlideClick=!1,e._curX=v.touch?t.touches[0].pageX:t.pageX||t.clientX,e._curY=v.touch?t.touches[0].pageY:t.pageY||t.clientY,e._moveX=e._curX-e._startX,e._moveY=e._curY-e._startY,"undefined"==typeof e.isScrolling&&(e.opts.axisX?e.isScrolling=!!(Math.abs(e._moveX)>=Math.abs(e._moveY)):e.isScrolling=!!(Math.abs(e._moveY)>=Math.abs(e._moveX))),e.isScrolling&&(t.preventDefault?t.preventDefault():t.returnValue=!1,i(e,e.opts.ul,0),e._moveDistance=e._moveDistanceIE=e.opts.axisX?e._moveX:e._moveY),e.opts.continuousScroll||e.opts.bounce||(0==e._index&&e._moveDistance>0||e._index+1>=e._liLength&&e._moveDistance<0)&&(e._moveDistance=0),void a(e,e.opts.ul,-(e._slideDistance*e._index-e._moveDistance)))}function c(t){t.active=!1,t.isScrolling||u(t),(m.ie10||m.ie11)&&(Math.abs(t._moveDistanceIE)<5&&(t.allowSlideClick=!0),setTimeout(function(){t.allowSlideClick=!0},100)),Math.abs(t._moveDistance)<=t._distance?h(t,"",t.opts.duration):t._moveDistance>t._distance?0===t._index&&t.opts.bounce?h(t,"",t.opts.duration):h(t,"prev",t.opts.duration):Math.abs(t._moveDistance)>t._distance&&(t._index===t._liLength-1&&t.opts.bounce?h(t,"",t.opts.duration):h(t,"next",t.opts.duration)),t._moveDistance=t._moveDistanceIE=0}function u(t){t.opts.autoSwipe&&(d(t),t.autoSlide=setInterval(function(){h(t,"next",t.opts.duration)},t.opts.speed))}function d(t){clearInterval(t.autoSlide)}function h(t,e,n){"number"==typeof e?(t._index=e,t.opts.lazyLoad&&(t.opts.continuousScroll?(o(t,t._index),o(t,t._index+1),o(t,t._index+2)):(o(t,t._index-1),o(t,t._index),o(t,t._index+1)))):"next"==e?(t._index++,t.opts.lazyLoad&&(t.opts.continuousScroll?(o(t,t._index+2),t._index+1==t._liLength?o(t,1):t._index==t._liLength&&o(t,0)):o(t,t._index+1))):"prev"==e&&(t._index--,t.opts.lazyLoad&&(t.opts.continuousScroll?(o(t,t._index),0==t._index?o(t,t._liLength):t._index<0&&o(t,t._liLength-1)):o(t,t._index-1))),t.opts.continuousScroll?t._index>=t._liLength?(f(t,n),t._index=0,setTimeout(function(){f(t,0),t._callback()},300)):t._index<0?(f(t,n),t._index=t._liLength-1,setTimeout(function(){f(t,0),t._callback()},300)):f(t,n):(t._index>=t._liLength?t._index=0:t._index<0&&(t._index=t._liLength-1),f(t,n)),t._callback()}function f(t,e){i(t,t.opts.ul,e),a(t,t.opts.ul,-t._index*t._slideDistance)}var p=n.utils,m={ie10:t.navigator.msPointerEnabled,ie11:t.navigator.pointerEnabled},v=n.support,b=n.touchEvents,g=function(t,n){var i=this;i.$el=e(t).css({visibility:"hidden"}),i._distance=50,i.allowSlideClick=!0,i.opts=e.extend({},{slideClass:"ui-slide",slideSelector:"",index:0,pagination:!0,continuousScroll:!0,autoSwipe:!0,speed:5e3,duration:300,distance:50,axisX:!0,transitionType:"ease",lazyLoad:!1,callback:function(){},swipeTrigger:!0},n),i.init()};g.prototype.init=function(){function n(){var t=d.opts.ul.children();d._slideDistance=d.opts.axisX?d.opts.ul.width():d.opts.ul.height(),d._distance=d.opts.distance>d._slideDistance/3?d._slideDistance/3:d.opts.distance,i(d,d.opts.ul,0),a(d,d.opts.ul,-d._slideDistance*d._index),i(d,t,0);var n=d.opts.continuousScroll?-1:0;t.each(function(t){a(d,e(this),d._slideDistance*(t+n))})}var d=this;if(d.$el.addClass(d.opts.slideClass).css("visibility","hidden"),d.opts.axisX&&d.$el.addClass(d.opts.slideClass+"-axisx"),d.opts.ul=e(d.opts.slideSelector),d.opts.ul.length<1&&(d.opts.ul=d.$el.children()),d.opts.li=d.opts.ul.children(),d.opts.ul.addClass(d.opts.slideClass+"-content"),d.opts.li.addClass(d.opts.slideClass+"-item"),d._index=d.opts.index,d._liLength=d.opts.li.length,d.isScrolling,d.opts.pagination&&(d.$pagination=e('<div class="'+d.opts.slideClass+'-dot" />'),d.$pagination.html(function(){for(var t="",e=0;e<d._liLength;e++)t+="<span></span>";return t}),d.$el.append(d.$pagination)),d._callback(),d._liLength<=1)return d.opts.lazyLoad&&o(d,0),!1;if(d.opts.continuousScroll&&d.opts.ul.prepend(d.opts.li.last().clone()).append(d.opts.li.first().clone()),d.opts.lazyLoad&&(o(d,d._index),d.opts.continuousScroll?(o(d,d._index+1),o(d,d._index+2),0==d._index?o(d,d._liLength):d._index+1==d._liLength&&o(d,1)):o(d,d._index+1==d._liLength?d._liLength-2:d._index+1)),n(),m.ie10||m.ie11){var h="";h=d.opts.axisX?"pan-y":"none",d.$el.css({"-ms-touch-action":h,"touch-action":h}),d.$el.on("click",function(){return d.allowSlideClick})}u(d),d.$el.css({visibility:"visible"}),d.opts.swipeTrigger&&d.$el.on(b.touchStart,function(t){r(t),s(t,d)}).on(b.touchMove,function(t){r(t),l(t,d)}).on(b.touchEnd,function(){c(d)}),v.transition&&d.opts.ul.on(v.transition.end,function(){u(d)}),e(t).on("onorientationchange"in t?"orientationchange":"resize",function(){clearTimeout(d.timer),d.timer=setTimeout(n,150)})},g.prototype._callback=function(){var t=this;t.$pagination&&t.$pagination.children().removeClass("cur").eq(t._index).addClass("cur"),t.opts.callback.call(t,t._index,t._liLength)},g.prototype.goTo=function(t){var e=this;h(e,t,e.opts.duration)},e.splipper=n.splipper=g,e.fn.swipeSlide=function(t){return new g(this,t)},n.pt.swipeSlide=function(t){return new g(this.$,t)}}(window,JQUERY,window.arm),!function(t,e){"use strict";t.fullpage=e.fullpage=function(n,i){var n=t(n),i=t.extend(!0,{slideClass:"ui-fullpage",continuousScroll:!1,autoSwipe:!1,axisX:!0,bounce:!0,bounceEasing:"ease-in"},i||{});return new e.splipper(n,i)}}(JQUERY,window.arm);
