/**
 * @func
 * @version 1.0
 * <AUTHOR> 2018/8/31
 * @desc {}
 */
Util = {
    loadStyles: function (color) {
        // alert("=="+color);
        $.get("css/themes.css", function (result) {
            result = result.replace(/@color1/g, color);
            result = result.replace(/@color2/g, Util.LightenDarkenColor(color, 50));

            var style = document.createElement('style');
            style.type = 'text/css';
            style.rel = 'stylesheet';
            style.appendChild(document.createTextNode(result));
            //for IE
            //style.styleSheet.cssText = code;
            var head = document.getElementsByTagName('head')[0];
            head.appendChild(style);

            //console.log(result);
        });
    },
    LightenDarkenColor: function (col, amt) {

        var usePound = false;
        if (col[0] == "#") {
            col = col.slice(1);
            usePound = true;
        }
        var num = parseInt(col, 16);

        var r = (num >> 16) + amt;
        if (r > 255) r = 255;
        else if (r < 0) r = 0;

        var b = ((num >> 8) & 0x0000FF) + amt;
        if (b > 255) b = 255;
        else if (b < 0) b = 0;

        var g = (num & 0x0000FF) + amt;
        if (g > 255) g = 255;
        else if (g < 0) g = 0;

        return 'RGB(' + r + ',' + b + ','+g+')';
//        return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16);
    }

};

(function () {
    // ���ö�ʱ������2��֮�ڣ�ÿ��100�����ж�һ��iportal�Ƿ��ʼ���ɹ��������ʼ���ɹ����޸�����ɫ
// �����ѯ20�Σ�2��󣩻�û�г�ʼ���ɹ��������ʱ��
    window.color = '#1A9BE9';
    var count = 0;
    var timer = setInterval(function () {
        //console.log(count++);
        if((window.iPortal && window.iPortal.getColor) || count > 20){

            clearInterval(timer);

            if(window.iPortal && window.iPortal.getColor){
                iPortal.getColor().getThemeColor(function (color) {
                    window.color = color;
                    Util.loadStyles(color);
                });
            }
        }
    }, 100);
})();


