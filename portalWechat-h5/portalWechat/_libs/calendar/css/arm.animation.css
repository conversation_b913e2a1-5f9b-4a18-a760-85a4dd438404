.ani-circleB,.ani-circleF,.ani-loading {
	animation:1s linear;
	-webkit-animation:1s linear;
	-moz-animation:1s linear;
	-ms-animation:1s linear;
}
.ani-pulse {
	-webkit-animation:1.1s ease 0.2s;
	-moz-animation:1.1s ease 0.2s;
	-ms-animation:1.1s ease 0.2s;
	animation:1.1s ease 0.2s;
}
.ani-bounce,.ani-flip,.ani-flash,.ani-shake,.ani-swing,.ani-wobble,.ani-ring,.ani-hinge,.ani-slideinT,.ani-slideinB,.ani-slideinL,.ani-slideinR,.ani-slideoutT,.ani-slideoutB,.ani-slideoutL,.ani-slideoutR {
	animation:0.75s ease;
	-webkit-animation:0.75s ease;
	-moz-animation:0.75s ease;
	-ms-animation:0.75s ease;
}
.ani-fadein,.ani-fadeinT,.ani-fadeinR,.ani-fadeinB,.ani-fadeinL,.ani-bouncein,.ani-bounceinT,.ani-bounceinR,.ani-bounceinB,.ani-bounceinL,.ani-rotatein,.ani-rotateinLT,.ani-rotateinLB,.ani-rotateinRT,.ani-rotateinRB,.ani-flipin,.ani-flipinX,.ani-flipinY,.ani-zoomin,.ani-rollin {
	animation:1s ease-out backwards;
	-webkit-animation:1s ease-out backwards;
	-moz-animation:1s ease-out backwards;
	-ms-animation:1s ease-out backwards;
}
.ani-fadeout,.ani-fadeoutT,.ani-fadeoutR,.ani-fadeoutB,.ani-fadeoutL,.ani-bounceout,.ani-bounceoutT,.ani-bounceoutR,.ani-bounceoutB,.ani-bounceoutL,.ani-rotateout,.ani-rotateoutLT,.ani-rotateoutLB,.ani-rotateoutRT,.ani-rotateoutRB,.ani-flipout,.ani-flipoutX,.ani-flipoutY,.ani-zoomout,.ani-rollout {
	animation:1s ease-in forwards;
	-webkit-animation:1s ease-in forwards;
	-moz-animation:1s ease-in forwards;
	-ms-animation:1s ease-in forwards;
}
.ani-infinite,.ani-circleB,.ani-circleF,.ani-loading {
	animation-iteration-count:infinite;
	-webkit-animation-iteration-count:infinite;
	-moz-animation-iteration-count:infinite;
	-ms-animation-iteration-count:infinite;
}
.ani-fadein {
	animation-name:fadein;
	-webkit-animation-name:fadein;
	-moz-animation-name:fadein;
	-ms-animation-name:fadein;
}
.ani-fadeinT {
	animation-name:fadeinT;
	-webkit-animation-name:fadeinT;
	-moz-animation-name:fadeinT;
	-ms-animation-name:fadeinT;
}
.ani-fadeinR {
	animation-name:fadeinR;
	-webkit-animation-name:fadeinR;
	-moz-animation-name:fadeinR;
	-ms-animation-name:fadeinR;
}
.ani-fadeinB {
	animation-name:fadeinB;
	-webkit-animation-name:fadeinB;
	-moz-animation-name:fadeinB;
	-ms-animation-name:fadeinB;
}
.ani-fadeinL {
	animation-name:fadeinL;
	-webkit-animation-name:fadeinL;
	-moz-animation-name:fadeinL;
	-ms-animation-name:fadeinL;
}
.ani-fadeout {
	animation-name:fadeout;
	-webkit-animation-name:fadeout;
	-moz-animation-name:fadeout;
	-ms-animation-name:fadeout;
}
.ani-fadeoutT {
	animation-name:fadeoutT;
	-webkit-animation-name:fadeoutT;
	-moz-animation-name:fadeoutT;
	-ms-animation-name:fadeoutT;
}
.ani-fadeoutR {
	animation-name:fadeoutR;
	-webkit-animation-name:fadeoutR;
	-moz-animation-name:fadeoutR;
	-ms-animation-name:fadeoutR;
}
.ani-fadeoutB {
	animation-name:fadeoutB;
	-webkit-animation-name:fadeoutB;
	-moz-animation-name:fadeoutB;
	-ms-animation-name:fadeoutB;
}
.ani-fadeoutL {
	animation-name:fadeoutL;
	-webkit-animation-name:fadeoutL;
	-moz-animation-name:fadeoutL;
	-ms-animation-name:fadeoutL;
}
.ani-slideinT {
	animation-name:slideinT;
	-webkit-animation-name:slideinT;
	-moz-animation-name:slideinT;
	-ms-animation-name:slideinT;
}
.ani-slideinR {
	animation-name:slideinR;
	-webkit-animation-name:slideinR;
	-moz-animation-name:slideinR;
	-ms-animation-name:slideinR;
}
.ani-slideinB {
	animation-name:slideinB;
	-webkit-animation-name:slideinB;
	-moz-animation-name:slideinB;
	-ms-animation-name:slideinB;
}
.ani-slideinL {
	animation-name:slideinL;
	-webkit-animation-name:slideinL;
	-moz-animation-name:slideinL;
	-ms-animation-name:slideinL;
}
.ani-slideoutT {
	animation-name:slideoutT;
	-webkit-animation-name:slideoutT;
	-moz-animation-name:slideoutT;
	-ms-animation-name:slideoutT;
}
.ani-slideoutR {
	animation-name:slideoutR;
	-webkit-animation-name:slideoutR;
	-moz-animation-name:slideoutR;
	-ms-animation-name:slideoutR;
}
.ani-slideoutB {
	animation-name:slideoutB;
	-webkit-animation-name:slideoutB;
	-moz-animation-name:slideoutB;
	-ms-animation-name:slideoutB;
}
.ani-slideoutL {
	animation-name:slideoutL;
	-webkit-animation-name:slideoutL;
	-moz-animation-name:slideoutL;
	-ms-animation-name:slideoutL;
}
.ani-bounce {
	animation-name:bounce;
	-webkit-animation-name:bounce;
	-moz-animation-name:bounce;
	-ms-animation-name:bounce;
}
.ani-bouncein {
	animation-name:bouncein;
	-webkit-animation-name:bouncein;
	-moz-animation-name:bouncein;
	-ms-animation-name:bouncein;
}
.ani-bounceinT {
	animation-name:bounceinT;
	-webkit-animation-name:bounceinT;
	-moz-animation-name:bounceinT;
	-ms-animation-name:bounceinT;
}
.ani-bounceinR {
	animation-name:bounceinR;
	-webkit-animation-name:bounceinR;
	-moz-animation-name:bounceinR;
	-ms-animation-name:bounceinR;
}
.ani-bounceinB {
	animation-name:bounceinB;
	-webkit-animation-name:bounceinB;
	-moz-animation-name:bounceinB;
	-ms-animation-name:bounceinB;
}
.ani-bounceinL {
	animation-name:bounceinL;
	-webkit-animation-name:bounceinL;
	-moz-animation-name:bounceinL;
	-ms-animation-name:bounceinL;
}
.ani-bounceout {
	animation-name:bounceout;
	-webkit-animation-name:bounceout;
	-moz-animation-name:bounceout;
	-ms-animation-name:bounceout;
}
.ani-bounceoutT {
	animation-name:bounceoutT;
	-webkit-animation-name:bounceoutT;
	-moz-animation-name:bounceoutT;
	-ms-animation-name:bounceoutT;
}
.ani-bounceoutR {
	animation-name:bounceoutR;
	-webkit-animation-name:bounceoutR;
	-moz-animation-name:bounceoutR;
	-ms-animation-name:bounceoutR;
}
.ani-bounceoutB {
	animation-name:bounceoutB;
	-webkit-animation-name:bounceoutB;
	-moz-animation-name:bounceoutB;
	-ms-animation-name:bounceoutB;
}
.ani-bounceoutL {
	animation-name:bounceoutL;
	-webkit-animation-name:bounceoutL;
	-moz-animation-name:bounceoutL;
	-ms-animation-name:bounceoutL;
}
.ani-rotatein {
	animation-name:rotatein;
	-webkit-animation-name:rotatein;
	-moz-animation-name:rotatein;
	-ms-animation-name:rotatein;
}
.ani-rotateinLT {
	animation-name:rotateinLT;
	-webkit-animation-name:rotateinLT;
	-moz-animation-name:rotateinLT;
	-ms-animation-name:rotateinLT;
}
.ani-rotateinLB {
	animation-name:rotateinLB;
	-webkit-animation-name:rotateinLB;
	-moz-animation-name:rotateinLB;
	-ms-animation-name:rotateinLB;
}
.ani-rotateinRT {
	animation-name:rotateinRT;
	-webkit-animation-name:rotateinRT;
	-moz-animation-name:rotateinRT;
	-ms-animation-name:rotateinRT;
}
.ani-rotateinRB {
	animation-name:rotateinRB;
	-webkit-animation-name:rotateinRB;
	-moz-animation-name:rotateinRB;
	-ms-animation-name:rotateinRB;
}
.ani-rotateout {
	animation-name:rotateout;
	-webkit-animation-name:rotateout;
	-moz-animation-name:rotateout;
	-ms-animation-name:rotateout;
}
.ani-rotateoutLT {
	animation-name:rotateoutLT;
	-webkit-animation-name:rotateoutLT;
	-moz-animation-name:rotateoutLT;
	-ms-animation-name:rotateoutLT;
}
.ani-rotateoutLB {
	animation-name:rotateoutLB;
	-webkit-animation-name:rotateoutLB;
	-moz-animation-name:rotateoutLB;
	-ms-animation-name:rotateoutLB;
}
.ani-rotateoutRT {
	animation-name:rotateoutRT;
	-webkit-animation-name:rotateoutRT;
	-moz-animation-name:rotateoutRT;
	-ms-animation-name:rotateoutRT;
}
.ani-rotateoutRB {
	animation-name:rotateoutRB;
	-webkit-animation-name:rotateoutRB;
	-moz-animation-name:rotateoutRB;
	-ms-animation-name:rotateoutRB;
}
.ani-flip {
	animation-name:flip;
	-webkit-animation-name:flip;
	-moz-animation-name:flip;
	-ms-animation-name:flip;
}
.ani-flipinX {
	animation-name:flipinX;
	-webkit-animation-name:flipinX;
	-moz-animation-name:flipinX;
	-ms-animation-name:flipinX;
}
.ani-flipin,.ani-flipinY {
	animation-name:flipinY;
	-webkit-animation-name:flipinY;
	-moz-animation-name:flipinY;
	-ms-animation-name:flipinY;
}
.ani-flipoutX {
	animation-name:flipoutX;
	-webkit-animation-name:flipoutX;
	-moz-animation-name:flipoutX;
	-ms-animation-name:flipoutX;
}
.ani-flipout,.ani-flipoutY {
	animation-name:flipoutY;
	-webkit-animation-name:flipoutY;
	-moz-animation-name:flipoutY;
	-ms-animation-name:flipoutY;
}
.ani-flash {
	animation-name:flash;
	-webkit-animation-name:flash;
	-moz-animation-name:flash;
	-ms-animation-name:flash;
}
.ani-shake {
	animation-name:shake;
	-webkit-animation-name:shake;
	-moz-animation-name:shake;
	-ms-animation-name:shake;
}
.ani-swing {
	animation-name:swing;
	-webkit-animation-name:swing;
	-moz-animation-name:swing;
	-ms-animation-name:swing;
}
.ani-wobble {
	animation-name:wobble;
	-webkit-animation-name:wobble;
	-moz-animation-name:wobble;
	-ms-animation-name:wobble;
}
.ani-ring {
	animation-name:ring;
	-webkit-animation-name:ring;
	-moz-animation-name:ring;
	-ms-animation-name:ring;
}
.ani-pulse {
	animation-name:pulse;
	-webkit-animation-name:pulse;
	-moz-animation-name:pulse;
	-ms-animation-name:pulse;
}
.ani-zoomin {
	animation-name:zoomin;
	-webkit-animation-name:zoomin;
	-moz-animation-name:zoomin;
	-ms-animation-name:zoomin;
}
.ani-zoomout {
	animation-name:zoomout;
	-webkit-animation-name:zoomout;
	-moz-animation-name:zoomout;
	-ms-animation-name:zoomout;
}
.ani-hinge {
	animation-name:hinge;
	-webkit-animation-name:hinge;
	-moz-animation-name:hinge;
	-ms-animation-name:hinge;
	animation-duration:2s;
	-webkit-animation-duration:2s;
	-moz-animation-duration:2s;
	-ms-animation-duration:2s;
}
.ani-circleB {
	animation-name:circleB;
	-webkit-animation-name:circleB;
	-moz-animation-name:circleB;
	-ms-animation-name:circleB;
}
.ani-circleF,.ani-loading {
	animation-name:circleF;
	-webkit-animation-name:circleF;
	-moz-animation-name:circleF;
	-ms-animation-name:circleF;
}
.ani-rollin {
	animation-name:rollin;
	-webkit-animation-name:rollin;
	-moz-animation-name:rollin;
	-ms-animation-name:rollin;
}
.ani-rollout {
	animation-name:rollout;
	-webkit-animation-name:rollout;
	-moz-animation-name:rollout;
	-ms-animation-name:rollout;
}
@-webkit-keyframes fadein {
	0% {
	opacity:0
}
100% {
	opacity:1
}
}@-moz-keyframes fadein {
	0% {
	opacity:0
}
100% {
	opacity:1
}
}@-ms-keyframes fadein {
	0% {
	opacity:0
}
100% {
	opacity:1
}
}@keyframes fadein {
	0% {
	opacity:0
}
100% {
	opacity:1
}
}@-webkit-keyframes fadeinT {
	0% {
	opacity:0;
	-webkit-transform:translateY(-100px)
}
100% {
	opacity:1;
	-webkit-transform:translateY(0)
}
}@-moz-keyframes fadeinT {
	0% {
	opacity:0;
	-moz-transform:translateY(-100px)
}
100% {
	opacity:1;
	-moz-transform:translateY(0)
}
}@-ms-keyframes fadeinT {
	0% {
	opacity:0;
	-ms-transform:translateY(-100px)
}
100% {
	opacity:1;
	-ms-transform:translateY(0)
}
}@keyframes fadeinT {
	0% {
	opacity:0;
	transform:translateY(-100px)
}
100% {
	opacity:1;
	transform:translateY(0)
}
}@-webkit-keyframes fadeinR {
	0% {
	opacity:0;
	-webkit-transform:translateX(100px)
}
100% {
	opacity:1;
	-webkit-transform:translateX(0)
}
}@-moz-keyframes fadeinR {
	0% {
	opacity:0;
	-moz-transform:translateX(100px)
}
100% {
	opacity:1;
	-moz-transform:translateX(0)
}
}@-ms-keyframes fadeinR {
	0% {
	opacity:0;
	-ms-transform:translateX(100px)
}
100% {
	opacity:1;
	-ms-transform:translateX(0)
}
}@keyframes fadeinR {
	0% {
	opacity:0;
	transform:translateX(100px)
}
100% {
	opacity:1;
	transform:translateX(0)
}
}@-webkit-keyframes fadeinB {
	0% {
	opacity:0;
	-webkit-transform:translateY(100px)
}
100% {
	opacity:1;
	-webkit-transform:translateY(0)
}
}@-moz-keyframes fadeinB {
	0% {
	opacity:0;
	-moz-transform:translateY(100px)
}
100% {
	opacity:1;
	-moz-transform:translateY(0)
}
}@-ms-keyframes fadeinB {
	0% {
	opacity:0;
	-ms-transform:translateY(100px)
}
100% {
	opacity:1;
	-ms-transform:translateY(0)
}
}@keyframes fadeinB {
	0% {
	opacity:0;
	transform:translateY(100px)
}
100% {
	opacity:1;
	transform:translateY(0)
}
}@-webkit-keyframes fadeinL {
	0% {
	opacity:0;
	-webkit-transform:translateX(-100px)
}
100% {
	opacity:1;
	-webkit-transform:translateX(0)
}
}@-moz-keyframes fadeinL {
	0% {
	opacity:0;
	-moz-transform:translateX(-100px)
}
100% {
	opacity:1;
	-moz-transform:translateX(0)
}
}@-ms-keyframes fadeinL {
	0% {
	opacity:0;
	-ms-transform:translateX(-100px)
}
100% {
	opacity:1;
	-ms-transform:translateX(0)
}
}@keyframes fadeinL {
	0% {
	opacity:0;
	transform:translateX(-100px)
}
100% {
	opacity:1;
	transform:translateX(0)
}
}@-webkit-keyframes fadeout {
	0% {
	opacity:1
}
100% {
	opacity:0
}
}@-moz-keyframes fadeout {
	0% {
	opacity:1
}
100% {
	opacity:0
}
}@-ms-keyframes fadeout {
	0% {
	opacity:1
}
100% {
	opacity:0
}
}@keyframes fadeout {
	0% {
	opacity:1
}
100% {
	opacity:0
}
}@-webkit-keyframes fadeoutT {
	0% {
	opacity:1;
	-webkit-transform:translateY(0)
}
100% {
	opacity:0;
	-webkit-transform:translateY(-100px)
}
}@-moz-keyframes fadeoutT {
	0% {
	opacity:1;
	-moz-transform:translateY(0)
}
100% {
	opacity:0;
	-moz-transform:translateY(-100px)
}
}@-ms-keyframes fadeoutT {
	0% {
	opacity:1;
	-ms-transform:translateY(0)
}
100% {
	opacity:0;
	-ms-transform:translateY(-100px)
}
}@keyframes fadeoutT {
	0% {
	opacity:1;
	transform:translateY(0)
}
100% {
	opacity:0;
	transform:translateY(-100px)
}
}@-webkit-keyframes fadeoutR {
	0% {
	opacity:1;
	-webkit-transform:translateX(0)
}
100% {
	opacity:0;
	-webkit-transform:translateX(100px)
}
}@-moz-keyframes fadeoutR {
	0% {
	opacity:1;
	-moz-transform:translateX(0)
}
100% {
	opacity:0;
	-moz-transform:translateX(100px)
}
}@-ms-keyframes fadeoutR {
	0% {
	opacity:1;
	-ms-transform:translateX(0)
}
100% {
	opacity:0;
	-ms-transform:translateX(100px)
}
}@keyframes fadeoutR {
	0% {
	opacity:1;
	transform:translateX(0)
}
100% {
	opacity:0;
	transform:translateX(100px)
}
}@-webkit-keyframes fadeoutB {
	0% {
	opacity:1;
	-webkit-transform:translateY(0)
}
100% {
	opacity:0;
	-webkit-transform:translateY(100px)
}
}@-moz-keyframes fadeoutB {
	0% {
	opacity:1;
	-moz-transform:translateY(0)
}
100% {
	opacity:0;
	-moz-transform:translateY(100px)
}
}@-ms-keyframes fadeoutB {
	0% {
	opacity:1;
	-ms-transform:translateY(0)
}
100% {
	opacity:0;
	-ms-transform:translateY(100px)
}
}@keyframes fadeoutB {
	0% {
	opacity:1;
	transform:translateY(0)
}
100% {
	opacity:0;
	transform:translateY(100px)
}
}@-webkit-keyframes fadeoutL {
	0% {
	opacity:1;
	-webkit-transform:translateX(0)
}
100% {
	opacity:0;
	-webkit-transform:translateX(-100px)
}
}@-moz-keyframes fadeoutL {
	0% {
	opacity:1;
	-moz-transform:translateX(0)
}
100% {
	opacity:0;
	-moz-transform:translateX(-100px)
}
}@-ms-keyframes fadeoutL {
	0% {
	opacity:1;
	-ms-transform:translateX(0)
}
100% {
	opacity:0;
	-ms-transform:translateX(-100px)
}
}@keyframes fadeoutL {
	0% {
	opacity:1;
	transform:translateX(0)
}
100% {
	opacity:0;
	transform:translateX(-100px)
}
}@-webkit-keyframes slideinT {
	0% {
	opacity:1;
	-webkit-transform:translateY(-100%)
}
100% {
	-webkit-transform:translateY(0)
}
}@-moz-keyframes slideinT {
	0% {
	opacity:1;
	-moz-transform:translateY(-100%)
}
100% {
	-moz-transform:translateY(0)
}
}@-ms-keyframes slideinT {
	0% {
	opacity:1;
	-ms-transform:translateY(-100%)
}
100% {
	-ms-transform:translateY(0)
}
}@keyframes slideinT {
	0% {
	opacity:1;
	transform:translateY(-100%)
}
100% {
	transform:translateY(0)
}
}@-webkit-keyframes slideinR {
	0% {
	opacity:1;
	-webkit-transform:translateX(100%)
}
100% {
	-webkit-transform:translateX(0)
}
}@-moz-keyframes slideinR {
	0% {
	opacity:1;
	-moz-transform:translateX(100%)
}
100% {
	-moz-transform:translateX(0)
}
}@-ms-keyframes slideinR {
	0% {
	opacity:1;
	-ms-transform:translateX(100%)
}
100% {
	-ms-transform:translateX(0)
}
}@keyframes slideinR {
	0% {
	opacity:1;
	transform:translateX(100%)
}
100% {
	transform:translateX(0)
}
}@-webkit-keyframes slideinB {
	0% {
	opacity:1;
	-webkit-transform:translateY(100%)
}
100% {
	-webkit-transform:translateY(0)
}
}@-moz-keyframes slideinB {
	0% {
	opacity:1;
	-moz-transform:translateY(100%)
}
100% {
	-moz-transform:translateY(0)
}
}@-ms-keyframes slideinB {
	0% {
	opacity:1;
	-ms-transform:translateY(100%)
}
100% {
	-ms-transform:translateY(0)
}
}@keyframes slideinB {
	0% {
	opacity:1;
	transform:translateY(100%)
}
100% {
	transform:translateY(0)
}
}@-webkit-keyframes slideinL {
	0% {
	opacity:1;
	-webkit-transform:translateX(-100%)
}
100% {
	-webkit-transform:translateX(0)
}
}@-moz-keyframes slideinL {
	0% {
	opacity:1;
	-moz-transform:translateX(-100%)
}
100% {
	-moz-transform:translateX(0)
}
}@-ms-keyframes slideinL {
	0% {
	opacity:1;
	-ms-transform:translateX(-100%)
}
100% {
	-ms-transform:translateX(0)
}
}@keyframes slideinL {
	0% {
	opacity:1;
	transform:translateX(-100%)
}
100% {
	transform:translateX(0)
}
}@-webkit-keyframes slideoutT {
	0% {
	-webkit-transform:translateY(0)
}
100% {
	-webkit-transform:translateY(-100%)
}
}@-moz-keyframes slideoutT {
	0% {
	-moz-transform:translateY(0)
}
100% {
	-moz-transform:translateY(-100%)
}
}@-ms-keyframes slideoutT {
	0% {
	-ms-transform:translateY(0)
}
100% {
	-ms-transform:translateY(-100%)
}
}@keyframes slideoutT {
	0% {
	transform:translateY(0)
}
100% {
	transform:translateY(-100%)
}
}@-webkit-keyframes slideoutR {
	0% {
	-webkit-transform:translateX(0)
}
100% {
	-webkit-transform:translateX(100%)
}
}@-moz-keyframes slideoutR {
	0% {
	-moz-transform:translateX(0)
}
100% {
	-moz-transform:translateX(100%)
}
}@-ms-keyframes slideoutR {
	0% {
	-ms-transform:translateX(0)
}
100% {
	-ms-transform:translateX(100%)
}
}@keyframes slideoutR {
	0% {
	transform:translateX(0)
}
100% {
	transform:translateX(100%)
}
}@-webkit-keyframes slideoutB {
	0% {
	-webkit-transform:translateY(0)
}
100% {
	-webkit-transform:translateY(100%)
}
}@-moz-keyframes slideoutB {
	0% {
	-moz-transform:translateY(0)
}
100% {
	-moz-transform:translateY(100%)
}
}@-ms-keyframes slideoutB {
	0% {
	-ms-transform:translateY(0)
}
100% {
	-ms-transform:translateY(100%)
}
}@keyframes slideoutB {
	0% {
	transform:translateY(0)
}
100% {
	transform:translateY(100%)
}
}@-webkit-keyframes slideoutL {
	0% {
	-webkit-transform:translateX(0)
}
100% {
	-webkit-transform:translateX(-100%)
}
}@-moz-keyframes slideoutL {
	0% {
	-moz-transform:translateX(0)
}
100% {
	-moz-transform:translateX(-100%)
}
}@-ms-keyframes slideoutL {
	0% {
	-ms-transform:translateX(0)
}
100% {
	-ms-transform:translateX(-100%)
}
}@keyframes slideoutL {
	0% {
	transform:translateX(0)
}
100% {
	transform:translateX(-100%)
}
}@-webkit-keyframes bounce {
	0%,20%,50%,80%,100% {
	-webkit-transform:translateY(0)
}
40% {
	-webkit-transform:translateY(-30px)
}
60% {
	-webkit-transform:translateY(-15px)
}
}@-moz-keyframes bounce {
	0%,20%,50%,80%,100% {
	-moz-transform:translateY(0)
}
40% {
	-moz-transform:translateY(-30px)
}
60% {
	-moz-transform:translateY(-15px)
}
}@-ms-keyframes bounce {
	0%,20%,50%,80%,100% {
	-ms-transform:translateY(0)
}
40% {
	-ms-transform:translateY(-30px)
}
60% {
	-ms-transform:translateY(-15px)
}
}@keyframes bounce {
	0%,20%,50%,80%,100% {
	transform:translateY(0)
}
40% {
	transform:translateY(-30px)
}
60% {
	transform:translateY(-15px)
}
}@-webkit-keyframes bouncein {
	0% {
	opacity:0;
	-webkit-transform:scale(0.3)
}
50% {
	opacity:1;
	-webkit-transform:scale(1.05)
}
70% {
	-webkit-transform:scale(0.9)
}
100% {
	-webkit-transform:scale(1)
}
}@-moz-keyframes bouncein {
	0% {
	opacity:0;
	-moz-transform:scale(0.3)
}
50% {
	opacity:1;
	-moz-transform:scale(1.05)
}
70% {
	-moz-transform:scale(0.9)
}
100% {
	-moz-transform:scale(1)
}
}@-ms-keyframes bouncein {
	0% {
	opacity:0;
	-ms-transform:scale(0.3)
}
50% {
	opacity:1;
	-ms-transform:scale(1.05)
}
70% {
	-ms-transform:scale(0.9)
}
100% {
	-ms-transform:scale(1)
}
}@keyframes bouncein {
	0% {
	opacity:0;
	transform:scale(0.3)
}
50% {
	opacity:1;
	transform:scale(1.05)
}
70% {
	transform:scale(0.9)
}
100% {
	transform:scale(1)
}
}@-webkit-keyframes bounceinT {
	0% {
	opacity:0;
	-webkit-transform:translateY(-100px)
}
60% {
	opacity:1;
	-webkit-transform:translateY(30px)
}
80% {
	-webkit-transform:translateY(-10px)
}
100% {
	-webkit-transform:translateY(0)
}
}@-moz-keyframes bounceinT {
	0% {
	opacity:0;
	-moz-transform:translateY(-100px)
}
60% {
	opacity:1;
	-moz-transform:translateY(30px)
}
80% {
	-moz-transform:translateY(-10px)
}
100% {
	-moz-transform:translateY(0)
}
}@-ms-keyframes bounceinT {
	0% {
	opacity:0;
	-ms-transform:translateY(-100px)
}
60% {
	opacity:1;
	-ms-transform:translateY(30px)
}
80% {
	-ms-transform:translateY(-10px)
}
100% {
	-ms-transform:translateY(0)
}
}@keyframes bounceinT {
	0% {
	opacity:0;
	transform:translateY(-100px)
}
60% {
	opacity:1;
	transform:translateY(30px)
}
80% {
	transform:translateY(-10px)
}
100% {
	transform:translateY(0)
}
}@-webkit-keyframes bounceinR {
	0% {
	opacity:0;
	-webkit-transform:translateX(100px)
}
60% {
	opacity:1;
	-webkit-transform:translateX(-30px)
}
80% {
	-webkit-transform:translateX(10px)
}
100% {
	-webkit-transform:translateX(0)
}
}@-moz-keyframes bounceinR {
	0% {
	opacity:0;
	-moz-transform:translateX(100px)
}
60% {
	opacity:1;
	-moz-transform:translateX(-30px)
}
80% {
	-moz-transform:translateX(10px)
}
100% {
	-moz-transform:translateX(0)
}
}@-ms-keyframes bounceinR {
	0% {
	opacity:0;
	-ms-transform:translateX(100px)
}
60% {
	opacity:1;
	-ms-transform:translateX(-30px)
}
80% {
	-ms-transform:translateX(10px)
}
100% {
	-ms-transform:translateX(0)
}
}@keyframes bounceinR {
	0% {
	opacity:0;
	transform:translateX(100px)
}
60% {
	opacity:1;
	transform:translateX(-30px)
}
80% {
	transform:translateX(10px)
}
100% {
	transform:translateX(0)
}
}@-webkit-keyframes bounceinB {
	0% {
	opacity:0;
	-webkit-transform:translateY(100px)
}
60% {
	opacity:1;
	-webkit-transform:translateY(-30px)
}
80% {
	-webkit-transform:translateY(10px)
}
100% {
	-webkit-transform:translateY(0)
}
}@-moz-keyframes bounceinB {
	0% {
	opacity:0;
	-moz-transform:translateY(100px)
}
60% {
	opacity:1;
	-moz-transform:translateY(-30px)
}
80% {
	-moz-transform:translateY(10px)
}
100% {
	-moz-transform:translateY(0)
}
}@-ms-keyframes bounceinB {
	0% {
	opacity:0;
	-ms-transform:translateY(100px)
}
60% {
	opacity:1;
	-ms-transform:translateY(-30px)
}
80% {
	-ms-transform:translateY(10px)
}
100% {
	-ms-transform:translateY(0)
}
}@keyframes bounceinB {
	0% {
	opacity:0;
	transform:translateY(100px)
}
60% {
	opacity:1;
	transform:translateY(-30px)
}
80% {
	transform:translateY(10px)
}
100% {
	transform:translateY(0)
}
}@-webkit-keyframes bounceinL {
	0% {
	opacity:0;
	-webkit-transform:translateX(-100px)
}
60% {
	opacity:1;
	-webkit-transform:translateX(30px)
}
80% {
	-webkit-transform:translateX(-10px)
}
100% {
	-webkit-transform:translateX(0)
}
}@-moz-keyframes bounceinL {
	0% {
	opacity:0;
	-moz-transform:translateX(-100px)
}
60% {
	opacity:1;
	-moz-transform:translateX(30px)
}
80% {
	-moz-transform:translateX(-10px)
}
100% {
	-moz-transform:translateX(0)
}
}@-ms-keyframes bounceinL {
	0% {
	opacity:0;
	-ms-transform:translateX(-100px)
}
60% {
	opacity:1;
	-ms-transform:translateX(30px)
}
80% {
	-ms-transform:translateX(-10px)
}
100% {
	-ms-transform:translateX(0)
}
}@keyframes bounceinL {
	0% {
	opacity:0;
	transform:translateX(-100px)
}
60% {
	opacity:1;
	transform:translateX(30px)
}
80% {
	transform:translateX(-10px)
}
100% {
	transform:translateX(0)
}
}@-webkit-keyframes bounceout {
	0% {
	-webkit-transform:scale(1)
}
25% {
	-webkit-transform:scale(0.95)
}
50% {
	opacity:1;
	-webkit-transform:scale(1.1)
}
100% {
	opacity:0;
	-webkit-transform:scale(0.3)
}
}@-moz-keyframes bounceout {
	0% {
	-moz-transform:scale(1)
}
25% {
	-moz-transform:scale(0.95)
}
50% {
	opacity:1;
	-moz-transform:scale(1.1)
}
100% {
	opacity:0;
	-moz-transform:scale(0.3)
}
}@-ms-keyframes bounceout {
	0% {
	-ms-transform:scale(1)
}
25% {
	-ms-transform:scale(0.95)
}
50% {
	opacity:1;
	-ms-transform:scale(1.1)
}
100% {
	opacity:0;
	-ms-transform:scale(0.3)
}
}@keyframes bounceout {
	0% {
	transform:scale(1)
}
25% {
	transform:scale(0.95)
}
50% {
	opacity:1;
	transform:scale(1.1)
}
100% {
	opacity:0;
	transform:scale(0.3)
}
}@-webkit-keyframes bounceoutT {
	0% {
	-webkit-transform:translateY(0)
}
20% {
	opacity:1;
	-webkit-transform:translateY(20px)
}
100% {
	opacity:0;
	-webkit-transform:translateY(-100px)
}
}@-moz-keyframes bounceoutT {
	0% {
	-moz-transform:translateY(0)
}
20% {
	opacity:1;
	-moz-transform:translateY(20px)
}
100% {
	opacity:0;
	-moz-transform:translateY(-100px)
}
}@-ms-keyframes bounceoutT {
	0% {
	-ms-transform:translateY(0)
}
20% {
	opacity:1;
	-ms-transform:translateY(20px)
}
100% {
	opacity:0;
	-ms-transform:translateY(-100px)
}
}@keyframes bounceoutT {
	0% {
	transform:translateY(0)
}
20% {
	opacity:1;
	transform:translateY(20px)
}
100% {
	opacity:0;
	transform:translateY(-100px)
}
}@-webkit-keyframes bounceoutR {
	0% {
	-webkit-transform:translateX(0)
}
20% {
	opacity:1;
	-webkit-transform:translateX(-20px)
}
100% {
	opacity:0;
	-webkit-transform:translateX(100px)
}
}@-moz-keyframes bounceoutR {
	0% {
	-moz-transform:translateX(0)
}
20% {
	opacity:1;
	-moz-transform:translateX(-20px)
}
100% {
	opacity:0;
	-moz-transform:translateX(100px)
}
}@-ms-keyframes bounceoutR {
	0% {
	-ms-transform:translateX(0)
}
20% {
	opacity:1;
	-ms-transform:translateX(-20px)
}
100% {
	opacity:0;
	-ms-transform:translateX(100px)
}
}@keyframes bounceoutR {
	0% {
	transform:translateX(0)
}
20% {
	opacity:1;
	transform:translateX(-20px)
}
100% {
	opacity:0;
	transform:translateX(100px)
}
}@-webkit-keyframes bounceoutB {
	0% {
	-webkit-transform:translateY(0)
}
20% {
	opacity:1;
	-webkit-transform:translateY(-20px)
}
100% {
	opacity:0;
	-webkit-transform:translateY(100px)
}
}@-moz-keyframes bounceoutB {
	0% {
	-moz-transform:translateY(0)
}
20% {
	opacity:1;
	-moz-transform:translateY(-20px)
}
100% {
	opacity:0;
	-moz-transform:translateY(100px)
}
}@-ms-keyframes bounceoutB {
	0% {
	-ms-transform:translateY(0)
}
20% {
	opacity:1;
	-ms-transform:translateY(-20px)
}
100% {
	opacity:0;
	-ms-transform:translateY(100px)
}
}@keyframes bounceoutB {
	0% {
	transform:translateY(0)
}
20% {
	opacity:1;
	transform:translateY(-20px)
}
100% {
	opacity:0;
	transform:translateY(100px)
}
}@-webkit-keyframes bounceoutL {
	0% {
	-webkit-transform:translateX(0)
}
20% {
	opacity:1;
	-webkit-transform:translateX(20px)
}
100% {
	opacity:0;
	-webkit-transform:translateX(-100px)
}
}@-moz-keyframes bounceoutL {
	0% {
	-moz-transform:translateX(0)
}
20% {
	opacity:1;
	-moz-transform:translateX(20px)
}
100% {
	opacity:0;
	-moz-transform:translateX(-100px)
}
}@-ms-keyframes bounceoutL {
	0% {
	-ms-transform:translateX(0)
}
20% {
	opacity:1;
	-ms-transform:translateX(20px)
}
100% {
	opacity:0;
	-ms-transform:translateX(-100px)
}
}@keyframes bounceoutL {
	0% {
	transform:translateX(0)
}
20% {
	opacity:1;
	transform:translateX(20px)
}
100% {
	opacity:0;
	transform:translateX(-200px)
}
}@-webkit-keyframes rotatein {
	0% {
	opacity:0;
	-webkit-transform:rotate(-200deg)
}
100% {
	opacity:1;
	-webkit-transform:rotate(0)
}
}@-moz-keyframes rotatein {
	0% {
	opacity:0;
	-moz-transform:rotate(-200deg)
}
100% {
	opacity:1;
	-moz-transform:rotate(0)
}
}@-ms-keyframes rotatein {
	0% {
	opacity:0;
	-ms-transform:rotate(-200deg)
}
100% {
	opacity:1;
	-ms-transform:rotate(0)
}
}@keyframes rotatein {
	0% {
	opacity:0;
	transform:rotate(-200deg)
}
100% {
	opacity:1;
	transform:rotate(0)
}
}@-webkit-keyframes rotateinLT {
	0% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(-90deg);
	opacity:0
}
100% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
}@-moz-keyframes rotateinLT {
	0% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(-90deg);
	opacity:0
}
100% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(0);
	opacity:1
}
}@-ms-keyframes rotateinLT {
	0% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(-90deg);
	opacity:0
}
100% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(0);
	opacity:1
}
}@keyframes rotateinLT {
	0% {
	transform-origin:left bottom;
	transform:rotate(-90deg);
	opacity:0
}
100% {
	transform-origin:left bottom;
	transform:rotate(0);
	opacity:1
}
}@-webkit-keyframes rotateinLB {
	0% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(90deg);
	opacity:0
}
100% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
}@-moz-keyframes rotateinLB {
	0% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(90deg);
	opacity:0
}
100% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(0);
	opacity:1
}
}@-ms-keyframes rotateinLB {
	0% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(90deg);
	opacity:0
}
100% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(0);
	opacity:1
}
}@keyframes rotateinLB {
	0% {
	transform-origin:left bottom;
	transform:rotate(90deg);
	opacity:0
}
100% {
	transform-origin:left bottom;
	transform:rotate(0);
	opacity:1
}
}@-webkit-keyframes rotateinRT {
	0% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(90deg);
	opacity:0
}
100% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
}@-moz-keyframes rotateinRT {
	0% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(90deg);
	opacity:0
}
100% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(0);
	opacity:1
}
}@-ms-keyframes rotateinRT {
	0% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(90deg);
	opacity:0
}
100% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(0);
	opacity:1
}
}@keyframes rotateinRT {
	0% {
	transform-origin:right bottom;
	transform:rotate(90deg);
	opacity:0
}
100% {
	transform-origin:right bottom;
	transform:rotate(0);
	opacity:1
}
}@-webkit-keyframes rotateinRB {
	0% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(-90deg);
	opacity:0
}
100% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
}@-moz-keyframes rotateinRB {
	0% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(-90deg);
	opacity:0
}
100% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(0);
	opacity:1
}
}@-ms-keyframes rotateinRB {
	0% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(-90deg);
	opacity:0
}
100% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(0);
	opacity:1
}
}@keyframes rotateinRB {
	0% {
	transform-origin:right bottom;
	transform:rotate(-90deg);
	opacity:0
}
100% {
	transform-origin:right bottom;
	transform:rotate(0);
	opacity:1
}
}@-webkit-keyframes rotateout {
	0% {
	-webkit-transform-origin:center center;
	-webkit-transform:rotate(0);
	opacity:1
}
100% {
	-webkit-transform-origin:center center;
	-webkit-transform:rotate(200deg);
	opacity:0
}
}@-moz-keyframes rotateout {
	0% {
	-moz-transform-origin:center center;
	-moz-transform:rotate(0);
	opacity:1
}
100% {
	-moz-transform-origin:center center;
	-moz-transform:rotate(200deg);
	opacity:0
}
}@-ms-keyframes rotateout {
	0% {
	-ms-transform-origin:center center;
	-ms-transform:rotate(0);
	opacity:1
}
100% {
	-ms-transform-origin:center center;
	-ms-transform:rotate(200deg);
	opacity:0
}
}@keyframes rotateout {
	0% {
	transform-origin:center center;
	transform:rotate(0);
	opacity:1
}
100% {
	transform-origin:center center;
	transform:rotate(200deg);
	opacity:0
}
}@-webkit-keyframes rotateoutLT {
	0% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
100% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(-90deg);
	opacity:0
}
}@-moz-keyframes rotateoutLT {
	0% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(0);
	opacity:1
}
100% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(-90deg);
	opacity:0
}
}@-ms-keyframes rotateoutLT {
	0% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(0);
	opacity:1
}
100% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(-90deg);
	opacity:0
}
}@keyframes rotateoutLT {
	0% {
	transform-origin:left bottom;
	transform:rotate(0);
	opacity:1
}
100% {
	transform-origin:left bottom;
	transform:rotate(-90deg);
	opacity:0
}
}@-webkit-keyframes rotateoutLB {
	0% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
100% {
	-webkit-transform-origin:left bottom;
	-webkit-transform:rotate(90deg);
	opacity:0
}
}@-moz-keyframes rotateoutLB {
	0% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(0);
	opacity:1
}
100% {
	-moz-transform-origin:left bottom;
	-moz-transform:rotate(90deg);
	opacity:0
}
}@-ms-keyframes rotateoutLB {
	0% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(0);
	opacity:1
}
100% {
	-ms-transform-origin:left bottom;
	-ms-transform:rotate(90deg);
	opacity:0
}
}@keyframes rotateoutLB {
	0% {
	transform-origin:left bottom;
	transform:rotate(0);
	opacity:1
}
100% {
	transform-origin:left bottom;
	transform:rotate(90deg);
	opacity:0
}
}@-webkit-keyframes rotateoutRT {
	0% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
100% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(90deg);
	opacity:0
}
}@-moz-keyframes rotateoutRT {
	0% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(0);
	opacity:1
}
100% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(90deg);
	opacity:0
}
}@-ms-keyframes rotateoutRT {
	0% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(0);
	opacity:1
}
100% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(90deg);
	opacity:0
}
}@keyframes rotateoutRT {
	0% {
	transform-origin:right bottom;
	transform:rotate(0);
	opacity:1
}
100% {
	transform-origin:right bottom;
	transform:rotate(90deg);
	opacity:0
}
}@-webkit-keyframes rotateoutBR {
	0% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(0);
	opacity:1
}
100% {
	-webkit-transform-origin:right bottom;
	-webkit-transform:rotate(-90deg);
	opacity:0
}
}@-moz-keyframes rotateoutBR {
	0% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(0);
	opacity:1
}
100% {
	-moz-transform-origin:right bottom;
	-moz-transform:rotate(-90deg);
	opacity:0
}
}@-ms-keyframes rotateoutBR {
	0% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(0);
	opacity:1
}
100% {
	-ms-transform-origin:right bottom;
	-ms-transform:rotate(-90deg);
	opacity:0
}
}@keyframes rotateoutBR {
	0% {
	transform-origin:right bottom;
	transform:rotate(0);
	opacity:1
}
100% {
	transform-origin:right bottom;
	transform:rotate(-90deg);
	opacity:0
}
}@-webkit-keyframes flip {
	0% {
	-webkit-transform:perspective(400px) rotateY(0);
	-webkit-animation-timing-function:ease-out
}
40% {
	-webkit-transform:perspective(400px) translateZ(150px) rotateY(170deg);
	-webkit-animation-timing-function:ease-out
}
50% {
	-webkit-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
	-webkit-animation-timing-function:ease-in
}
80% {
	-webkit-transform:perspective(400px) rotateY(360deg) scale(0.95);
	-webkit-animation-timing-function:ease-in
}
100% {
	-webkit-transform:perspective(400px) scale(1);
	-webkit-animation-timing-function:ease-in
}
}@-moz-keyframes flip {
	0% {
	-moz-transform:perspective(400px) rotateY(0);
	-moz-animation-timing-function:ease-out
}
40% {
	-moz-transform:perspective(400px) translateZ(150px) rotateY(170deg);
	-moz-animation-timing-function:ease-out
}
50% {
	-moz-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
	-moz-animation-timing-function:ease-in
}
80% {
	-moz-transform:perspective(400px) rotateY(360deg) scale(0.95);
	-moz-animation-timing-function:ease-in
}
100% {
	-moz-transform:perspective(400px) scale(1);
	-moz-animation-timing-function:ease-in
}
}@-ms-keyframes flip {
	0% {
	-ms-transform:perspective(400px) rotateY(0);
	-ms-animation-timing-function:ease-out
}
40% {
	-ms-transform:perspective(400px) translateZ(150px) rotateY(170deg);
	-ms-animation-timing-function:ease-out
}
50% {
	-ms-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
	-ms-animation-timing-function:ease-in
}
80% {
	-ms-transform:perspective(400px) rotateY(360deg) scale(0.95);
	-ms-animation-timing-function:ease-in
}
100% {
	-ms-transform:perspective(400px) scale(1);
	-ms-animation-timing-function:ease-in
}
}@keyframes flip {
	0% {
	transform:perspective(400px) rotateY(0);
	animation-timing-function:ease-out
}
40% {
	transform:perspective(400px) translateZ(150px) rotateY(170deg);
	animation-timing-function:ease-out
}
50% {
	transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
	animation-timing-function:ease-in
}
80% {
	transform:perspective(400px) rotateY(360deg) scale(0.95);
	animation-timing-function:ease-in
}
100% {
	transform:perspective(400px) scale(1);
	animation-timing-function:ease-in
}
}@-webkit-keyframes flipinX {
	0% {
	-webkit-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
40% {
	-webkit-transform:perspective(400px) rotateX(-10deg)
}
70% {
	-webkit-transform:perspective(400px) rotateX(10deg)
}
100% {
	-webkit-transform:perspective(400px) rotateX(0);
	opacity:1
}
}@-moz-keyframes flipinX {
	0% {
	-moz-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
40% {
	-moz-transform:perspective(400px) rotateX(-10deg)
}
70% {
	-moz-transform:perspective(400px) rotateX(10deg)
}
100% {
	-moz-transform:perspective(400px) rotateX(0);
	opacity:1
}
}@-ms-keyframes flipinX {
	0% {
	-ms-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
40% {
	-ms-transform:perspective(400px) rotateX(-10deg)
}
70% {
	-ms-transform:perspective(400px) rotateX(10deg)
}
100% {
	-ms-transform:perspective(400px) rotateX(0);
	opacity:1
}
}@keyframes flipinX {
	0% {
	transform:perspective(400px) rotateX(90deg);
	opacity:0
}
40% {
	transform:perspective(400px) rotateX(-10deg)
}
70% {
	transform:perspective(400px) rotateX(10deg)
}
100% {
	transform:perspective(400px) rotateX(0);
	opacity:1
}
}@-webkit-keyframes flipinY {
	0% {
	-webkit-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
40% {
	-webkit-transform:perspective(400px) rotateY(-10deg)
}
70% {
	-webkit-transform:perspective(400px) rotateY(10deg)
}
100% {
	-webkit-transform:perspective(400px) rotateY(0);
	opacity:1
}
}@-moz-keyframes flipinY {
	0% {
	-moz-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
40% {
	-moz-transform:perspective(400px) rotateY(-10deg)
}
70% {
	-moz-transform:perspective(400px) rotateY(10deg)
}
100% {
	-moz-transform:perspective(400px) rotateY(0);
	opacity:1
}
}@-ms-keyframes flipinY {
	0% {
	-ms-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
40% {
	-ms-transform:perspective(400px) rotateY(-10deg)
}
70% {
	-ms-transform:perspective(400px) rotateY(10deg)
}
100% {
	-ms-transform:perspective(400px) rotateY(0);
	opacity:1
}
}@keyframes flipinY {
	0% {
	transform:perspective(400px) rotateY(90deg);
	opacity:0
}
40% {
	transform:perspective(400px) rotateY(-10deg)
}
70% {
	transform:perspective(400px) rotateY(10deg)
}
100% {
	transform:perspective(400px) rotateY(0);
	opacity:1
}
}@-webkit-keyframes flipoutX {
	0% {
	-webkit-transform:perspective(400px) rotateX(0);
	opacity:1
}
100% {
	-webkit-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
}@-moz-keyframes flipoutX {
	0% {
	-moz-transform:perspective(400px) rotateX(0);
	opacity:1
}
100% {
	-moz-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
}@-ms-keyframes flipoutX {
	0% {
	-ms-transform:perspective(400px) rotateX(0);
	opacity:1
}
100% {
	-ms-transform:perspective(400px) rotateX(90deg);
	opacity:0
}
}@keyframes flipoutX {
	0% {
	transform:perspective(400px) rotateX(0);
	opacity:1
}
100% {
	transform:perspective(400px) rotateX(90deg);
	opacity:0
}
}@-webkit-keyframes flipoutY {
	0% {
	-webkit-transform:perspective(400px) rotateY(0);
	opacity:1
}
100% {
	-webkit-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
}@-moz-keyframes flipoutY {
	0% {
	-moz-transform:perspective(400px) rotateY(0);
	opacity:1
}
100% {
	-moz-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
}@-ms-keyframes flipoutY {
	0% {
	-ms-transform:perspective(400px) rotateY(0);
	opacity:1
}
100% {
	-ms-transform:perspective(400px) rotateY(90deg);
	opacity:0
}
}@keyframes flipoutY {
	0% {
	transform:perspective(400px) rotateY(0);
	opacity:1
}
100% {
	transform:perspective(400px) rotateY(90deg);
	opacity:0
}
}@-webkit-keyframes flash {
	0%,50%,100% {
	opacity:1
}
25%,75% {
	opacity:0
}
}@-moz-keyframes flash {
	0%,50%,100% {
	opacity:1
}
25%,75% {
	opacity:0
}
}@-ms-keyframes flash {
	0%,50%,100% {
	opacity:1
}
25%,75% {
	opacity:0
}
}@keyframes flash {
	0%,50%,100% {
	opacity:1
}
25%,75% {
	opacity:0
}
}@-webkit-keyframes shake {
	0%,100% {
	-webkit-transform:translateX(0)
}
10%,30%,50%,70%,90% {
	-webkit-transform:translateX(-10px)
}
20%,40%,60%,80% {
	-webkit-transform:translateX(10px)
}
}@-moz-keyframes shake {
	0%,100% {
	-moz-transform:translateX(0)
}
10%,30%,50%,70%,90% {
	-moz-transform:translateX(-10px)
}
20%,40%,60%,80% {
	-moz-transform:translateX(10px)
}
}@-ms-keyframes shake {
	0%,100% {
	-ms-transform:translateX(0)
}
10%,30%,50%,70%,90% {
	-ms-transform:translateX(-10px)
}
20%,40%,60%,80% {
	-ms-transform:translateX(10px)
}
}@keyframes shake {
	0%,100% {
	transform:translateX(0)
}
10%,30%,50%,70%,90% {
	transform:translateX(-10px)
}
20%,40%,60%,80% {
	transform:translateX(10px)
}
}@-webkit-keyframes swing {
	20% {
	-webkit-transform:rotate(15deg)
}
40% {
	-webkit-transform:rotate(-10deg)
}
60% {
	-webkit-transform:rotate(5deg)
}
80% {
	-webkit-transform:rotate(-5deg)
}
100% {
	-webkit-transform:rotate(0)
}
}@-moz-keyframes swing {
	20% {
	-moz-transform:rotate(15deg)
}
40% {
	-moz-transform:rotate(-10deg)
}
60% {
	-moz-transform:rotate(5deg)
}
80% {
	-moz-transform:rotate(-5deg)
}
100% {
	-moz-transform:rotate(0)
}
}@-ms-keyframes swing {
	20% {
	-ms-transform:rotate(15deg)
}
40% {
	-ms-transform:rotate(-10deg)
}
60% {
	-ms-transform:rotate(5deg)
}
80% {
	-ms-transform:rotate(-5deg)
}
100% {
	-ms-transform:rotate(0)
}
}@keyframes swing {
	20% {
	transform:rotate(15deg)
}
40% {
	transform:rotate(-10deg)
}
60% {
	transform:rotate(5deg)
}
80% {
	transform:rotate(-5deg)
}
100% {
	transform:rotate(0)
}
}@-webkit-keyframes wobble {
	0% {
	-webkit-transform:translateX(0)
}
15% {
	-webkit-transform:translateX(-100px) rotate(-5deg)
}
30% {
	-webkit-transform:translateX(80px) rotate(3deg)
}
45% {
	-webkit-transform:translateX(-65px) rotate(-3deg)
}
60% {
	-webkit-transform:translateX(40px) rotate(2deg)
}
75% {
	-webkit-transform:translateX(-20px) rotate(-1deg)
}
100% {
	-webkit-transform:translateX(0)
}
}@-moz-keyframes wobble {
	0% {
	-moz-transform:translateX(0)
}
15% {
	-moz-transform:translateX(-100px) rotate(-5deg)
}
30% {
	-moz-transform:translateX(80px) rotate(3deg)
}
45% {
	-moz-transform:translateX(-65px) rotate(-3deg)
}
60% {
	-moz-transform:translateX(40px) rotate(2deg)
}
75% {
	-moz-transform:translateX(-20px) rotate(-1deg)
}
100% {
	-moz-transform:translateX(0)
}
}@-ms-keyframes wobble {
	0% {
	-ms-transform:translateX(0)
}
15% {
	-ms-transform:translateX(-100px) rotate(-5deg)
}
30% {
	-ms-transform:translateX(80px) rotate(3deg)
}
45% {
	-ms-transform:translateX(-65px) rotate(-3deg)
}
60% {
	-ms-transform:translateX(40px) rotate(2deg)
}
75% {
	-ms-transform:translateX(-20px) rotate(-1deg)
}
100% {
	-ms-transform:translateX(0)
}
}@keyframes wobble {
	0% {
	transform:translateX(0)
}
15% {
	transform:translateX(-100px) rotate(-5deg)
}
30% {
	transform:translateX(80px) rotate(3deg)
}
45% {
	transform:translateX(-65px) rotate(-3deg)
}
60% {
	transform:translateX(40px) rotate(2deg)
}
75% {
	transform:translateX(-20px) rotate(-1deg)
}
100% {
	transform:translateX(0)
}
}@-webkit-keyframes ring {
	0% {
	-webkit-transform:scale(1)
}
10%,20% {
	-webkit-transform:scale(0.9) rotate(-3deg)
}
30%,50%,70%,90% {
	-webkit-transform:scale(1.1) rotate(3deg)
}
40%,60%,80% {
	-webkit-transform:scale(1.1) rotate(-3deg)
}
100% {
	-webkit-transform:scale(1) rotate(0)
}
}@-moz-keyframes ring {
	0% {
	-moz-transform:scale(1)
}
10%,20% {
	-moz-transform:scale(0.9) rotate(-3deg)
}
30%,50%,70%,90% {
	-moz-transform:scale(1.1) rotate(3deg)
}
40%,60%,80% {
	-moz-transform:scale(1.1) rotate(-3deg)
}
100% {
	-moz-transform:scale(1) rotate(0)
}
}@-ms-keyframes ring {
	0% {
	-ms-transform:scale(1)
}
10%,20% {
	-ms-transform:scale(0.9) rotate(-3deg)
}
30%,50%,70%,90% {
	-ms-transform:scale(1.1) rotate(3deg)
}
40%,60%,80% {
	-ms-transform:scale(1.1) rotate(-3deg)
}
100% {
	-ms-transform:scale(1) rotate(0)
}
}@keyframes ring {
	0% {
	transform:scale(1)
}
10%,20% {
	transform:scale(0.9) rotate(-3deg)
}
30%,50%,70%,90% {
	transform:scale(1.1) rotate(3deg)
}
40%,60%,80% {
	transform:scale(1.1) rotate(-3deg)
}
100% {
	transform:scale(1) rotate(0)
}
}@-webkit-keyframes pulse {
	0% {
	-webkit-transform:scale3d(1,1,1)
}
50% {
	-webkit-transform:scale3d(1.2,1.2,1.2)
}
100% {
	-webkit-transform:scale3d(1,1,1)
}
}@-moz-keyframes pulse {
	0% {
	-moz-transform:scale3d(1,1,1)
}
50% {
	-moz-transform:scale3d(1.2,1.2,1.2)
}
100% {
	-moz-transform:scale3d(1,1,1)
}
}@-ms-keyframes pulse {
	0% {
	-ms-transform:scale3d(1,1,1)
}
50% {
	-ms-transform:scale3d(1.2,1.2,1.2)
}
100% {
	-ms-transform:scale3d(1,1,1)
}
}@keyframes pulse {
	0% {
	transform:scale3d(1,1,1)
}
50% {
	transform:scale3d(1.2,1.2,1.2)
}
100% {
	transform:scale3d(1,1,1)
}
}@-webkit-keyframes zoomin {
	0% {
	opacity:0;
	-webkit-transform:scale3d(.3,.3,.3)
}
50% {
	opacity:1
}
}@-moz-keyframes zoomin {
	0% {
	opacity:0;
	-moz-transform:scale3d(.3,.3,.3)
}
50% {
	opacity:1
}
}@-ms-keyframes zoomin {
	0% {
	opacity:0;
	-ms-transform:scale3d(.3,.3,.3)
}
50% {
	opacity:1
}
}@keyframes pulse {
	0% {
	opacity:0;
	transform:scale3d(.3,.3,.3)
}
50% {
	opacity:1
}
}@-webkit-keyframes zoomout {
	0% {
	opacity:1
}
50% {
	opacity:0;
	-webkit-transform:scale3d(.3,.3,.3)
}
100% {
	opacity:0
}
}@-moz-keyframes zoomout {
	0% {
	opacity:1
}
50% {
	opacity:0;
	-moz-transform:scale3d(.3,.3,.3)
}
100% {
	opacity:0
}
}@-ms-keyframes zoomout {
	0% {
	opacity:1
}
50% {
	opacity:0;
	-ms-transform:scale3d(.3,.3,.3)
}
100% {
	opacity:0
}
}@keyframes zoomout {
	0% {
	opacity:1
}
50% {
	opacity:0;
	transform:scale3d(.3,.3,.3)
}
100% {
	opacity:0
}
}@-webkit-keyframes hinge {
	0% {
	-webkit-transform-origin:top left;
	-webkit-animation-timing-function:ease-in-out
}
20%,60% {
	-webkit-transform:rotate3d(0,0,1,80deg);
	-webkit-transform-origin:top left;
	-webkit-animation-timing-function:ease-in-out
}
40%,80% {
	-webkit-transform:rotate3d(0,0,1,60deg);
	-webkit-transform-origin:top left;
	-webkit-animation-timing-function:ease-in-out;
	opacity:1
}
100% {
	-webkit-transform:translate3d(0,700px,0);
	opacity:0
}
}@-moz-keyframes hinge {
	0% {
	-moz-transform-origin:top left;
	-moz-animation-timing-function:ease-in-out
}
20%,60% {
	-moz-transform:rotate3d(0,0,1,80deg);
	-moz-transform-origin:top left;
	-moz-animation-timing-function:ease-in-out
}
40%,80% {
	-moz-transform:rotate3d(0,0,1,60deg);
	-moz-transform-origin:top left;
	-moz-animation-timing-function:ease-in-out;
	opacity:1
}
100% {
	-moz-transform:translate3d(0,700px,0);
	opacity:0
}
}@-ms-keyframes hinge {
	0% {
	-ms-transform-origin:top left;
	-ms-animation-timing-function:ease-in-out
}
20%,60% {
	-ms-transform:rotate3d(0,0,1,80deg);
	-ms-transform-origin:top left;
	-ms-animation-timing-function:ease-in-out
}
40%,80% {
	-ms-transform:rotate3d(0,0,1,60deg);
	-ms-transform-origin:top left;
	-ms-animation-timing-function:ease-in-out;
	opacity:1
}
100% {
	-ms-transform:translate3d(0,700px,0);
	opacity:0
}
}@keyframes hinge {
	0% {
	transform-origin:top left;
	animation-timing-function:ease-in-out
}
20%,60% {
	transform:rotate3d(0,0,1,80deg);
	transform-origin:top left;
	animation-timing-function:ease-in-out
}
40%,80% {
	transform:rotate3d(0,0,1,60deg);
	transform-origin:top left;
	animation-timing-function:ease-in-out;
	opacity:1
}
100% {
	transform:translate3d(0,700px,0);
	opacity:0
}
}@-webkit-keyframes circleB {
	0% {
	-webkit-transform:rotate(360deg)
}
100% {
	-webkit-transform:rotate(0)
}
}@-moz-keyframes circleB {
	0% {
	-moz-transform:rotate(360deg)
}
100% {
	-moz-transform:rotate(0)
}
}@-ms-keyframes circleB {
	0% {
	-ms-transform:rotate(360deg)
}
100% {
	-ms-transform:rotate(0)
}
}@keyframes circleB {
	0% {
	transform:rotate(360deg)
}
100% {
	transform:rotate(0)
}
}@-webkit-keyframes circleF {
	0% {
	-webkit-transform:rotate(-360deg)
}
100% {
	-webkit-transform:rotate(0)
}
}@-moz-keyframes circleF {
	0% {
	-moz-transform:rotate(-360deg)
}
100% {
	-moz-transform:rotate(0)
}
}@-ms-keyframes circleF {
	0% {
	-ms-transform:rotate(-360deg)
}
100% {
	-ms-transform:rotate(0)
}
}@keyframes circleF {
	0% {
	transform:rotate(-360deg)
}
100% {
	transform:rotate(0)
}
}@-webkit-keyframes rollin {
	0% {
	opacity:0;
	-webkit-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)
}
100% {
	opacity:1;
	-webkit-transform:none
}
}@-moz-keyframes rollin {
	0% {
	opacity:0;
	-moz-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)
}
100% {
	opacity:1;
	-moz-transform:none
}
}@-ms-keyframes rollin {
	0% {
	opacity:0;
	-ms-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)
}
100% {
	opacity:1;
	-ms-transform:none
}
}@keyframes rollin {
	0% {
	opacity:0;
	transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)
}
100% {
	opacity:1;
	transform:none
}
}@-webkit-keyframes rollout {
	0% {
	opacity:1
}
100% {
	opacity:0;
	-webkit-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)
}
}@-moz-keyframes rollout {
	0% {
	opacity:1
}
100% {
	opacity:0;
	-moz-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)
}
}@-ms-keyframes rollout {
	0% {
	opacity:1
}
100% {
	opacity:0;
	-ms-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)
}
}@keyframes rollout {
	0% {
	opacity:1
}
100% {
	opacity:0;
	transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)
}
}