body{
  -webkit-user-select:initial;
}
body>a{display: none;}
.demo-desc{
    padding: 10px;
    font-size: 16px;
    color: #7CAE23;
}
.demo-block{
    position: relative;
    margin: 0 10px;
}

.demo-block .ui-header,
.demo-block .ui-footer{
    bottom: 56px;
}
/*.ui-footer~.ui-container { padding-bottom: 56px;}*/
.ui-tab-nav{
    top: 45px;
}
.ui-tab-content{

}
.demo-block > .ui-list,.demo-block >.ui-form,.demo-block >.ui-tooltips{margin-bottom: 20px;}
.content h3{
    padding: 0 15px;
    line-height: 44px;
    font-size: 15px;  
}
.border-list{
    background-color: #fff;
}
.border-list li{
    width: 100px;
    margin: 10px auto;
    -webkit-box-pack: center;
    text-align: center;
}
.ui-btn-group-bottom{bottom: 56px;}
.ui-table{
    line-height: 40px;
    text-align: center;
    background-color: #fff;
}
.ui-scroller {width:auto;height:300px;margin:20px;padding:10px;overflow:hidden;}
.ui-scroller li {margin-bottom:10px;}
.ui-notice{
    top: 0;
    z-index: -1;
    height: 400px;
}
.icon-lists li{
  float:left;
  height: 100px;
  width: 103px;
  text-align: center;
  
}
.icon-lists li i{
    font-size: 32px;
    line-height: 44px;
}
.icon-lists .ui-icon,.icon-lists [class^="ui-icon-"]{
  margin: 10px 0;
  color: #000;
}
.code,.fontclass{
    font-size: 12px;
}
.demo-block .ui-col{
    padding: 5px;
    background: #777;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid #ddd;
}
.demo-block .ui-flex{
    border: 1px solid #ddd;
    min-height: 80px;
    margin-bottom: 10px;
}
.demo-block .ui-flex-ver{
    border: 1px solid #ddd;
    min-height: 80px;
    margin-bottom: 10px;
}
.demo-block .ui-row-flex-ver{
    border: 1px solid #ddd;
    min-height: 100px;
}
.test-img {
    width: 50px;
    height: 50px;
    background: #777;
}
