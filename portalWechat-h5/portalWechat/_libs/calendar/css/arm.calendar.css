@charset "UTF-8";
/**
 * 
 * @authors <PERSON> (<EMAIL>)
 * @date    2015-11-24 13:49:54
 * @version 2015-11-24 13:49:54
 */
.arm-calendar, .arm-calendar .arm-calendar-days{
	position: relative;
}
.arm-calendar .calendar-days-box { overflow: hidden;}
.arm-calendar .calendar-next-view,.arm-calendar .calendar-prev-view {
	width: 100%;
	position: absolute;
	top: 0;
}
.arm-calendar .calendar-next-view {
	left: 100%;
}
.arm-calendar .calendar-prev-view {
	left: -100%;
}
.arm-calendar-default .calendar-toolbar {position: relative; overflow: hidden; background: #18b4ed; color: #fff;}
.arm-calendar-default .calendar-title {
	height: 44px;
	/* line-height: 44px; */
	text-align: left;
	margin:0 32px;
	position: relative;
}

.arm-calendar-default .calendar-tool {
	position: absolute;
	top: 50%;
	margin-top: -22px;
	height: 44px;
	line-height: 44px;
	color: #fff;
	z-index: 1;
}
.arm-calendar-default .calendar-view-switch{
	right: 52px;
}
.arm-calendar-default .calendar-view-switch .ui-btn {
	height: 20px;
	line-height: 20px;
	font-size: 12px;
	min-width: 0px;
	padding: 0 4px;
	margin-top: 12px;
	border-color: #329EC5;
	background: #36BDEF;
	color: #D0E9F9;
	border-radius: 5px;
}
.arm-calendar-default .calendar-view-switch .ui-btn:before {
	border-color: #329EC5;
	border-radius: 0;
}

.arm-calendar-default .calendar-view-switch .view-mode {
	background: #77D2F3;
	color: #fff;
}

.arm-calendar-default a:active{
	opacity: 0.5
}
.arm-calendar-default .calendar-next {
	right: 0;
}
.arm-calendar-default .calendar-today {
    right: 32px;
}
.arm-calendar-default .calendar-wrap {
	font-size: 75%;
}
.arm-calendar-default .calendar-week-bar { 
	color: #666;
	line-height: 2em;
}

.arm-calendar-default .calendar-grid {
	height:auto;
	color: #333;
	position: relative;
}
.arm-calendar-default .calendar-grid .grid {
	padding: 3px 0;
	min-height: 37px;
	width: 100%;
	font-size: 17px;
}

.arm-calendar-default .calendar-grid .day-number {
	font-size: 110%;
	line-height: 140%;
	padding: 5px 0;
}
.arm-calendar-default .grid-lunar .day-number{
	line-height: 110%;
	padding:2px 0;
}
.arm-calendar-default .grid-lunar .day-lunar {
	height: 12px;
	line-height: 12px;
	overflow: hidden;
	font-size: 60%;
	color: #B1B1B1;
}

.arm-calendar-default .grid-lunar .day-festival{
	color: #F98734;
}

/* .arm-calendar-default .calendar-week-bar .week-0-label,
.arm-calendar-default .calendar-week-bar .week-6-label,
.arm-calendar-default .grid-week-0,
.arm-calendar-default .grid-week-6 {
	color: #FF7171;
} */

.arm-calendar-default .grid-hidden{
	opacity: 0.5;
	background-color: #f9f9f9;
}

.arm-calendar-default .grid-hidden .grid{
	visibility: hidden;
}

.arm-calendar-default .grid-today .grid {
	background: #CEEDF9;
	color: #27B1E4;
	position: relative;
}

.arm-calendar-default .grid-today .grid:before{
    color: #EC9D21;
    content: "今";
    position: absolute;
    left: 1px;
    top: 1px;
    font-size: 65%;
}

.arm-calendar-default .grid-curday .grid {
	background: #16A2D6;
}
.arm-calendar-default .grid-curday .grid *{
	color: #fff;
}

.arm-calendar-default .grid-curday .grid:before {
	color: #F1C175;
}

.arm-calendar-default .grid-siblings-month .grid {
	opacity: 0.3;
}