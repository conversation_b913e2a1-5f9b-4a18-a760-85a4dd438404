*{
	-webkit-overflow-scrolling: touch;
}
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td,header,hgroup,nav,section,article,aside,footer,figure,figcaption,menu,button {
	margin: 0;
	padding: 0;
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}
html,body {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
body {
	font-family: "Helvetica Neue",Helvetica,STHeiTi,sans-serif;
	line-height: 1.5;
	font-size: 16px;
	color: #000;
	-webkit-user-select: none;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	outline: 0;
	overflow: auto;
}

h1,h2,h3,h4,h5,h6 {
	font-size: 100%;
	font-weight: 400
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

caption,th {
	text-align: left
}

fieldset,img {
	border: 0
}

li {
	list-style: none
}

ins {
	text-decoration: none
}

del {
	text-decoration: line-through
}

input,button,textarea,select,optgroup,option {
	font-family: inherit;
	font-size: inherit;
	font-style: inherit;
	font-weight: inherit;
	outline: 0
}

button {
	-webkit-appearance: none;
	border: 0;
	background: 0 0
}

a {
	-webkit-touch-callout: none;
	text-decoration: none
}

img {
	border: 0;
	vertical-align: middle;
}

:focus {
	outline: 0;
	-webkit-tap-highlight-color: transparent;
}

em,i {
	font-style: normal
}
.clear { overflow:auto!important; }
.none { display:none!important;}
.block{ display: block!important;}
.inline{ display: inline!important;}
.inline-bock{ display: inline-block!important;}
.hidden { visibility:hidden!important;}
.clearfix:after{clear: both;content: ".";display: block; line-height: 0; height: 0; visibility:hidden;}
.clearfix{display: block;}
.loading-bg {background:#fff url(../img/loading.gif) no-repeat 50%;}

@media screen and (max-width:319px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width:320px) and (max-width:359px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width:360px) and (max-width:374px) {
	html {
		font-size: 96px
	}
}

@media screen and (min-width:375px) and (max-width:383px) {
	html {
		font-size: 100px
	}
}

@media screen and (min-width:384px) and (max-width:399px) {
	html {
		font-size: 102.4px
	}
}

@media screen and (min-width:400px) and (max-width:413px) {
	html {
		font-size: 106.66667px
	}
}

@media screen and (min-width:414px) {
	html {
		font-size: 110.4px
	}
}

@charset "UTF-8";

a {
	color: #00a5e0
}

em {
	color: #ff8444
}

::-webkit-input-placeholder {
	color: #bbb
}

h1 {
	font-size: 18px
}

h2 {
	font-size: 17px
}

h3,h4 {
	font-size: 16px
}

h5,.ui-txt-sub {
	font-size: 14px
}

h6,.ui-txt-tips {
	font-size: 12px
}

.ui-txt-default {
	color: #000
}

.ui-txt-white {
	color: #fff
}

.ui-txt-info {
	color: #777
}

.ui-txt-muted {
	color: #bbb
}

.ui-txt-warning,.ui-txt-red {
	color: #ff4222
}

.ui-txt-feeds {
	color: #314c83
}

.ui-txt-highlight {
	color: #ff8444
}

.ui-txt-active {
	color: #00a5e0
}

.ui-txt-justify {
	text-align: justify
}

.ui-txt-justify-one {
	text-align: justify;
	overflow: hidden;
	height: 24px
}

.ui-txt-justify-one:after {
	display: inline-block;
	content: '';
	overflow: hidden;
	width: 100%;
	height: 0
}

.ui-preloading {
	position:fixed; right:0px; top:0px; bottom:0px; left:0px; z-index:100000; background:#fff;
}

.ui-placehold {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold-cnt {
	color: #bbb;
	position: absolute;
	top: 0;
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-sizing: border-box;
	text-align: center;
	height: 100%;
	z-index: -1
}

.ui-placehold-img {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-placehold-img img {
	width: 100%;
	height: 100%
}

@charset "UTF-8";

.ui-header,.ui-footer,.ui-container {
	position: absolute;
	width: 100%;
	z-index: 100;
	left: 0;
	-webkit-transform-style: preserve-3d;
}

.ui-header {
	top: 0;
	height: 45px;
	line-height: 45px
}

.ui-header-stable,.ui-header-positive {
	padding: 0 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-header-stable,.ui-footer-stable {
	background-color: #f8f8f8
}

.ui-header-positive,.ui-footer-positive {
	background-color: #18b4ed;
	color: #fff
}

.ui-header-positive a,.ui-header-positive a:active,.ui-header-positive i,.ui-footer-positive a,.ui-footer-positive a:active,.ui-footer-positive i {
	color: #fff
}

.ui-footer-btn {
    background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0,#f9f9f9),to(#e0e0e0));
    color: #00a5e0;
}

.ui-footer-btn .ui-tiled {
	height: 100%
}
.ui-footer-btn .ui-con,.ui-footer-btn [class^=ui-icon-]{
	line-height: 16px;
}
.ui-footer-btn .ui-con:before,.ui-footer-btn [class^=ui-icon-]:before{
	font-size: 28px;
	line-height: 28px;
}

.ui-footer-btn .ui-tiled .ui-tiled-active .ui-con,.ui-footer-btn .ui-tiled .ui-tiled-active [class^=ui-icon-] {
	color: #03a6e1
}

.ui-footer {
	bottom: 0;
	height: 49px;
}
.ui-container {
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	overflow: auto;
	z-index: 0;
	position: absolute;

}
.ui-header~.ui-container {
	/*border-top: 45px solid transparent*/
	top: 45px;
}

.ui-footer~.ui-container {
	/*border-bottom: 49px solid transparent*/
	bottom: 49px;
}

.ui-header h1 {
	text-align: center;
	font-size: 18px
}

.ui-header .ui-icon-return {
	position: absolute;
	top: 0;
	left: 0;
}
.ui-header .ui-icon-home {
	position: absolute;
	right: 0;
	top: 0;
	padding: 0 3px;
}

.ui-header .ui-btn {
	display: block;
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -15px;
	border-color: #DADADA;
	padding: 0
}

.ui-center {
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 150px
}

.ui-flex,.ui-tiled {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-flex-ver {
	-webkit-box-orient: vertical
}

.ui-flex-pack-start {
	-webkit-box-pack: start
}

.ui-flex-pack-end {
	-webkit-box-pack: end
}

.ui-flex-pack-center {
	-webkit-box-pack: center
}

.ui-flex-align-start {
	-webkit-box-align: start
}

.ui-flex-align-end {
	-webkit-box-align: end
}

.ui-flex-align-center {
	-webkit-box-align: center
}

.ui-tiled li {
	-webkit-box-flex: 1;
	width: 100%;
	text-align: center;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center
}

@charset "UTF-8";

.ui-arrowlink {
	position: relative
}

.ui-arrowlink:before {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media (max-width:320px) {
	.ui-arrowlink:before {
		right: 10px
	}
}

.ui-arrowlink.active {
	background: #e5e6e7
}

.ui-whitespace {
	padding-left: 15px;
	padding-right: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media (max-width:320px) {
	.ui-whitespace {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-whitespace-left {
	padding-left: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media (max-width:320px) {
	.ui-whitespace-left {
		padding-left: 10px
	}
}

.ui-whitespace-right {
	padding-right: 15px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

@media (max-width:320px) {
	.ui-whitespace-right {
		padding-right: 10px
	}
}

@charset "UTF-8";

.ui-nowrap {
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-nowrap-flex {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-nowrap-multi {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2
}

.ui-justify {
	text-align: justify;
	font-size: 0
}

.ui-justify:after {
	content: '';
	display: inline-block;
	width: 100%;
	height: 0;
	overflow: hidden
}

.ui-justify li {
	display: inline-block;
	text-align: center
}

.ui-justify p {
	font-size: 16px
}

.ui-justify-flex {
	width: 100%;
	display: -webkit-box;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between
}

@charset "UTF-8";

.ui-grid,.ui-grid-trisect,.ui-grid-halve {
	padding-left: 15px;
	padding-right: 10px;
	overflow: hidden;
	padding-top: 10px
}

@media (max-width:320px) {
	.ui-grid,.ui-grid-trisect,.ui-grid-halve {
		padding-left: 10px;
		padding-right: 5px
	}
}

.ui-grid li,.ui-grid-trisect li,.ui-grid-halve li {
	padding-right: 5px;
	padding-bottom: 10px;
	float: left;
	position: relative;
	-webkit-box-sizing: border-box
}

.ui-grid-trisect>li {
	width: 33.3333%
}

.ui-grid-trisect-img {
	padding-top: 149.47%
}

.ui-grid-trisect h4 {
	position: relative;
	margin: 7px 0 3px
}

.ui-grid-trisect h4 span {
	display: inline-block;
	margin-left: 12px;
	color: #777
}

.ui-grid-halve>li {
	width: 50%
}

.ui-grid-halve-img {
	padding-top: 55.17%
}

.ui-grid-trisect-img,.ui-grid-halve-img {
	position: relative;
	width: 100%
}

.ui-grid-trisect-img>span,.ui-grid-halve-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-grid-trisect-img img,.ui-grid-halve-img img {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0
}

.ui-grid-trisect-img.active,.ui-grid-halve-img.active {
	opacity: .5
}

.ui-row {
	display: block;
	overflow: hidden
}

.ui-col {
	float: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%
}

.ui-col-10 {
	width: 10%
}

.ui-col-20 {
	width: 20%
}

.ui-col-25 {
	width: 25%
}

.ui-col-33 {
	width: 33.3333%
}

.ui-col-50 {
	width: 50%
}

.ui-col-67 {
	width: 66.6666%
}

.ui-col-75 {
	width: 75%
}

.ui-col-80 {
	width: 80%
}

.ui-col-90 {
	width: 90%
}

.ui-row-flex {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-row-flex .ui-col {
	float: none;
	-webkit-box-flex: 1;
	width: 0
}

.ui-row-flex .ui-col-2 {
	-webkit-box-flex: 2
}

.ui-row-flex .ui-col-3 {
	-webkit-box-flex: 3
}

.ui-row-flex .ui-col-4 {
	-webkit-box-flex: 4
}

.ui-row-flex-ver {
	-webkit-box-orient: vertical
}

.ui-row-flex-ver .ui-col {
	width: 100%;
	height: 0
}

.ui-border-t {
	border-top: 1px solid #e0e0e0
}

.ui-border-b {
	border-bottom: 1px solid #e0e0e0
}

.ui-border-tb {
	border-top: #e0e0e0 1px solid;
	border-bottom: #e0e0e0 1px solid;
	background-image: none
}

.ui-border-l {
	border-left: 1px solid #e0e0e0
}

.ui-border-r {
	border-right: 1px solid #e0e0e0
}

.ui-border-lr {
	border-left: #e0e0e0 1px solid;
	border-right: #e0e0e0 1px solid;
	background-image: none
}

.ui-border-rb {
	border-bottom: #e0e0e0 1px solid;
	border-right: #e0e0e0 1px solid;
	background-image: none
}

.ui-border {
	border: 1px solid #e0e0e0
}

.ui-border-radius {
	border: 1px solid #e0e0e0;
	border-radius: 4px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-border-radius {
		position: relative;
		border: 0
	}

	.ui-border-radius:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #e0e0e0;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 8px;
		pointer-events: none
	}
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-border {
		position: relative;
		border: 0
	}

	.ui-border-t,.ui-border-b,.ui-border-l,.ui-border-r,.ui-border-tb,.ui-border-lr,.ui-border-rb {
		border: 0
	}

	.ui-border-t {
		background-position: left top;
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}

	.ui-border-b {
		background-position: left bottom;
		background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}

	.ui-border-t,.ui-border-b,.ui-border-tb {
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}

	.ui-border-tb {
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		background-position: top,bottom
	}

	.ui-border-l {
		background-position: left top;
		background-image: -webkit-gradient(linear,right top,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}

	.ui-border-r {
		background-position: right top;
		background-image: -webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}

	.ui-border-l,.ui-border-r,.ui-border-lr {
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%
	}

	.ui-border-lr {
		background-image: -webkit-gradient(linear,right top,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		background-position: left,right
	}

	.ui-border:after {
		content: "";
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,right top,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		-webkit-background-size: 100% 1px,1px 100%,100% 1px,1px 100%;
		background-size: 100% 1px,1px 100%,100% 1px,1px 100%;
		-webkit-background-size: 100% 1px,1px 100%,100% 1px,1px 100%;
		background-size: 100% 1px,1px 100%,100% 1px,1px 100%;
		background-repeat: no-repeat;
		background-position: top,right,bottom,left;
		padding: 1px;
		-webkit-box-sizing: border-box;
		z-index: 10;
		pointer-events: none
	}
}

.ui-icon,[class^=ui-icon-] {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: inline-block;
}

.ui-icon-close:before {
	content: "\e60a"
}

.ui-icon-search:before {
	content: "\e60c"
}

.ui-icon-return:before {
	content: "\e614"
}

.ui-icon-close,.ui-icon-search {
	color: #8e8e93
}
.ui-icon-change {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
    -webkit-transform: rotate(0);
    transform: rotate(0);
    border-radius: 9px;
    overflow: hidden;
}
.ui-icon-change:before, .ui-icon-change:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 1px #8b8b8b solid;
    border-radius: 50%;
    left: 1px;
    top: 1px;
    background: #fff;
}
.ui-icon-change:after {
    border: none;
    left: 50%;
    top: 50%;
    width: 8px;
    height: 8px;
    z-index: 0;
}
.ui-icon-changing {
	-webkit-animation: circleF 0.7s ease 0.5s infinite;
	-o-animation: circleF 0.7s ease 0.5s infinite;
	animation: circleF 0.7s ease 0.5s infinite;
}

.ui-panel {
	overflow: hidden;
	padding-top: 10px
}

.ui-panel .ui-grid-halve,.ui-panel .ui-grid-trisect {
	padding-top: 0
}

.ui-panel h1,.ui-panel h2,.ui-panel h3 {
	padding-left: 15px;
	padding-right: 15px;
	line-height: 44px;
	position: relative;
	overflow: hidden;
	display: -webkit-box
}

@media (max-width:320px) {
	.ui-panel h1,.ui-panel h2,.ui-panel h3 {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-panel h1 span,.ui-panel h2 span,.ui-panel h3 span {
	display: block
}

.ui-panel-pure h2,.ui-panel-pure h3 {
	color: #777
}

.ui-panel-card {
	margin-bottom: 10px
}

.ui-panel-card,.ui-panel-simple {
	background-color: #fff
}

.ui-panel-simple {
	padding-top: 0
}

.ui-panel-subtitle {
	font-size: 14px;
	color: #777;
	margin-left: 10px
}

.ui-panel-title-tips {
	font-size: 12px;
	color: #777;
	position: absolute;
	right: 15px
}

.ui-panel-drawer .ui-panel-bd {
	height: 0;
	overflow: hidden;
}
.ui-panel-drawer.active .ui-panel-bd{
 	height: auto;
}

@media (max-width:320px) {
	.ui-panel-title-tips {
		right: 10px
	}
}

.ui-arrowlink .ui-panel-title-tips {
	right: 30px
}

@media (max-width:320px) {
	.ui-arrowlink .ui-panel-title-tips {
		right: 25px
	}
}

@font-face {
	font-family:iconfont;src:url(../font/iconfont-full.ttf) format("truetype")
}

.ui-icon-add:before {
	content: "\e615"
}

.ui-icon-more:before {
	content: "\e616"
}

.ui-icon-arrow:before {
	content: "\e600"
}

.ui-icon-return:before {
	content: "\e614"
}

.ui-icon-checked:before {
	content: "\e601"
}

.ui-icon-checked-s:before {
	content: "\e602"
}

.ui-icon-info-block:before {
	content: "\e603"
}

.ui-icon-success-block:before {
	content: "\e604"
}

.ui-icon-warn-block:before {
	content: "\e605"
}

.ui-icon-info:before {
	content: "\e606"
}

.ui-icon-success:before {
	content: "\e607"
}

.ui-icon-warn:before {
	content: "\e608"
}

.ui-icon-next:before {
	content: "\e617"
}

.ui-icon-prev:before {
	content: "\e618"
}

.ui-icon-tag:before {
	content: "\e60d"
}

.ui-icon-tag-pop:before {
	content: "\e60f"
}

.ui-icon-tag-s:before {
	content: "\e60e"
}

.ui-icon-warn-lg:before {
	content: "\e609"
}

.ui-icon-close:before {
	content: "\e60a"
}

.ui-icon-close-progress:before {
	content: "\e619"
}

.ui-icon-close-page:before {
	content: "\e60b"
}

.ui-icon-emo:before {
	content: "\e61a"
}

.ui-icon-delete:before {
	content: "\e61b"
}

.ui-icon-search:before {
	content: "\e60c"
}

.ui-icon-order:before {
	content: "\e61c"
}

.ui-icon-news:before {
	content: "\e61d"
}

.ui-icon-personal:before {
	content: "\e61e"
}

.ui-icon-dressup:before {
	content: "\e61f"
}

.ui-icon-cart:before {
	content: "\e620"
}

.ui-icon-history:before {
	content: "\e621"
}

.ui-icon-wallet:before {
	content: "\e622"
}

.ui-icon-refresh:before {
	content: "\e623"
}

.ui-icon-thumb:before {
	content: "\e624"
}

.ui-icon-file:before {
	content: "\e625"
}

.ui-icon-hall:before {
	content: "\e626"
}

.ui-icon-voice:before {
	content: "\e627"
}

.ui-icon-unfold:before {
	content: "\e628"
}

.ui-icon-gototop:before {
	content: "\e629"
}

.ui-icon-share:before {
	content: "\e62a"
}

.ui-icon-home:before {
	content: "\e62b"
}

.ui-icon-pin:before {
	content: "\e62c"
}

.ui-icon-star:before {
	content: "\e62d"
}

.ui-icon-bugle:before {
	content: "\e62e"
}

.ui-icon-trend:before {
	content: "\e62f"
}

.ui-icon-unchecked:before {
	content: "\e610"
}

.ui-icon-unchecked-s:before {
	content: "\e611"
}

.ui-icon-play-active:before {
	content: "\e630"
}

.ui-icon-stop-active:before {
	content: "\e631"
}

.ui-icon-play:before {
	content: "\e632"
}

.ui-icon-stop:before {
	content: "\e633"
}

.ui-icon-set:before {
	content: "\e634"
}

.ui-icon-add-group:before {
	content: "\e635"
}

.ui-icon-add-people:before {
	content: "\e636"
}

.ui-icon-pc:before {
	content: "\e637"
}

.ui-icon-scan:before {
	content: "\e638"
}

.ui-icon-tag-svip:before {
	content: "\e613"
}

.ui-icon-tag-vip:before {
	content: "\e612"
}

.ui-icon-male:before {
	content: "\e639"
}

.ui-icon-female:before {
	content: "\e63a"
}

.ui-icon-collect:before {
	content: "\e63b"
}

.ui-icon-commented:before {
	content: "\e63c"
}

.ui-icon-like:before {
	content: "\e63d"
}

.ui-icon-liked:before {
	content: "\e63e"
}

.ui-icon-comment:before {
	content: "\e63f"
}

.ui-icon-collected:before {
	content: "\e640"
}

@charset "UTF-8";

.ui-btn,.ui-btn-lg,.ui-btn-s {
	height: 30px;
	line-height: 30px;
	padding: 0 11px;
	min-width: 55px;
	display: inline-block;
	position: relative;
	text-align: center;
	font-size: 15px;
	background-color: #fdfdfd;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#fff),to(#fafafa));
	vertical-align: top;
	color: #00a5e0;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid #cacccd;
	border-radius: 3px;
	overflow: hidden;
	cursor: pointer;
}

.ui-btn-noradius {
	border-radius: 0px;
}

.ui-radius {
	border-radius: 3px;
}

.ui-round {
	border-radius: 1000px;
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn,.ui-btn-lg,.ui-btn-s {
		position: relative;
		border: 0
	}

	.ui-btn:before,.ui-btn-lg:before,.ui-btn-s:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 6px;
		pointer-events: none
	}

}

.ui-btn:not(.disabled):not(:disabled):active,.ui-btn-lg:not(.disabled):not(:disabled):active,.ui-btn-s:not(.disabled):not(:disabled):active,.ui-btn.active,.active.ui-btn-lg,.active.ui-btn-s {
	background: #f9f9f9;
	color: rgba(0,165,224,.5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-primary {
	background-color: #18b4ed;
	border-color: #0baae4;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#1fbaf3),to(#18b4ed));
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-primary:not(.disabled):not(:disabled):active,.ui-btn-primary.active {
	background: #1ca7da;
	border-color: #1ca7da;
	color: rgba(255,255,255,.5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-success {
	background-color: #5eb95e;
	border-color: #47B647;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#69BB69),to(#5eb95e));
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-success:not(.disabled):not(:disabled):active,.ui-btn-success.active {
	background: #50A050;
	border-color: #4D994D;
	color: rgba(255,255,255,.5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-warning {
	background-color: #f37b1d;
	border-color: #EA7418;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#F5832A),to(#f37b1d));
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-warning:not(.disabled):not(:disabled):active,.ui-btn-warning.active {
	background: #DA7628;
	border-color: #DB7C32;
	color: rgba(255,255,255,.5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger {
	background-color: #f75549;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#fc6156),to(#f75549));
	color: #fff;
	border-color: #f43d30;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger:not(.disabled):not(:disabled):active,.ui-btn-danger.active {
	background: #e2574d;
	border-color: #e2574d;
	color: rgba(255,255,255,.5);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn.disabled,.disabled.ui-btn-lg,.disabled.ui-btn-s,.ui-btn:disabled,.ui-btn-lg:disabled,.ui-btn-s:disabled {
	border: 0;
	color: #ccc;
	background: #e9ebec;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-lg {
	font-size: 18px;
	height: 44px;
	line-height: 44px;
	display: block;
	width: 100%;
	border-radius: 5px
}

.ui-btn-wrap {
	padding: 15px 10px
}

@media (max-width:320px) {
	.ui-btn-wrap {
		padding: 10px
	}
}

.ui-btn-s {
	padding: 0;
	width: 55px;
	height: 25px;
	line-height: 25px;
	font-size: 13px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn-primary:before {
		border: 1px solid #0baae4
	}

	.ui-btn-danger:before {
		border: 1px solid #f43d30
	}

	.ui-btn,.ui-btn-lg,.ui-btn-s {
		border: 0
	}

	.ui-btn.disabled,.disabled.ui-btn-lg,.disabled.ui-btn-s,.ui-btn:disabled,.ui-btn-lg:disabled,.ui-btn-s:disabled,.ui-btn.disabled:before,.disabled.ui-btn-lg:before,.disabled.ui-btn-s:before,.ui-btn:disabled:before,.ui-btn-lg:disabled:before,.ui-btn-s:disabled:before {
		border: 1px solid #e9ebec
	}

	.ui-btn-lg:before {
		border-radius: 10px
	}
}

.ui-btn-group {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center
}

.ui-btn-group button,.ui-btn-group .ui-btn,.ui-btn-group [class^=ui-btn-] {
	display: block;
	-webkit-box-flex: 1;
	margin-right: 10px
}

.ui-btn-group button:first-child,.ui-btn-group .ui-btn:first-child,.ui-btn-group [class^=ui-btn-]:first-child {
	margin-left: 10px;
}

.ui-btn-multi {
	display: inline-block;
	position: relative;
	vertical-align: middle;
}
.ui-btn-multi button,.ui-btn-multi [class^=ui-btn]{
	margin-left: 0px;
	float: left;
}
.ui-btn-multi button:not(:first-child),.ui-btn-multi [class^=ui-btn]:not(:first-child){
	margin-left: -1px;
}
.ui-btn-multi button:not(:first-child):not(:last-child),.ui-btn-multi [class^=ui-btn]:not(:first-child):not(:last-child){
	border-radius: 0;
}
.ui-btn-multi button:first-child:not(:last-child),.ui-btn-multi [class^=ui-btn]:first-child:not(:last-child){
	border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.ui-btn-multi button:last-child:not(:first-child),.ui-btn-multi [class^=ui-btn]:last-child:not(:first-child){
	border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}

.ui-btn-progress {
	width: 55px;
	padding: 0;
	overflow: hidden
}

.ui-btn-progress .ui-btn-inner {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	overflow: hidden;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#1fbaf3),to(#18b4ed));
	border-bottom-left-radius: 3px;
	border-top-left-radius: 3px
}

.ui-btn-progress .ui-btn-inner span {
	display: inline-block;
	color: #fff;
	position: absolute;
	width: 55px;
	left: 0
}

.ui-btn-progress.disabled,.ui-btn-progress:disabled {
	background-color: #fefefe;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,#fff),to(#fafafa));
	color: #ccc;
	border: 1px solid #cacccd;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn-progress.disabled,.ui-btn-progress:disabled {
		border: 0
	}

	.ui-btn-progress.disabled:before,.ui-btn-progress:disabled:before {
		border: 1px solid #cacccd
	}
}

@charset "UTF-8";

.ui-avatar,.ui-avatar-lg,.ui-avatar-s,.ui-avatar-one,.ui-avatar-tiled {
	display: block;
	-webkit-background-size: cover;
	background-image: url(../img/avatar.jpg);
}

.ui-avatar {
	width: 50px;
	height: 50px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-lg,.ui-avatar-one {
	width: 70px;
	height: 70px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-lg>span,.ui-avatar-one>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-s {
	width: 40px;
	height: 40px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-s>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-tiled {
	width: 30px;
	height: 30px;
	-webkit-border-radius: 200px;
	overflow: hidden;
	display: inline-block
}

.ui-avatar-tiled>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

@charset "UTF-8";

.ui-blank-cover {
	display: block;
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: url(../img/blank.gif);
	outline: 0;
	border: 0;
	padding: 0;
	margin: 0;
	opacity: 0;
}

.ui-badge,.ui-badge-muted,.ui-badge-num,.ui-badge-corner,.ui-badge-cornernum {
	display: inline-block;
	text-align: center;
	background: #f74c31;
	color: #fff;
	font-size: 11px;
	height: 16px;
	line-height: 16px;
	-webkit-border-radius: 8px;
	padding: 0 6px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-badge-muted {
	background: #b6cae0
}

.ui-badge-num {
	height: 19px;
	line-height: 20px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px
}

.ui-badge-wrap {
	position: relative;
	text-align: center
}

.ui-badge-corner {
	position: absolute;
	border: 2px #fff solid;
	height: 20px;
	line-height: 20px;
	top: -4px;
	right: -9px
}

.ui-badge-cornernum {
	position: absolute;
	top: -4px;
	right: -9px;
	height: 19px;
	line-height: 19px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px;
	top: -5px;
	right: -5px
}

@charset "UTF-8";

.ui-reddot,.ui-reddot-border,.ui-reddot-s {
	position: relative;
	display: inline-block;
	line-height: 22px;
	padding: 0 6px
}

.ui-reddot:after,.ui-reddot-border:after,.ui-reddot-s:after {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #f74c31;
	border-radius: 5px;
	right: -3px;
	top: -3px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-reddot-static {
	display: block;
	width: 8px;
	height: 8px;
	padding: 0
}

.ui-reddot-static:after {
	top: 0;
	right: 0
}

.ui-reddot-border:before {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #fff;
	border-radius: 5px;
	right: -4px;
	top: -4px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	padding: 1px
}

.ui-reddot-s:after {
	width: 6px;
	height: 6px;
	top: -5px;
	right: -5px
}

.ui-label {
	display: inline-block;
	position: relative;
	line-height: 30px;
	height: 30px;
	padding: 0 15px;
	border: 1px solid #cacccd;
	border-radius: 15px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-label {
		position: relative;
		border: 0
	}

	.ui-label:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 30px;
		pointer-events: none
	}
}

.ui-label:active {
	background-color: #f3f2f2
}

.ui-label-list {
	margin: 0 10px
}

.ui-label-list .ui-label {
	margin: 0 10px 10px 0
}

.ui-label-s {
	font-size: 11px;
	line-height: 13px;
	display: inline-block;
	position: relative;
	padding: 0 1px;
	color: #ff7f0d;
	border: 1px solid #ff7f0d;
	border-radius: 2px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-label-s {
		position: relative;
		border: 0
	}

	.ui-label-s:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #ff7f0d;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 4px;
		pointer-events: none
	}
}

.ui-label-s:active {
	background-color: #f3f2f2
}

.ui-label-s:after {
	content: "";
	position: absolute;
	top: -5px;
	bottom: -5px;
	left: -5px;
	right: -5px
}

@charset "UTF-8";

.ui-tag-t,.ui-tag-hot,.ui-tag-new,.ui-tag-s-hot,.ui-tag-s-new,.ui-tag-pop-hot,.ui-tag-pop-new {
	position: relative
}

.ui-tag-t:before,.ui-tag-hot:before,.ui-tag-new:before,.ui-tag-s-hot:before,.ui-tag-s-new:before,.ui-tag-pop-hot:before,.ui-tag-pop-new:before,.ui-tag-t:after,.ui-tag-hot:after,.ui-tag-new:after,.ui-tag-s-hot:after,.ui-tag-s-new:after,.ui-tag-pop-hot:after,.ui-tag-pop-new:after {
	height: 20px;
	left: 0;
	top: 0;
	z-index: 9;
	display: block
}

.ui-tag-t:before,.ui-tag-hot:before,.ui-tag-new:before,.ui-tag-s-hot:before,.ui-tag-s-new:before,.ui-tag-pop-hot:before,.ui-tag-pop-new:before,.ui-tag-vip:before,.ui-tag-svip:before,.ui-tag-selected:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	position: absolute
}

.ui-tag-t:before,.ui-tag-hot:before,.ui-tag-new:before,.ui-tag-s-hot:before,.ui-tag-s-new:before,.ui-tag-pop-hot:before,.ui-tag-pop-new:before {
	content: "\e60d";
	line-height: 20px;
	color: red
}

.ui-tag-t:after,.ui-tag-hot:after,.ui-tag-new:after,.ui-tag-s-hot:after,.ui-tag-s-new:after,.ui-tag-pop-hot:after,.ui-tag-pop-new:after {
	position: absolute;
	content: '';
	width: 22px;
	text-align: right;
	line-height: 20px;
	font-size: 12px;
	color: #fff;
	padding-right: 14px
}

.ui-tag-b,.ui-tag-freelimit,.ui-tag-free,.ui-tag-last,.ui-tag-limit,.ui-tag-act,.ui-tag-xy,.ui-tag-vip,.ui-tag-svip {
	position: relative
}

.ui-tag-b:before,.ui-tag-freelimit:before,.ui-tag-free:before,.ui-tag-last:before,.ui-tag-limit:before,.ui-tag-act:before,.ui-tag-xy:before,.ui-tag-vip:before,.ui-tag-svip:before {
	position: absolute;
	font-size: 10px;
	width: 28px;
	height: 13px;
	line-height: 13px;
	bottom: 0;
	right: 0;
	z-index: 9;
	color: #fff;
	border-radius: 2px;
	text-align: center
}

.ui-tag-vip:before,.ui-tag-svip:before {
	font-size: 32px;
	text-indent: -2px;
	border-radius: 2px
}

.ui-tag-vip:before {
	background-color: red;
	color: #fffadf;
	content: "\e612"
}

.ui-tag-svip:before {
	background-color: #ffd400;
	color: #b7440e;
	content: "\e613"
}

.ui-tag-freelimit:before {
	background-color: #18b4ed;
	content: '限免'
}

.ui-tag-free:before {
	background-color: #5fb336;
	content: '免费'
}

.ui-tag-last:before {
	background-color: #8f6adb;
	content: '绝版'
}

.ui-tag-limit:before {
	background-color: #3385e6;
	content: '限量'
}

.ui-tag-act:before {
	background-color: #00c795;
	content: '活动'
}

.ui-tag-xy:before {
	background-color: #d7ba42;
	content: '星影'
}

.ui-tag-freemonthly:before {
	background-color: #ff7f0d;
	content: '包月'
}

.ui-tag-onsale:before {
	background-color: #00c795;
	content: '特价'
}

.ui-tag-hot:after,.ui-tag-s-hot:after,.ui-tag-pop-hot:after {
	content: '热'
}

.ui-tag-new:after,.ui-tag-s-new:after,.ui-tag-pop-new:after {
	content: '新'
}

.ui-tag-hot:before,.ui-tag-s-hot:before,.ui-tag-pop-hot:before {
	color: #ff7200
}

.ui-tag-s-hot:before,.ui-tag-s-new:before {
	content: "\e60e";
	left: -2px
}

.ui-tag-s-hot:after,.ui-tag-s-new:after {
	width: 16px;
	padding-right: 12px
}

.ui-tag-selected:after {
	content: "\e601";
	color: #18b4ed;
	right: -5px;
	top: -5px;
	z-index: 9;
	width: 26px;
	height: 26px;
	background: #fff;
	border-radius: 13px;
	line-height: 26px;
	text-indent: -3px
}

.ui-tag-wrap {
	display: inline-block;
	position: relative;
	padding-right: 32px
}

.ui-tag-wrap .ui-tag-vip,.ui-tag-wrap .ui-tag-svip {
	position: static
}

.ui-tag-wrap .ui-tag-vip:before,.ui-tag-wrap .ui-tag-svip:before {
	top: 50%;
	margin-top: -7px
}

.ui-tag-pop-hot:before,.ui-tag-pop-new:before {
	content: "\e60f";
	left: -10px;
	top: 1px
}

.ui-tag-pop-hot:after,.ui-tag-pop-new:after {
	font-size: 11px;
	padding-right: 0;
	text-align: center;
	left: -5px
}

.ui-searchbar-wrap {
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background-color: #ebeced;
	height: 44px
}

.ui-searchbar-wrap button {
	margin-right: 10px
}

.ui-searchbar-wrap .ui-searchbar-cancel {
	color: #00a5e0;
	font-size: 16px;
	padding: 4px 8px
}

.ui-searchbar-wrap .ui-searchbar-input,.ui-searchbar-wrap button,.ui-searchbar-wrap .ui-icon-close {
	display: none
}

.ui-searchbar-wrap.focus {
	-webkit-box-pack: start
}

.ui-searchbar-wrap.focus .ui-searchbar-input,.ui-searchbar-wrap.focus button,.ui-searchbar-wrap.focus .ui-icon-close {
	display: block
}

.ui-searchbar-wrap.focus .ui-searchbar-text {
	display: none
}

.ui-searchbar {
	border-radius: 5px;
	margin: 0 10px;
	background: #fff;
	height: 30px;
	line-height: 30px;
	position: relative;
	padding-left: 4px;
	-webkit-box-flex: 1;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	color: #bbb;
	width: 100%
}

.ui-searchbar input {
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	color: #000;
	width: 100%;
	padding: 4px 0
}

.ui-searchbar.ui-border-radius {
	border-radius: 5px
}

.ui-searchbar .ui-icon-search {
	line-height: 30px
}

.ui-searchbar .ui-icon-close {
	line-height: 30px
}

.ui-searchbar-input {
	-webkit-box-flex: 1
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-searchbar.ui-border-radius:before {
		border-radius: 10px
	}
}

@charset "UTF-8";

.ui-list {
	background-color: #fff;
	width: 100%
}

.ui-list>li {
	position: relative;
	margin-left: 15px;
	line-height: 24px;
	display: -webkit-box
}

.ui-list-pure>li {
	display: block
}

.ui-list-text>li,.ui-list-pure>li {
	position: relative;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-right: 15px;
	-webkit-box-align: center
}

.ui-list-text h4,.ui-list-text p {
	-webkit-box-flex: 1
}

.ui-list-cover>li {
	padding-left: 15px;
	margin-left: 0
}

.ui-list>li.ui-border-t:first-child,.ui-list>li:first-child>.ui-border-t {
	border: 0;
	background-image: none
}

.ui-list-thumb,.ui-list-thumb-s,.ui-list-img,.ui-list-icon {
	position: relative;
	margin: 10px 10px 10px 0
}

.ui-list-thumb>span,.ui-list-thumb-s>span,.ui-list-img>span,.ui-list-icon>span {
	display: block;
	width: 100%;
	height: 100%;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-list-thumb {
	width: 50px;
	height: 50px
}

.ui-list-img {
	width: 100px;
	height: 68px
}

.ui-list-thumb-s {
	width: 28px;
	height: 28px
}

.ui-list-icon {
	width: 40px;
	height: 40px
}

.ui-list .ui-avatar,.ui-list .ui-avatar-s,.ui-list .ui-avatar-lg {
	margin: 10px 10px 10px 0
}

.ui-list-info {
	-webkit-box-flex: 1;
	padding-top: 10px;
	padding-bottom: 10px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	padding-right: 15px
}

.ui-list-info p {
	color: #777;
	font-size: 14px
}

.ui-list-text .ui-list-info {
	padding-top: 0;
	padding-bottom: 0
}

.ui-list li h4 {
	font-size: 16px
}

.ui-list:not(.ui-list-text) li>p,.ui-list li>h5 {
	font-size: 14px;
	color: #777
}

.ui-list-active>li:active,.ui-list li.active {
	background-color: #e5e6e7;
	padding-left: 15px;
	margin-left: 0
}

.ui-list-active>li:active,.ui-list>li.active,.ui-list>li.active>.ui-border-t,.ui-list>li.active+li>.ui-border-t,.ui-list>li.active+li.ui-border-t {
	background-image: none;
	border-top-color: #e5e6e7
}

.ui-list-link>li:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media (max-width:320px) {
	.ui-list-link>li:after {
		right: 10px
	}
}

.ui-list-text.ui-list-link>li {
	padding-right: 30px
}

.ui-list-link .ui-list-info {
	padding-right: 30px
}

.ui-list-function .ui-list-info {
	padding-right: 75px
}

.ui-list-function .ui-btn {
	position: absolute;
	top: 50%;
	right: 15px;
	margin-top: -15px
}

.ui-list-function .ui-btn-s {
	margin-top: -12px
}

.ui-list-function.ui-list-link .ui-list-info {
	padding-right: 90px
}

.ui-list-function.ui-list-link .ui-btn {
	right: 30px
}

.ui-list-function li {
	-webkit-box-align: inherit
}

.ui-list-one>li {
	padding-top: 0;
	padding-bottom: 0;
	line-height: 44px
}

.ui-list-one .ui-list-info {
	-webkit-box-orient: horizontal;
	-webkit-box-align: center
}

.ui-list-one h4 {
	-webkit-box-flex: 1
}

@media (max-width:320px) {
	.ui-list>li {
		margin-left: 10px
	}

	.ui-list-text>li,.ui-list-pure>li,.ui-list-info {
		padding-right: 10px
	}

	.ui-list-cover>li,.ui-list-active>li:active,.ui-list li.active {
		padding-left: 10px
	}

	.ui-list-text.ui-list-link>li {
		padding-right: 25px
	}

	.ui-list-function .ui-list-info {
		padding-right: 70px
	}

	.ui-list-function .ui-btn {
		right: 10px
	}

	.ui-list-function.ui-list-link .ui-list-info {
		padding-right: 85px
	}

	.ui-list-function.ui-list-link .ui-btn {
		right: 25px
	}
}

@charset "UTF-8";

.ui-form {
	background-color: #fff
}

.ui-form-item-order.active {
	background-color: #e5e6e7
}

.ui-form-item {
	position: relative;
	font-size: 16px;
	height: 44px;
	line-height: 44px;
	padding-right: 15px;
	padding-left: 15px
}

.ui-form-item label:not(.ui-switch):not(.ui-checkbox):not(.ui-radio) {
	width: 95px;
	position: absolute;
	text-align: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item input,.ui-form-item textarea {
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	padding-left: 95px;
}

.ui-form-item input[type=checkbox],.ui-form-item input[type=radio] {
	padding-left: 0
}

.ui-form-item .ui-icon-close {
	position: absolute;
	top: 0;
	right: 6px
}

@media (max-width:320px) {
	.ui-form-item .ui-icon-close {
		right: 1px
	}
}

@media (max-width:320px) {
	.ui-form-item {
		padding-left: 10px;
		padding-right: 10px
	}
}

.ui-form-item-textarea {
	height: auto;
	min-height: 44px;
}

.ui-form-item-textarea label {
	vertical-align: top
}

.ui-form-item-textarea textarea {
	margin: 13px 0;
	height: 36px;
	line-height: 18px;
	border: 0;
	vertical-align: top;
}

.ui-form-item-textarea textarea:focus {
	outline: 0
}

.ui-form-item-link>li:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media (max-width:320px) {
	.ui-form-item-link>li:after {
		right: 10px
	}
}

.ui-form-item-l label,.ui-form-item-r button {
	color: #00a5e0;
	text-align: center
}

.ui-form-item-r .ui-icon-close {
	right: 125px
}

.ui-form-item-l input:not([type=checkbox]):not([type=radio]) {
	padding-left: 115px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r {
	padding-right: 0
}

.ui-form-item-r input:not([type=checkbox]):not([type=radio]) {
	padding-left: 0;
	padding-right: 150px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r button {
	width: 110px;
	height: 44px;
	position: absolute;
	top: 0;
	right: 0
}

.ui-form-item-r button.disabled {
	color: #bbb
}

.ui-form-item-r button:not(.disabled):active {
	background-color: #e5e6e7
}

.ui-form-item-pure input,.ui-form-item-pure textarea {
	padding-left: 0
}

.ui-form-item-show label {
	color: #777
}

.ui-form-item-link:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media (max-width:320px) {
	.ui-form-item-link:after {
		right: 10px
	}
}

.ui-form-item-checkbox,.ui-form-item-radio,.ui-form-item-switch {
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-switch {
	position: absolute;
	font-size: 16px;
	right: 15px;
	top: 50%;
	margin-top: -16px;
	width: 52px;
	height: 32px;
	line-height: 32px
}

@media (max-width:320px) {
	.ui-switch {
		right: 10px
	}
}

.ui-switch input {
	width: 52px;
	height: 32px;
	position: absolute;
	z-index: 2;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0
}

.ui-switch input:before {
	content: '';
	width: 50px;
	height: 30px;
	border: 1px solid #dfdfdf;
	background-color: #fdfdfd;
	border-radius: 20px;
	cursor: pointer;
	display: inline-block;
	position: relative;
	vertical-align: middle;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	border-color: #dfdfdf;
	-webkit-box-shadow: #dfdfdf 0 0 0 0 inset;
	box-shadow: #dfdfdf 0 0 0 0 inset;
	-webkit-transition: border .4s,-webkit-box-shadow .4s;
	transition: border .4s,box-shadow .4s;
	-webkit-background-clip: content-box;
	background-clip: content-box
}

.ui-switch input:checked:before {
	border-color: #64bd63;
	-webkit-box-shadow: #64bd63 0 0 0 16px inset;
	box-shadow: #64bd63 0 0 0 16px inset;
	background-color: #64bd63;
	transition: border .4s,box-shadow .4s,background-color 1.2s;
	-webkit-transition: border .4s,-webkit-box-shadow .4s,background-color 1.2s;
	background-color: #64bd63
}

.ui-switch input:checked:after {
	left: 21px
}

.ui-switch input:after {
	content: '';
	width: 30px;
	height: 30px;
	position: absolute;
	top: 1px;
	left: 0;
	border-radius: 100%;
	background-color: #fff;
	-webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
	box-shadow: 0 1px 3px rgba(0,0,0,.4);
	-webkit-transition: left .2s;
	transition: left .2s
}

.ui-checkbox,.ui-checkbox-s {
	display: inline-block
}

.ui-checkbox input,.ui-checkbox-s input {
	display: inline-block;
	width: 25px;
	height: 25px;
	-moz-height:25px;
	position: relative;
	overflow: visible;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0;
	margin-right: 8px;
	vertical-align: middle
}

.ui-checkbox input:before,.ui-checkbox-s input:before {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 32px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	content: "\e610";
	color: #18b4ed;
	position: absolute;
	top: -4px;
	left: -4px;
	color: #dedfe0
}

.ui-checkbox input:checked:before,.ui-checkbox-s input:checked:before {
	content: "\e601";
	color: #18b4ed
}

.ui-checkbox-s {
	width: 23px
}

.ui-checkbox-s input:before {
	content: "\e611"
}

.ui-checkbox-s input:checked:before {
	content: "\e602"
}

.ui-radio {
	line-height: 25px;
	display: inline-block
}

.ui-radio input {
	display: inline-block;
	width: 26px;
	height: 26px;
	position: relative;
	overflow: visible;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0;
	margin-right: 8px;
	vertical-align: middle
}

.ui-radio input:before {
	content: '';
	display: block;
	width: 24px;
	height: 24px;
	border: 1px solid #dfe0e1;
	border-radius: 13px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	position: absolute;
	left: 0;
	top: 0
}

.ui-radio input:checked:after {
	content: '';
	display: block;
	width: 14px;
	height: 14px;
	background: #18b4ed;
	border-radius: 7px;
	position: absolute;
	left: 6px;
	top: 6px
}

.ui-select {
	position: relative;
	margin-right: 6px
}

.ui-select select {
	-webkit-appearance: none;
	border: 0;
	background: 0 0;
	width: 100%;
	padding-right: 14px
}

.ui-select:after {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -4px;
	width: 0;
	height: 0;
	border-top: 6px solid;
	border-right: 5px solid transparent;
	border-left: 5px solid transparent;
	color: #a6a6a6;
	content: "";
	pointer-events: none
}

.ui-select-group {
	margin-left: 95px;
	overflow: hidden
}

.ui-select-group .ui-select {
	float: left
}

.ui-form-item>.ui-select {
	margin-left: 95px
}

@charset "UTF-8";

.ui-table {
	width: 100%;
	border-collapse: collapse
}

.ui-table td,.ui-table th {
	border-bottom: 1px solid #e0e0e0;
	border-right: 1px solid #e0e0e0;
	text-align: center
}

.ui-table th {
	font-weight:500;
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-table td,.ui-table th {
		position: relative;
		border-right: 0;
		border-bottom: 0
	}

	.ui-table td:after,.ui-table th:after {
		content: "";
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-image: -webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0)),-webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		-webkit-background-size: 1px 100%,100% 1px;
		background-size: 1px 100%,100% 1px;
		background-repeat: no-repeat;
		background-position: right,bottom;
		pointer-events: none
	}

	.ui-table tr td:last-child:after,.ui-table tr th:last-child:after {
		background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		-webkit-background-size: 100% 1px;
		background-size: 100% 1px;
		background-repeat: no-repeat;
		background-position: bottom
	}

	.ui-table tr:last-child td:not(:last-child):after {
		background-image: -webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		-webkit-background-size: 1px 100%;
		background-size: 1px 100%;
		background-repeat: no-repeat;
		background-position: right
	}
}

.ui-table tr td:last-child,.ui-table tr th:last-child {
	border-right: 0
}

.ui-table tr:last-child td {
	border-bottom: 0
}

.ui-input-wrap {
	background-color: #ebeced;
	height: 44px;
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-input-wrap .ui-btn,.ui-input-wrap i {
	margin-right: 10px
}

.ui-input {
	height: 30px;
	line-height: 30px;
	margin: 7px 10px;
	background: #fff;
	padding-left: 10px;
	-webkit-box-flex: 1
}

.ui-input input {
	width: 100%;
	height: 100%;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0
}

.ui-file-wrap {
	padding: 6px 10px;
}
.ui-file {
	position: relative;
	overflow: hidden;
}
.ui-file input {
	display: block;
	opacity: 0;
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 0;
	background: transparent;
	z-index: 1;
}

.ui-tips {
	padding: 15px;
	text-align: center;
	font-size: 16px;
	line-height: 22px;
	color: #333
}

.ui-tips i {
	display: inline-block;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-tips-block i,.ui-tips-block span {
	display: block;
	height: auto;
	width: 100%;
	text-align: center;
	line-height: 22px;
	font-size: 18px;
}

.ui-tips i:before {
	font-family: iconfont!important;
	font-size: 32px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: #666;
	line-height: 21px
}

.ui-tips-block i:before{
	margin:0 auto;
	font-size: 5em;
	line-height: 1em;
}

.ui-tips-info i:before {
	content: "\e606";
	color: #0090ff
}

.ui-tips-success i:before {
	content: "\e607";
	color: #65d521
}

.ui-tips-warn i:before {
	content: "\e608";
	color: #EABC2D
}

.ui-tips-error i:before {
	content: "\e60a";
	color: #f76249
}

@charset "UTF-8";

.ui-notice {
	width: 100%;
	height: 100%;
	z-index: 99;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center
}

.ui-notice>i {
	display: block;
	margin-bottom: 20px
}

.ui-notice>i:before {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	content: "\e609";
	font-size: 100px;
	line-height: 100px;
	color: rgba(0,0,0,.3)
}

.ui-notice p {
	font-size: 16px;
	line-height: 20px;
	color: #bbb;
	text-align: center;
	padding: 0 15px
}

.ui-notice-btn {
	width: 100%;
	-webkit-box-sizing: border-box;
	padding: 50px 15px 15px
}

.ui-notice-btn button {
	margin: 10px 0
}

.ui-tooltips {
	width: 100%;
	position: relative;
	z-index: 99;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-tooltips-cnt {
	background-color: #fff;
	line-height: 44px;
	height: 44px;
	padding-left: 10px;
	padding-right: 30px;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-tooltips-cnt .ui-icon-close:before {
	font-size: 40px;
	color: rgba(0,0,0,.2);
	margin-left: -10px;
	position: absolute;
	right: 0;
	top: 0
}

.ui-tooltips-warn .ui-tooltips-cnt {
	background-color: rgba(255,242,183,.95);
	color: #000
}

.ui-tooltips-warn:active .ui-tooltips-cnt {
	background-color: #e1d498
}

.ui-tooltips-guide .ui-tooltips-cnt {
	color: #00a5e0;
	background-color: rgba(205,242,255,.95)
}

.ui-tooltips-guide .ui-tooltips-cnt .ui-icon-close:before {
	color: rgba(0,165,224,.2)
}

.ui-tooltips-guide:active .ui-tooltips-cnt {
	background-color: #b5dbe8
}

.ui-tooltips-cnt-link:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px;
	color: rgba(0,0,0,.5)
}

@media (max-width:320px) {
	.ui-tooltips-cnt-link:after {
		right: 10px
	}
}

.ui-tooltips-guide .ui-tooltips-cnt-link:after {
	color: #00aeef
}

.ui-tooltips-warn i {
	display: inline-block;
	margin-right: 4px;
	margin-left: -4px;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-tooltips-warn i:before {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	content: "\e608";
	color: #f76249
}

@charset "UTF-8";

.ui-newstips-wrap {
	margin: 20px 15px;
	text-align: center
}

.ui-newstips {
	background: #383939;
	position: relative;
	height: 40px;
	line-height: 40px;
	display: -webkit-inline-box;
	-webkit-box-align: center;
	padding-right: 25px;
	border-radius: 5px;
	font-size: 14px;
	color: #fff;
	padding-left: 15px
}

.ui-newstips .ui-avatar-tiled,.ui-newstips .ui-newstips-thumb,.ui-newstips i {
	display: block;
	margin-left: -5px;
	margin-right: 10px
}

.ui-newstips .ui-newstips-thumb {
	width: 30px;
	height: 30px;
	position: relative
}

.ui-newstips .ui-newstips-thumb>span {
	display: block;
	width: 100%;
	height: 100%;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-newstips div {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-newstips:after {
	font-family: iconfont!important;
	font-size: 32px;
	line-height: 44px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	color: #c7c7c7;
	content: "\e600";
	position: absolute;
	right: 15px;
	top: 50%;
	margin-top: -22px;
	margin-right: -10px
}

@media (max-width:320px) {
	.ui-newstips:after {
		right: 10px
	}
}

.ui-newstips .ui-reddot,.ui-newstips .ui-badge-num {
	margin-left: 10px;
	margin-right: 5px
}

.ui-poptips {
	width: 100%;
	position: fixed;
	text-align: center;
	top:auto;
	bottom: 60px;
	z-index: 9999;
	padding: 0 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: none;
}

.ui-poptips.show {
	display: -webkit-box;
	display: block;
}

.ui-poptips-cnt {
	background-color: rgba(0,0,0,.8);
	color: #fff;
	font-size: 16px;
	text-align: center;
	border-radius: 4px;
	max-width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 23px;
	min-width: 17px;
	padding:7px 10px;
	vertical-align: baseline;
}

.ui-poptips .ui-poptips-cnt {
	display: inline-block;
	vertical-align: top;
}
 
.ui-poptips-top {
	top:0;
	bottom:auto;
}

.ui-poptips-top .ui-poptips-cnt {
	display: block;
	line-height: 37px;
	padding: 0 10px;
	border-top-left-radius:0;
	border-top-right-radius: 0; 
	white-space: nowrap;
}

.ui-poptips-bottom {
	bottom:0;
	top:auto;
}

.ui-poptips-bottom .ui-poptips-cnt {
	display: block;
	line-height: 37px;
	padding: 0 10px;
	border-bottom-left-radius:0;
	border-bottom-right-radius: 0; 
	white-space: nowrap;
}

.ui-poptips-center {
	top: auto;
	bottom: 22%;
}

.ui-poptips i {
	display: inline-block;
	vertical-align: top;
	position: relative;
	top: 0;
	left: -5px;
}


.ui-poptips i:before {
	font-family: iconfont!important;
	width: 30px;
	font-size: 38px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: inline-block;
	color: rgba(0,0,0,.5);
	color: #fff;
	line-height: 38px;
	vertical-align: top;
}

.ui-poptips-center i:before {
	line-height: 23px;
}

.ui-poptips-primary i:before {
	content: "\e603"
}

.ui-poptips-primary.ui-poptips-color .ui-poptips-cnt {
	background: rgba(14, 144, 210,0.9);
}

.ui-poptips-success i:before {
	content: "\e604"
}

.ui-poptips-success.ui-poptips-color .ui-poptips-cnt {
	background: rgba(94, 185, 94,0.9);
}

.ui-poptips-warning i:before {
	content: "\e605"
}

.ui-poptips-warning.ui-poptips-color .ui-poptips-cnt {
	background: rgba(243, 123, 29,0.9);
}

.ui-poptips-error i{
	left: -8px;
	top: -1px;
}
.ui-poptips-error i:before {
	content: "\e60a";
	width: 32px;
	font-size: 48px;
}

.ui-poptips-error.ui-poptips-color .ui-poptips-cnt {
	background: rgba(221, 81, 76,0.9);
}

.ui-poptips-block {
	top: auto;
	bottom: 45%;
}

.ui-poptips-block .ui-poptips-cnt{
	padding: 10px;
	min-width: 64px;
	line-height: 28px;
	overflow: hidden;
	text-overflow:ellipsis;
}

.ui-poptips-block i {
	display: block;
	width: 36px;
	height: 36px;
	margin: 0 auto;
	text-align: center;
	position: relative;
	left: 0;
}
.ui-poptips-block.ui-poptips-error i{
	position: relative;
	left: -5px;
	top: 0px;
}
.ui-poptips-block i:before{
	width: 36px;
	height: 36px;
	line-height: 36px;
	text-align: center;
}

.ui-poptips-noicon i{
	display: none;
}

.ui-selector header {
	padding: 6px 10px;
	color: #a6a6a6;
	overflow: hidden
}

.ui-selector header h3 {
	float: left
}

.ui-selector ul>.ui-selector-item {
	display: block;
}

.ui-selector-content {
	background: #fff
}

.ui-selector-item p {
	margin-left: 0px;
	-webkit-box-flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-selector-item .ui-txt-info {
	margin: 0 10px;
	font-size: 12px
}

.ui-selector-item ul {
	display: none;
}

.ui-selector-item .ui-list-link li:after {
	display: none;
}

.ui-selector-item h3:before {
	content: '';
	display: block;
	width: 0;
	height: 0;
	border-left: 6px solid;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	color: #a6a6a6;
	position: absolute;
	left: 25px;
	top: 15px;
	-webkit-transition: all .2s
}

.ui-selector-item h3 {
	display: -webkit-box;
	font-size: 16px;
	padding-left: 54px;
	line-height: 44px;
	height: 44px;
	position: relative
}


.ui-selector-item.active>h3:before {
	-webkit-transform: rotate(90deg)
}

.ui-selector-item.active>h3 {
	border: 0;
	background-image: none
}

.ui-selector-item.active>ul {
	display: block;
	height: 100%;
}

.ui-selector ul>.ui-selector-item.active {
	display: block;
	background: none;
}


@-webkit-keyframes am-rotate {
	from {
		background-position: 0 0
	}

	to {
		background-position: -240px 0
	}
}

@-webkit-keyframes am-rotate2 {
	from {
		background-position: 0 0
	}

	to {
		background-position: -444px 0
	}
}

.ui-progress {
	overflow: hidden;
	width: 100%;
	height: 2px;
	font-size: 0;
	background-color: #e2e2e2;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-progress span {
	display: block;
	width: 0;
	background: #65d521;
	height: 100%;
	font-size: 0
}

.ui-grid-trisect li .ui-progress,.ui-grid-halve li .ui-progress {
	position: absolute;
	height: 13px;
	bottom: 0;
	z-index: 9;
	border: 5px solid rgba(248,248,248,.9)
}

.ui-grid-trisect li .ui-progress span,.ui-grid-halve li .ui-progress span {
	border-radius: 3px
}

@charset "UTF-8";

.ui-modal {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	display: none
}

.ui-modal.show {
	display: -webkit-box;
    display: box;
}

.ui-modal-cnt {
	width: 86%;
	max-width: 320px;
	max-height: 100%;
	position: relative;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	pointer-events: auto;
	position: relative;
	font-size: 16px;
	margin: 0;
	background: #fff;
	overflow: hidden;
	border-radius: 6px;
}
.ui-page .ui-modal-cnt {
	width: 100%;
	height: 84%;
	max-width: 100%;
	max-height: 84%;
	border-radius: 0;
}

.ui-iframe .ui-modal-cnt {
	width: 100%;
	height: 100%;
	max-width: 100%;
	max-height: 100%;
	border-radius: 0;
}

.ui-modal-hd {
	height: 44px;
	line-height: 44px;
	text-align: center;
	position: absolute;
	width: 100%;
	white-space: nowrap;
	z-index: 100;
	overflow: hidden;
	background: #fff;
}
.ui-page .ui-modal-hd,.ui-iframe .ui-modal-hd {
	border-radius: 0;
}
.ui-modal-hd>h3{
	margin: 0 36px;
	overflow: hidden;
	text-overflow:ellipsis;
}

.ui-modal-bd{
	white-space: normal;
	word-wrap:break-word;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-orient: vertical;
	font-size: 14px;
	min-height: 24px;
	max-height: 320px;
	overflow: auto;
	padding: 7px 10px;
}

.ui-modal-bd>h4 {
	margin-bottom: 4px;
	width: 100%;
	text-align: center;
	font-weight: bold;
	font-size: 18px;
}

.ui-modal-bd>div,.ui-modal-bd>ul {
	width: 100%
}
.ui-modal-hd~.ui-modal-bd{
	border-top:44px solid transparent;
}
.ui-modal-ft~.ui-modal-bd {
	border-bottom:44px solid transparent;
}

.ui-modal-ft {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center;
	height: 44px;
	line-height: 44px;
	overflow: hidden;
	position: absolute;
	bottom: 0;
	z-index: 100;
	background: #fff;
}

.ui-modal-close:before{
	font-family: iconfont!important;
	font-size: 28px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: .2px;
	display: block;
	color: rgba(0,0,0,.5);
	content: "\e60b";
	color: #828282;
	display: block;
	line-height: 32px;
	position: absolute;
	top: 7px;
	right: 7px
}

.ui-modal-close:active {
	opacity: .5
}

.ui-modal-ft button {
	color: #00a5e0;
	text-align: center;
	border-right: 1px #e0e0e0 solid;
	width: 100%;
	line-height: 42px;
	background: transparent;
	display: block;
	margin: 0!important;
	-webkit-box-flex: 1;
}

.ui-modal-ft button.select {

	font-weight: bold;
}

.ui-modal-ft button:active {
	background-color: rgba(0,0,0,.03)!important;
}

.ui-modal-ft button:first-child {
	border-bottom-left-radius: 6px
}

.ui-modal-ft button:last-child {
	border-right: 0;
	border-bottom-right-radius: 6px
}

@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-modal-ft {
		border: 0;
		background-position: left top;
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}
	.ui-modal-hd {
		border: 0;
		background-position: left bottom;
		background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}

	.ui-modal-ft button {
		border-right: 0;
		background-position: right top;
		background-image: -webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%
	}

	.ui-modal-ft button:last-child {
		background: 0 0
	}
}

.ui-alert {
	text-align: center;
}

.ui-actionsheet {
	width: 100%;
	position: fixed;
	text-align: center;
	top:auto;
	bottom: 0;
	z-index: 9999;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: none;
}

.ui-actionsheet-cnt {
	font-size: 21px;
	padding: 0 8px;
	text-align: center;
}

.ui-actionsheet.show {
	pointer-events: inherit;
	display: block;
}

.ui-actionsheet button,.ui-actionsheet h4 {
	background: rgba(255,255,255,1);
	display: block;
	width: 100%;
	color: #0079ff;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-actionsheet button {
	line-height: 44px;
	height: 44px
}

.ui-actionsheet h4 {
	line-height: 24px;
	padding-left: 20px;
	padding-right: 20px;
	padding-top: 10px;
	padding-bottom: 10px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.ui-actionsheet button:not(:last-child) {
	border-top: 1px #d5d5d5 solid
}

.ui-actionsheet button:last-child {
	margin: 5px 0;
	border-radius: 3px;
	color: #a2a2a2;
}

.ui-actionsheet button:nth-last-child(2) {
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px
}

.ui-actionsheet button:active {
	opacity: .84
}

.ui-actionsheet h4 {
	font-size: 13px;
	color: #8a8a8a
}

.ui-actionsheet .ui-actionsheet-del {
	color: #fd472b
}


@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-actionsheet button:not(:last-child) {
		border: 0;
		background-position: left top;
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#d5d5d5),to(#d5d5d5));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}
}

.ui-loading-wrap {
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 40px
}

.ui-preloading .ui-loading-wrap{
	height: 100%;
}

.ui-loading-icon {
	width: 20px;
	height: 20px;
	display: block;
	background: url(../img/loading_sprite.png);
	-webkit-background-size: auto 20px;
	-webkit-animation: am-rotate 1s steps(12) infinite
}

.ui-loading-bright {
	width: 37px;
	height: 37px;
	display: block;
	background-image: url(../img/loading_sprite_white.png);
	-webkit-background-size: auto 37px;
	-webkit-animation: am-rotate2 1s steps(12) infinite
}

.ui-loading-wrap .ui-loading {
	margin: 10px
}

.ui-loading-block{
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	display: none;
}

.ui-loading-block .ui-loading-cnt {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-align: center;
	text-align: center;
	background: rgba(0,0,0,.65);
	border-radius: 6px;
	color: #eee;
	font-size: 16px;
	padding: 10px;
}

.ui-loading-theme-white .ui-loading-cnt{
	background: rgba(255,255,255,1);
	color: #666;
}

.ui-loading-block .ui-loading-bright {
	margin: 0;
}

.ui-loading-block p {
	margin:3px 8px 0 8px;
}

.ui-loading-block.show {
	display: -webkit-box;
	display: box
}

.ui-loading-blank,.ui-loading-blank .ui-loading-cnt {
	background: #fff;
	color: #333;
}
.ui-loading-inline .ui-loading-cnt {
	-webkit-box-orient: horizontal!important;
	border-radius: 5px;
	padding: 6px;
}
.ui-loading-inline p {
	margin: 0 5px;
}

.ui-iframe .ui-modal-bd,.ui-page .ui-modal-bd {
	-webkit-overflow-scrolling:touch;
	overflow: auto;
	position: absolute;
	left: 0;
	right: 0;
	top: 44px;
	bottom: 0;
	margin: 0;
	padding: 0;
	border-bottom:none;
	border-top:none;
	max-height: 100%;
}
.ui-iframe .ui-modal-bd {
	bottom: 44px;
	background-image: url(../img/loading.gif);
	background-repeat: no-repeat;
	background-position: 50%;
}
.ui-iframe iframe, .ui-page iframe { 
	float: left;
}

@charset "UTF-8";

.ui-tab {
	width: 100%;
}

.ui-tab-nav {
	width: 100%;
	background-color: #fff;
	display: box;
	display: -webkit-box;
	font-size: 16px;
	height: 45px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-tab-content {
	display: block;
}

.ui-tab-content>li {
	display: block;
	width: 100%;
	vertical-align:top;
	display: none;
}
.ui-tab-content>li.active{
	display: block;
}

.ui-tab-nav li {
	height: 45px;
	line-height: 45px;
	min-width: 70px;
	box-flex: 1;
	-webkit-box-flex: 1;
	text-align: center;
	color: #777;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border-bottom: 2px solid transparent;
	width: 100%
}

.ui-tab-nav li.current {
	color: #00a5e0;
	border-bottom: 2px #00a5e0 solid
}

.ui-tab-nav li:active {
	opacity: .8
}

.ui-mask {
	position: relative;
}

.ui-mask-layer {
	position: absolute;
	display: block;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	z-index: 998;
	background: rgba(0,0,0,0.5);
}
.ui-mask-layer.ui-mask-body {
	position: fixed;
}
.ui-mask-fix {
	overflow: hidden;
}

.ui-pullrefresh {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 100%;
	overflow: hidden;
}
.ui-pullrefresh-container{
	position: relative;
	z-index: 1;
}
.ui-footer~.ui-pullrefresh {
	bottom: 49px;
}
.ui-header~.ui-pullrefresh {
	bottom: 45px;
}

.ui-pullrefresh-tip {
	width: 100%;
	text-align: center;
	padding: 10px 0;
}

.ui-pullrefresh-tip i,.ui-pullrefresh-tip span {
	display: inline-block;
	height: 24px;
	line-height: 24px;
	vertical-align: top;
	color: #999;
}
.ui-pullrefresh-tip i {
	width: 24px;
	height: 24px;
	background: url(../img/icon-pullrefresh.png) no-repeat 50% 0;
	background-size: 24px auto;
	margin-right: 3px;
}
.ui-pullrefresh-tip i.ui-pullrefresh-down {
	background-position: 0 0;
}
.ui-pullrefresh-tip i.ui-pullrefresh-down+span:after{
	content: "下拉刷新...";
}
.ui-pullrefresh-tip i.ui-pullrefresh-up {
	background-position: 0 0;
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg);
}
.ui-pullrefresh-tip i.ui-pullrefresh-up+span:after {
	content: "上拉更多...";
}
.ui-pullrefresh-tip i.ui-pullrefresh-refresh {
	background-position: 0 -24px;
}
.ui-pullrefresh-tip i.ui-pullrefresh-refresh+span:after {
	content: "释放刷新...";
}
.ui-pullrefresh-tip i.ui-pullrefresh-refresh.ani-loading+span:after{
	content: "正在刷新...";
}
.ui-pullrefresh-tip i.ui-pullrefresh-loading {
	background-position: 0 -48px;
}

.ui-pullrefresh-tip i.ui-pullrefresh-loading+span:after{
	content: "释放加载..."
}

.ui-pullrefresh-tip i.ui-pullrefresh-loading.ani-loading+span:after{
	content: "正在加载..."
}


.ui-pullrefresh-tip span {

	font-size: 15px;
}

.ui-pullrefresh-tip.pullrefresh {
	position: absolute;
	left: 0;
	top: 0;
	-webkit-transform: translateY(-100%);
	-ms-transform: translateY(-100%);
	-o-transform: translateY(-100%);
	transform: translateY(-100%);
}


.ui-slide{
    position: relative;
    overflow: hidden;
    max-width: 640px;
    margin: 0 auto;
    visibility: hidden;
}
.ui-slide:after{
    content: '';
    display: block;
    width: 100%;
    padding-top: 50%;
}
.ui-slide .ui-slide-content{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}
.ui-slide .ui-slide-item{
    list-style: none;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}
/* 解决js阻塞页面显示首屏 */
.ui-slide .ui-slide-item:first-child{
    z-index: 1;
}
.ui-slide .ui-slide-item img{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: none;
}
.ui-slide .ui-slide-dot{
    position: absolute;
    right: 10px;
    bottom: 10px;
    font-size: 0;
}
.ui-slide-dot span,.ui-fullpage-dot span{
    display: inline-block;
    width: 5px;
    height: 5px;
    margin-left: 5px;
    border: 1px solid #fff;
    border-radius: 50%;
}
.ui-slide-dot .cur,.ui-fullpage-dot .cur{
    background-color: #fff;
}

.ui-fullpage {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 800;
	display: block;
	overflow: hidden;
	visibility: hidden;
}
.ui-fullpage-content{
	position: absolute;
	width: 100%;
	height: 100%;
}

.ui-fullpage .ui-fullpage-item {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	overflow: auto;
}

.ui-fullpage .ui-fullpage-dot {
	position: fixed;
	height: 100%;
	line-height: 14px;
	width: 7px;
	display: box;
	display: -webkit-box;
	right: 7px;
	z-index: 10;
	text-align: center;
	-webkit-box-orient: horizontal;
    -webkit-box-pack: center;
  	-webkit-box-align: center;
  	box-orient: horizontal;
    box-pack: center;
    box-align: center;
}

.ui-fullpage .ui-fullpage-dot span {
	margin: 0;
}

.ui-fullpage-axisx .ui-fullpage-dot {
	width: 100%;
	height: 7px;
	line-height: 7px;
	right: 0;
	bottom: 20px;
}
.ui-fullpage-axisx .ui-fullpage-dot span{
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	margin: 0 3px;
}

.ui-lattice  {
	display: block;
	overflow: hidden;
}

.ui-lattice-grid {
	position: relative;
	float: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
	text-align: center;
	background-color: #fff;
}
.ui-lattice-grid a {
	display: block;
	margin: 1px;
}

.ui-lattice-grid img {
	max-width: 100%;
	max-height: 100%;
}

.ui-lattice-2x .ui-lattice-grid{
	width: 50%;
}

.ui-lattice-3x .ui-lattice-grid{
	width: 33.3333%;
}

.ui-lattice-4x .ui-lattice-grid{
	width: 25%;
}

.ui-lattice-5x .ui-lattice-grid{
	width: 20%;
}

.ui-lattice-cutline {
	border-top: 1px solid #e0e0e0;
	border-left: 1px solid #e0e0e0;
}

.ui-lattice-cutline .ui-lattice-grid{
	border-right: 1px solid #e0e0e0;
	border-bottom: 1px solid #e0e0e0;
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-lattice-cutline,.ui-lattice-cutline .ui-lattice-grid{
		position: relative;
		border:0;
	}
	.ui-lattice-cutline{
		padding-top: 1px;
	}
	.ui-lattice-cutline:before{
		display: block;
		content: "";
		width: 100%;
		height: 1px;
		position: absolute;
		left: 0;
		top: 0;
		background-position: left bottom;
		background-image: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
	}

	.ui-lattice-cutline .ui-lattice-grid:after{
		display: block;
		content: '';
		position: absolute;
		left: 0;
		bottom: 0;
		width:100%;
		height: 1px;
		background-position: left bottom;
		background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}
	.ui-lattice-cutline:before,.ui-lattice-cutline .ui-lattice-grid:after{
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px;
	}

	.ui-lattice-cutline:after {
		display: block;
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		width: 1px;
		height: 100%;
		background-position: left top;
		background-image: -webkit-gradient(linear,right top,left top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0))
	}

	.ui-lattice-cutline .ui-lattice-grid {
		background-position: right top;
		background-image: -webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#e0e0e0),to(#e0e0e0));
	}
	.ui-lattice-cutline:after,.ui-lattice-cutline .ui-lattice-grid{
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%;
	}
 
}

.ui-map-holder {
	background:url(../img/map_holder.png) no-repeat 50%;
}