"use strict";

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

/**
 * 
 * @authors <PERSON> (<EMAIL>)
 * @date    2015-12-09 14:36:52
 * @version 2015-12-09 14:36:52
 */
;

(function ($, A) {
  var j = [43856, 19416, 19168, 42352, 21717, 53856, 55632, 25940, 22191, 39632, 21970, 19168, 42422, 42192, 53840, 53845, 46415, 54944, 44450, 38320, 18807, 18815, 42160, 46261, 27216, 27968, 43860, 11119, 38256, 21234, 18800, 25958, 54432, 59984, 27285, 23263, 11104, 34531, 37615, 51415, 51551, 54432, 55462, 46431, 22176, 42420, 9695, 37584, 53938, 43344, 46423, 27808, 46416, 21333, 19887, 42416, 17779, 21183, 43432, 59728, 27296, 44710, 43856, 19296, 43748, 42352, 21088, 62051, 55632, 23383, 22176, 38608, 19925, 19152, 42192, 54484, 53840, 54616, 46400, 46752, 38310, 38335, 18864, 43380, 42160, 45690, 27216, 27968, 44870, 43872, 38256, 19189, 18800, 25776, 29859, 59984, 27480, 23232, 43872, 38613, 37600, 51552, 55636, 54432, 55888, 30034, 22176, 43959, 9680, 37584, 51893, 43344, 46240, 47780, 44368, 21977, 19360, 42416, 20854, 21183, 43312, 31060, 27296, 44368, 23378, 19296, 42726, 42208, 53856, 60005, 54576, 23200, 30371, 38608, 19195, 19152, 42192, 53430, 53855, 54560, 56645, 46496, 22224, 21938, 18864, 42359, 42160, 43600, 45653, 27951, 44448, 19299, 37759, 18936, 18800, 25776, 26790, 59999, 27424, 42692, 43759, 37600, 53987, 51552, 54615, 54432, 55888, 23893, 22176, 42704, 21972, 21200, 43448, 43344, 46240, 46758, 44368, 21920, 43940, 42416, 21168, 45683, 26928, 29495, 27296, 44368, 19285, 19311, 42352, 21732, 53856, 59752, 54560, 55968, 27302, 22239, 19168, 43476, 42192, 53584, 62034, 54560];
  var o = ["9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c3598082c95f8c965cc920f", "97bd0b06bdb0722c965ce1cfcc920f", "b027097bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c359801ec95f8c965cc920f", "97bd0b06bdb0722c965ce1cfcc920f", "b027097bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c359801ec95f8c965cc920f", "97bd0b06bdb0722c965ce1cfcc920f", "b027097bd097c36b0b6fc9274c91aa", "9778397bd19801ec9210c965cc920e", "97b6b97bd19801ec95f8c965cc920f", "97bd09801d98082c95f8e1cfcc920f", "97bd097bd097c36b0b6fc9210c8dc2", "9778397bd197c36c9210c9274c91aa", "97b6b97bd19801ec95f8c965cc920e", "97bd09801d98082c95f8e1cfcc920f", "97bd097bd097c36b0b6fc9210c8dc2", "9778397bd097c36c9210c9274c91aa", "97b6b97bd19801ec95f8c965cc920e", "97bcf97c3598082c95f8e1cfcc920f", "97bd097bd097c36b0b6fc9210c8dc2", "9778397bd097c36c9210c9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c3598082c95f8c965cc920f", "97bd097bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c3598082c95f8c965cc920f", "97bd097bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c359801ec95f8c965cc920f", "97bd097bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c359801ec95f8c965cc920f", "97bd097bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf97c359801ec95f8c965cc920f", "97bd097bd07f595b0b6fc920fb0722", "9778397bd097c36b0b6fc9210c8dc2", "9778397bd19801ec9210c9274c920e", "97b6b97bd19801ec95f8c965cc920f", "97bd07f5307f595b0b0bc920fb0722", "7f0e397bd097c36b0b6fc9210c8dc2", "9778397bd097c36c9210c9274c920e", "97b6b97bd19801ec95f8c965cc920f", "97bd07f5307f595b0b0bc920fb0722", "7f0e397bd097c36b0b6fc9210c8dc2", "9778397bd097c36c9210c9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bd07f1487f595b0b0bc920fb0722", "7f0e397bd097c36b0b6fc9210c8dc2", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf7f1487f595b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf7f1487f595b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf7f1487f531b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c965cc920e", "97bcf7f1487f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b97bd19801ec9210c9274c920e", "97bcf7f0e47f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "9778397bd097c36b0b6fc9210c91aa", "97b6b97bd197c36c9210c9274c920e", "97bcf7f0e47f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "9778397bd097c36b0b6fc9210c8dc2", "9778397bd097c36c9210c9274c920e", "97b6b7f0e47f531b0723b0b6fb0722", "7f0e37f5307f595b0b0bc920fb0722", "7f0e397bd097c36b0b6fc9210c8dc2", "9778397bd097c36b0b70c9274c91aa", "97b6b7f0e47f531b0723b0b6fb0721", "7f0e37f1487f595b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc9210c8dc2", "9778397bd097c36b0b6fc9274c91aa", "97b6b7f0e47f531b0723b0b6fb0721", "7f0e27f1487f595b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "9778397bd097c36b0b6fc9274c91aa", "97b6b7f0e47f531b0723b0787b0721", "7f0e27f0e47f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "9778397bd097c36b0b6fc9210c91aa", "97b6b7f0e47f149b0723b0787b0721", "7f0e27f0e47f531b0723b0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "9778397bd097c36b0b6fc9210c8dc2", "977837f0e37f149b0723b0787b0721", "7f07e7f0e47f531b0723b0b6fb0722", "7f0e37f5307f595b0b0bc920fb0722", "7f0e397bd097c35b0b6fc9210c8dc2", "977837f0e37f14998082b0787b0721", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e37f1487f595b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc9210c8dc2", "977837f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "977837f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd097c35b0b6fc920fb0722", "977837f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "977837f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "977837f0e37f14998082b0787b06bd", "7f07e7f0e47f149b0723b0787b0721", "7f0e27f0e47f531b0b0bb0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "977837f0e37f14998082b0723b06bd", "7f07e7f0e37f149b0723b0787b0721", "7f0e27f0e47f531b0723b0b6fb0722", "7f0e397bd07f595b0b0bc920fb0722", "977837f0e37f14898082b0723b02d5", "7ec967f0e37f14998082b0787b0721", "7f07e7f0e47f531b0723b0b6fb0722", "7f0e37f1487f595b0b0bb0b6fb0722", "7f0e37f0e37f14898082b0723b02d5", "7ec967f0e37f14998082b0787b0721", "7f07e7f0e47f531b0723b0b6fb0722", "7f0e37f1487f531b0b0bb0b6fb0722", "7f0e37f0e37f14898082b0723b02d5", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e37f1487f531b0b0bb0b6fb0722", "7f0e37f0e37f14898082b072297c35", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e37f0e37f14898082b072297c35", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e37f0e366aa89801eb072297c35", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f149b0723b0787b0721", "7f0e27f1487f531b0b0bb0b6fb0722", "7f0e37f0e366aa89801eb072297c35", "7ec967f0e37f14998082b0723b06bd", "7f07e7f0e47f149b0723b0787b0721", "7f0e27f0e47f531b0723b0b6fb0722", "7f0e37f0e366aa89801eb072297c35", "7ec967f0e37f14998082b0723b06bd", "7f07e7f0e37f14998083b0787b0721", "7f0e27f0e47f531b0723b0b6fb0722", "7f0e37f0e366aa89801eb072297c35", "7ec967f0e37f14898082b0723b02d5", "7f07e7f0e37f14998082b0787b0721", "7f07e7f0e47f531b0723b0b6fb0722", "7f0e36665b66aa89801e9808297c35", "665f67f0e37f14898082b0723b02d5", "7ec967f0e37f14998082b0787b0721", "7f07e7f0e47f531b0723b0b6fb0722", "7f0e36665b66a449801e9808297c35", "665f67f0e37f14898082b0723b02d5", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e36665b66a449801e9808297c35", "665f67f0e37f14898082b072297c35", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e26665b66a449801e9808297c35", "665f67f0e37f1489801eb072297c35", "7ec967f0e37f14998082b0787b06bd", "7f07e7f0e47f531b0723b0b6fb0721", "7f0e27f1487f531b0b0bb0b6fb0722"];
  var l = ["小寒", "大寒", "立春", "雨水", "惊蛰", "春分", "清明", "谷雨", "立夏", "小满", "芒种", "夏至", "小暑", "大暑", "立秋", "处暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至"];
  var h = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"];
  var d = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"];
  var p = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"];
  var k = ["初", "十", "廿", "三十"];
  var g = ["", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  var n = ["正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "腊"];
  var m = {
    yearDataCache: {},
    getDate: function getDate(u) {
      var x = Math.ceil((u - new Date(1899, 1, 10)) / 86400000);
      var w = 1899;
      var s;
      var r;
      var q;
      var t;
      var v;

      for (; w < 2100 && x > 0; w++) {
        s = this.getYearDays(w);
        x -= s;
      }

      x < 0 && (x += s, w--);
      q = w;
      r = this.getLeapMonth(q) || false;

      for (w = 1; w <= 12; w++) {
        s = this.getMonthDays(q, w);

        if (r === true) {
          r = false;
          w--;
          s = this.getLeapDays(q);

          if (x < s) {
            t = true;
          }
        }

        if (r === w) {
          r = true;
        }

        if (x < s) {
          v = s === 30;
          break;
        }

        x -= s;
      }

      return {
        lunarYear: q,
        lunarMonth: w,
        lunarDay: x + 1,
        isLeap: t,
        isBigMonth: v
      };
    },
    getYearDays: function getYearDays(q) {
      var r;
      var t = this.yearDataCache;

      if (t[q]) {
        return t[q];
      }

      var s = 348;
      var u = j[q - 1899];

      for (r = 32768; r > 8; r >>= 1) {
        s += r & u ? 1 : 0;
      }

      s += this.getLeapDays(q);
      t[q] = s;
      return s;
    },
    getLeapDays: function getLeapDays(q) {
      return this.getLeapMonth(q) ? j[q - 1899 + 1] & 15 === 15 ? 30 : 29 : 0;
    },
    getLeapMonth: function getLeapMonth(r) {
      var q = j[r - 1899] & 15;
      return q == 15 ? 0 : q;
    },
    getMonthDays: function getMonthDays(r, q) {
      return j[r - 1899] & 65536 >> q ? 30 : 29;
    }
  };

  var b = function b(u, r) {
    var v = o[u - 1900];
    var t = [];
    var s = 0;
    var q;

    for (; s < 30; s += 5) {
      q = (+("0x" + v.substr(s, 5))).toString();
      t.push(q.substr(0, 1));
      t.push(q.substr(1, 2));
      t.push(q.substr(3, 1));
      t.push(q.substr(4, 2));
    }

    return new Date(u, parseInt(r / 2, 10), t[r]);
  };

  var c = {
    calculate: function calculate(q) {
      return h[q % 10] + d[q % 12];
    },
    getGzYear: function getGzYear(r, s, q) {
      return this.calculate(s - 1900 + 36 - (q === s ? 0 : 1));
    },
    getGzMonth: function getGzMonth(q, r, s) {
      var t = b(r, q.getMonth() * 2);
      return this.calculate((r - 1900) * 12 + s + 12 - (q < t ? 1 : 0));
    },
    getGzDay: function getGzDay(q) {
      return this.calculate(Math.ceil(q / 86400000 + 25567 + 10));
    }
  };
  var i = {
    b0101: "b,春节 ",
    b0115: "b,元宵节",
    b0202: "b,龙头节",
    b0505: "b,端午节",
    b0707: "b,七夕节",
    b0715: "b,中元节",
    b0815: "b,中秋节",
    b0909: "b,重阳节",
    b1001: "b,寒衣节",
    b1015: "b,下元节",
    b1208: "b,腊八节",
    b1223: "b,小年",
    i0202: "i,湿地日,1996",
    i0308: "i,妇女节,1975",
    i0315: "i,消费者权益日,1983",
    i0401: "i,愚人节,1564",
    i0422: "i,地球日,1990",
    i0501: "i,劳动节,1889",
    i0512: "i,护士节,1912",
    i0518: "i,博物馆日,1977",
    i0605: "i,环境日,1972",
    i0623: "i,奥林匹克日,1948",
    i1020: "i,骨质疏松日,1998",
    i1117: "i,学生日,1942",
    i1201: "i,艾滋病日,1988",
    h0101: "h,元旦",
    h0312: "h,植树节,1979",
    h0504: "h,五四青年节,1939",
    h0601: "h,儿童节,1950",
    h0701: "h,建党节,1941",
    h0801: "h,建军节,1933",
    h0903: "h,抗战胜利日,1945",
    h0930: "h,烈士纪念日,1949",
    h0910: "h,教师节,1985",
    h1001: "h,国庆节,1949",
    h1204: "h,宪法日,1982",
    h1213: "h,国家公祭日,1937",
    c1224: "c,平安夜",
    c1225: "c,圣诞节",
    c0214: "c,情人节",
    w0520: "a,母亲节,1913",
    w0630: "a,父亲节",
    w1144: "a,感恩节"
  };

  var e = function e(q) {
    return q < 10 ? "0" + q : q;
  };

  var a = function a(r, C, term) {
    var y = r.getFullYear();
    var w = r.getMonth() + 1;
    var B = r.getDate();
    var q = r.getDay();
    var s = Math.ceil(B / 7);
    var W = "w" + e(w) + s + q;
    var t = "b" + e(C.lunarMonth) + e(C.lunarDay);
    var I = "i" + e(w) + e(B);
    var h = "h" + e(w) + e(B);
    var c = "c" + e(w) + e(B);
    var x = [];
    var D;

    if (C.lunarMonth === 12 && C.lunarDay === (C.isBigMonth ? 30 : 29)) {
      x.push("t,除夕");
    }

    if (term && term === "清明") {
      x.push("t,清明节");
    }

    x.push(i[t], i[h], i[W], i[c], i[I]);
    var u = 0;
    var out = [];

    for (; u < x.length; u++) {
      if (x[u]) {
        D = x[u].split(",");

        if (D[2] && y < Number(D[2])) {
          continue;
        }

        out.push({
          type: D[0],
          desc: D[1],
          value: D[1]
        });
      }
    }

    out.sort(function (F, E) {
      if (F && E) {
        return F.type.charCodeAt(0) - E.type.charCodeAt(0);
      }

      return !F ? 1 : -1;
    });
    return out;
  };

  var f = function f(r) {
    var w = r.getFullYear();
    var u = r.getMonth() + 1;
    var y = r.getDate();
    var v = (u - 1) * 2;
    var s = b(w, v);
    var q;
    var t = "";

    if (y != s.getDate()) {
      q = b(w, v + 1);

      if (y == q.getDate()) {
        t = l[v + 1];
      }
    } else {
      t = l[v];
    }

    var x = m.getDate(r);
    return {
      animal: p[(x.lunarYear - 4) % 12],
      gzDate: c.getGzDay(r),
      gzMonth: c.getGzMonth(r, w, u),
      gzYear: c.getGzYear(r, w, x.lunarYear),
      lunarYear: x.lunarYear,
      lunarMonth: x.lunarMonth,
      lunarDate: x.lunarDay,
      lMonth: (x.isLeap ? "闰" : "") + n[x.lunarMonth - 1],
      lDate: x.lunarDay % 10 == 0 ? ["初十", "二十", "三十"][x.lunarDay / 10 - 1] : k[parseInt(x.lunarDay / 10, 10)] + g[parseInt(x.lunarDay % 10, 10)],
      term: t,
      festival: function () {
        return a(r, x, t);
      }(),
      isBigMonth: x.isBigMonth,
      oDate: r,
      cnDay: "日一二三四五六七".charAt(r.getDay())
    };
  };

  A.utils.lunar = function (q) {
    var q = new Date(q);
    var y = q.getFullYear();
    return y < 1900 || y > 2100 ? false : f(q);
  };
})(JQUERY, window.arm);
/**
 * 
 * @authors Nat Liu (<EMAIL>)
 * @date    2015-11-16 11:05:20
 * @version 2015-11-16 11:05:20
 */


!function ($, A) {
  var _default = {
    daysIndexOfWeek: [0, 1, 2, 3, 4, 5, 6],
    daysLabelOfWeek: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    calendarTheme: "default",
    tpl: {
      build: '<%# if(d.toolbar){ %><div class="calendar-toolbar">' + '<div class="calendar-title" data-calendar-title>' + '<div class="dateInfo"></div>' + '<div class="year-week">' + '<div class="weekInfo"></div>' + '<div class="yearInfo"></div></div>' + '</div>' + '<%# if(d.modeBtn){ %><div class="ui-btn-multi calendar-tool calendar-view-switch"><a data-calendar-view="month" class="ui-btn"><%d.string.month%></a><a data-calendar-view="week" class="ui-btn"><%d.string.week%></a><a data-calendar-view="day" class="ui-btn"><%d.string.day%></a></div><%# } %>' + '<%# if(d.todayBtn){ %><a data-calendar-control="today" class="calendar-tool calendar-today"><%d.string.today%></a><%#}%>' + '<%# if(d.navigation){ %><a data-calendar-control="prev" class="calendar-tool calendar-prev"><%d.navigation.prev%></a>' + '<a data-calendar-control="next" class="calendar-tool calendar-next"><%d.navigation.next%></a><%#}%>' + '</div><%# } %>' + '<%# if(d.calendar){ %><div class="calendar-wrap">' + '<div class="calendar-days-box ui-border-r"></div>' + '<ul class="ui-tiled calendar-week-bar ui-border-b"><%# for (var i = 0; i < d.daysIndexOfWeek.length; i++) { %>' + '<li class="calendar-week-label week-label-<%i%> week-<%d.daysIndexOfWeek[i]%>-label"><% d.daysLabelOfWeek[d.daysIndexOfWeek[i]] %></li>' + '<%# }%></ul>' + '</div><%# } %>',
      grid: '<div class="calendar-days calendar-<%d.role%>-view"><%# for (var i = 0; i < d.rows; i++) { %>' + '<div class="ui-row-flex ui-border-b calendar-row row-<%i%>" data-weeknumber="<%i+d.weekIndex%>">' + '<%# for (var j = 0; j < d.daysIndexOfWeek.length; j++) { var grid = i*d.daysIndexOfWeek.length+j; %>' + '<div class="ui-col ui-border-l ui-center calendar-grid grid-<%grid%> grid-week-<%d.daysIndexOfWeek[j]%><%# if(d.dates[grid].siblings){ %> grid-siblings grid-siblings-<%d.dates[grid].siblings%><%# } %><%# if(d.dates[grid].date===d.today){ %> grid-today<%# } %><%# if(d.dates[grid].date===d.curday){ %> grid-curday<%# } %><%# if(d.hidePolish&&d.dates[grid].siblings){ %> grid-hidden"<%# }else{ %>" data-date="<%d.dates[grid].date%>"<%# }%>>' + '<div class="grid">' + '<div class="day-number"><%d.dates[grid].M+1%>' + '-' + '<%d.dates[grid].D%></div>' + '</div>' + '</div>' + '<%# } %>' + '</div>' + '<%# } %></div>'
    },
    selector: {
      wrap: '.calendar-wrap',
      toolbar: '.calendar-toolbar',
      box: '.calendar-days-box',
      grid: '.calendar-grid',
      date: '[data-date]',
      today: '.calendar-today',
      days: '.calendar-days',
      gridBox: '.grid',
      dayNum: '.day-number'
    },
    classNames: {
      curday: 'grid-curday',
      mode: 'view-mode',
      lunar: 'grid-lunar',
      festival: 'grid-festival'
    },
    string: {
      today: "今",
      week: "周",
      month: "月",
      day: "日",
      index: "第",
      weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    },
    format: {
      day: "yyyy年M月d日",
      month: "yyyy年M月",
      lunar: "农历M月"
    },
    sixRows: 1,
    rows: 5,
    toolbar: true,
    viewMode: "week",
    //0 不要年月日切换按钮,1需要
    modeBtn: 0,
    todayBtn: 1,
    //去掉 -上一页-下一页
    // navigation:{
    // 	prev:'<i class="ui-icon-prev"></i>',
    // 	next:'<i class="ui-icon-next"></i>'
    // },
    swipe: 1,
    weekNumber: 1,
    slide: 400,
    transitionType: "ease",
    hidePolish: 0,
    lunar: 1,
    festival: 1,
    holiday: 0,
    curday: new Date()
  };
  var _config = {
    dateFormat: "yyyy/MM/dd",
    monthFormat: "yyyy/MM",
    calendarClass: 'arm-calendar',
    holidayTip: {
      "1": "休",
      "2": "班"
    },
    viewModes: ['month', 'week', 'day']
  };

  function getDate(date, single, format) {
    if (/string|object/i.test(_typeof(date))) {
      var _date = new Date(date);
    } else {
      var _date = new Date();

      single = typeof single === "undefined" ? date : single;
    }

    return single ? _date.format(format || _config.dateFormat) : _date;
  }

  function clipDate(_date, format) {
    var date = getDate(_date, false);
    var d = {
      Y: date.getFullYear(),
      M: date.getMonth(),
      D: date.getDate(),
      H: date.getHours(),
      m: date.getMinutes(),
      s: date.getSeconds(),
      S: date.getMilliseconds(),
      t: date.getTime(),
      w: date.getDay()
    };
    d.days = new Date(d.Y, d.M + 1, 0).getDate();
    d[0] = date;
    d.date = date.format(format || _config.dateFormat);
    d.day = date.format(_config.dateFormat);
    d.month = date.format(_config.monthFormat);
    return d;
  }

  function getWeekNumber(_date, daysIndex) {
    var date = clipDate(_date),
        weekdays = daysIndex.length,
        day1Index = $.inArray(new Date(date.Y, 0, 1).getDay(), daysIndex),
        startTime = Number(day1Index === 0 ? new Date(date.Y, 0, 1) : new Date(date.Y, 0, weekdays - day1Index + 1)),
        endTime = Number(new Date(date.Y, date.M, date.D)),
        deltaWeek = Math.floor((endTime - startTime) / 86400 / weekdays / 1000) + 1;
    if (endTime < 0) return 1;
    if (day1Index === 0) return deltaWeek;
    return deltaWeek + 1;
  }

  function getPrev(date, by, step) {
    var _date = clipDate(date),
        year = _date.Y,
        month = _date.M,
        day = _date.D;

    var by = by || "day";
    var step = typeof step === "undefined" ? 1 : step;
    if (step === 0) return _date[0];

    if (by === "day") {
      day -= step;
    }

    if (by === "week") day = day - 7;
    if (day < 1) by = "month";

    if (by === "month") {
      month--;

      if (month < 0) {
        by = "year";
        month = 11;
      }
    }

    if (by === "year") year--;
    var days = new Date(year, month + 1, 0).getDate();
    if (day > days) day = days;
    if (day < 1) day += days;
    return new Date(year, month, day);
  }

  function getNext(date, by, step) {
    var _date = clipDate(date),
        year = _date.Y,
        month = _date.M,
        day = _date.D;

    var by = by || "month";
    var step = typeof step === "undefined" ? 1 : step;
    if (step === 0) return _date[0];
    if (by === "day") day += step;
    if (by === "week") day = day + 7;

    if (day > _date.days) {
      by = "month";
      day -= _date.days;
    }

    if (by === "month") {
      month++;

      if (month > 11) {
        by = "year";
        month = 0;
      }
    }

    if (by === "year") year++;
    var days = new Date(year, month + 1, 0).getDate();
    if (day > days) day = days;
    if (day < 1) day += days;
    return new Date(year, month, day);
  }

  $.extend(true, A.utils, {
    clipDate: clipDate,
    getDate: getDate,
    getPrevDate: getPrev,
    getNextDate: getNext
  });

  function Clndr(element, options) {
    var C = this;
    C.$element = $(element);
    C.options = $.extend(true, {}, _default, options);

    if ($.isFunction(C.options.render)) {
      C.$container = C.$element.children();
    } else {
      C.$element.html('<div class="' + _config.calendarClass + '" />');
      C.$container = $(".arm-calendar", C.$element);
    }

    C.$container.addClass(_config.calendarClass + "-" + C.options.calendarTheme);
    C.boxDistance = 0;

    C._init();
  }

  Clndr.prototype._init = function () {
    var C = this,
        O = C.options; // 绑定事件

    C.bindEvents(); // 构建日历

    C.build(); //初始化日历

    C.setCurday(O.curday);
  };

  Clndr.prototype.bindEvents = function () {
    var C = this,
        O = C.options;
    C.$element.on('clndr:build', function (event) {
      $.isFunction(O.onbuild) && O.onbuild.call(C, event);
    }).on('clndr:render', function (event) {
      $.isFunction(O.onrender) && O.onrender.call(C, event);
    }).on('clndr:change', function (event) {
      C.view();
      $.isFunction(O.onchange) && O.onchange.call(C, event);
    });
    C.$container.on("clndr:viewmode", function () {
      C.build();
      C.render();
      C.view();
    }).on("click", "[data-calendar-view]", function () {
      var mode = $(this).data("calendar-view");
      C.changeMode(mode);
    }).touch("tap", "[data-calendar-control]", function (e) {
      var action = $(this).data("calendar-control");
      C[action] && C[action]();
    }).touch("swipeLeft", O.selector.wrap, function (e) {
      e.preventDefault();
      O.swipe && e.touch.angleX && C.next();
    }).touch("swipeRight", O.selector.wrap, function (e) {
      e.preventDefault();
      O.swipe && e.touch.angleX && C.prev();
    }).touch("tap", O.selector.date, function () {
      C.setCurday($(this).data("date")); //自定义事件--

      window.changeShowDayInfo();
    }).touch("drag", O.selector.wrap, function (e) {
      if (O.slide && e.touch.dragX) {
        C.translateBox(e.touch.moveX);
      }
    }).touch("dragEnd", O.selector.wrap, function (e) {
      if (C.boxDistance && !C.transitioning) {
        if (C.boxDistance > C.containerWidth / 2) return C.slideBox(-1);
        if (C.boxDistance < -C.containerWidth / 2) return C.slideBox(1);
        C.slideBox(0);
      }
    }).touch("tap", '.calendar-week-label', function (e) {
      var this_index = $(this).index();
      var curDate = $('.calendar-cur-view .calendar-grid').eq(this_index).data('date');
      C.setCurday(curDate); //自定义事件--

      window.changeShowDayInfo();
    });
  };

  Clndr.prototype.build = function () {
    var C = this,
        O = C.options;
    var data = {
      toolbar: O.toolbar,
      calendar: O.viewMode !== "day",
      daysIndexOfWeek: O.daysIndexOfWeek,
      daysLabelOfWeek: O.daysLabelOfWeek,
      string: O.string,
      modeBtn: O.modeBtn,
      todayBtn: O.todayBtn,
      navigation: O.navigation
    };
    if ($.isFunction(O.build)) return O.build.call(C, data);
    var html = A.tpl(O.tpl.build).render(data);
    C.$container.html(html);
    C.containerWidth = C.$container.width();
    C.$element.trigger('clndr:build', [C]);
  };

  Clndr.prototype.getRenderDates = function (curday, role) {
    var C = this,
        O = C.options;
    var curday = curday || C.curday;
    var date = clipDate(curday);
    var prevDate = clipDate(getPrev(curday, O.viewMode));
    var nextDate = clipDate(getNext(curday, O.viewMode));
    var weekdays = O.daysIndexOfWeek.length;
    var weekIndex = getWeekNumber(curday, O.daysIndexOfWeek);
    var renderDates = [];

    if (O.viewMode === "day") {
      renderDates.push(date);
    } else if (O.viewMode === "week") {
      var rows = 1;
      var dayIndex = $.inArray(date.w, O.daysIndexOfWeek);
      var firstDay = getPrev(curday, "day", dayIndex);

      for (var i = 0; i < weekdays; i++) {
        var d = clipDate(getNext(firstDay, "day", i));
        renderDates.push(d);
      }

      ;
    } else {
      var day1 = new Date(date.Y, date.M, 1);
      var day1Index = $.inArray(day1.getDay(), O.daysIndexOfWeek);
      var daysIndex = $.inArray(new Date(date.Y, date.M, date.days).getDay(), O.daysIndexOfWeek);
      var autoRows = Math.ceil((date.days + day1Index - weekdays) / weekdays) + 1;
      var rows = O.sixRows ? 6 : O.rows ? O.rows : autoRows;
      var daysoff = (rows - autoRows + 1) * weekdays - 1 - daysIndex;
      var weekIndex = getWeekNumber(day1, O.daysIndexOfWeek);

      if (day1Index > 0) {
        for (var i = day1Index - 1; i >= 0; i--) {
          var d = $.extend(true, clipDate(new Date(prevDate.Y, prevDate.M, prevDate.days - i)), {
            siblings: O.viewMode
          });
          renderDates.push(d);
        }

        ;
      }

      for (var i = 0; i < date.days; i++) {
        var d = clipDate(new Date(date.Y, date.M, i + 1));
        renderDates.push(d);
      }

      if (daysoff > 0) {
        for (var i = 0; i < daysoff; i++) {
          var d = $.extend(true, clipDate(new Date(nextDate.Y, nextDate.M, i + 1)), {
            siblings: O.viewMode
          });
          renderDates.push(d);
        }
      }
    }

    var data = {
      dates: renderDates,
      string: O.string,
      daysIndexOfWeek: O.daysIndexOfWeek,
      rows: rows,
      weekIndex: weekIndex,
      weekNumber: O.weekNumber,
      today: getDate(true),
      curday: curday,
      curDate: date,
      prevDate: prevDate,
      nextDate: nextDate,
      role: role || "cur",
      hidePolish: O.hidePolish && O.viewMode === "month"
    };
    return data;
  };

  Clndr.prototype.render = function () {
    var C = this,
        O = C.options;
    var data = C.getRenderDates();
    var prevData = C.getRenderDates(data.prevDate.date, "prev");
    var nextData = C.getRenderDates(data.nextDate.date, "next");
    C.renderDates = data.dates;
    C.prevDates = prevData.dates;
    C.nextDates = nextData.dates;
    C.$render = $('<div class="arm-calendar-days">');
    C.$render.html(A.tpl(O.tpl.grid).render(data));
    $(O.selector.box, C.$container).html(C.$render);
    C.$element.trigger('clndr:render', [C]);

    if (O.slide) {
      C.$render.prepend(A.tpl(O.tpl.grid).render(prevData)).append(A.tpl(O.tpl.grid).render(nextData));
    }

    C.addIns(); //---自定义事件--加载一周日程相关数据---
    //先判断是否有课表

    var hasLoadHaveKb = window.scheduleInfo && window.scheduleInfo.hasLoadHaveKb;

    if (hasLoadHaveKb === false) {
      window.getUserIsHaveKb();
    }

    var weekdayFirst = '';
    var weekdayLast = '';
    var datesArr = data.dates;
    datesArr = datesArr.length > 0 ? datesArr : [];
    var weekdayFirstInfo = datesArr.length > 0 ? datesArr[0] : {};
    var weekdayLastInfo = datesArr.length > 6 ? datesArr[6] : {};
    weekdayFirst = weekdayFirstInfo.date;
    weekdayLast = weekdayLastInfo.date;
    window.getOneWeekSched(weekdayFirst, weekdayLast);
    var showKb = window.scheduleInfo && window.scheduleInfo.showKb;

    if (hasLoadHaveKb === true && showKb === true) {
      window.getKbWeek();
    }

    window.hideDelCalItem(); //---自定义事件结束---
  };

  Clndr.prototype.addIns = function () {
    var C = this,
        O = C.options;
    $('[data-date]', C.$container).each(function () {
      var date = $(this).data("date");

      if (O.lunar || O.festival) {
        var lunar = A.utils.lunar(date);

        if (lunar) {
          $(this).addClass(O.classNames.lunar);
          var text = lunar.term ? ' day-festival">' + lunar.term : '">' + lunar.lDate;

          if (O.festival && lunar.festival && lunar.festival.length) {
            $(this).addClass(O.classNames.festival);
            text = ' day-festival">' + lunar.festival[0].value;
          } //修改 -去掉农历
          // $(O.selector.gridBox, this).append('<div class="day-lunar'+text+'</div>');

        }
      }
    });
  };

  Clndr.prototype.view = function () {
    var C = this,
        O = C.options;
    var curdate = C.curDate;
    var title = {
      day: curdate[0].format(O.format.day) + " " + O.string.weekdays[curdate.w],
      month: curdate[0].format(O.format.month) + (O.lunar && curdate.lunar ? " " + O.format.lunar.replace(/[m]+/i, curdate.lunar.lMonth) : ""),
      week: curdate[0].format(O.format.month) + " " + O.string.index + getWeekNumber(C.curday, O.daysIndexOfWeek) + O.string.week
    };
    $('.' + O.classNames.mode, C.$container).removeClass(O.classNames.mode);
    $('[data-calendar-view="' + O.viewMode + '"]', C.$container).addClass(O.classNames.mode); // $("[data-calendar-title]", C.$container).html(title[O.viewMode]);
    //表头以年月日 星期的信息展示
    // $("[data-calendar-title]", C.$container).html(title['day']);
    //截取当前 日期

    var yearInfo = title['day'].slice(0, title['day'].indexOf('年')) + '.' + title['day'].slice(title['day'].indexOf('年') + 1, title['day'].indexOf('月'));
    var dateInfo = title['day'].slice(title['day'].indexOf('月') + 1, title['day'].indexOf('日'));
    var weekInfo = title['day'].slice(title['day'].indexOf('星')); //当前星期几 加上标识

    $(".ui-tiled li").each(function () {
      if ($(this).text() == weekInfo) {
        $(this).addClass("curWeek");
        $(this).siblings().removeClass("curWeek");
      }
    });
    $(".calendar-title .dateInfo", C.$container).html(dateInfo);
    $(".calendar-title .year-week .yearInfo", C.$container).html(yearInfo);
    $(".calendar-title .year-week .weekInfo", C.$container).html(weekInfo); //--加载当天课表 --
    // var timeTable_curArr = yearInfo.split('.');
    // var timeTable_curDate = dateInfo < 10 ? ('0'+parseInt(dateInfo)) : dateInfo;
    // var timeTable_curMonth = timeTable_curArr[1] < 10 ? ('0' + parseInt(timeTable_curArr[1])) : timeTable_curArr[1];
    // var timeTable_curDay = timeTable_curArr[0] + '/' + timeTable_curMonth + '/' + timeTable_curDate;
    // if(window.getQueryString("curDate") != undefined && window.firstLoad == true){
    // }else{
    // 	window.init_timeTable(timeTable_curDay);
    // }
    // window.firstLoad = false;
    //--结束--

    $("." + O.classNames.curday, C.$container).removeClass(O.classNames.curday);
    $('[data-date="' + C.curday + '"]', C.$container).addClass(O.classNames.curday);
    $(O.selector.today, C.$container).toggle(getDate(true) !== C.curday);
  };

  Clndr.prototype.changeMode = function (mode) {
    var C = this,
        O = C.options;

    if (O.viewMode !== mode && $.inArray(mode, _config.viewModes) !== -1) {
      O.viewMode = mode;
      C.$container.trigger("clndr:viewmode");
    }
  };

  Clndr.prototype.setCurday = function (date) {
    var C = this,
        O = C.options;
    var curday = getDate(date, true);

    if (curday !== C.curday) {
      C.curday = curday;
      C.curDate = clipDate(curday);
      C.curDate.lunar = A.utils.lunar(curday);
      C.$element.trigger('clndr:change', [C]);

      if (!C.renderDates) {
        C.render();
      } else {
        var dateInRender = A.inObject(C.renderDates, function (date, index) {
          return date.date === curday;
        });

        if (dateInRender === -1 || O.viewMode === "month" && C.renderDates[dateInRender].siblings) {
          C.render();
        }
      }
    }

    return curday;
  };

  Clndr.prototype.translateBox = function (distance) {
    var C = this,
        O = C.options;
    C.boxDistance = distance;
    C.$render[0].style[A.utils.prefixStyle("transform")] = "translateX(" + distance + "px)";
  };

  Clndr.prototype.slideBox = function (dir) {
    var C = this,
        O = C.options;
    if (C.transitioning) return;
    C.transitioning = true;
    var width = $(O.selector.box, C.$container).width();
    var distance = -width * dir;
    C.$render.removeAttr('style');
    C.$render[0].style[A.utils.prefixStyle("transition")] = "transform " + O.slide + "ms " + O.transitionType;
    C.translateBox(distance);
    C.$container.one(A.support.transition.end, function () {
      C.$render.removeAttr('style');
      C.transitioning = false;
      C.boxDistance = 0;
      if (dir < 0) C.setCurday(getPrev(C.curday, O.viewMode));
      if (dir > 0) C.setCurday(getNext(C.curday, O.viewMode));
    }).emulateTransitionEnd(O.slide);
  };

  Clndr.prototype.prev = function () {
    var C = this,
        O = C.options;
    if (O.slide) return C.slideBox(-1);
    C.setCurday(getPrev(C.curday, O.viewMode));
  };

  Clndr.prototype.next = function () {
    var C = this,
        O = C.options;
    if (O.slide) return C.slideBox(1);
    C.setCurday(getNext(C.curday, O.viewMode));
  };

  Clndr.prototype.today = function () {
    var C = this,
        O = C.options;
    C.setCurday(new Date());
  }; // 注册插件


  A.register("calendar", Clndr, true);
}(JQUERY, window.arm);