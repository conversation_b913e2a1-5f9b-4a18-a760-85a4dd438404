/**
 * 
 * @authors <PERSON> (<EMAIL>)
 * @date    2015-06-18 03:37:42
 * @version 2015-06-18 03:37:42
 */

var JQUERY = window.jQuery || window.Zepto;
if(!JQUERY){
	throw new Error('jQ<PERSON>y or <PERSON><PERSON><PERSON> is needed!');
}else{
	window.console && console.log(JQUERY.fn['jquery'] ? 'jQuery-'+jQuery.fn['jquery'] : 'Zepto');
}

Date.prototype.format = function (fmt) {
    
    var o = {
        "M+": this.getMonth() + 1, //月份 
        "d+": this.getDate(), //日 
        "H+": this.getHours(), //小时 
        "m+": this.getMinutes(), //分 
        "s+": this.getSeconds(), //秒 
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
        "S": this.getMilliseconds() //毫秒 
    };
    if (/(y+)/i.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
};

;(function($){
	"use strict";
	var arm = function(selector, context){
		return new arm.pt.init( selector, context);
	}

	var version = "1.1";
	var index = 0;
	var _console = {
			log: $.noop, error: $.noop, info:$.noop, warn: $.noop, debug: $.noop, group: $.noop, groupEnd:$.noop
		};

	var config = {
		base:"./",
		charset:"utf-8",
		debug:false,
		iPortal: false,
		console:false
	};

	// 继承属性
	arm.pt = arm.prototype = {
		arm : version,
		constructor: arm
	}

	// 实例化jQuery对象
	var ArmClass = arm.pt.init = function(selector, context){
		this.index = ++index;
		this.$ = $(selector, context); // 将 jQuery 对象传给属性 $
		return this;
	}

	// 实例化ClassArm, 并赋予 arm 继承属性
	ArmClass.prototype = arm.pt;

	// 正则
	var reg = arm.R = {
		trim : /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,
		http : /^http(s)?:\/\//i,
		_http: /^(http(s)?:\/\/|file:\/\/)/i
	}
	// 扩展内置方法
	$.extend(true, arm, {

        data: {},
		config: function(args, single){
			if($.type(args)==="string" || $.type(args)==="number"){
				if($.type(single)==="undefined")
					return config[args];
				config[args] = single;
			}
			if($.isPlainObject(args)){
				if(args.modules!==config.modules)
					arm.use(args.modules);
				$.extend(true, config, args);
			}
			return config;
		},
		console: function(always){
			var __console = window.console&&(config.debug||always||config.console) ? window.console : _console;
			var name = typeof always === "string" ?  always : '调试';
			__console.warn('['+name+'] '+ new Date().format('yyyy-MM-dd HH:mm:ss'));
			return __console;
		},
		// 获取arm目录路径
		path: (function(){
	        var js = document.scripts, script = js[js.length - 1], jsPath = script.src;
	        if(script.getAttribute('merge')) return;
	        return jsPath.substring(0, jsPath.lastIndexOf("/") + 1);
	    })(),
	    // 获取arm引用地址
		srcUrl: (function(){
	        var js = document.scripts, script = js[js.length - 1], jsPath = script.src;
	        if(script.getAttribute('merge')) return;
	        return jsPath;
	    })(),

	    // 获取引用页目录路径
	    refer: window.location.href.substring(0, window.location.href.lastIndexOf('/')+1),
	    // 判断环境
		isMobile : ($.os&&($.os.tablet||$.os.phone)) || /Android|Windows Phone|webOS|iPhone|iPod|iPad|BlackBerry/i.test(navigator.userAgent),
        rAF : window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||
                function (callback) { window.setTimeout(callback, 1000 / 60); },
		// 检测支持
	    support : {
	        // 触摸
	        touch : (window.Modernizr && Modernizr.touch === true) || (function () {
	            return !!(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch);
	        })(),
            transition: (function() {
              // 创建一个元素用于测试
              var el = document.createElement('arm');

              // 将所有主流浏览器实现方式整合成一个对象，用于遍历
              // key   是属性名称
              // value 是事件名称
              var transEndEventNames = {
              	transition       : 'transitionend',
                WebkitTransition : 'webkitTransitionEnd',
                MozTransition    : 'transitionend',
                OTransition      : 'oTransitionEnd otransitionend'
              };

              // 循环遍历上面那个对象，判断 CSS 属性是否存在
              for (var name in transEndEventNames) {
                if (el.style[name] !== undefined) {
                  return { end: transEndEventNames[name] };
                }
              }

              return false;
            })()
	    },

		is_lessIE : function (v){
	    	if(/Microsoft Internet Explorer/i.test(navigator.appName)){
				var ver = navigator.appVersion.match(/msie(\s+)?(\d)/i);
				if(ver&&Number(ver[2]) < v){
					return true;
				}
			}
			return false;
		},

        // 阻止默认事件
        prevent: function(e){
            if(e){
                if (e.preventDefault) e.preventDefault();
                else e.returnValue = false;
            }else{
                return false;
            }      
        },

        JSONstring: function(obj){
            if(window.JSON) return JSON.stringify(obj);
            return 'JSON is not support!';
        },

        objectJoin: function(obj,sep){
        	var sept = sep || "";
        	if(typeof obj === "string")
        		return obj;
        	if($.isArray(obj)){
	        	return obj.join(sept);
	        }
	        if($.isPlainObject(obj)){
	        	var arr = [];
	        	$.each(obj, function(index, val) {
	        		arr.push(val);
	        	});
	        	return arr.join(sept);
	        }
	        return String(obj);
        },
        /**
		** 在对象长度范围内
		**/
        inLen: function(len, index){
			if(index>len-1)
		        index = len-1;
		    if(index<0)
		        index = 0;
		    return index;
		},
		/**
		* 对象过滤
		**/
		inObject: function(obj, filter){
			var index = -1;
			$.each(obj, function(i, v) {
				if(filter(v, i)){
					index = i;
					return false;
				}
			});
			return index;
		},		
		/**
		** 根据某一属性，查找对象数组中是否包含该成员
		**/
		inArrayObj: function(key, val, array){
			var index = -1;
			var _array = $.isArray(array) ? array.slice(0) : [];
			if(!_array.length)
				return index;
		    for (var i = 0; i < _array.length; i++) {
		    	if(_array[i][key] === val){
		    		return i;
		    	}
		    };
		    return index;
		},

		/**
		*** 根据某键值，判断是某符合对象
		**/
		inObjKey:function(key, obj, name){
			var out = false;
			if(!key||typeof obj !=="object")
				return out;
			var k;
			for ( k in obj){
				if(obj[k] === key){
					if(typeof name === "undefined"||name===k){
						out = true;
						break;
					}
				}
			}
			return out;
		},

		/*
		根据name读取obj中的成员数据
		 */
		getNameValue: function(obj, name, sep){
			var sep = sep || /[\.|\||_]/;
			var names = name.split(sep);
			var temp = obj;
			for(var i =0; i < names.length; i++){
				var key = names[i];
				temp = temp[key];
				if(!temp){
					break;
				}
			}
			return temp;
		},
		/*
		根据name修改obj中的成员数据
		 */
		setNameValue: function(obj, name, value, sep){
			var sep = sep || /[\.|\||_]/;
			var names = name.split(sep);
			var temp = obj;
			for(var i =0; i < names.length; i++){
				var key = names[i];
				if(!temp.hasOwnProperty(key)){
					temp[key] = {};
				}
				if(i !== names.length-1 && typeof temp[key]!=="object"){
					break;
				}
				if(i==names.length-1){
					temp[key] = value;
				}else{
					temp = temp[key];
				}
			}
		},
		/**
		** 数组元素移动位置，重新排序
		**/
		sortAarry : function(arr, from, to){
			var len = arr.length;
			if(!arr.length)return -1;
		    var _index = to;
		    if(to==="uper"){
		        _index = from + 1; 
		    }
		    if(to==="downer"){
		        _index = from - 1; 
		    }
		    if(to==="top")
		        _index = len-1;
		    if(to==="bottom")
		        _index = 0;

		    _index = arm.inLen(len, _index);
		    if(_index===from)
		    	return _index;
		    var item = arr.splice(arm.inLen(len, from), 1);
		    arr.splice(_index, 0, item[0]);
		    return _index;
		},
		/**
		** getImgNaturalSize 获取图片真实尺寸
		**/
		getImgNaturalSize: function (img, callback) {
		    var nWidth, nHeight;
		    if (img.naturalWidth) { // 现代浏览器
		        nWidth = img.naturalWidth;
		        nHeight = img.naturalHeight;
		        callback(nWidth, nHeight);
		    } else { // IE6/7/8
		        var image = new Image();
		        image.src = img.src;
		        image.onload = function() {
		            callback(image.width, image.height);
		        }
		    }
		    return [nWidth, nHeight];
		},
        // 获取url参数 ? or #
	    getUrlParam : function(name, url, hash){
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			var hash = hash || (typeof url === 'boolean' ? url : false);
			var url = /^http(s)?:\/\//i.test(url)? url : window.location.href;
            if(!hash){
                url = url.replace(/#(.*)?/i,'');
            }
			var r = url.substr(url.indexOf(hash ? '#' : '?')+1).match(reg); 
			if (r != null) 
			return decodeURIComponent(r[2]);
			return null; 
	    },
	    updateUrlParam: function (url, name, value, callback) {
		   var r = url,
		   		change = false,
		   		v = "";
		   if (r != null && r != 'undefined' && r != "") {
		    value = encodeURIComponent(value);
		    var reg = new RegExp("(^|)" + name + "=([^&]*)(|$)");
		    var tmp = name + "=" + value;
		    if (url.match(reg) != null) {
		     r = url.replace(eval(reg), tmp);
		     v = url.match(reg)[2];
		     change = value!== v;
		    }
		    else {
		     if (url.match("[\?]")) {
		      r = url + "&" + tmp;
		     } else {
		      r = url + "?" + tmp;
		     }
		     change = true;
		    }
		   }
		   if(typeof callback==="function")
		   		callback({
		   			url: r,
		   			_url: url,
		   			name: name,
		   			value: value,
		   			_value: v,
		   			change: change
		   		});
		   return r;
		},
	    toHump: function(str, isbig){
	    	var _strs = $.trim(str).split(/\s+/);
	    	var out = "";
	    	$.each(_strs, function(index, word) {
	    		out += arm.utils.firstUpper(word);
	    	});
	    	if(!isbig)
	    		out = arm.utils.firstLower(out);
	    	return out;
	    },
        utils: (function(){
        	var me = {};
			var _elementStyle = document.createElement('div').style;
		    function _prefixStyle (style) {
		    	if(style in _elementStyle)
		    		return style;
		    	var vendors = ['webkit', 'Moz', 'ms', 'O'],
		            s,
		            i = 0,
		            l = vendors.length;

		        for ( ; i < l; i++ ) {
		            s = vendors[i] + style.charAt(0).toUpperCase() + style.substr(1);
		            if ( s in _elementStyle) return s;
		        }
		        return false;
		    }
        	me.getTime = Date.now || function getTime () { return new Date().getTime(); };
        	me.generateGUID = function(namespace){
		        var uid = namespace + '-' || 'arm-';
		        do {
		            uid += Math.random().toString(36).substring(2, 7);
		        } while (document.getElementById(uid));

		        return uid;
		    };

		    // 首字母大写
		    me.firstUpper = function(str){
		    	return str.replace(/^\w|\s\w/g,function(v){return v.toUpperCase()});
		    };

		    // 首字母小写
		    me.firstLower = function(str){
		    	return str.replace(/^\w|\s\w/g,function(v){return v.toLowerCase()});
		    };


        	me.realPath = function (path, base){
				var path = path || "";
				path = path.replace(/^\.\//,'');
				while (path.match(/^\.\.\//)) {
				    path = path.replace(/\.\.\//, "");
				    base = base.replace(/[^\/]+\/$/,"");
				}
				return base + path;

			};

			me.realBase = function(){
				var base = arm.config('base');
				base = typeof base === "string" ? base : arm.path;
				return arm.R._http.test(base) ? base : me.realPath(base, arm.path);
			};

			me.moduleUrl = function(module){
				if(arm.R._http.test(module)){
					return module;
				}
				if(/^\//.test(module)){
					return location.protocol + "//"+ location.host + module;
				}
				var base = me.realBase();
				var url = me.realPath(module, base);
				return url;
			};

			me.moduleType = function (module){
				var moduleName = module.substring(module.lastIndexOf('/')+1);
				var moduleType = moduleName.substr(moduleName.lastIndexOf('.'));
				
				var isCSS = /^\.css(\W)?/.test(moduleType);
				var noCSSJS = !/[\?|&]/.test(module)&&!/^\.(js|css)(\W)?/.test(moduleType);
			    return {
			    	name: moduleName.replace(/[^a-zA-Z0-9\-]/g, '-'),
			    	isCSS : isCSS,
			    	isJS: isCSS ? false : true,
			    	noCSSJS: noCSSJS
			    }
			};

		    me.getJSUrl = function(){
		        var js = document.scripts;
		        for (var i = js.length -1; i >=0; i--) {
		        	if(js[i].src&&!js[i].getAttribute('merge'))
		        		return js[i].src;
		        };
		        return location.href;
		    };

		    // getClassFn by reg
		    me.getClassFn = function(regStr){
		    	var reg = new RegExp(regStr, "ig");
		    	return function(index, oldClassName){
		    		var c = oldClassName.match(reg);
		    		return c ? c.join(" ") : "";
		    	}
		    }

		    // error
		    me.error = function(log, e, type){
		        type = (type ? (type+" ") :"") + "Error：";
		        window.console && console.error(type + (log || '') + '\n'+ (e || ''));
		        return log;
		    };

		    me.extend = function (target, obj) {
		        for ( var i in obj ) {
		            target[i] = obj[i];
		        }
		    };

		    me.addEvent = function (el, type, fn, capture) {
		        el.addEventListener(type, fn, !!capture);
		    };

		    me.removeEvent = function (el, type, fn, capture) {
		        el.removeEventListener(type, fn, !!capture);
		    };

		    me.prefixPointerEvent = function (pointerEvent) {
		        return window.MSPointerEvent ? 
		            'MSPointer' + pointerEvent.charAt(9).toUpperCase() + pointerEvent.substr(10):
		            pointerEvent;
		    };

		    me.momentum = function (current, start, time, lowerMargin, wrapperSize, deceleration) {
		        var distance = current - start,
		            speed = Math.abs(distance) / time,
		            destination,
		            duration;

		        deceleration = deceleration === undefined ? 0.0006 : deceleration;

		        destination = current + ( speed * speed ) / ( 2 * deceleration ) * ( distance < 0 ? -1 : 1 );
		        duration = speed / deceleration;

		        if ( destination < lowerMargin ) {
		            destination = wrapperSize ? lowerMargin - ( wrapperSize / 2.5 * ( speed / 8 ) ) : lowerMargin;
		            distance = Math.abs(destination - current);
		            duration = distance / speed;
		        } else if ( destination > 0 ) {
		            destination = wrapperSize ? wrapperSize / 2.5 * ( speed / 8 ) : 0;
		            distance = Math.abs(current) + destination;
		            duration = distance / speed;
		        }

		        return {
		            destination: Math.round(destination),
		            duration: duration
		        };
		    };

		    var _transform = _prefixStyle('transform');
		    me.prefixStyle = _prefixStyle;
		    me.extend(me, {
		        hasTransform: _transform !== false,
		        hasPerspective: _prefixStyle('perspective') in _elementStyle,
		        hasTouch: 'ontouchstart' in window,
		        hasPointer: window.PointerEvent || window.MSPointerEvent, // IE10 is prefixed
		        hasTransition: _prefixStyle('transition') in _elementStyle
		    });
		    // This should find all Android browsers lower than build 535.19 (both stock browser and webview)
		    me.isBadAndroid = /Android /.test(window.navigator.appVersion) && !(/Chrome\/\d/.test(window.navigator.appVersion));

		    me.extend(me.style = {}, {
		        transform: _transform,
		        transitionTimingFunction: _prefixStyle('transitionTimingFunction'),
		        transitionDuration: _prefixStyle('transitionDuration'),
		        transitionDelay: _prefixStyle('transitionDelay'),
		        transformOrigin: _prefixStyle('transformOrigin')
		    });

		    me.hasClass = function (e, c) {
		        var re = new RegExp("(^|\\s)" + c + "(\\s|$)");
		        return re.test(e.className);
		    };

		    me.addClass = function (e, c) {
		        if ( me.hasClass(e, c) ) {
		            return;
		        }

		        var newclass = e.className.split(' ');
		        newclass.push(c);
		        e.className = newclass.join(' ');
		    };

		    me.removeClass = function (e, c) {
		        if ( !me.hasClass(e, c) ) {
		            return;
		        }

		        var re = new RegExp("(^|\\s)" + c + "(\\s|$)", 'g');
		        e.className = e.className.replace(re, ' ');
		    };

		    me.offset = function (el) {
		        var left = -el.offsetLeft,
		            top = -el.offsetTop;

		        // jshint -W084
		        while (el = el.offsetParent) {
		            left -= el.offsetLeft;
		            top -= el.offsetTop;
		        }
		        // jshint +W084

		        return {
		            left: left,
		            top: top
		        };
		    };

		    me.preventDefaultException = function (el, exceptions) {
		        for ( var i in exceptions ) {
		            if ( exceptions[i].test(el[i]) ) {
		                return true;
		            }
		        }

		        return false;
		    };

		    me.extend(me.eventType = {}, {
		        touchstart: 1,
		        touchmove: 1,
		        touchend: 1,

		        mousedown: 2,
		        mousemove: 2,
		        mouseup: 2,

		        pointerdown: 3,
		        pointermove: 3,
		        pointerup: 3,

		        MSPointerDown: 3,
		        MSPointerMove: 3,
		        MSPointerUp: 3
		    });

		    me.extend(me.ease = {}, {
		        quadratic: {
		            style: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
		            fn: function (k) {
		                return k * ( 2 - k );
		            }
		        },
		        circular: {
		            style: 'cubic-bezier(0.1, 0.57, 0.1, 1)',   // Not properly "circular" but this looks better, it should be (0.075, 0.82, 0.165, 1)
		            fn: function (k) {
		                return Math.sqrt( 1 - ( --k * k ) );
		            }
		        },
		        back: {
		            style: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
		            fn: function (k) {
		                var b = 4;
		                return ( k = k - 1 ) * k * ( ( b + 1 ) * k + b ) + 1;
		            }
		        },
		        bounce: {
		            style: '',
		            fn: function (k) {
		                if ( ( k /= 1 ) < ( 1 / 2.75 ) ) {
		                    return 7.5625 * k * k;
		                } else if ( k < ( 2 / 2.75 ) ) {
		                    return 7.5625 * ( k -= ( 1.5 / 2.75 ) ) * k + 0.75;
		                } else if ( k < ( 2.5 / 2.75 ) ) {
		                    return 7.5625 * ( k -= ( 2.25 / 2.75 ) ) * k + 0.9375;
		                } else {
		                    return 7.5625 * ( k -= ( 2.625 / 2.75 ) ) * k + 0.984375;
		                }
		            }
		        },
		        elastic: {
		            style: '',
		            fn: function (k) {
		                var f = 0.22,
		                    e = 0.4;

		                if ( k === 0 ) { return 0; }
		                if ( k == 1 ) { return 1; }

		                return ( e * Math.pow( 2, - 10 * k ) * Math.sin( ( k - f / 4 ) * ( 2 * Math.PI ) / f ) + 1 );
		            }
		        }
		    });

		    me.tap = function (e, eventName) {
		        var ev = document.createEvent('Event');
		        ev.initEvent(eventName, true, true);
		        ev.pageX = e.pageX;
		        ev.pageY = e.pageY;
		        e.target.dispatchEvent(ev);
		    };

		    me.dispatchEvent = function(type, e, target){
		    	var ev = document.createEvent('MouseEvents');
		        ev.initMouseEvent(type, true, true, e.view, 1,
		            target.screenX, target.screenY, target.clientX, target.clientY,
		            e.ctrlKey, e.altKey, e.shiftKey, e.metaKey,
		            0, null);

		        ev._constructed = true;
		        target.dispatchEvent(ev);
		    };

		    me.click = function (e) {
		        var target = e.target,
		            ev;

		        if ( !(/(SELECT|INPUT|TEXTAREA)/i).test(target.tagName) ) {
		            me.dispatchEvent("click", e, target);
		        }
		    };

			return me;
        })()
	});

	/**
	** 外部接口
	**/

	// 防冲突
	var _arm = window.arm,
		_A = window.A;

	// 全局接口
	window.arm = window.A = arm;

	// 冲突控制
	arm.noConflict = function( deep ) {
		// 移交 A
		window.A = _A;
		if ( deep && _arm === arm ) {
			// 移交 arm 给第一个版本
			window.arm = _arm;
		}
		arm._isConflict = true;
		return arm;
	};

	var varArm = arm.getUrlParam('var', arm.srcUrl);

	if(varArm&&/^[a-zA-Z|_]+/.test(varArm)&&!window[varArm]){
		arm.pt._var = varArm;
		window[varArm] = arm;
	}

	// 模块接口
	if("function"=== typeof define){
		define('arm', function(){
			return arm;
		})
	}

	return arm;
})(JQUERY);

// $ extend
(function(win, $, A){
	$.fn.emulateTransitionEnd  = function(duration) {
          var called = false;
          var $el = this;

          $(this).one(A.support.transition.end, function() {
            called = true;
          });

          var callback = function() {
            if (!called) {
              $($el).trigger(A.support.transition.end);
            }
            $el.transitionEndTimmer = undefined;
          };
          this.transitionEndTimmer = setTimeout(callback, duration);
          return this;
    };

    $.extend(true, A, {
    	// touch事件
        touchEvents: (function(){
            // 触摸事件
            var events = arm.support.touch ? ['touchstart','touchmove','touchend','touchcancel'] : ['mousedown',"mousemove","mouseup","mouseout"];

            // 判断IE
            var browser = {
                ie10 : win.navigator.msPointerEnabled,
                ie11 : win.navigator.pointerEnabled
            };
            if (browser.ie10) events = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp', 'MSPointerCancel'];
            if (browser.ie11) events = ['pointerdown', 'pointermove', 'pointerup', 'pointercancel'];
            // 触摸赋值
            return {
                touchStart : events[0],
                touchMove : events[1],
                touchEnd : events[2],
                touchCancel : events[3]
            };

        })(),
        inAarryObj: A.inArrayObj
    });
    
})(window, JQUERY, window.arm);

/**
 * arm.register
 * @authors Nat Liu (<EMAIL>)
 * @date    2015-12-14 14:06:55
 * @version 2015-12-14 14:06:55
 */
// 通用插件注册

;!function($, A){
	function Plugin(name, fn, single, options){
    	if(!this.length)
    		return this;
    	if(single){
    		if(this.length>1){
    			A.console().warn("插件", name, "不支持多元素实例化，默认实例化第1个元素！");
    		}
    		return new fn(this[0], options);
    	}
    	var instance = [];
        this.each(function(index, el) {
            instance[index] = new fn(el, options);
        });
        this[name] = instance;
        return this;
    }

	A.register = function(name, fn, single){
		// 注册插件
    	$[name] = A[name] = function(selector, options){
			var $el = $(selector);
			if($el.selector!==selector){
				$el = $(document);
				options = options || selector;
			}
	        return Plugin.call($el, name, fn, single, options);
	    };
	    $.fn[name] = A.pt[name]= function(options){
	        return Plugin.call(this.$||this, name, fn, single, options);
	    }
	}
}(JQUERY,window.arm);


/**
 * arm.module
 * @authors Nat Liu (<EMAIL>)
 * @date    2015-12-03 13:21:13
 * @version 2015-12-03 13:21:13
 */
// 模块加载
;!function($, A, win){
	var doc = document;
	var utils = A.utils;
	var head = doc.head || doc.getElementsByTagName("head")[0] || doc.documentElement;
	var currentScript,interactiveScript;
	var modulesQueue = [];
	var requesting = false;
	var pollingTimer = 10;
	var STATUS = {
		SAVED: 1,	// 创建模块
		LOADING: 2,	// 正在加载
		PENDING:3,	// 加载依赖
		READY:4,	// 依赖完成
		ERROR:5     // 失败
	}

	function getMoudleId(id){
		if(!id||!id.length)
			return;
		var alias = A.config("alias");
    	if($.isPlainObject(alias)){
    		$.each(alias, function(key, val) {
    			id.replace(key, val);
    		});
    	}
    	id = A.R._http.test(id) ? id : utils.moduleUrl(id);
    	var type = utils.moduleType(id);
    	if(type.noCSSJS){
			id+=".js"; // 默认为JS类型模块
		}
		return id;
	}

	function getModIds(id){
		var ids = id || [];
    	if(typeof id === "string")
    		ids = [id];
    	var mids = [];
		$.each(ids, function(index, mid) {
		    mids.push(getMoudleId(mid));
    	});
    	return mids;
	}

	function getCurrentScript(base) {
		if(currentScript)
			return currentScript;
		if(doc.currentScript)
			return doc.currentScript.src;

		if (interactiveScript && interactiveScript.readyState === 'interactive') {
			return interactiveScript.src;
		}
	    var stack;
	    try {
	        a.b.c() //强制报错,以便捕获e.stack
	    } catch (e) { //safari的错误对象只有line,sourceId,sourceURL
	        stack = e.stack
	        if (!stack && window.opera) {
	            //opera 9没有e.stack,但有e.Backtrace,但不能直接取得,需要对e对象转字符串进行抽取
	            stack = (String(e).match(/of linked script \S+/g) || []).join(" ")
	        }
	    }
	    if (stack) {
	        stack = stack.split(/[@ ]/g).pop() //取得最后一行,最后一个空格或@之后的部分
	        stack = stack[0] === "(" ? stack.slice(1, -1) : stack.replace(/\s/, "") //去掉换行符
	        return stack.replace(/(:\d+)?:\d+$/i, "") //去掉行号与或许存在的出错字符起始位置
	    }
	    var nodes = (base ? doc : head).getElementsByTagName("script") //只在head标签中寻找
	    for (var i = nodes.length, node; node = nodes[--i];) {
	        if (node.readyState === "interactive") {
	            return node.hasAttribute ? node.src : node.getAttribute("src", 4);
	        }
	    }
	}

	// 回调轮循
	function callbackQueues(ids, time, callback, uri){
		var exports = [];
		time = (time || 0) + 1;
		if(!ids.length){
			return callback(time, exports);
		}
		var deps = 0, callback = $.isFunction(callback) ? callback : $.noop, uri = uri || false;
		$.each(ids, function(index, id) {
			var dep = A.modules[id];
			if((dep.status<STATUS.READY&&$.inArray(uri, dep.deps)===-1)){
				return false;
			}
			deps++;
		});

		if(deps===ids.length){
			for (var i = 0; i < deps; i++) {
				exports.push(A.modules[ids[i]].exports);
			}
			return callback(time, exports);
		}
		setTimeout(function(){
			callbackQueues(ids, time, callback, uri);
		},pollingTimer);
		
	}

	// 开始载入模块
	function requestMod(mod){
		if(mod.status>STATUS.SAVED){
			return setRequest();
		}
		mod.status = STATUS.LOADING;
		mod.requestTime = utils.getTime();
	    var node = document.createElement(mod.type.isCSS ? 'link' : 'script');
	    node.charset = A.config("charset") || "utf-8";
	    if(mod.type.isCSS){
	        node.type = 'text/css';
	        node.rel = 'stylesheet';
	    }else{
	    	node.type = 'text/javascript';
	    	if(mod.async){
		    	node.async = mod.async;
		    }
	    	currentScript = mod.uri;
	    }
	    loadedMod(node, mod);
		node[mod.type.isCSS ? 'href' : 'src'] = mod.uri;
	    head.appendChild(node);
	    currentScript = null;
	    return setRequest();
	}

	function setRequest(){
		var m = modulesQueue.shift();
		if(!m||!A.modules[m])
			return requesting = false;
		var mod = A.modules[m];
		requesting = true;
		requestMod(mod);
	}

	// 模块加载完成
	function loadedMod(node, mod){

		var onload = function(error){
			node.onload = node.onerror = node.onreadystatechange = null;
		    if (!A.config("debug")&&!mod.type.isCSS) {
		        head.removeChild(node);
		    }
		    node = null;
		    mod.loadTime = utils.getTime();
		    if(error)
		    	mod.status = STATUS.ERROR;

			A.console()[error ? 'error':'log']("加载模块->", mod.uri, error ? "失败":"成功",",耗时", (mod.loadTime-mod.requestTime)/1000,"秒");
			// 启动依赖模块巡检
		    mod.polling();
		}
		if('onload' in node){
			node.onload = function(){
				onload();
			};
			node.onerror = function() {
		       onload(true);
		    }
		}else{
			node.onreadystatechange = function() {
                if (/loaded|complete/i.test(node.readyState)) {
                    onload();
                }
            }
		}
	    
	}

	// 构建模块
	function Module(id){
		var async = false;
		if(/:async/i.test(id)){
			async = true;
			id = id.replace(/:async/i, '');
		}
    	this.uri = id;
    	this.deps = [];
    	this.times = 0;
    	this.exports = undefined;
    	this.type = utils.moduleType(id);
    	this.status = STATUS.SAVED;
    	this.async = async;
    	A.modules[id] = this;
	}

	Module.prototype.compile = function(){
		var mod = this,
			port = mod.factory;
			
        	if($.isFunction(port)){
        		function require(ids, callback){
					if($.isFunction(callback)){
						return A.use(ids, callback);
					}
					var exports = [];
					$.each(getModIds(ids), function(index, id) {
						var _mod = A.module[id];
						exports.push(_mod ? _mod.exports : undefined);
					});
					if(exports.length===1)
						return exports[0];
					return exports;
				}
        		port = port(require, mod);
        	}
        	if(typeof mod.exports ==="undefined")
        		mod.exports = port;

        	mod.status = STATUS.READY;
        	A.console().log("编译模块->", mod.uri, ',巡检', mod.times,"次" );
	}

	Module.prototype.polling = function(){
		var mod = this;
		callbackQueues(mod.deps, 0, function(time){
			mod.times = time;
			mod.compile();
		}, mod.uri);
		
	}

	// 定义模块
    Module.define = function(id, deps, factory){
    	var argsLen = arguments.length;
		// define(factory)
		if (argsLen === 1) {
			factory = id;
			id = undefined;
		}
		else if (argsLen === 2) {
			factory = deps;
			// define(deps, factory)
			if ($.isArray(id)) {
			  deps = id;
			  id = undefined;
			}
			// define(id, factory)
			else {
			  deps = undefined;
			}
		}

		deps = getModIds(deps);
		var uri = getMoudleId(id) || getCurrentScript();
		var module = arm.modules[uri];

		if(!module){
			var newMod = true;
			module = new Module(uri);
			A.modules[uri] = module;
		}
		
		if(deps.length){
			module.status = STATUS.PENDING;
			// 模块依赖
			A.console().info("模块", module.uri, "依赖于", deps);
			module.deps = deps;
			A.use(deps);
		}
    	module.factory = factory;

    	if(newMod)
    		module.polling();
    }

    Module.use = function(ids, callback){

    	if(typeof ids == "function")
    		return ids();

    	var mids = getModIds(ids);
    	if(!mids.length)
    		return ids;

    	var queue = [];
    	for (var i = 0; i < mids.length; i++) {
    		var id = mids[i],
    		    mod = A.modules[id];
    		    if(!mod){
    		    	mod = new Module(id);
    		    	modulesQueue.push(id);
    		    }else{
    		    	A.console().log('模块-> '+id+' 已加载！');
    		    }
    		    if(!mod.type.isCSS)
    		    	queue.push(id);
    	};
    	callbackQueues(queue, 0, function(time, _exports){
			$.isFunction(callback)&&callback.apply(this, _exports);
		});
		if(!requesting)
    		setRequest();
    }

    $.extend(true, A, {
        // arm框架根目录
        root: (function(){
            return A.path.replace(/js\//,"");
        })(),

        modules: {},

        // 模块加载
	    use: Module.use,
	    define: Module.define
    });
 
}(JQUERY, window.arm, window)

/**
 * arm.app
 * @authors Nat Liu (<EMAIL>)
 * @date    2015-06-16 15:11:20
 * @version 2015-06-16 15:11:20
 */

!function($, A){
	var app = {}, config = A.config({
		appConfig:{}
	});
	// config
	app.config = function(options, single){
		config = A.config($.extend(true, {
			templateUrl: 'template.html'
		}, options || {}), single);
		if(config.console){
			A.use(A.path+'vconsole.js');
		}
		return config;
	};

	app.setConfig = function(obj){
		var appConfig = A.config('appConfig') || {};
		$.extend(true, appConfig, obj || {});
		A.config({
			appConfig: appConfig
		});
	};

	app.getConfig = function(key){
		var appConfig = A.config('appConfig') || {};
		var value = appConfig[key] || '';
		if(typeof value === "string"){
			value = value.replace('{apppath}', A.refer);
		}
		return value;
	}

	// app主入口
	function appRun(preMods, modules, callback){
		// 运行APP
		A.console().log("APP 运行成功！", new Date());
		if(preMods && preMods.length){
			A.use(preMods, function(){	
				A.use(modules, callback);
			});
		}else{
			A.use(modules, callback);
		}
	};

	var template = null,
		templateError = 0;

	function getTemplate(name, callback){
		if("string" == typeof name && 'function'== typeof callback){
			var regx = new RegExp('<!--(\\s+)?begin(\\s+)?:(\\s+)?'+name+'(\\s+)?-->([\\S\\s]*)?<!--(\\s+)?end(\\s+)?:(\\s+)?'+name+'(\\s+)?-->','i');
			var matches = template.match(regx);

			if(matches && matches[5]){
				callback(matches[5]);
			}
		}
	}

	app.template = function(name, callback){
		if(template!=null){
			return getTemplate(name, callback);
		}
		app.config('templateUrl') && $.ajax({
			url: app.config('templateUrl'),
			type:"get",
			dataType: 'html',
			success:function(data){
				template = data;
				getTemplate(name, callback);
			},
			error:function(){
				templateError++;
				if(name===true && templateError < 3){
					app.template(name, callback);
				}
			}
		});
	};
	app.template(true); // 加载模板文件
	//初始化
	app.init = function(modules, callback){
		if(config.base=='./'){
			config.base = A.refer;
		}
		if(typeof modules === "function"){
			callback = modules;
			modules = [];
		}
		if(typeof callback !== "function"){
			callback = function(){};
		}
		if((!$.isArray(modules) && typeof modules !== "string") || !modules.length){
			modules = [];
		}
		var preMods = ['config'];
		if(config.console){
			preMods.unshift(A.path+'vconsole.js');
		}
		if(!config.debug){
			preMods.push('config.custom');
		}
		if(config.iPortal == false){
			A.console(true).log('iPortal is disabled');
			window.iPortal = {
				isReady:true
			};
			return appRun(preMods, modules, callback);
		}
		
		window.iPortal = {
			"sudyInitCallback" : (function() {
				var init = false;
				return function(context) {
					if (init) {
						return init; 
					}
					init = true;
					window.iPortal.context = context;
					var script = document.createElement('script');
					var vt = '?_vt='+ Number(new Date());
					var src = /\?/.test(context.libPath) ? context.libPath.replace(/\?/,vt+"&") : context.libPath + vt;
					script.type = 'text/javascript';
					script.src = src;
					script.charset = "utf-8";
					document.getElementsByTagName("head")[0].appendChild(script);
					return init;
				};
			})()
		};
		if(!iPortal.isReady){
			A.console().log('iPortal is not ready!');
			iPortal.onReady = function(){
				A.console().log('iPortal is ready!');
				appRun(preMods, modules, callback);
			}
		}else{
			A.console().log('iPortal is ready!');
			appRun(preMods, modules, callback);
		}
	};

	// 对外接口
	window.app = window.APP = A.app = app;

}(JQUERY, window.arm);