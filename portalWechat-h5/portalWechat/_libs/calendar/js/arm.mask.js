/**
 * arm.mask
 * @authors <PERSON> (<EMAIL>)
 * @date    2015-12-07 11:35:39
 * @version 2015-12-07 11:35:39
 */

!function($, A) {

	var defaults = {
        bg:"rgba(0,0,0,0.25)",
        maskFix: true
    }
    var Mask = function($parent, $mask, args){
        this.element = $parent;
        this.masklayer = $mask;
        this.maskliving = [];
        this.args = $.extend(true, {}, defaults, args);
        this.creat();
    }
    Mask.prototype = {

        creat: function(){
            var args = this.args;
            this.reset(args);
            this.masklayer.appendTo(this.element);
        },
        remove: function(index){
            // 如果有其他实例，不删除
            var id = $.inArray(index, this.maskliving);
            if(id===-1)
            	return;
            this.maskliving.splice(id,1);
            if(this.maskliving.length>0){
            	return this.masklayer.css("z-index", this.maskliving[this.maskliving.length-1]);
            }
            this.masklayer.remove();
            this.element.removeClass('ui-mask ui-mask-fix');
            this.element.data("hasmask", false);
            this.element.off(A.touchEvents.touchMove, A.prevent);
        },
        reset: function(args){
            this.args = $.extend(true, {}, defaults, args || {});
            this.masklayer.css({
            	"background":this.args.bg,
            	"zIndex": this.args.zIndex
            });
            // 调用一次增加一个索引
            this.maskliving.push(this.args.zIndex);
            if(this.args.maskFix){
                this.element.addClass('ui-mask-fix');
            }else{
                this.element.removeClass('ui-mask-fix');
            }

        }
    }

	A.mask = function(args, element){
        	if(!args)return;
            var $parent = $(element || "body").addClass('ui-mask').eq(0);
            var maskliving = $parent.data("maskliving") || [];
            // 调用一次增加一个索引
            maskliving.push(1);
            $parent.data("maskliving", maskliving);
            var mid = arm.utils.generateGUID("ui-mask");
            var $mask = $parent.children('ui-mask-layer');
            if($mask.length>0){
                $mask.attr("id", mid);
            }else{
                $mask = $("<div class=\"ui-mask-layer\" id=\"" + mid +"\" />");
                if(/body/i.test($parent[0].tagName))$mask.addClass("ui-mask-body");
            }
            var hasMask = $parent.data("hasmask");
            if(hasMask){
                hasMask.reset(args);
            }else{
                hasMask = new Mask($parent, $mask, args);
                $parent.data("hasmask", hasMask);
            }
            return hasMask;
        }
}(window.jQuery || window.Zepto, window.arm)