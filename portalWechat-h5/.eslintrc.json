{
  "extends": ["react-app", "prettier"],
  "plugins": [ "react", "react-hooks","prettier" ],
  "rules":{
    "no-console": "off",
    "no-alert": 0, //禁止使用alert confirm prompt
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "prettier/prettier": ["error", { "endOfLine": "lf" }],
    "jsx-quotes": [1, "prefer-single"],
    "no-class-assign": "error",
    "no-dupe-keys": "error",
    "no-dupe-args": "error",
    "no-duplicate-case": "error",
    "no-fallthrough": "error",
    "no-func-assign": "error",
    // "linebreak-style": [0, "windows"],
    "no-multi-spaces": "warn",
    "no-var": "error",
    "eqeqeq": [2, "allow-null"],
    "quotes": [2, "single"],
    "no-unreachable": "error",
    // "camelcase": "warn",
    "react/jsx-key": 2,
    // "react/jsx-pascal-case":"allowAllCaps",
    "react/jsx-max-props-per-line": 0,
    "space-infix-ops": "error",
    "no-mixed-spaces-and-tabs": [ 2, false ], //禁止混用tab和空格
    "no-multiple-empty-lines": [ 1, {
      "max": 3
    } ], //空行最多不能超过2行
    "no-extra-semi": 2, //禁止多余的冒号
    "no-debugger": 2, //禁止使用debugger
    "semi": [ 2, "never" ], //语句强制分号结尾  不要分号
    "eol-last": 2, // 文件末尾强制换行
    "semi-spacing": [ 0, {
      "before": false,
      "after": true
    } ], //分号前后空格
    "arrow-body-style": 0, // 不禁止箭头函数直接return对象
    "strict": 2, //使用严格模式
    "use-isnan": 2, //禁止比较时使用NaN，只能用isNaN()
    "valid-typeof": 2, //必须使用合法的typeof的值
    "space-in-parens": [ 0, "always" ],
    "import/no-anonymous-default-export": ["error", {
      "allowArray": false,
      "allowArrowFunction": false,
      "allowAnonymousClass": false,
      "allowAnonymousFunction": true,
      "allowCallExpression": true, // The true value here is for backward compatibility
      "allowLiteral": false,
      "allowObject": true
    }],
    "jsx-a11y/anchor-is-valid": [
      "off",
      {
        "components": ["Link"],
        "specialLink": ["hrefLeft", "hrefRight"],
        "aspects": ["noHref", "invalidHref", "preferButton"]
      }
    ]
  }
}